module.exports = {
  "presets": [["@vue/cli-plugin-babel/preset", { "modules": false, targets: {
    chrome: 90
  } }]],
  "plugins": [
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator',
    '@babel/plugin-proposal-numeric-separator',
    [
      "component",
      {
        "libraryName": "element-ui",
        "styleLibraryName": "theme-chalk"
      }
    ],
    // 移除 babel-plugin-lodash 以避免兼容性问题
    // 使用 LodashModuleReplacementPlugin 在 webpack 层面优化
  ]
}
