<template>
  <Modal
    :value="value"
    :mask-closable="false"
    :styles="{ top: '45px' }"
    @on-visible-change="visibleChange"
    :width="600"
    title="分发诊所"
  >
    <div class="clinic-select">
      <span class="form-item-required"> 分发：</span>
      <Select v-model="status" style="width: 300px" placeholder="请选择对全部诊所分发状态">
        <Option v-for="item in options" :key="item.id" :label="item.desc" :value="item.id"></Option>
      </Select>
    </div>
    <div slot="footer">
      <Button type="primary" @click="handleConfirm" :loading="submitLoading">确定</Button>
      <Button @click="visibleChange(false)">取消</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ResourceSettingModal',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    resourceId: {
      type: String,
      default: '',
    },
    options: {
      type: Array,
      default: () => [],
    },
    defaultDistribute: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      status: '',
      submitLoading: false,
    };
  },
  methods: {
    visibleChange(v) {
      if (!v) {
        this.$emit('input', false);
      } else {
        console.log(this.defaultDistribute)
        this.status = this.defaultDistribute;
      }
    },
    handleConfirm() {
      this.submitLoading = true;
      this.$api
        .syncClinicResource({
          id: this.resourceId,
          status: this.status,
        })
        .then(res => {
          this.$Message.success('分发成功');
          this.$emit('distributed', this.status);
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.clinic-select {
  padding: 20px;
}
</style>
