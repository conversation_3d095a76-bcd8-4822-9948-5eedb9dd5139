<template>
  <div v-show="pageLoad">
    <Form
      ref="formData"
      :disabled="!isAllowEdit"
      :model="formData"
      :rules="rules"
      :label-width="120"
      class="form-wrapper"
    >
      <FormItem label="诊所编号" prop="clinic_code">
        <Input v-model="formData.clinic_code" disabled placeholder="请输入诊所编号" />
      </FormItem>

      <FormItem label="诊所名称" prop="name" :class="{ 'reason-error': reject_reason_obj['name'] }">
        <Input v-model="formData.name" maxlength="15" show-word-limit placeholder="请输入诊所名称" />
        <div class="reason-error--text" v-if="reject_reason_obj['name']">{{ reject_reason_obj['name'] }}</div>
      </FormItem>

      <div>
        <div class="address-wrapper">
          <FormItem
            label="诊所地址"
            prop="county_code"
            class="select-address"
            :class="{ 'reason-error': reject_reason_obj['address'] }"
          >
            <div class="address flex-1">
              <el-cascader
                v-model="selectedAddress"
                :options="options"
                clearable
                placeholder="请选择注册地址"
                size="small"
                popper-class="address-com"
                style="width: 100%"
                @change="regionChange"
                :disabled="!isAllowEdit"
              />
            </div>
            <div class="address-tip">地址将用于用户到店提货的地址指引和到店地址</div>
          </FormItem>

          <FormItem
            label=""
            :label-width="0"
            prop="address_other"
            class="detail-address"
            :class="{ 'reason-error': reject_reason_obj['address'] }"
          >
            <Input v-model.trim="formData.address_other" placeholder="详细地址" />
          </FormItem>
        </div>
        <div class="reason-error--text id-card-img-text ml120" v-if="reject_reason_obj['address']">
          {{ reject_reason_obj['address'] }}
        </div>
      </div>

      <FormItem label="营业时间" prop="work_time_desc" :class="{ 'reason-error': reject_reason_obj['work_time'] }">
        <el-popover
          v-model="popoverIsShow"
          trigger="click"
          transfer
          style="width: 100%"
          placement="bottom"
          popper-class="time-picker-poptip"
        >
          <div>
            <div class="rsj-checkbox-box flex flex-item-align">
              <div class="rsj-checkbox-group">
                <label
                  v-for="(date, index) in weekDays"
                  :key="index + 'date'"
                  :class="['rsj-checkbox-wrap', { 'rsj-checkbox-isChecked': date.isChecked }]"
                  @click="checkDate(date, index)"
                >
                  <span class="rsj-checkbox">
                    <span class="zent-checkbox-inner" />
                  </span>
                  <span>{{ date.desc }}</span>
                </label>
              </div>
              <div class="rsj-checkbox-btn">
                <Button type="primary" style="margin-left: 8px" @click="selectDateHandler">确定</Button>
              </div>
            </div>
          </div>
          <Input
            slot="reference"
            v-model="formData.work_time_desc"
            style="width: 100%; margin-bottom: 20px"
            readonly
            placeholder="请选择营业时间"
          />
        </el-popover>
        <div class="time-box flex">
          <Select v-model="formData.work_time.st" placeholder="请选择开始营业时间" style="flex: 1; margin-right: 12px">
            <Option
              v-for="(item, index) in getTimePieces('st')"
              :key="item.time"
              :label="item.time"
              :value="item.time"
            />
          </Select>
          <Select
            v-model="formData.work_time.et"
            :disabled="!formData.work_time.st"
            style="flex: 1"
            placeholder="请选择停止营业时间"
          >
            <Option
              v-for="(item, index) in getTimePieces('et')"
              :key="item.time"
              :label="item.time"
              :value="item.time"
            />
          </Select>
        </div>
        <div class="reason-error--text" v-if="reject_reason_obj['work_time']">{{ reject_reason_obj['work_time'] }}</div>
      </FormItem>

      <FormItem
        label="负责人姓名"
        prop="leading_person"
        :class="{ 'reason-error': reject_reason_obj['leading_person'] }"
      >
        <Input v-model="formData.leading_person" placeholder="请输入负责人姓名" />
        <div class="reason-error--text" v-if="reject_reason_obj['leading_person']">
          {{ reject_reason_obj['leading_person'] }}
        </div>
      </FormItem>

      <FormItem
        label="客服电话"
        prop="clinic_phone_no"
        :class="{ 'reason-error': reject_reason_obj['clinic_phone_no'] }"
      >
        <Input v-model="formData.clinic_phone_no" placeholder="请输入客服电话" />
        <div class="reason-error--text" v-if="reject_reason_obj['clinic_phone_no']">
          {{ reject_reason_obj['clinic_phone_no'] }}
        </div>
      </FormItem>

      <FormItem
        label="法人手机号"
        prop="legal_person_mobile"
        :class="{ 'reason-error': reject_reason_obj['legal_person_mobile'] }"
      >
        <Input v-model="formData.legal_person_mobile" maxlength="11" show-word-limit placeholder="请输入法人手机号" />
        <div class="reason-error--text" v-if="reject_reason_obj['legal_person_mobile']">
          {{ reject_reason_obj['legal_person_mobile'] }}
        </div>
      </FormItem>

      <FormItem label="身份证人像面照片" class="mt-30" prop="legal_person_id_card.front">
        <Picture v-model="formData.legal_person_id_card.front" :limit="1" :disabled="!isAllowEdit"> </Picture>
        <div class="upload-note">
          1.请上传彩色照片，要求正面拍摄，露出证件四角且清晰、完整，所有字符清晰可识别，不得反光或遮挡。不得翻拍、截图、镜像、PS。
          <br />
          2.图片只支持JPG、BMP、PNG格式，文件大小不能超过3M。
        </div>
        <!--        <div class="reason-error&#45;&#45;text" v-if="reject_reason_obj['legal_person_id_card']">-->
        <!--          {{ reject_reason_obj['legal_person_id_card'] }}-->
        <!--        </div>-->
      </FormItem>
      <FormItem
        prop="legal_person_id_card.back"
        class="mt-30"
        label="身份证国徽面照片"
        :class="{ 'reason-error': reject_reason_obj['legal_person_id_card'] }"
      >
        <Picture v-model="formData.legal_person_id_card.back" :limit="1" :disabled="!isAllowEdit"> </Picture>
        <div class="upload-note">
          1.请上传彩色照片，要求正面拍摄，露出证件四角且清晰、完整，所有字符清晰可识别，不得反光或遮挡。不得翻拍、截图、镜像、PS。
          <br />
          2.图片只支持JPG、BMP、PNG格式，文件大小不能超过3M。
        </div>
        <div class="reason-error--text" v-if="reject_reason_obj['legal_person_id_card']">
          {{ reject_reason_obj['legal_person_id_card'] }}
        </div>
      </FormItem>
      <FormItem
        label="门面图"
        prop="facade_images"
        class="mt-30"
        :class="{ 'reason-error': reject_reason_obj['facade_images'] }"
      >
        <div class="flex">
          <Picture v-model="formData.facade_images" :limit="9" :disabled="!isAllowEdit" />
        </div>
        <div class="reason-error--text img--text" v-if="reject_reason_obj['facade_images']">
          {{ reject_reason_obj['facade_images'] }}
        </div>
      </FormItem>

      <FormItem label="营业执照" prop="business_img" :class="{ 'reason-error': reject_reason_obj['business_img'] }">
        <div class="flex">
          <Picture v-model="formData.business_img" :limit="9" :disabled="!isAllowEdit" />
        </div>
        <div class="reason-error--text img--text" v-if="reject_reason_obj['business_img']">
          {{ reject_reason_obj['business_img'] }}
        </div>
      </FormItem>

      <FormItem label="中医备案证名称" prop="tcm_name" :class="{ 'reason-error': reject_reason_obj['tcm_name'] }">
        <Input v-model="formData.tcm_name" placeholder="请输入中医备案证名称" />
        <div class="reason-error--text" v-if="reject_reason_obj['tcm_name']">{{ reject_reason_obj['tcm_name'] }}</div>
      </FormItem>

      <FormItem label="中医备案证" prop="tcm_img" :class="{ 'reason-error': reject_reason_obj['tcm_img'] }">
        <div class="flex">
          <Picture v-model="formData.tcm_img" :limit="1" :disabled="!isAllowEdit" />
        </div>
        <div class="reason-error--text img--text" v-if="reject_reason_obj['tcm_img']">
          {{ reject_reason_obj['tcm_img'] }}
        </div>
      </FormItem>
      <FormItem label="入驻协议编号" prop="settle_code" :class="{ 'reason-error': reject_reason_obj['settle_code'] }">
        <Input v-model="formData.settle_code" placeholder="请输入入驻协议编号" />
        <div class="reason-error--text" v-if="reject_reason_obj['settle_code']">
          {{ reject_reason_obj['settle_code'] }}
        </div>
      </FormItem>

      <FormItem label="实体商品售卖" :class="{ 'reason-error': reject_reason_obj['business_img'] }">
        <RadioGroup v-model="formData.physical_goods_status">
          <Radio label="1">开通售卖</Radio>
          <Radio label="2">暂不售卖</Radio>
        </RadioGroup>
        <div class="sale-tips" style="color: #aaaaaa">开通后，诊所实体商品可在互联网医院的患者端中显示售卖。</div>
      </FormItem>

      <FormItem
        v-if="formData.physical_goods_status === '1'"
        label="食品经营许可证件"
        prop="food_business_img"
        :class="{ 'reason-error': reject_reason_obj['food_business_img'] }"
      >
        <div class="flex" v-if="formData.food_business_img.length || isAllowEdit">
          <Picture v-model="formData.food_business_img" :limit="9" :disabled="!isAllowEdit" />
        </div>
        <div v-else>-</div>
        <div class="reason-error--text img--text" v-if="reject_reason_obj['food_business_img']">
          {{ reject_reason_obj['food_business_img'] }}
        </div>
      </FormItem>
    </Form>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button
        v-if="audit_status !== 2 && isAllowEdit && !isEdit"
        type="primary"
        class="ml10"
        :loading="saveLoading"
        @click="submitForm"
      >
        {{ audit_status == 3 ? '修改' : '提交' }}
      </Button>
      <!-- 审核完成后修改的按钮。start -->
      <Poptip
        confirm
        v-model="commitTipVisible"
        v-if="audit_status == 2 && isAllowEdit && isEdit"
        @on-ok="submitForm"
        :transfer="false"
        @on-cacel="commitTipVisible = false"
      >
        <template slot="title">
          <div style="display: flex; flex-direction: column; align-items: start">
            <p>提交后平台会重新审核，在审核通</p>
            <p>过之前将继续使用当前入驻信息。</p>
            <p>确认提交修改信息？</p>
          </div>
        </template>
        <Button type="primary" class="ml10" :loading="saveLoading" @click.stop="handleOpenCommitTip"> 提交</Button>
      </Poptip>
      <Button
        v-if="audit_status == 2 && !isEdit"
        type="primary"
        class="ml10"
        :loading="saveLoading"
        @click="isEdit = true"
      >
        修改入驻信息
      </Button>
      <Button v-if="!isAllowEdit" type="primary" class="ml10" @click="editRecordVisible = true"> 修改记录</Button>

      <!-- 审核完成后修改的按钮。end -->
    </div>
    <edit-record
      :visible="editRecordVisible"
      :clinic-code="formData.clinic_code"
      @closeModel="handleCloseEditRecordModel"
    />
  </div>
</template>

<script>
import { CodeToText, regionData } from '@/libs/chinaMap';

import EditRecord from './component/edit-record.vue';
import { cloneDeep } from 'lodash-es';
import { deepEqual, isEmpty } from '../../../utils/helper';
import Picture from '@/components/upload/picture';

export default {
  name: 'Detail',
  components: {
    EditRecord, Picture
  },
  data() {
    /**
     * @description: 校验注册地址
     * */
    const countyValid = (rule, value, callback) => {
      if (!this.formData.county_code) {
        callback(new Error('请选择注册地址'));
      } else {
        callback();
      }
    };

    /**
     * @description: 校验详细地址
     * */
    const addressValid = (rule, value, callback) => {
      if (!this.formData.address_other) {
        callback(new Error('请输入详细地址'));
      } else {
        callback();
      }
    };

    /**
     * @description: 校验营业时间
     * */
    const workTimeValid = (rule, value, callback) => {
      let st_index = this.halfTimePieces.findIndex(st_item => st_item.time === this.formData.work_time.st);
      let et_index = this.halfTimePieces.findIndex(et_item => et_item.time === this.formData.work_time.et);
      console.log('-> st_index', st_index);
      if (!this.formData.work_time_desc) {
        callback(new Error('请选择营业时间'));
      } else if (!this.formData.work_time.st) {
        callback(new Error('请选择开始营业时间'));
      } else if (!this.formData.work_time.et) {
        callback(new Error('请选择停止营业时间'));
      } else if (this.formData.work_time.st && this.formData.work_time.et && st_index >= et_index) {
        callback(new Error('开始营业时间需大于停止营业时间'));
      } else {
        callback();
      }
    };

    const mobileValid = (rule, value, callback) => {
      let mobile = this.formData.legal_person_mobile;
      const reg = /^1[3456789]\d{9}$/;
      if (mobile === '') {
        callback(new Error('请输入法人手机号'));
      } else if (!reg.test(mobile)) {
        callback(new Error('请输入正确的手机号'));
      } else {
        callback();
      }
    };

    /**
     * @description: 校验法人身份证
     * */
    const idCardValid = (rule, value, callback) => {
      console.log('legal_person_id_card', this.formData.legal_person_id_card);
      const front = this.formData.legal_person_id_card.front;
      const back = this.formData.legal_person_id_card.back;
      if (!front && !back) {
        callback(new Error('请上传法人身份证照'));
      } else if (!front) {
        callback(new Error('请上传法人身份人像照'));
      } else if (!back) {
        callback(new Error('请上传法人身份国徽照'));
      } else {
        callback();
      }
    };

    return {
      commitTipVisible: false,
      editRecordVisible: false,
      formData: {
        out_clinic_id: '', // 外部诊所编号
        clinic_code: '', // 诊所编号
        name: '', // 诊所名称
        workdays: [], // 工作日期
        work_time_desc: '', // 营业时间
        work_time: {
          st: '', // 开始营业时间
          et: '', // 停止营业时间
        },
        leading_person: '', // 负责人姓名
        clinic_phone_no: '', // 客服电话
        legal_person_mobile: '', // 法人手机号
        legal_person_id_card: {
          front: '', // 人像照
          back: '', // 国徽照
        }, // 法人身份证

        facade_images: [], // 营业执照
        business_img: [], // 营业执照
        tcm_name: '', // 中医备案证名称
        tcm_img: [], // 中医备案证

        prov_name: '',
        prov_code: '',
        city_name: '',
        city_code: '',
        county_name: '',
        county_code: '',
        address_other: '', // 详细地址
        physical_goods_status: '1', // 实体商品售卖
        food_business_img: [], //食品经营
        settle_code: '',
      },
      saveLoading: false, // 创建loading
      // 规则校验
      rules: {
        clinic_code: [{ required: true, message: '请输入诊所编号', trigger: 'change' }],
        name: [{ required: true, message: '请输入诊所名称', trigger: 'change' }],
        county_code: [{ required: true, validator: countyValid, trigger: 'change' }],
        address_other: [{ required: true, validator: addressValid, trigger: 'change' }],
        work_time_desc: [{ required: true, validator: workTimeValid, trigger: 'change' }],
        leading_person: [{ required: true, message: '请输入负责人姓名', trigger: 'change' }],
        clinic_phone_no: [{ required: true, message: '请输入客服电话', trigger: 'change' }],
        legal_person_mobile: [{ required: true, validator: mobileValid, trigger: 'blur' }],
        'legal_person_id_card.front': [{ required: true, message: '请上传法人身份人像照', trigger: 'change' }],
        'legal_person_id_card.back': [{ required: true, message: '请上传法人身份国徽照', trigger: 'change' }],
        facade_images: [{ required: true, type: 'array', message: '请上传门面图', trigger: 'change' }],
        business_img: [{ required: true, type: 'array', message: '请上传营业执照', trigger: 'change' }],
        tcm_name: [{ required: true, message: '请输入中医备案证名称', trigger: 'change' }],
        tcm_img: [{ required: true, type: 'array', message: '请上传中医备案证', trigger: 'change' }],
        food_business_img: [{ required: true, type: 'array', message: '请上传食品经营许可证', trigger: 'change' }],
        settle_code: [{ required: true, message: '请输入入驻协议编号', trigger: 'change' }],
      },
      options: regionData,
      selectedAddress: [],

      // 营业时间
      popoverIsShow: false,
      weekDays: [
        { id: '1', kw: '1', desc: '周一' },
        { id: '2', kw: '2', desc: '周二' },
        { id: '3', kw: '3', desc: '周三' },
        { id: '4', kw: '4', desc: '周四' },
        { id: '5', kw: '5', desc: '周五' },
        { id: '6', kw: '6', desc: '周六' },
        { id: '7', kw: '7', desc: '周日' },
      ],
      halfTimePieces: [
        { time: '00:00', value: 0 },
        { time: '00:30', value: 0.5 },
        { time: '01:00', value: 1 },
        { time: '01:30', value: 1.5 },
        { time: '02:00', value: 2 },
        { time: '02:30', value: 2.5 },
        { time: '03:00', value: 3 },
        { time: '03:30', value: 3.5 },
        { time: '04:00', value: 4 },
        { time: '04:30', value: 4.5 },
        { time: '05:00', value: 5 },
        { time: '05:30', value: 5.5 },
        { time: '06:00', value: 6 },
        { time: '06:30', value: 6.5 },
        { time: '07:00', value: 7 },
        { time: '07:30', value: 7.5 },
        { time: '08:00', value: 8 },
        { time: '08:30', value: 8.5 },
        { time: '09:00', value: 9 },
        { time: '09:30', value: 9.5 },
        { time: '10:00', value: 10 },
        { time: '10:30', value: 10.5 },
        { time: '11:00', value: 11 },
        { time: '11:30', value: 11.5 },
        { time: '12:00', value: 12 },
        { time: '12:30', value: 12.5 },
        { time: '13:00', value: 13 },
        { time: '13:30', value: 13.5 },
        { time: '14:00', value: 14 },
        { time: '14:30', value: 14.5 },
        { time: '15:00', value: 15 },
        { time: '15:30', value: 15.5 },
        { time: '16:00', value: 16 },
        { time: '16:30', value: 16.5 },
        { time: '17:00', value: 17 },
        { time: '17:30', value: 17.5 },
        { time: '18:00', value: 18 },
        { time: '18:30', value: 18.5 },
        { time: '19:00', value: 19 },
        { time: '19:30', value: 19.5 },
        { time: '20:00', value: 20 },
        { time: '20:30', value: 20.5 },
        { time: '21:00', value: 21 },
        { time: '21:30', value: 21.5 },
        { time: '22:00', value: 22 },
        { time: '22:30', value: 22.5 },
        { time: '23:00', value: 23 },
        { time: '23:30', value: 23.5 },
        { time: '次日 00:00', value: 24 },
      ],

      reject_reason_obj: {},
      pageLoad: false,
      isEdit: false,
      audit_status: '',
      copyInitFormData: {},
    };
  },
  computed: {
    isAllowEdit() {
      if (this.isEdit) return this.isEdit;
      return this.audit_status == 0 || this.audit_status == 3;
    },

    getTimePieces() {
      return type => {
        let timePieces = [];
        if (type === 'st') {
          timePieces = this.halfTimePieces.slice(0, this.halfTimePieces.length - 1);
        } else {
          const index = this.halfTimePieces.findIndex(item => item.time === this.formData.work_time.st);
          timePieces = this.halfTimePieces.slice(index + 1, this.halfTimePieces.length);
        }
        return timePieces;
      };
    },
  },
  watch: {
    popoverIsShow() {
      this.$nextTick(() => {
        this.weekDays.map((item, index) => {
          if (this.formData.workdays.indexOf(item.id) !== -1) {
            this.$set(this.weekDays, index, { ...item, isChecked: true });
          } else {
            this.$set(this.weekDays, index, { ...item, isChecked: false });
          }
        });
      });
    },
  },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      this.copyInitFormData = {};
      this.getHospitalClinicInfo();
    },
    // /******* 创建诊所带出对应数据 ********/
    // getBeCreateClinic(clinic_code = '') {
    //   let params = {
    //     clinic_code,
    //   };
    //   this.$api.getHospitalClinicAddSearch(params).then(
    //     res => {
    //       let currentClinicObj = (res.list && res.list[0]) || {};
    //       this.echoClinicInfo(currentClinicObj);
    //     },
    //     error => {
    //       this.$Message.error(error.message);
    //     }
    //   );
    // },

    /**
     * @description: 回显诊所信息，添加诊所带出来的不包含法人身份证信息
     * */
    echoClinicInfo(obj = {}) {
      // this.formData.out_clinic_id = obj.out_clinic_id;
      // this.formData.id = obj.id;
      // this.formData.clinic_code = obj.clinic_code;
      // this.formData.name = obj.name;
      //
      // this.selectedAddress = [obj.prov_code, obj.city_code, obj.county_code];
      // this.formData.prov_code = obj.prov_code;
      // this.formData.prov_name = obj.prov_name;
      // this.formData.city_code = obj.city_code;
      // this.formData.city_name = obj.city_name;
      // this.formData.county_code = obj.county_code;
      // this.formData.county_name = obj.county_name;
      // this.formData.address_other = obj.address_other;
      //
      // this.formData.work_time_desc = obj.work_time_desc;
      // this.formData.workdays = obj.workdays;
      // this.formData.work_time.st = obj.work_time?.st;
      // this.formData.work_time.et = obj.work_time?.et;
      //
      // this.formData.leading_person = obj.leading_person;
      // this.formData.clinic_phone_no = obj.clinic_phone_no;
      // this.formData.legal_person_mobile = obj.legal_person_mobile;
      // this.formData.facade_images = obj.facade_images;
      // this.formData.business_img = obj.business_img;
      // this.formData.tcm_name = obj.tcm_name;
      // this.formData.tcm_img = obj.tcm_img;
      // this.formData.food_business_img = obj.food_business_img || [];
      // this.formData.settle_code = obj.settle_code || '';
      // this.formData.legal_person_id_card.front = obj.legal_person_id_card?.front;
      // this.formData.legal_person_id_card.back = obj.legal_person_id_card?.back;
      for (const objKey in this.formData) {
        obj[objKey] && (this.formData[objKey] = obj[objKey]);
      }
      if (obj.county_code) {
        this.selectedAddress = [obj.prov_code, obj.city_code, obj.county_code];
      } else {
        this.selectedAddress = [];
      }

      this.copyInitFormData = cloneDeep(this.formData);
    },

    /**
     * @description: 获取诊所详情
     * */
    getHospitalClinicInfo() {
      this.$api.getClinicSettledInfo().then(
        res => {
          this.audit_status = res.audit_status;
          this.isEdit = false;
          this.echoClinicInfo(res.clinic || {});
          res.reject_reason && this.handleRejectReason(res.reject_reason || []);
          this.pageLoad = true;
        },
        error => {}
      );
    },

    handleRejectReason(reject_reason = []) {
      this.reject_reason_obj = {};
      reject_reason?.forEach(item => {
        this.$set(this.reject_reason_obj, item.field, '驳回原因：' + item.reason);
      });
    },

    /**
     * @description: 创建诊所
     * */
    getHospitalClinicSave() {
      let params = {
        out_clinic_id: this.$route.query.out_clinic_id,
        ...this.formData,
      };
      const isChange = deepEqual(params, this.copyInitFormData);
      if (!isEmpty(this.copyInitFormData) && isChange) {
        this.$Message.error('当前信息未修改，请修改后提交');
        return;
      }
      this.saveLoading = true;
      this.$api
        .applyClinicSettled(params)
        .then(
          res => {
            this.saveLoading = false;
            let text = `入驻互联网医院${this.audit_status == 3 ? '修改' : '申请'}成功`;
            this.$Message.success(`${text}`);
            this.$router.back();
          },
          error => {
            console.log(error, 'getHospitalClinicInfo');
            this.$Message.error(error.message || error.errmsg);
          }
        )
        .finally(() => (this.saveLoading = false));
    },

    handleOpenCommitTip() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          this.commitTipVisible = true;
        } else {
          this.commitTipVisible = false;
          this.$Message.error('请按要求完善表单内容');
          return false;
        }
      });
    },
    submitForm() {
      console.log(123123);
      this.$refs['formData'].validate(valid => {
        if (valid) {
          this.getHospitalClinicSave();
        } else {
          console.log('error submit!!');
          this.$Message.error('请按要求完善表单内容');
          return false;
        }
      });
    },

    // 营业时间
    selectDateHandler() {
      console.log(this.weekDays);
      const selectedDate = this.weekDays.filter(item => item.isChecked).map(item => item.id);
      console.log('selectedDate', selectedDate);
      this.formData.workdays = selectedDate;
      this.getBusinessDatetext();
      this.popoverIsShow = false;
    },

    checkDate(date, index) {
      console.log('s', date, index);
      this.$set(this.weekDays, index, { ...this.weekDays[index], isChecked: !date.isChecked });
    },

    getBusinessDatetext() {
      const days = this.formData.workdays.sort();
      console.log('-> days', days);
      if (!days.length) {
        this.formData.work_time_desc = '';
        return;
      }
      const dayMap = {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日',
      };
      let resultArr = [],
        i = 0;
      resultArr[i] = [days[0]];
      days.reduce((pre, cur) => {
        cur - pre === 1 ? resultArr[i].push(cur) : (resultArr[++i] = [cur]);
        return cur;
      });
      console.log('-> resultArr', resultArr);
      this.formData.work_time_desc = resultArr
        .map(item => {
          if (item.length === 1) {
            return dayMap[item[0]];
          } else {
            return `${dayMap[item[0]]}至${dayMap[item[item.length - 1]]}`;
          }
        })
        .join('、');
      console.log('-> this.formData.work_time_desc', this.formData.work_time_desc);
    },

    // 地址
    regionChange(address) {
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.prov_code = prov.code;
        this.formData.prov_name = prov.name;
        this.formData.city_code = city.code;
        this.formData.city_name = city.name;
        this.formData.county_code = county.code;
        this.formData.county_name = county.name;
      } else {
        this.formData.prov_code = '';
        this.formData.prov_name = '';
        this.formData.city_code = '';
        this.formData.city_name = '';
        this.formData.county_code = '';
        this.formData.county_name = '';
        this.formData.address_other = '';
      }
    },
    handleCloseEditRecordModel() {
      this.editRecordVisible = false;
    },
  },
};
</script>

<style scoped lang="less">
.form-wrapper {
  width: 75%;
  margin-bottom: 100px;

  .address-wrapper {
    display: flex;

    .select-address {
      flex: 3;
    }

    .detail-address {
      flex: 2;
      margin-left: 12px;
    }

    .address-tip {
      font-size: 13px;
      color: #ccc;
    }
  }
}

.ml10 {
  margin-left: 10px;
}

::v-deep .FormItem__label {
  position: relative;

  &::after {
    width: 10px;
    height: 10px;
    display: inline-block;
    content: ':';
    position: absolute;
    right: 7px;
    top: -1px;
  }
}
</style>

<style lang="less" scoped>
::v-deep .FormItem__error {
  min-width: 140px !important;
}

// 营业时间
.time-picker-poptip {
  .rsj-checkbox-group {
    width: 660px;

    .rsj-checkbox-wrap {
      position: relative;
      text-align: center;
      line-height: 30px;
      border: 1px solid #bbb;
      width: 80px;
      height: 30px;
      border-radius: 2px;
      cursor: pointer;
      font-weight: 400;
      margin-right: 10px;
      padding: 0;
      vertical-align: middle;
      display: inline-block;
      font-size: 14px;
      box-sizing: border-box;

      .rsj-checkbox {
        opacity: 0;
        position: absolute;
        bottom: 0;
        right: 0;
        background-size: 100% 100%;
        background-image: url('~@/assets/image/base/rsj-checked.png');
        display: inline-block;
        width: 16px;
        height: 16px;
        white-space: nowrap;
        outline: none;
        vertical-align: middle;
        line-height: 1;
        margin: 0;
        padding: 0;

        .zent-checkbox-inner {
        }

        input {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          margin: 0;
          padding: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          opacity: 0;
          cursor: pointer;
        }
      }
    }

    .rsj-checkbox-isChecked {
      border: 1px solid #1157e5;
      color: #1157e5;

      .rsj-checkbox {
        opacity: 1;
      }
    }
  }
}

.upload-note {
  width: 480px;
  color: #aaaaaa;
  line-height: 20px;
  margin-top: -12px;
}

.reason-error--text {
  color: red;
}

.img--text {
  margin-top: -10px;
}

.id-card-img-text {
  margin-top: -20px;
  padding-bottom: 26px;
}

.ml120 {
  margin-left: 120px;
}

::v-deep .fixed-bottom-wrapper .ivu-poptip-confirm .ivu-poptip-body .ivu-icon {
  left: 10px;
}
</style>
