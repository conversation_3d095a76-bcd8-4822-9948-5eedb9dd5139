<template>
  <div class="invoice-container">
    <Form
      :model="formData"
      ref="formData"
      label-position="right"
      :label-width="100"
      :rules="ruleValidate"
      :disabled="isEdit"
    >
      <div class="block-header">申请方信息</div>
      <Row>
        <Col :span="14" v-if="$route.query.id">
          <FormItem label="状态:" class="mb10">
            <div class="item-lineHeight" :style="{ color: formData.status === 4 ? 'red' : '' }">
              {{ formData.status_text || '-' }}
              <span v-if="formData.status === '4'"
                >(<span style="color: red">驳回原因：{{ formData.reject_reason || '-' }}</span
                >)</span
              >
            </div>
          </FormItem>
        </Col>

        <Col :span="14">
          <FormItem label="申请方:" class="mb10">
            {{ clinicName }}
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票人姓名:" prop="buyer_name">
            <Input v-model="formData.buyer_name"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票人手机:" prop="buyer_phone">
            <Input v-model="formData.buyer_phone"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票邮箱:" prop="buyer_email">
            <Input v-model="formData.buyer_email"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票地址:" prop="address" class="mb10">
            <div class="flex items-center">
              <div class="addressBox" style="width: 55%">
                <el-cascader
                  v-model="selectedAddress"
                  :options="options"
                  clearable
                  popper-class="address-com-small"
                  placeholder="请选择收件地址"
                  size="small"
                  style="width: 100%"
                  @change="regionChange"
                  :disabled="isEdit"
                >
                </el-cascader>
              </div>
              <div class="ml10" style="width: 45%; margin-top: 1px">
                <Input v-model="formData.buyer_address.other" placeholder="详细地址"></Input>
              </div>
            </div>
          </FormItem>
        </Col>
      </Row>

      <div class="block-header">发票内容</div>
      <Row>
        <Col :span="14">
          <FormItem label="发票类型:" class="mb10" prop="line">
            <RadioGroup v-model="formData.line">
              <Radio :label="item.key" v-for="item in line_enum" :key="item.key">{{ item.name }}</Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="发票金额:" required class="mb0">
            <span class="mr10" v-if="order_list.length">服务费金额: {{ service_fee_cent }}</span>
            <span class="mr10" v-if="order_list.length">通道费金额: {{ channel_fee_cent }}</span>
            <a @click="showOrderVisible" v-if="order_list.length > 0 && !isEdit">重选订单</a>
            <a @click="showOrderVisible" v-if="!isEdit && order_list.length === 0">选择订单</a>

            <Table ref="selection" :columns="orderColumns" :data="showOrderList()" v-if="order_list.length">
              <!-- 金额 -->
              <template v-slot:amount_cent="{ row }">
                {{ formatYuan(row?.amount_cent) }}
              </template>
              <template v-slot:service_fee_cent="{ row }">
                {{ formatYuan(row?.service_fee_cent) }}
              </template>
              <template v-slot:channel_fee_cent="{ row }">
                {{ formatYuan(row?.channel_fee_cent) }}
              </template>

              <template slot-scope="{ row, index }" slot="action">
                <a v-if="!isEdit" @click="deleteOrder(index)">删除</a>
              </template>
            </Table>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem required class="mb10" v-if="order_list.length > this.split_num">
            <div class="border-arrow" style="text-align: center">
              <a @click="foldOrExpand('expand')" v-if="showOrderList().length <= this.split_num">
                展开
                <Icon type="ios-arrow-down" />
              </a>
              <a @click="foldOrExpand('fold')" v-else>
                收起
                <Icon type="ios-arrow-up" />
              </a>
            </div>
          </FormItem>
        </Col>
      </Row>
      <div class="block-header flex flex-align-center">
        发票信息
        <k-link v-if="!isEdit" class="ml10" :to="{ path: '/setting/Invoice' }" target="_blank">修改发票信息</k-link>
        <Icon v-if="!isEdit" type="md-refresh" size="18" class="ml10 cursor" @click="getInvoiceInfo()" />
      </div>

      <div class="block">
        <Spin v-if="spinLoading"></Spin>
        <Row v-else>
          <Col :span="14">
            <FormItem label="发票抬头:" class="mb10">
              {{ this.formData.invoice.organization_name || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="公司税号:" class="mb10">
              {{ this.formData.invoice.organization_code || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="注册地址:" class="mb10">
              {{ this.formData.invoice.reg_address_text || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="注册电话:" class="mb10">
              {{ this.formData.invoice.reg_mobile || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="开户银行:" class="mb10">
              {{ this.formData.invoice.bank || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="银行账户:" class="mb10">
              {{ this.formData.invoice.bank_account || '-' }}
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
    <div class="block_40"></div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <dvd />
      <dvd />
      <dvd />
      <Button v-if="!isEdit" type="primary" @click="submit()" :loading="saveBtnLoading">提交</Button>
    </div>

    <!-- 商品明细 -->
    <select-order-modal
      :modal-value.sync="orderVisible"
      @on-selected="onSelected"
      :checkedList="order_list"
      :relate_type_enum="relate_type_enum"
    ></select-order-modal>
  </div>
</template>

<script>
import { getClinicName } from '@/libs/runtime';
import { cloneDeep, debounce  } from 'lodash-es';
import { CodeToText, regionData } from '@/libs/chinaMap';
import selectOrderModal from './component/SelectOrderModal.vue';
import { formatOptions, formatYuan, isEmpty } from '../../../utils/helper';
export default {
  name: 'edit',
  components: {
    selectOrderModal,
  },
  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };

    const validateEmail = (rule, value, callback) => {
      let reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
      if (!reg.test(value)) {
        callback(new Error('请输入正确的邮箱'));
      } else {
        callback();
      }
    };

    return {
      formatYuan,
      saveBtnLoading: false,
      // invoice里面统一放置发票信息
      formData: {
        invoice: {
          organization_name: '', // 发票抬头
          organization_code: '', // 公司税号
          reg_address_text: '', // 注册地址
          reg_mobile: '', // 注册电话
          bank: '', // 开户银行
          bank_account: '', // 银行账号
        },
        buyer_name: '',
        buyer_phone: '',
        buyer_email: '',
        line: '', // 发票类型
        //收票地址
        buyer_address: {
          province: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          other: '',
        },
      },
      ruleValidate: {
        buyer_name: [{ required: true, message: '请输入收票人姓名', trigger: 'blur' }],
        buyer_phone: [
          { required: true, message: '请输入收票人电话', trigger: 'change' },
          { required: true, validator: validateMobile },
        ],
        buyer_email: [
          { required: true, message: '请输入正确的邮箱', trigger: 'change' },
          { required: true, validator: validateEmail },
        ],
        line: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
      },
      options: regionData,
      selectedAddress: [],
      orderColumns: [
        { title: '订单编号', key: 'relate_code', align: 'center' },
        { title: '订单类型', key: 'relate_type_text', align: 'center' },
        { title: '订单金额', slot: 'amount_cent', align: 'center', width: 100 },
        { title: '服务费金额', slot: 'service_fee_cent', align: 'center', width: 100 },
        { title: '通道费金额', slot: 'channel_fee_cent', align: 'center', width: 100 },
        { title: '开票方', key: 'invoicing_party', align: 'center' },
        { title: '操作', slot: 'action', algin: 'center', width: 60 },
      ], // 选中订单columns
      order_list: [], // 选中订单数据
      showType: 'fold', // 默认折叠
      split_num: 5, // 折叠数量
      status_enum: [],
      line_enum: [],
      relate_type_enum: [],
      type_enum: [],
      spinLoading: false,
      orderVisible: false,
    };
  },

  computed: {
    clinicName() {
      return getClinicName();
    },
    service_fee_cent() {
      const amount = this.order_list?.reduce((acc, cur) => acc + +cur.service_fee_cent, 0) || 0;
      return formatYuan(amount);
    },
    channel_fee_cent() {
      const amount = this.order_list?.reduce((acc, cur) => acc + +cur.channel_fee_cent, 0) || 0;
      return formatYuan(amount);
    },
    isEdit() {
      const { id, type } = this.$route.query;
      if (isEmpty(id) && type === 'add') return false;
      if (!isEmpty(id) && type === 'edit') return false;
      if (!isEmpty(id) && type === 'view') return true;
      return true;
    },
  },
  watch: {},

  created() {},
  mounted() {
    this.init();
  },
  methods: {
    // 初始化数据
    init() {
      let id = this.$route.query.id;
      if (id) {
        this.getPurInvoiceDetail(id);
      } else {
        // 初始化创建时，通过回显接口回显上次开票得用户
        this.echoInvoiceApplicant();
      }
      // 获取枚举
      this.getHospitalInvoiceOptions();
      // 获取发票信息
      this.getInvoiceInfo();
    },

    // 发票详情
    getPurInvoiceDetail(id) {
      let params = {
        code: this.$route.query.id,
      };
      this.$api.getHospitalInvoiceInfo(params).then(res => {
        this.order_list = res.invoicing_detail;

        // 驳回状态
        this.formData.status = res.status;
        this.formData.reject_reason = res.reject_reason;
        this.formData.status_text = res.status_text;

        this.formData.buyer_name = res.buyer_name;
        this.formData.buyer_phone = res.buyer_phone;
        this.formData.buyer_email = res.buyer_email;
        if (res.buyer_address.county && res.buyer_address.county.code) {
          this.selectedAddress = [
            res.buyer_address.province.code,
            res.buyer_address.city.code,
            res.buyer_address.county.code,
          ];
        } else {
          this.selectedAddress = [res.buyer_address.province.code, res.buyer_address.city.code];
        }
        this.formData.buyer_address = res.buyer_address;
        this.formData.line = res.line;
        this.formData.type = res.type;
      });
    },
    //地区选择
    regionChange(address) {
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const area = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.buyer_address.province = province;
        this.formData.buyer_address.city = city;
        this.formData.buyer_address.county = area;
      } else {
        this.formData.buyer_address.province = { name: '', code: '' };
        this.formData.buyer_address.city = { name: '', code: '' };
        this.formData.buyer_address.county = { name: '', code: '' };
      }
    },

    // 展开/收起
    foldOrExpand(type = 'fold') {
      this.showType = type;
      this.showOrderList();
    },
    // 展示订单数据
    showOrderList() {
      let copyOrderList = cloneDeep(this.order_list);
      let returnOrderList = copyOrderList;
      if (this.showType === 'fold') {
        returnOrderList = copyOrderList.splice(0, this.split_num);
      }
      return returnOrderList;
    },

    onSelected(item) {
      this.order_list = item;
    },

    deleteOrder(index) {
      this.$delete(this.order_list, index);
    },

    back() {
      this.$router.push('/internet-hospital/invoice/list');
    },

    getHospitalInvoiceOptions() {
      this.$api.getHospitalInvoiceOptions().then(res => {
        this.status_enum = formatOptions(res?.status_enum || []);
        this.line_enum = formatOptions(res?.line_enum || []);
        this.relate_type_enum = formatOptions(res?.relate_type_enum || []);
        this.type_enum = formatOptions(res?.type_enum || []);
      });
    },

    submit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          if (!this.order_list.length) {
            this.$Message.error('请选择订单');
            return;
          }
          const params = {
            line: this.formData.line,
            buyer_phone: this.formData.buyer_phone,
            buyer_email: this.formData.buyer_email,
            buyer_name: this.formData.buyer_name,
            buyer_address: this.formData.buyer_address,
            trans_code: this.order_list?.map(item => item.trans_code),
          };

          const { id, type } = this.$route.query;
          if (type === 'add' && !id) {
            this.saveBtnLoading = true;
            this.$api
              .createHospitalInvoice(params)
              .then(() => {
                this.$Message.success('开票成功');
                this.back();
              })
              .finally(() => {
                this.saveBtnLoading = false;
              });
          }
          if (type === 'edit' && id) {
            this.saveBtnLoading = true;
            this.$api
              .editHospitalInvoice({
                code: id,
                ...params,
              })
              .then(() => {
                this.$Message.success('修改成功');
                this.back();
              })
              .finally(() => {
                this.saveBtnLoading = false;
              });
          }
        }
      });
    },

    // 获取发票信息
    getInvoiceInfo() {
      this.spinLoading = true;
      this.$api
        .getClinicInfo()
        .then(res => {
          this.formData.invoice.organization_name = res.organization_name;
          this.formData.invoice.organization_code = res.organization_code;

          let reg_address_text = '';
          if (res.invoice?.reg_address?.county?.code) {
            let invoice = res.invoice?.reg_address;
            reg_address_text = `${invoice.prov.name}${invoice.city.name}${invoice.county.name}${invoice.detail}`;
          }

          this.formData.invoice.reg_address_text = reg_address_text;
          this.formData.invoice.reg_mobile = res.invoice?.reg_mobile;
          this.formData.invoice.bank = res.invoice?.bank;
          this.formData.invoice.bank_account = res.invoice?.bank_account;
        })
        .finally(() => (this.spinLoading = false));
    },

    // 回显开票人信息
    echoInvoiceApplicant() {
      this.$api.getHospitalInvoiceLastBuyerInfo().then(
        res => {
          this.formData.buyer_name = res.buyer_name;
          this.formData.buyer_phone = res.buyer_phone;
          this.formData.buyer_email = res.buyer_email;
          if (res.buyer_address.county && res.buyer_address.county.code) {
            this.selectedAddress = [
              res.buyer_address.province.code,
              res.buyer_address.city.code,
              res.buyer_address.county.code,
            ];
          } else {
            this.selectedAddress = [res.buyer_address.province.code, res.buyer_address.city.code];
          }
          this.formData.buyer_address.city = res.buyer_address.city;
          this.formData.buyer_address.county = res.buyer_address.county;
          this.formData.buyer_address.province = res.buyer_address.province;
          this.formData.buyer_address.other = res.buyer_address.other;
        },
        () => {}
      );
    },
    showOrderVisible() {
      this.orderVisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .invoice-container {
  .page-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
}

::v-deep .el-cascader {
  .el-cascader-panel {
    font-size: 12px;
  }

  .el-input {
    .el-input__inner {
      border: 1px solid #bcc3d7;
      border-radius: 2px;
      padding-left: 7px;
      font-size: 12px;
    }
  }
}

.mb10 {
  margin-bottom: 10px;
}

.mb0 {
  margin-bottom: 0px;
}

.ml10 {
  margin-left: 10px;
}

.cursor {
  cursor: pointer;
}

.mr10 {
  margin-right: 10px;
}

.block {
  position: relative;
}

.border-arrow {
  // border: 1px solid #d7d9de;
  background: #fafafa;

  a {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
}

.ivu-spin {
  height: 300px;
  width: 50%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
