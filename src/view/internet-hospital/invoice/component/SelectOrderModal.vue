<template>
  <Modal :value="visible" title="选择订单" :mask-closable="false" @on-visible-change="changeVisible" :width="850">
    <div class="flex flex-item-l-start">
      <div>
        <!-- 订单类型 -->
        <DatePicker
          class="mr10"
          @keyup.enter.native="onSearch"
          @on-clear="clearDate"
          v-model="queryFormData.month"
          type="month"
          format="yyyy-MM"
          placeholder="选择月份"
          @on-change="handleChangeDate"
          style="width: 150px"
        />
        <Select
          ref="type"
          clearable
          v-model="queryFormData.type"
          class="mr10"
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 150px"
          placeholder="选择订单类型"
        >
          <Option v-for="item in relate_type_enum" :value="item.key" :key="item.key">{{ item.name }}</Option>
        </Select>
        <Input
          v-model="queryFormData.relate_code"
          placeholder="输入订单编号筛选"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          class="mr10"
          style="width: 180px"
        >
          <Icon type="ios-search" slot="suffix" @click="onSearch" />
        </Input>
      </div>
      <Button type="primary" @click="onSearch">搜索</Button>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; height: 345px; overflow: auto">
      <Table
        ref="selection"
        height="280"
        @on-select="onSelect"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-select-all-cancel="onSelectAllCancel"
        :columns="tableCols"
        :data="list"
        :loading="tableLoading"
      >
        <!-- 金额 -->
        <template v-slot:amount_cent="{ row }">
          {{ formatYuan(row?.amount_cent) }}
        </template>
        <template v-slot:service_fee_cent="{ row }">
          {{ formatYuan(row?.service_fee_cent) }}
        </template>
        <template v-slot:channel_fee_cent="{ row }">
          {{ formatYuan(row?.channel_fee_cent) }}
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage
        v-if="total > 0"
        :total="+total"
        :page-size="queryFormData.pageSize"
        :page-size-opts="[10, 20, 50]"
        :current="queryFormData.page"
        @on-change="onPageChange"
        style="text-align: center"
      />
    </div>
    <div slot="footer">
      <div v-if="selected_items.length > 0" style="display: inline-block" class="lr15 text-muted">
        已选择<span class="text-error">{{ selected_items.length }}</span
        >订单
        <dvd />
        <dvd />
        <dvd />
        <span>总金额：<span v-html="totalAmount"></span></span>
      </div>
      <Button @click="changeVisible(false)">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { formatYuan } from '../../../../utils/helper';
import moment from 'moment';
import { cloneDeep } from 'lodash-es';

let init_query_from_data = {
  page: 1,
  pageSize: 10,
  relate_type: '', // 类型,默认全部
  relate_code: '',
  month: '',
};

export default {
  name: 'InvoiceOrderList',
  props: {
    modalValue: {
      type: Boolean,
      default: false,
    },
    checkedList: {
      type: Array,
      default() {
        return [];
      },
    },
    relate_type_enum: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      moment,
      formatYuan,
      queryFormData: { ...init_query_from_data },
      tableCols: [
        { type: 'selection', width: 60 },
        { title: '订单编号', key: 'relate_code', align: 'center' },
        { title: '订单类型', key: 'relate_type_text', align: 'center' },
        { title: '订单金额', slot: 'amount_cent', align: 'center', width: 100 },
        { title: '服务费金额', slot: 'service_fee_cent', align: 'center', width: 100 },
        { title: '通道费金额', slot: 'channel_fee_cent', align: 'center', width: 100 },
        { title: '开票方', key: 'invoicing_party', align: 'center' },
      ],
      tableLoading: false,
      list: [],
      total: 0,
      selected_items: [], // 勾选的订单
    };
  },
  computed: {
    visible: {
      get() {
        return this.modalValue;
      },
      set(val) {
        this.$emit('update:modalValue', val);
      },
    },
    totalAmount() {
      const amount = this.selected_items?.reduce((acc, cur) => acc + +cur.amount_cent, 0) || 0;
      return formatYuan(amount);
    },
  },
  methods: {
    init() {
      this.getHospitalInvoiceOrderList();
    },

    getHospitalInvoiceOrderList() {
      this.tableLoading = true;
      this.$api
        .getHospitalInvoiceOrderList(this.queryFormData)
        .then(res => {
          this.list = res?.list || [];
          this.total = res?.total || 0;
          this.list.forEach(ele => {
            if (this.checkedList.some(item => item.trans_code === ele.trans_code)) {
              ele._checked = true;
            }
          });
          this.$set(this, 'selected_items', cloneDeep(this.checkedList));
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    close() {
      this.clearQuery();
      this.$emit('update:modalValue', false);
    },

    changeVisible(show) {
      this.scrollTop();
      if (show) {
        this.init();
      } else {
        this.close();
      }
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.getHospitalInvoiceOrderList();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getHospitalInvoiceOrderList();
      this.scrollTop();
    },

    scrollTop() {
      let overflowY = this.$el.getElementsByClassName('ivu-table-body')[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    onSelect: function (selection, row) {
      this.selected_items.push(row);
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        if (!this.selected_items.some(order => order.trans_code === item.trans_code)) {
          this.selected_items.push(item);
        }
      });
    },

    onSelectCancel: function (selection, row) {
      this.selected_items = this.selected_items.filter(item => row.trans_code !== item.trans_code);
    },

    onSelectAllCancel: function () {
      this.selected_items = this.selected_items.filter(item =>
        this.list.every(order => order.trans_code !== item.trans_code)
      );
    },

    onConfirm: function () {
      if (this.selected_items.length == 0) {
        this.$Message.error('请先选择订单');
        return false;
      }
      this.$emit('on-selected', this.selected_items);
      this.close();
    },
    clearQuery: function () {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
      this.selected_items = [];
    },
    handleChangeDate(date) {
      this.queryFormData.month = date;
    },
    clearDate() {
      this.queryFormData.month = '';
      this.getHospitalInvoiceOrderList();
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ivu-modal {
  top: 20%;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
