<template>
  <div class="audit-wrapper">
    <!--订单基础信息-->
    <order-base-info :consignee-info="consigneeInfo" :order-info="orderInfo" :is-show-consignee-box="false" />
    <!--    用户基础信息-->
    <user-base-info :user-info="userInfo" :patient-info="patientInfo" />
    <div class="pres-info" style="margin-bottom: 20px">
      <prescription-info
        :pres_id="pres_id"
        :pres_code="pres_code"
        ref="info"
        :belong_type="belong_type"
        :pres_record_list="pres_record_list"
      />
    </div>
    <div class="p-title">发药明细</div>
    <Table :data="dispensingDrugs" :columns="tableCols" stripe style="margin-bottom: 20px"> </Table>
    <div class="record-box">
      <audit-record :recordList="auditRecords" />
    </div>
    <div class="fixed-bottom-wrapper">
      <back-button class="mr10"></back-button>
      <div
        class="flex-inline"
        v-if="
          (auditStatus === 'A_WAIT' || auditStatus === 'A_PHAR' || auditStatus === 'C_REJECT') && $route.query.is_audit
        "
      >
        <audit-refuse-btn :title="`${confirmContent}驳回`" :label="refuseLabel" @ok="handleAuditRefuse" class="mr10"
          >审核驳回</audit-refuse-btn
        >
        <audit-pass-btn
          class="mr10"
          :content="confirmContent"
          :contentText="confirmContentText"
          ref="passBtn"
          @ok="handleAuditConfirm"
          >审核通过
        </audit-pass-btn>
      </div>
      <Button type="primary" class="mr10" @click="print('prescription')">打印处方笺</Button>
      <Button type="primary" class="mr10" @click="print('records')">打印电子病历</Button>
    </div>
  </div>
</template>
<script>
import AuditPassBtn from '@/components/AuditButton/AuditPassBtn';
import AuditRefuseBtn from '@/components/AuditButton/AuditRefuseBtn';
// import { cloneDeep } from 'lodash-es';
// import LogisticsInfo from '../order/components/LogisticsInfo.vue'
import OrderBaseInfo from '../components/OrderBaseInfo.vue';
import UserBaseInfo from '../components/UserBaseInfo.vue';
import AuditRecord from '../components/AuditRecord.vue';
import PrescriptionInfo from '../components/PrescriptionInfo/index.vue';
// import { $operator } from '../../../libs/operation';
import TipModel from '../../../components/confirmModal';

const init_query_form_data = {
  name: '',
};
export default {
  name: 'AuditDetail',
  components: {
    AuditRecord,
    UserBaseInfo,
    OrderBaseInfo,
    AuditPassBtn,
    AuditRefuseBtn,
    PrescriptionInfo,
  },
  mixins: [],
  props: {},
  data() {
    return {
      tab: 'pharInfo',
      queryFormData: { ...init_query_form_data },
      presOptionList: [
        { label: '处方笺', value: 'wait' },
        { label: '电子病历', value: 'none' },
      ],
      activeName: '1',
      dispensingDrugs: [],
      originDrugs: [],
      auditRecords: [],
      order_code: '',
      order_id: '',
      orderInfo: {
        order_code: '', //订单编号
        order_status: '', //订单状态
        info_status_text: '', //订单状态文本
        order_type: '', //订单类型
        pay_type_text: '', //支付方式文本
      },
      consigneeInfo: {
        name: '', //收货人姓名
        mobile: '', //收货人电话
        address: '', //收货人地址
      },
      patientInfo: {
        name: '', //患者姓名
        mobile: '', //患者电话
        age: '', //患者地址
        gender_text: '', //患者性别文本
        id_card: '', //患者身份证号
      },
      userInfo: {
        name: '',
        nick_name: '',
        uid: '',
        mobile: '',
      },
      presInfo: {
        pres_code: '',
      },
      auditStatus: '',
      logistic_status: '',
      overStockList: [], //超库存药品列表
      pres_id: '',
      pres_code: '',
      tableCols: [
        { title: '商品编码', key: 'prod_info_id', align: 'left', minWidth: 100 },
        { title: '商品名称', key: 'prod_name', align: 'left', minWidth: 180 },
        { title: '商品单位', key: 'prod_unit', align: 'left', minWidth: 80 },
        { title: '现库存', key: 'prod_stock_num', align: 'left', minWidth: 120 },
        { title: '待发数量', key: 'num', align: 'left', minWidth: 120 },
      ],
      belong_type: '',

      // 处方明细
      pres_record_list: [],
    };
  },
  computed: {
    isDispenseAudit() {
      return (this.auditStatus === 'A_WAIT' || this.auditStatus === 'C_REJECT') && this.$route.query.is_audit;
    },
    confirmContent() {
      return this.auditStatus === 'A_WAIT' || this.auditStatus === 'C_REJECT' ? '调配药师审核' : '复核药师审核';
    },
    confirmContentText() {
      return this.auditStatus === 'A_WAIT' || this.auditStatus === 'C_REJECT'
        ? '调配药师审核通过后，会流转到核对药师进行复审，确认审核通过？'
        : '核对药师审核通过后，可在处方订单中进行发货，确认审核通过？';
    },
    refuseLabel() {
      return this.auditStatus === 'A_WAIT' || this.auditStatus === 'C_REJECT'
        ? '调配药师确认审核驳回，处方费用将原路退款给付款用户。如需继续确定驳回请输入驳回原因：'
        : '复核药师确认审核驳回，调配药师需要重新进行审核。如需继续确定驳回请输入驳回原因：';
    },
  },
  watch: {},
  created() {
    this.$router.onReady(() => {
      this.order_code = this.$route.query.order_code;
      this.order_id = this.$route.query.order_id;
      this.initDetail();
    });
  },
  mounted() {},
  methods: {
    // 发货
    initDetail() {
      this.getDetail();
    },
    handleAudit(status, reason) {
      let auditStatus = '';
      let auditApi;
      const params = {
        status: '',
        reject_reason: reason,
        order_code: this.orderInfo.order_code,
        pres_id: this.orderInfo.pres_info?.pres_id,
      };
      if (this.auditStatus === 'A_WAIT' || this.auditStatus === 'C_REJECT') {
        auditApi = 'dispensingPharmacistAudit';
        auditStatus = status === 'PASS' ? 'A_PHAR' : 'CLOSE';
      } else if (this.auditStatus === 'A_PHAR') {
        auditApi = 'pharmacistVerificationAudit';
        auditStatus = status === 'PASS' ? 'A_PASS' : 'C_REJECT';
      }
      params.status = auditStatus;
      console.log('-> %c params  ===    %o', 'font-size: 15px;color: #fa8c16 ;', params);
      this.$api[auditApi](params).then(res => {
        this.$Message.success('审核成功');
        if (this.auditStatus === 'A_PHAR' && status === 'PASS') {
          TipModel({
            content: '发货提示',
            contentText: '处方已审核通过，可在处方订单中进行发货，是否需要现在发货？',
            cancelText: '稍后发货',
            confirmText: '去发货',
          })
            .then(() => {
              console.log(res, 'ressssssssssssssssssssssssssssssssssssssss');
              this.$store.commit('global/SET_HOS_ORDER', res);
              this.$router.push({
                path: '/internet-hospital/order/list',
              });
            })
            .catch(() => {
              this.$router.back();
            });
          return;
        }
        this.$router.back();
      });
    },
    handleAuditRefuse(reason) {
      console.log('-> %c reason  ===    %o', 'font-size: 15px;color: #F56C6C ;', reason);
      this.handleAudit('REJECT', reason);
    },
    handleAuditConfirm() {
      this.handleAudit('PASS', '');
    },
    getDetail() {
      this.$api.getHospitalOrderInfo({ order_code: this.order_code }).then(res => {
        this.consigneeInfo = res.consignee_info;
        this.userInfo = res.user_info;
        this.pres_record_list = res.pres_record_list;
        this.handleOrderInfo(res);
        this.getHospitalAuditRecord();
        this.getHospitalMedicalDetail();
      });
    },
    handleOrderInfo(orderInfo) {
      this.orderInfo = orderInfo;
      this.auditStatus = orderInfo.status;
      this.logistic_status = orderInfo.logistic_status;
      this.presInfo = orderInfo.pres_info;
      this.pres_id = orderInfo.pres_info.pres_id;
      this.pres_code = orderInfo.pres_info.pres_code;
      this.patientInfo = orderInfo.patient_info;
      this.belong_type = orderInfo.pres_info.belong_type;
    },
    getHospitalMedicalDetail() {
      this.$api.getHospitalMedicalDetail({ order_id: this.order_id }).then(res => {
        this.dispensingDrugs = res.list;
      });
    },
    getHospitalAuditRecord() {
      this.$api.getHospitalAuditRecord({ pres_id: this.presInfo.pres_id, statuses: '' }).then(res => {
        this.auditRecords = res.list;
      });
    },
    print(name) {
      this.$refs.info.printFunc(name);
    },
  },
};
</script>
<style lang="less" scoped>
.audit-wrapper {
  padding-bottom: 40px;

  ::v-deep .p-title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
    margin-bottom: 20px;
  }

  ::v-deep .info-box {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    background-color: #f9fbfb;
    margin-bottom: 20px;

    .info-item {
      width: 33%;
      margin-bottom: 14px;
      font-size: 13px;
      line-height: 17px;

      .info-label {
        width: 76px;
        min-width: fit-content;
        text-align: right;
        color: #666666;
        margin-right: 10px;
      }

      .info-value {
        flex: 1;
        color: #333333;
      }

      &:nth-last-child(1),
      &:nth-last-child(2),
      &:nth-last-child(3) {
        margin-bottom: 0;
      }
    }
  }
}

.custom-logis {
  display: flex;
  flex-wrap: wrap;

  .logis-box {
    display: flex;
    flex-wrap: wrap;

    .logis-item {
      padding: 10px 20px;
      border: 1px solid #ebeef5;
      margin-right: 20px;
      margin-bottom: 20px;

      > div:not(:last-child) {
        margin-bottom: 10px;
      }
    }
  }

  .add-express {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ebeef5;
    height: 80px;
    width: 120px;
  }
}
</style>
