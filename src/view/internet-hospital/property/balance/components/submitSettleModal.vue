<template>
  <Modal
    ref="customModal"
    :value="value"
    width="1000"
    :title="title"
    class="settle-dialog"
    :footer-hide="currentTab === '2'"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div slot="header" class="flex tabs">
      <div
        @click="changeTab('1')"
        class="tab"
        :class="[currentTab === '1' ? 'active' : '', isCheckRecord ? 'disabled' : '']"
      >
        申请结算
      </div>
      <div @click="changeTab('2')" class="tab" :class="currentTab === '2' ? 'active' : ''">申请结算记录</div>
    </div>
    <div class="content">
      <Form
        v-show="currentTab === '1'"
        ref="formData"
        :model="formData"
        :label-width="100"
        :label-colon="true"
        :rules="formDataRules"
      >
        <FormItem label="结算金额" required>
          <Input v-model.trim="totalPrice" disabled></Input>
        </FormItem>
        <FormItem label="手续费"> 0元</FormItem>
        <FormItem label="发票送达方式" prop="invoicing_send_type">
          <RadioGroup v-model="formData.invoicing_send_type">
            <Radio label="1">电子邮箱</Radio>
            <Radio label="2">快递发送</Radio>
          </RadioGroup>
        </FormItem>
        <div class="flex" v-if="formData.invoicing_send_type === '2'">
          <FormItem style="flex: 1" label="填写快递" prop="invoicing_express_code">
            <Select v-model="formData.invoicing_express_code" clearable placeholder="请选择快递公司">
              <Option v-for="(type, typeKey) in expressCompanys" :value="type.kuai_di_100_code" :key="typeKey"
                >{{ type.name }}
              </Option>
            </Select>
          </FormItem>

          <FormItem label="" :label-width="10" style="flex: 1" prop="invoicing_express_no">
            <Input v-model.trim="formData.invoicing_express_no" clearable placeholder="请输入快递单号"></Input>
          </FormItem>
        </div>
        <FormItem label="邮箱地址" v-if="formData.invoicing_send_type === '1'">
          <div>{{ formData.invoicing_email }}</div>
        </FormItem>

        <FormItem label="收货地址" v-if="formData.invoicing_send_type === '2'">
          <div>收货人：王素珍 17521251936</div>
          <div>收货地址：上海市闵行区申长路1466弄A座北楼3A</div>
        </FormItem>

        <FormItem label="上传发票" prop="invoicing_url" class="item-error-tip-position">
          <MaterialPicture v-model="formData.invoicing_url" :limit="9" :disabled="false"></MaterialPicture>
          <div class="note">
            上传不低于结算金额的发票，发票金额超出申请结算金额按结算金额计算。图片格式png、jpg。
            <Tooltip
              theme="light"
              placement="top-end"
              :offset="20"
              max-width="1000"
              transfer-class-name="customTooltip"
            >
              <template slot="content">
                <img src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0312/152550_77838.jpg" alt="" />
              </template>
              <div class="see-mould cursor">
                <a>查看示例</a>
              </div>
            </Tooltip>
          </div>
        </FormItem>

        <FormItem label="备注" prop="note">
          <Input
            v-model.trim="formData.note"
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 5 }"
            placeholder="请输入提现用途"
            maxlength="300"
            show-word-limit
          ></Input>
        </FormItem>
      </Form>

      <div v-show="currentTab === '2'">
        <apply-record
          :express-companys="expressCompanys"
          :batchDivideCode="batchDivideCode"
          ref="applyRecord"
        ></apply-record>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { $operator } from '@/libs/operation';
// import { CodeToText, regionData, TextToCode } from '@/libs/chinaMap';
import ApplyRecord from './applyRecord.vue';

export default {
  name: 'submitSettleModal',
  mixins: [],

  components: {
    ApplyRecord,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: 'title',
    },
    selectedObj: {
      type: Object,
      default: () => {},
    },
    isCheckRecord: {
      type: Boolean,
      default: false,
    },
    batchDivideCode: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      totalPrice: 0,
      expressCompanys: [],
      currentTab: '1',
      relatedVisible: false,
      recordId: '',

      tableLoading: false,
      /** 表单校验 **/
      confirmLoading: false,
      dateRange: [],
      formData: {
        mch_bill_code: [],
        note: '',
        invoicing_send_type: '1',
        invoicing_url: [],
        invoicing_email: '<EMAIL>',
        invoicing_express_no: '',
        invoicing_express_code: '',
      },
      /** 规则校验声明 **/
      formDataRules: {
        invoicing_send_type: [{ required: true, message: '请选择发票送达方式', trigger: 'change' }],
        invoicing_url: [{ required: true, message: '请上传发票', type: 'array', trigger: 'change' }],
        invoicing_express_code: [{ required: true, message: '请选择快递公司', trigger: 'change' }],
        invoicing_express_no: [{ required: true, message: '请输入快递单号', trigger: 'change' }],
      },
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {
    this.getErpLogisticsOptions();
  },

  destroyed() {},

  methods: {
    changeTab(tab) {
      if (tab === '2') {
        this.$refs.applyRecord.onSearch();
      }
      if (tab === '1' && this.isCheckRecord) {
        return;
      }
      this.currentTab = tab;
    },
    getErpLogisticsOptions() {
      this.$api.getHospitalExpressList().then(res => {
        this.expressCompanys = res.list;
      });
    },

    /**
     * @description: 确定事件
     * */
    confirm() {
      this.$refs.formData.validate(
        valid => {
          if (valid) {
            this.$Modal.confirm({
              title: '温馨提示',
              content: '等待平台审核通过后，D+1个工作日会将申请金额流转到互医资产可用余额。',
              onOk: () => {
                this.createHospitalWithdraw();
              },
              onCancel: () => {},
            });
          }
        },
        err => {}
      );
    },
    /**
     * @description: 弹窗滚动条回归顶部
     * @note: 如果全局没有处理，弹窗内部执行此方法处理
     * */
    MODAL_SCROLL_TOP() {
      let MODAL_EL = this.$el.getElementsByClassName('ivu-modal-body')[0];
      if (MODAL_EL) {
        MODAL_EL.scrollTop = 0;
      }
    },
    /**
     * @description: 弹窗状态检测
     * @params  { Boolean } visible true: 弹窗打开 false:弹窗关闭
     * */
    changeVisible(visible) {
      if (visible) {
        if (this.isCheckRecord) {
          this.$refs.applyRecord.queryFormData.batch_divide_code = this.batchDivideCode || '';
          this.changeTab('2');
        } else {
          this.changeTab('1');
          let total = Object.values(this.selectedObj).reduce((prev, curr) => {
            return $operator.add(prev, Number(curr.settle_amount));
          }, 0);
          this.totalPrice = '￥' + total.toFixed(2);
        }
      } else {
        this.closeModal();
      }
    },

    /**
     * @description: 弹窗数据清除
     * */
    clearData() {
      // 清除表单数据
      this.dateRange = [];
      this.totalPrice = '';
      this.$refs.formData.resetFields();
    },

    /**
     * @description: 弹窗关闭
     * */
    closeModal() {
      this.clearData();
      this.MODAL_SCROLL_TOP();
      this.$emit('input', false);
    },

    createHospitalWithdraw() {
      this.confirmLoading = true;
      this.formData.mch_bill_code = this.getAuditOrderIds();
      let params = this.formData;

      this.$api
        .createHospitalWithdraw(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.closeModal();
          this.$emit('success');
        })
        .catch(err => {})
        .finally(() => (this.confirmLoading = false));
    },

    /**
     * 过滤选中的ids
     * */
    getAuditOrderIds(row) {
      if (row) {
        return [row.code];
      } else {
        return Object.values(this.selectedObj).map(item => item.code);
      }
    },
  },
};
</script>
<style scoped lang="less">
.tabs {
  font-size: 14px;
  font-weight: 500;

  .tab {
    margin-right: 20px;
    cursor: pointer;
  }

  .disabled {
    cursor: not-allowed;
    color: #ccc;
  }

  .active {
    color: #155bd4;
  }
}
</style>

<style lang="less">
.settle-dialog {
  .ivu-modal-body {
    max-height: 500px;
    height: 500px;
  }
}

// 地址选择框样式约束
.custom-address-common-style {
  .el-scrollbar__wrap {
    max-height: 340px;
    width: 220px;
  }
}
</style>

<style scoped lang="less">
// 利用iview的表单校验，标错不被iview表单识别的控件
::v-deep .ivu-form-item-error {
  .el-cascader {
    input {
      border-color: red !important;
    }
  }
}

// 调整图片上传的标红提醒文案
::v-deep .item-error-tip-position {
  .ivu-form-item-error-tip {
    margin-top: -10px;
  }
}
</style>
<style lang="less">
.customTooltip {
  .ivu-tooltip-inner {
    max-height: max-content;
  }
}
</style>
