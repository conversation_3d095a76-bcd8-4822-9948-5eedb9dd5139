<template>
  <div class="detail">
    <div class="block-header">申请方信息</div>
    <template>
      <KWidget :labelWidth="100" label="申请方:">
        <div class="item-lineHeight">{{ formData.applicant.applicant || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="状态:">
        <div class="item-lineHeight" :style="{ color: formData.status == 'PART_FINISHED' ? 'red' : '' }">
          {{ formData.status_desc || '-' }}
          <span v-if="formData.status == 'REJECTED'"
            >(<span style="color: red">驳回原因：{{ formData.rejected_desc || '-' }}</span
            >)</span
          >
        </div>
      </KWidget>

      <KWidget :labelWidth="100" label="申请时间:">
        <div class="item-lineHeight">{{ formData.create_time || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="开票时间:">
        <div class="item-lineHeight">{{ formData.finished_at || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="收票人姓名:">
        <div class="item-lineHeight">{{ formData.applicant.name || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="收票人手机:">
        <div class="item-lineHeight">{{ formData.applicant.mobile || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="收票邮箱:">
        <div class="item-lineHeight">{{ formData.applicant.email || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="收票地址:">
        <div class="item-lineHeight">{{ formData.applicant.address_text || '-' }}</div>
      </KWidget>
    </template>

    <div class="block-header">发票内容</div>
    <template>
      <KWidget :labelWidth="100" label="发票类型:">
        <div class="item-lineHeight">{{ formData.type_desc || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="发票金额:">
        <div class="item-lineHeight">
          <span v-if="formData.amount">{{ `￥${formData.amount}` }}</span>
          <span v-if="Math.sign(formData.red_amount) < 0"
            >(<span style="color: red">¥{{ formData.red_amount }}</span
            >)</span
          >
          <!-- {{ formData.amount ?`￥${formData.amount}` : '-' }} -->
          <a @click="goodsDetail()">商品明细</a>
        </div>
        <Table border :columns="order_columns" :data="showOrderList()" style="width: 800px">
          <!-- 订单类型 -->
          <template slot-scope="{ row }" slot="type_text">
            <span>{{ row.type_text || '-' }}</span>
          </template>
          <!-- 金额 -->
          <template slot-scope="{ row }" slot="amount">
            <span>{{ row.amount ? `￥${row.amount}` : '-' }}</span>
          </template>
        </Table>
      </KWidget>

      <KWidget :labelWidth="100">
        <div class="border-arrow" style="text-align: center" v-if="order_list.length > this.split_num">
          <a @click="foldOrExpand('expand')" v-if="showOrderList().length <= this.split_num">
            展开
            <Icon type="ios-arrow-down" />
          </a>
          <a @click="foldOrExpand('fold')" v-else>
            收起
            <Icon type="ios-arrow-up" />
          </a>
        </div>
      </KWidget>

      <KWidget :labelWidth="100" label="开票结果:">
        <div class="item-lineHeight flex mt10">
          <Table
            border
            :span-method="handleSpan"
            :row-class-name="rowClassName"
            :columns="getColumns"
            :data="invoice_details"
            style="width: 800px"
          >
            <!-- 合并列 -->
            <template slot-scope="{ row, index }" slot="custom">
              <span>{{ getBillType(row, index) }}</span>
            </template>

            <!-- 序号 -->
            <template slot-scope="{ row, index }" slot="orderNumber">
              <span v-if="success_data_length > index">{{ `${index + 1}` }}</span>
              <span v-else-if="row.type == 'RED'" style="color: red">红票</span>
              <span v-else-if="row.type == 'BLUE'" style="color: blue">蓝票</span>
              <span v-else>{{ `失败原因：${row.rejected_desc}` }}</span>
            </template>

            <!-- 发票编号 -->
            <template slot-scope="{ row }" slot="invoice_no">
              <span>{{ row.invoice_no || '-' }}</span>
            </template>

            <!-- 金额 -->
            <template slot-scope="{ row }" slot="amount">
              <span>{{ row.amount ? `￥${row.amount}` : '-' }}</span>
            </template>

            <!-- 发票内容 -->
            <template slot-scope="{ row }" slot="content">
              <a @click="goodsDetail(row.goods)">商品明细</a>
            </template>

            <!-- 发票预览 -->
            <template slot-scope="{ row, index }" slot="nuonuo">
              <div
                v-if="
                  index < success_data_length ||
                  (index >= success_data_length + error_data_length && red_data_length > 0)
                "
              >
                <span
                  v-if="(row.nuonuo && row.nuonuo.c_paper_pdf_url?.length) || (row.nuonuo && row.nuonuo.c_url?.length)"
                >
                  <a @click="previewInvoice(row)">点击预览</a>
                </span>
                <span v-else>-</span>
              </div>

              <div v-else>-</div>
            </template>
          </Table>
        </div>
      </KWidget>
    </template>

    <div class="block-header">发票信息</div>
    <div style="minheight: 340px">
      <KWidget :labelWidth="100" label="发票抬头:">
        <div class="item-lineHeight">{{ formData.invoice.organization_name || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="公司税号:">
        <div class="item-lineHeight">{{ formData.invoice.organization_code || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="注册地址:">
        <div class="item-lineHeight">{{ formData.invoice.reg_address_text || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="注册电话:">
        <div class="item-lineHeight">{{ formData.invoice.reg_mobile || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="开户银行:">
        <div class="item-lineHeight">{{ formData.invoice.bank || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="100" label="银行账户:">
        <div class="item-lineHeight">{{ formData.invoice.bank_account || '-' }}</div>
      </KWidget>
    </div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <dvd />
      <dvd />
      <dvd />
      <Button :loading="downloadLoading" v-if="isCanDownload" @click="downloadInvoiceExcel">导出开票结果</Button>
    </div>

    <!-- 商品明细 -->
    <goods-modal
      :goods-visible.sync="goodsVisible"
      :id="$route.query.id"
      :order-codes="order_codes"
      :goodsDetails="goodsDetails"
    ></goods-modal>
    <invoice-preview v-model="invoicePreviewVisible" :invoice-urls="previewInvoiceUrls"></invoice-preview>

    <!-- 重新申请开票 -->
    <!--    <apply-invoice-modal v-model="applyInvoceVisible" :pt_id="$route.query.id" :goodsDetails="goodsDetails" @refresh="init"></apply-invoice-modal>-->

    <!-- 红票标记开票 -->
    <!--    <red-invoice-modal v-model="redInvoiceVisible" :invoice_id="formData.invoice_id" :detail_id="red_detail_id" :amount="rowClickData.amount" @refresh="init"/>-->
  </div>
</template>

<script>
import downloadExcel from '@/mixins/downloadExcel';
import { cli_joinin_res } from './components/mockInvoiceData';
import { cloneDeep  } from 'lodash-es';
import InvoicePreview from '@/components/InvoicePreview/index.vue';

export default {
  name: 'detail',
  components: {
    GoodsModal: () => import('./components/GoodsDetail'),
    InvoicePreview,
  },
  mixins: [downloadExcel],
  props: {},
  data() {
    return {
      formData: {
        applicant: {},
        invoice: {},
      },
      goodsVisible: false, // 商品明细弹窗
      order_columns: [
        { title: '订单编号', key: 'order_code', align: 'center' },
        { title: '订单类型', slot: 'type_text', align: 'center' },
        { title: '金额', slot: 'amount', align: 'center' },
        { title: '开票方', key: 'drawer', align: 'center' },
      ],
      order_list: [],
      showType: 'fold', // 默认折叠
      split_num: 5, // 折叠数量

      invoice_columns: [
        { title: ' ', slot: 'custom', align: 'center' }, // 做合并展示
        { title: '序号', slot: 'orderNumber', align: 'center' },
        { title: '发票编号', slot: 'invoice_no', align: 'center' },
        { title: '金额', slot: 'amount', align: 'center', className: 'table-success' },
        { title: '发票内容', slot: 'content', align: 'center' },
        { title: '发票预览', slot: 'nuonuo', align: 'center' },
      ],

      invoice_normal_columns: [
        { title: '序号', slot: 'orderNumber', align: 'center' },
        { title: '发票编号', slot: 'invoice_no', align: 'center' },
        { title: '金额', slot: 'amount', align: 'center', className: 'table-success' },
        { title: '发票内容', slot: 'content', align: 'center' },
        { title: '发票预览', slot: 'nuonuo', align: 'center' },
      ],
      invoice_details: [], // 已开发票
      success_data_length: 0,
      error_data_length: 0, // 当前设置失败的开票数量为1条，所有的失败的条数均被整合成一条
      red_data_length: 0, // 当前设置红的开票数量为1条，所有的红的条数均被整合成一条
      blue_data_length: 0, // 当前设置蓝票的开票数量为1条，所有的蓝票的条数均被整合成一条
      wait_data_length: 0,
      goodsDetails: [], //商品明细对应的数据
      rowClickData: {},

      // 申请开票弹窗
      applyInvoceVisible: false,
      // 红票标记开票弹窗
      redInvoiceVisible: false,
      red_detail_id: null,
      // 导出开票结果
      downloadApiName: 'invoiceExportdetail',

      order_codes: {}, // 采购订单codes合集
      previewInvoiceUrls: [],
      invoicePreviewVisible: false,
    };
  },
  computed: {
    isCanDownload() {
      let status = this.formData.status;
      switch (status) {
        case 'PART_FINISHED':
        case 'FINISHED':
        case 'FAIL':
          return true;
        default:
          return false;
      }
    },
    getColumns() {
      if (this.formData.status !== 'REJECTED' || this.formData.is_cli_joinin == 1) {
        return this.invoice_columns;
      } else {
        return this.invoice_normal_columns;
      }
    },
    /**
     * 待审核 ｜ 待开票 ｜ 开票成功 ｜ 已驳回 ｜ 开票失败
     * */
    getBillType() {
      return (row, index) => {
        if (index == 0 && this.success_data_length > 0) {
          return '开票成功';
        } else if (row.status === 'AUDIT_WAIT') {
          return '待审核';
        } else if (row.status === 'WAIT') {
          return '待开票';
        } else if (row.status === 'REJECTED') {
          return '已驳回';
        } else if (row.status === 'FAIL') {
          return '开票失败';
        } else {
          return '';
        }
      };
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    previewInvoice(row) {
      const urls = row.nuonuo.c_paper_pdf_url.length ? row.nuonuo.c_paper_pdf_url : row.nuonuo.c_url;

      if (urls.length === 1) {
        const a = document.createElement('a');
        a.href = urls[0];
        a.target = '_blank';
        a.download = '';
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        return;
      }
      this.previewInvoiceUrls = urls;
      this.invoicePreviewVisible = true;
    },
    init() {
      // 发票详情
      this.getPurInvoiceDetail();
    },

    // 展开/收起
    foldOrExpand(type = 'fold') {
      this.showType = type;
      this.showOrderList();
    },
    // 展示订单数据
    showOrderList() {
      let copyOrderList = cloneDeep(this.order_list);
      let returnOrderList = copyOrderList;
      if (this.showType === 'fold') {
        returnOrderList = copyOrderList.splice(0, this.split_num);
      }
      return returnOrderList;
    },

    // 导出开票结果
    downloadInvoiceExcel() {
      let params = {
        id: this.$route.query.id,
      };
      this.downloadExcel(params);
    },

    /**
     * @description: 合并单元格
     * @note 开票成功，竖列合并 红票｜蓝票｜开票失败 第二三列合并，此三种类型最多有一条
     * @note 根据开票合并数组数据的顺序，【开票成功，开票失败，蓝票，红票】
     * */
    handleSpan({ row, column, rowIndex, columnIndex }) {
      // 存在开票成功的数据, 竖列合并所有开票成功的数据
      if (this.success_data_length > 0 && columnIndex === 0 && rowIndex < this.success_data_length) {
        // 从第一列开始合并
        if (rowIndex === 0) {
          return [this.success_data_length, 1];
        } else {
          // 其他单元格均被第一条合并掉
          return [0, 0];
        }
      }

      // 开票失败 ｜ 红票 ｜ 蓝票 三种的序号和发票编号俩列均合并
      if (row.status === 'FAIL' || row.status === 'WAIT' || row.status === 'AUDIT_WAIT' || row.status === 'REJECTED') {
        // 序号列和发票编号列合并
        if (columnIndex == 1) {
          return [1, 2];
        }
        // 发票编号被合并掉
        if (columnIndex == 2) {
          return [0, 0];
        }
      }
    },

    /**
     * @description: 单独设置表格样式
     * @note: 开票成功 待开票 待审核 已驳回 开票失败
     * */
    rowClassName(row, index) {
      // 有开具成功的票
      if (this.success_data_length > 0 && index < this.success_data_length) {
        // 开票成功合并，针对第一列做成功标识即可
        if (index === 0) {
          return 'table-success';
        }
      } else if (row.status === 'WAIT' || row.status === 'AUDIT_WAIT' || row.status === 'REJECTED') {
        return 'table-red';
      } else if (row.status === 'FAIL') {
        // 开票失败
        return 'table-error';
      } else {
        return '';
      }
    },

    goodsDetail(list) {
      this.goodsDetails = [];
      if (list != undefined) {
        this.goodsDetails = list;
      }
      this.order_codes = this.getShipOrderCodeIds();
      this.goodsVisible = true;
    },

    // 获取选中订单的ship_code_ids
    getShipOrderCodeIds() {
      let codes = {};
      this.order_list.forEach(item => {
        this.$set(codes, item.trade_flow_ids, item.order_code);
      });
      return codes || {};
    },

    // 发票详情
    getPurInvoiceDetail() {
      let params = {
        id: this.$route.query.id,
      };
      this.$api.getPurInvoiceDetail(params).then(res => {
        this.formData = res;
        this.order_list = res.order_details;

        let invoiceData = res;
        // mock
        // invoiceData = cli_joinin_res;

        // 设置成功和失败，蓝票，红票的条数
        this.invoice_details = [
          ...invoiceData.invoice_details,
          ...invoiceData.fail_invoice,
          ...invoiceData.blue_invoice,
          ...invoiceData.red_invoice,
        ];
        this.success_data_length = invoiceData.invoice_details.length;
        this.error_data_length = invoiceData.fail_invoice.length;
        this.red_data_length = invoiceData.red_invoice.length;
        this.blue_data_length = invoiceData.blue_invoice.length;
      });
    },
  },
  filters: {},
};
</script>

<style lang="less" scoped>
.detail {
  padding-bottom: 40px;
}

.item-lineHeight {
  line-height: 30px;
  font-size: 13px;
}

.widget-form-group {
  margin-top: 0px;
}

::v-deep .ivu-table .ivu-table-tbody {
  .table-success > td {
    &:nth-child(1) {
      background: #1fc05f;
      color: #fff;
      border-bottom: none;
    }
  }

  .table-error > td {
    &:nth-child(1) {
      background: #e8582e;
      color: #fff;
      border-bottom: none;
    }
  }

  .table-red > td {
    &:nth-child(1) {
      background: #f88d2e;
      color: #fff;
      border-bottom: none;
    }
  }
}

.border-arrow {
  // border: 1px solid #d7d9de;
  width: 800px;
  text-align: center;
  padding: 10px 0;
  background: #fafafa;

  a {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
}

.ml8 {
  margin-left: 8px;
}

.mt10 {
  margin-top: 10px;
}
</style>
