<template>
  <div v-if="showPurchaseModal">
    <Modal
      :mask-closable="false"
      :value="showPurchaseModal"
      title="采购单明细"
      width="85"
      @on-visible-change="changeVisible"
    >
      <div slot="header" class="flex flex-item-align">
        <div style="height: 20px; line-height: 20px; font-size: 14px; color: #17233d; font-weight: 500">采购单明细</div>
        <div v-if="isReplace" class="replace-box">部分产品已售罄，已为你替换为其他厂家</div>
      </div>
      <div class="content">
        <div class="table-wrapper">
          <Table
            :columns="tableCols"
            :row-class-name="rowClassName"
            row-key="id"
            :data="selectGoodsList_copy"
            class="pl-table tc-table-style"
            size="small"
          >
            <template slot="img" slot-scope="{ row, index }">
              <div v-if="row.is_taocan_goods != 1 && !row.isChild" style="width: 20px; display: inline-block"></div>
              <img
                v-if="!row.isChild"
                :src="row.img | imageStyle"
                style="width: 40px; margin-right: 5px"
                class="img-rounded"
              />
            </template>
            <!-- 名称 -->
            <template slot="generic_name" slot-scope="{ row, index }">
              <div v-if="row.is_taocan_goods == 1" class="tc-tag">套餐</div>

              {{ row.generic_name }}
            </template>
            <!-- 品级 -->
            <template slot="grand_desc" slot-scope="{ row, index }">
              <div v-if="row.is_taocan_goods == 1">-</div>
              <div v-else style="white-space: pre-wrap">{{ row.grand_desc || '-' }}</div>
            </template>
            <!-- 规格 -->
            <template slot="prod_spec" slot-scope="{ row, index }">
              <div v-if="row.is_taocan_goods == 1">-</div>
              <div v-else style="white-space: pre-wrap">{{ row.prod_spec || '-' }}</div>
            </template>

            <template slot="operate" slot-scope="{ row, index }">
              <span v-if="row.isChild">-</span>
              <a v-else @click="removeGood(index)">移除</a>
            </template>

            <!-- 进货数量 -->
            <template slot="quantity" slot-scope="{ row, index }">
              <span v-if="row.isChild" style="margin-left: 6px">x{{ dynamicCalcQuantity(row) }}</span>
              <InputNumber
                v-else
                :precision="0"
                v-model="selectGoodsList_copy[index].quantity"
                placeholder="进货数量"
                @on-blur.capture="val => changeUnit(index, val)"
              ></InputNumber>
            </template>

            <template slot="stock_num" slot-scope="{ row }">
              {{ row.stock_num_text }}
            </template>

            <!-- 供应商 -->
            <template slot="supplier_name" slot-scope="{ row }">
              <span v-if="row.isChild">-</span>
              <span v-else>{{ row.supplier_name || '-' }}</span>
            </template>
            <!-- 生产厂家 -->
            <template slot="manufacturer" slot-scope="{ row }">
              {{ row.manufacturer || '-' }}
            </template>
            <!-- 产地 -->
            <template slot="prod_area" slot-scope="{ row }">
              {{ row.prod_area || '-' }}
            </template>

            <!-- 进货单价 -->
            <template slot="clinic_price" slot-scope="{ row }">
              <span v-if="row.isChild">-</span>
              <span v-else>￥{{ row.clinic_price }}</span>
            </template>

            <!-- 最低起购 -->
            <template slot="start_purchase" slot-scope="{ row }">
              <span v-if="row.isChild">-</span>
              <span v-else>{{ row.purchase_info ? row.purchase_info.text : '' }}</span>
            </template>
          </Table>
        </div>
      </div>
      <div slot="footer">
        <div class="flex" style="justify-content: flex-end">
          <div class="goods-price flex">
            <div class="flex">
              <div class="goods-item flex">
                <span class="label">采购商品种类：</span>
                <span class="value">{{ total_type }}</span>
              </div>
              <div class="goods-item flex" style="margin-left: 10px">
                <span class="label">采购商品总数：</span>
                <span>{{ total_num }}</span>
              </div>
              <div class="goods-item flex">
                <span class="label">待支付总额：</span>
                <span class="money">￥{{ total_price }}</span>
              </div>
            </div>
          </div>
          <div>
            <Button @click="$store.commit('purchase/CLEAR_GOODS_LIST', true)">清空采购单</Button>
            <Button @click="changeVisible(false)">关闭</Button>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex';
import { cloneDeep } from 'lodash-es';
import { $operator } from '@/libs/operation';
export default {
  name: 'purchase-modal',
  components: {},
  mixins: [],
  data() {
    return {
      tableCols: [
        { title: '图片', slot: 'img', width: 90, tree: true },
        { title: '商品名', slot: 'generic_name', width: 80 },
        { title: '产品类型', key: 'prod_type_desc', width: 60 },
        { title: '品级', slot: 'grand_desc', width: 80 },
        { title: '规格', slot: 'prod_spec', width: 80 },
        { title: '产地', slot: 'prod_area', width: 100 },
        { title: '生产厂家', slot: 'manufacturer', width: 100 },
        { title: '供应商', slot: 'supplier_name', width: 140 },
        { title: '单位	', key: 'pack_unit', width: 60 },
        { title: '进货单价', slot: 'clinic_price', width: 60 },
        { title: '进货数量', slot: 'quantity', width: 100 },
        // {title: '库存', slot: 'stock_num',width: 80},
        { title: '最低起购', slot: 'start_purchase', width: 60 },
        { title: '操作', slot: 'operate', width: 50 },
      ],
      selectGoodsList_copy: [],
    };
  },
  computed: {
    ...mapState('purchase', {
      // selectGoodsList: state => state.selectGoodsList,
      showPurchaseModal: state => state.showPurchaseModal,
      isReplace: state => state.isReplace,
    }),
    ...mapGetters('purchase', ['total_price', 'total_num', 'total_type', 'selectGoodsList']),
    computedStyle(row) {
      return row => {
        if (row.is_effective === '0' || row.isUpStock) {
          return { color: 'red' };
        } else {
          return { color: 'green' };
        }
      };
    },
    // 动态计算套餐商品的起购倍数
    dynamicCalcQuantity() {
      return row => {
        let quantity = this.selectGoodsList[row.parentIndex]?.quantity;
        console.log('-> quantityquantityquantity', row, this.selectGoodsList[row.parentIndex]);
        return $operator.multiply(Number(row.purchase_info.num), Number(quantity));
      };
    },
  },
  watch: {
    selectGoodsList: {
      handler(val) {
        console.log('-> val', val);
        this.selectGoodsList_copy = cloneDeep(val);
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    ...mapMutations('purchase', [
      'SHOW_PURCHASE_MODAL',
      'REMOVE_SELECT_GOODS',
      'CHANGE_GOODS_UNIT',
      'CHANGE_SELECT_GOODS_UNIT',
    ]),
    changeVisible(visible) {
      if (!visible) {
        this.SHOW_PURCHASE_MODAL(visible);
      }
    },

    // 给表格行设置样式
    rowClassName(row, index) {
      if (row.children && row.children.length) {
        return 'ivu-table-row-tree';
      }
      if (row.isChild) {
        return 'ivu-table-row-tree-child';
      }
    },

    removeGood(index) {
      this.REMOVE_SELECT_GOODS(index);
    },
    //改变采购数量
    changeUnit(index, val) {
      console.log('-> index', index, val);

      if (this.selectGoodsList_copy[index].quantity < Number(this.selectGoodsList_copy[index].purchase_info.num)) {
        console.log('你笑了');
        this.selectGoodsList_copy[index].quantity = Number(this.selectGoodsList_copy[index].purchase_info.num);
        this.CHANGE_SELECT_GOODS_UNIT({
          index,
          quantity: Number(this.selectGoodsList_copy[index].purchase_info.num),
        });
      }
      console.log(this.selectGoodsList_copy[index].purchase_info.type, this.selectGoodsList_copy[index].stock_num);
      console.log(
        '-> %c this.selectGoodsList_copy[index].quantity  === %o ',
        'font-size: 15px',
        this.selectGoodsList_copy[index].quantity
      );
      if (
        this.selectGoodsList_copy[index].quantity > Number(this.selectGoodsList_copy[index].stock_num) &&
        this.selectGoodsList_copy[index].stock_mod == '1'
      ) {
        this.$Message.error(`商品${this.selectGoodsList_copy[index].generic_name}采购数量不能大于当前库存`);
        if (this.selectGoodsList_copy[index].purchase_info.type === 'MUL') {
          console.log(this.selectGoodsList_copy[index].quantity % Number(this.selectGoodsList_copy[index].stock_num));
          this.selectGoodsList_copy[index].quantity =
            this.selectGoodsList_copy[index].stock_num -
            (Number(this.selectGoodsList_copy[index].stock_num) % this.selectGoodsList_copy[index].purchase_info.num);
        } else {
          this.selectGoodsList_copy[index].quantity = Number(this.selectGoodsList_copy[index].stock_num);
        }
      }
      if (
        this.selectGoodsList_copy[index].purchase_info.type === 'MUL' &&
        this.selectGoodsList_copy[index].quantity % this.selectGoodsList_copy[index].purchase_info.num !== 0
      ) {
        this.selectGoodsList_copy[index].quantity =
          this.selectGoodsList_copy[index].quantity -
          (this.selectGoodsList_copy[index].quantity % this.selectGoodsList_copy[index].purchase_info.num);
        this.CHANGE_SELECT_GOODS_UNIT({
          index,
          quantity:
            this.selectGoodsList_copy[index].quantity -
            (this.selectGoodsList_copy[index].quantity % this.selectGoodsList_copy[index].purchase_info.num),
        });
      } else {
        console.log(this.selectGoodsList_copy[index]);
        this.CHANGE_SELECT_GOODS_UNIT({
          index,
          quantity: this.selectGoodsList_copy[index].quantity,
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.goods-price {
  //margin-top: 20px;
  justify-content: flex-end;
  align-items: center;
  margin-right: 20px;
  .goods-item {
    text-align: right;
  }

  .label {
    width: 100px;
  }

  .money {
    font-size: 16px;
    font-weight: 600;
  }
}
::v-deep .ivu-modal-body {
  max-height: 550px;
  overflow-y: auto;
  height: 400px;
}
::v-deep .ivu-table-cell {
  display: flex;
  align-items: center;
  .ivu-table-cell-tree {
    margin-right: 4px;
    margin-top: 4px;
  }
  .ivu-icon {
    margin-top: 1px;
    margin-left: 1px;
  }
}
.replace-box {
  margin-left: 20px;
  padding: 3px;
  border: 1px solid #155bd4;
  color: #155bd4;
  border-radius: 5px;
}
</style>
