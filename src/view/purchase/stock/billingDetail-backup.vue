<template>
  <div class="bill-wrapper">
    <div class="block-header"><span>采购明细</span></div>
    <Table
      :columns="tableCols"
      :row-class-name="rowClassName"
      class="tc-table-style"
      row-key="sku_code"
      :indent-size="100"
      :data="return_select_goods"
      size="small"
      style="margin-bottom: 10px"
    >
      <!--商品名-->
      <template slot="generic_name" slot-scope="{ row }">
        <div>
          <span v-if="row.item_type == 'TAOCAN'" class="tc-tag">套餐</span>
          <span>{{ row.generic_name }}</span>
          <span v-if="row.is_activity_product" class="activity-tag">特惠加购</span>
        </div>
      </template>

      <template slot="grand_desc" slot-scope="{ row, index }">
        <div v-if="row.is_taocan_goods == 1">-</div>
        <div v-else style="white-space: pre-wrap">{{ row.grand_desc || '-' }}</div>
      </template>

      <template slot="img" slot-scope="{ row }">
        <!--				<viewer v-if="row.img" :images="[row.img]" class="flex  flex-item-center" style="margin: 8px 0;">-->
        <!--					<img :src="row.img"-->
        <!--					     class="image" style="width: 60px;height: auto;max-height: 100px;">-->
        <!--				</viewer>-->
        <!--				<span v-else>-</span>-->
        <div style="max-height: 100px; height: 80px" class="flex flex-item-center">
          <viewer
            v-if="row.desc_imgs.length"
            :images="row.desc_imgs"
            class="flex flex-item-center"
            style="margin: 6px 0"
          >
            <img
              v-for="(img, index) in row.desc_imgs"
              v-show="index === 0"
              :key="img"
              :src="img"
              class="image"
              style="width: 60px; height: auto; max-height: 70px"
            />
          </viewer>
          <span style="vertical-align: middle" v-else>-</span>
        </div>
      </template>

      <!--规格-->
      <template slot="prod_spec" slot-scope="{ row }">
        <div v-if="row.item_type == 'TAOCAN'">-</div>
        <p v-else>{{ row.prod_spec || '-' }}</p>
      </template>

      <!-- 供应商 -->
      <template slot-scope="{ row, index }" slot="supplier_name">
        <div v-if="row.isChild">-</div>
        <p v-else>{{ row.supplier_name || '-' }}</p>
      </template>

      <!-- 生产厂家 -->
      <template slot="manufacturer" slot-scope="{ row }">
        {{ row.manufacturer || '-' }}
      </template>
      <!-- 产地 -->
      <template slot="prod_area" slot-scope="{ row }">
        {{ row.prod_area || '-' }}
      </template>

      <!-- 经销商 -->
      <template slot-scope="{ row, index }" slot="seller">
        <div v-if="row.isChild">-</div>
        <p v-else>{{ row.seller || '-' }}</p>
      </template>

      <!-- 采购单价 -->
      <template slot-scope="{ row, index }" slot="price">
        <div v-if="row.isChild">-</div>
        <p v-else>￥{{ row.price }}</p>
      </template>

      <!-- 采购数量 -->
      <template slot-scope="{ row, index }" slot="num">
        <div v-if="row.isChild">x{{ row.num || '0' }}</div>
        <p v-else>{{ row.num || '0' }}</p>
      </template>
    </Table>

    <!-- 活动期间内 && 满足常繁活动价格 && 未追加   -->
    <div class="activity-tip" v-if="cf_activity?.is_activity_on === '1' && Number(cf_activity?.gift_num)">
      <div class="activity-note">
        本单已满{{ cf_activity?.order_money }}元，已为您自动加购
        <span class="activity-bold">{{ cf_activity?.gift_num }}份</span>「{{ cf_activity?.gift_name }}」，仅需<span
          class="activity-bold"
          >{{ cf_activity?.attach_pay }}元</span
        >
      </div>
    </div>

    <div class="block-header"><span>收货信息</span></div>
    <Form ref="goodPayForm" :label-width="110" :model="formData" :rules="ruleValidate" label-colon>
      <div class="receipt-info">
        <div>
          <Row>
            <Col span="8">
              <FormItem label="收货主体" prop="yizhan_id" v-if="isJiShanChannel">
                <Select
                  v-model="formData.yizhan_id"
                  placeholder="请选择收货主体"
                  filterable
                  clearable
                  @on-change="selectYiZhan"
                >
                  <Option
                    v-for="(item, index) in healthStationList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="8">
              <FormItem label="收件人" prop="consignee_info.consignee">
                <Input
                  v-model="formData.consignee_info.consignee"
                  placeholder="请输入收件人姓名"
                  :disabled="isDisabled"
                ></Input>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="手机号" prop="consignee_info.mobile">
                <Input
                  v-model="formData.consignee_info.mobile"
                  placeholder="请输入收件人手机号"
                  :disabled="isDisabled"
                ></Input>
              </FormItem>
            </Col>
            <Col span="16">
              <FormItem label="收件地址" prop="consignee_info.address.city.code">
                <div class="flex">
                  <div class="address" style="width: 55%">
                    <el-cascader
                      v-model="selectedAddress"
                      :options="options"
                      clearable
                      placeholder="请选择收件地址"
                      size="small"
                      popper-class="address-com"
                      style="width: 100%"
                      :disabled="isDisabled"
                      @change="regionChange"
                    >
                    </el-cascader>
                  </div>
                  <div class="addressInput ml10">
                    <Input
                      v-model="formData.consignee_info.address.detail"
                      placeholder="详细地址"
                      :disabled="isDisabled"
                    ></Input>
                  </div>
                </div>
              </FormItem>
            </Col>
          </Row>
        </div>
      </div>
      <div class="pay-type">
        <div class="block-header"><span>备注</span></div>
        <Row v-if="isDisplayHealth === '1'">
          <Col span="8">
            <FormItem label="采购单所属医疗事业部" :label-width="160" prop="is_belong_health">
              <RadioGroup v-model="formData.is_belong_health">
                <Radio label="1">是</Radio>
                <Radio label="0">否</Radio>
              </RadioGroup>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="16" class="flex flex-item-align">
            <p style="text-align: right; width: 80px">备注：</p>
            <Input
              style="width: 100%; max-width: 100%"
              :autosize="{ minRows: 3, maxRows: 6 }"
              v-model="formData.remark"
              type="textarea"
              placeholder="请添加服装类商品尺码标注或其他补充信息"
            ></Input>
          </Col>
        </Row>
      </div>
    </Form>

    <div class="block_30"></div>
    <div class="fixed-bottom-wrapper">
      <Button class="marginRight10" @click="back">返回</Button>
      <Button type="primary" @click="createOrder('goodPayForm')" :loading="createLoading">下单并提交审核</Button>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { CodeToText, regionData, TextToCode } from '@/libs/chinaMap';
import renderHeader from '@/mixins/renderHeader';
import { isJiShanChannel } from '@/libs/runtime';
import { cloneDeep } from 'lodash-es';
import { $operator } from '../../../libs/operation';
import S from '@/libs/util';

const init_form_data = {
  listof: {},
  tc_listof: {},
  yizhan_id: '',
  remark: '',
  consignee_info: {
    consignee: '',
    mobile: '',
    address: {
      prov: {
        name: '',
        code: '',
      },
      city: {
        name: '',
        code: '',
      },
      county: {
        name: '',
        code: '',
      },
      detail: '',
    },
  },
};
export default {
  name: 'detail',
  components: {},
  mixins: [renderHeader],
  data() {
    return {
      createLoading: false, //下单loading
      payVisible: false,
      single: true,
      selectedAddress: [],
      options: regionData,
      tableCols: [
        { title: '编号', key: 'sku_code', resizable: true, tree: true },
        { title: '介绍图', slot: 'img', resizable: true, align: 'center' },
        { title: '商品名', slot: 'generic_name', resizable: true, minWidth: 100 },
        { title: '类型', key: 'prod_type_text', resizable: true },
        { title: '品级', slot: 'grand_desc', resizable: true },

        { title: '规格	', slot: 'prod_spec', resizable: true },
        { title: '单位	', key: 'pack_unit', resizable: true },

        { title: '产地', slot: 'prod_area', width: 100 },
        { title: '生产厂家', slot: 'manufacturer', width: 100 },
        { title: '供应商', slot: 'supplier_name', resizable: true },
        {
          title: '销售商',
          slot: 'seller',
          minWidth: 50,
          tooltip: true,
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具'),
        },
        // {title: '批号', key: 'batch_code', resizable: true},
        // {title: '生产日期', key: 'prod_date', resizable: true},
        // {title: '过期时间', key: 'expire_date', resizable: true},
        {
          title: '采购单价',
          slot: 'price',
          resizable: true,
          renderHeader: (h, params) => this._renderHeader(h, params, '采购单价 = 货品金额 + 平台使用费'),
        },
        { title: '采购数量', slot: 'num', resizable: true },
      ],
      ruleValidate: {
        yizhan_id: [{ required: true, message: '请选择收货主体', trigger: 'change' }],
        is_belong_health: [{ required: true, message: '请选择采购单所属医疗事业部', trigger: 'change' }],
        'consignee_info.consignee': [{ required: true, message: '请填写收件人姓名', trigger: 'change' }],
        'consignee_info.mobile': [{ required: true, message: '请填写收件人电话号码', trigger: 'change' }],
        'consignee_info.address.city.code': [{ required: true, message: '请选择收件地址', trigger: 'change' }],
      },
      formData: cloneDeep(init_form_data),
      isDisplayHealth: '',
      payInfo: {
        balance: '',
        goods_num: '',
        goods_types: '',
        gunion_enabled_transfer: '',
        payment_fee: '',
        is_belong_health: '',
        autoPay: false,
      },
      return_select_goods: [],

      // 审核历史
      examineCols: [
        { title: '编号', key: 'sku_code', resizable: true },
        { title: '审核状态', key: 'sku_code', resizable: true },
        { title: '审核时间', key: 'sku_code', resizable: true },
        { title: '审核人', key: 'sku_code', resizable: true },
        { title: '审核内容', key: 'sku_code', resizable: true, minWidth: 300 },
      ],
      examineList: [],
      healthStationList: [],
      isJiShanChannel: isJiShanChannel(),
      isAddActivityProduct: false,
      cf_activity: {},
    };
  },
  computed: {
    ...mapState('purchase', {
      // select_goods: state => state.selectGoodsList,
      covidListof: state => state.covidListof,
      ysHomologyListof: state => state.ysHomologyListof,
    }),
    ...mapGetters('purchase', { select_goods: 'selectGoodsList' }),

    // hasEnoughMoney(){
    // 	// !TODO 目前余额 后端未做校验  去除该校验(已完成)
    // 	return( Number(this.payInfo.balance) > Number(this.payInfo.payment_fee)|| this.payInfo.autoPay)
    // },
    // 内容是否只读
    // 如果采购订单在审核中,展示只读
    isDisabled() {
      return false;
    },
  },
  watch: {},
  created() {
    this.$router.onReady(() => {
      if (this.$route.query.type == 'covid') {
        //  新冠
        this.purchaseConfirm(this.covidListof);
        return;
      }

      if (this.$route.query.type == 'ys-homology') {
        //  药食同源
        this.purchaseConfirm({ listof: this.ysHomologyListof, tc_listof: {} });
        return;
      }
      const listof = {};
      const tc_listof = {};
      this.select_goods.map(item => {
        if (item.is_taocan_goods == 1) {
          tc_listof[item.id] = item.quantity;
        } else {
          listof[item.id] = item.quantity;
        }
      });
      this.purchaseConfirm({ listof, tc_listof });
      if (this.isJiShanChannel) {
        this.getHealthStationList();
      }
    });
  },
  mounted() {},
  methods: {
    // 给表格行设置样式
    rowClassName(row, index) {
      if (row.children && row.children.length) {
        return 'ivu-table-row-tree';
      }
      if (row.isChild) {
        return 'ivu-table-row-tree-child';
      }
    },

    createOrder(formName) {
      this.$refs[formName].validate(valid => {
        if (!this.formData.consignee_info.address.detail) {
          this.$Message.error('请完善收件地址信息');
          return;
        }
        // 三周年庆典 追加成功新增参数
        if (this.isAddActivityProduct) {
          this.formData.with_cf_gift = 1;
        }
        if (valid) {
          this.createLoading = true;
          this.$api
            .createTwicePurchase(this.formData)
            .then(
              res => {
                this.$store.commit('purchase/CLEAR_GOODS_LIST');
                this.$router.replace('/purchase/order/list');
              },
              err => {
                {
                }
              }
            )
            .finally(() => (this.createLoading = false));
        } else {
          // this.$Message.error('请先完善收货信息');
        }
      });
    },

    // 采购常繁商品时，需判断是否达到常繁设置的包邮价格

    // confirmPayment(){
    // 	this.$api.confirmPayment(this.formData).then(res=>{
    // 		this.$store.commit('purchase/CLEAR_GOODS_LIST')
    // 		this.$Message.success('支付成功')
    // 		this.payVisible = false
    // 		this.$router.replace('/purchase/stock/list')
    // 	},err=>{})
    // },
    // confirmPay(name) {
    // 	this.$refs[name].validate(valid=>{
    // 		if(!this.formData.consignee_info.address.detail){
    // 			this.$Message.error('请完善收件地址信息')
    // 			return
    // 		}
    // 		if(valid){
    // 			this.payVisible = true
    // 		}else {
    // 			this.$Message.error('请先完善收货信息')
    // 		}
    // 	})
    // },

    // 返回
    back() {
      let path = this.$route.query.type == 'ys-homology' ? '/purchase/stock/ys-homology' : '/purchase/stock/list';
      this.$router.replace(path);
    },

    purchaseConfirm({ listof, tc_listof }) {
      console.log('-> %c listof  ===    %o', 'font-size: 15px;color: #fa8c16 ;', listof);
      let params = {
        listof,
        tc_listof,
      };
      this.formData.listof = listof;
      this.formData.tc_listof = tc_listof;
      this.$api.getPurchaseConfirmInfo(params).then(
        res => {
          this.return_select_goods = res.list;
          this.return_select_goods.forEach(item => {
            if (item.item_type == 'TAOCAN') {
              item.tc_skus?.forEach(c_item => (c_item.isChild = true));
              item.children = item.tc_skus;
              item._showChildren = false;
            }
          });
          console.log('return_select_goods', this.return_select_goods);

          this.payInfo.balance = res.balance;
          this.payInfo.goods_num = res.goods_num;
          this.payInfo.goods_types = res.goods_types;
          this.payInfo.payment_fee = res.payment_fee;
          this.payInfo.autoPay = !!res?.auto_pay;
          this.isDisplayHealth = res?.is_display_health || '0';
          // 非己善渠道诊所继续回显收货信息，己善渠道诊所需优先选择收货主体
          if (!this.isJiShanChannel) {
            this.formData.consignee_info = res.consignee_info;
            let provName = res.consignee_info.address.prov.name;
            let cityName = res.consignee_info.address.city.name;
            let countyName = res.consignee_info.address.county.name;
            let provCode = res.consignee_info.address.prov.code || TextToCode[provName].code;
            let cityCode = res.consignee_info.address.city.code || TextToCode[provName][cityName].code;
            let countyCode = res.consignee_info.address.county.code || TextToCode[provName][cityName][countyName].code;
            this.selectedAddress = [provCode, cityCode, countyCode];
            this.regionChange(this.selectedAddress);
          }

          // 三周年活动
          this.cf_activity = res.cf_activity;
          if (this.cf_activity?.is_activity_on === '1' && Number(this.cf_activity?.gift_num)) {
            this.activityAddPurchase();
          }
        },
        err => {
          {
          }
        }
      );
    },
    regionChange(address) {
      if (address.length) {
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.consignee_info.address.prov = prov;
        this.formData.consignee_info.address.city = city;
        this.formData.consignee_info.address.county = county;
      } else {
        this.formData.consignee_info.address = {
          prov: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          detail: '',
        };
      }
    },

    getHealthStationList() {
      this.$api
        .getHealthStationList()
        .then(res => {
          this.healthStationList = res.list;
          console.log('=>(detail.vue:242) res', res);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    selectYiZhan(val) {
      if (val) {
        let currentSelect = this.healthStationList.find(item => item.id === val);
        console.log('=>(billingDetail.vue:477) currentSelect', currentSelect);
        this.formData.consignee_info.consignee = currentSelect.consignee_info.consignee;
        this.formData.consignee_info.mobile = currentSelect.consignee_info.mobile;
        this.formData.consignee_info.address.detail = currentSelect.consignee_info.other;
        let provName = currentSelect.consignee_info.province;
        let cityName = currentSelect.consignee_info.city;
        let countyName = currentSelect.consignee_info.county;
        let provCode = TextToCode[provName]?.code;
        let cityCode = TextToCode[provName][cityName]?.code;
        let countyCode = TextToCode[provName][cityName][countyName]?.code;
        this.selectedAddress = [provCode, cityCode, countyCode];
        console.log('=>(billingDetail.vue:484) this.selectedAddress', this.selectedAddress);
        this.regionChange(this.selectedAddress);
      } else {
        this.formData.consignee_info.consignee = '';
        this.formData.consignee_info.mobile = '';
        this.formData.consignee_info.address.detail = '';
        this.selectedAddress = [];
        this.regionChange(this.selectedAddress);
      }
    },

    activityAddPurchase() {
      const listof = {};
      const tc_listof = {};
      this.select_goods.map(item => {
        if (item.is_taocan_goods == 1) {
          tc_listof[item.id] = item.quantity;
        } else {
          listof[item.id] = item.quantity;
        }
      });

      this.$api.confirmCFActivity({ listof, tc_listof }).then(res => {
        let activityProduct = this.cf_activity?.gift_sku;
        activityProduct.is_activity_product = '1';
        this.return_select_goods.push(activityProduct);
        this.isAddActivityProduct = true;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.addressInput {
  flex: 1;
}

.goods-price {
  justify-content: flex-end;
  align-items: flex-end;

  .goods-item {
    margin-top: 4px;
    text-align: right;
  }

  .label {
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .money {
    font-size: 16px;
    font-weight: 600;
  }
}

.modal-title {
  text-align: center;
  margin-bottom: 15px;
  margin-top: -30px;
  font-size: 16px;
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

::v-deep .ivu-modal-header {
  border-bottom: none;
}
.red {
  color: red;
}
.activity-tag {
  display: inline-block;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
  background: #d63232;
  border-radius: 3px;
  border: 1px solid #d63232;
  padding: 1px 3px;
  min-width: 45px;
  height: 23px;
  line-height: 20px;
  text-align: center;
  margin-left: 10px;
}
.activity-tip {
  display: flex;
  align-items: center;
  .activity-note {
    padding: 7px 10px;
    background: #fce9e8;
    border-radius: 2px;
    border: 1px solid #f3a8a2;
    margin-right: 10px;
    color: #e6372b;
    font-size: 14px;
    .activity-bold {
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>
<style lang="less">
p {
  margin: 0px;
}
</style>
