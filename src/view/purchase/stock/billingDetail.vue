<template>
  <div class="bill-wrapper">
    <!--  药品采购  -->
    <div v-if="presGoodsList.length > 0">
      <div class="receive-title-header">
        <div class="line"></div>
        <div>收货信息</div>
      </div>
      <div class="receive-box mb20">
        <Button v-if="procurement_tcm_status === '2'" type="primary" ghost class="receive-button" @click="showReceive">
          请选择收货地址
          <Icon type="md-add" />
        </Button>
        <div v-else class="receive-content">
          <Row class-name="receive-header receive-pd">
            <Col :span="4" style="padding-left: 20px">姓名</Col>
            <Col :span="5">手机号</Col>
            <Col :span="13">收件地址</Col>
            <Col :span="2">操作</Col>
          </Row>
          <!--   展示地址     -->
          <Form ref="mobileForm" class="editMobile" :model="registerAddress" :rules="editMobileRules" :label-width="0">
            <Row style="min-height: 54px">
              <Col :span="4" style="padding-left: 20px; display: flex; align-items: center">
                {{ registerAddress.consignee }}
              </Col>
              <Col :span="5" style="display: flex; align-items: center">
                <span v-if="!editMobileVisible">
                  {{ registerAddress.mobile }}
                </span>
                <FormItem v-else label="" prop="mobile">
                  <Input style="max-width: 120px" v-model="registerAddress.mobile" placeholder="手机号" />
                </FormItem>
              </Col>
              <Col :span="13" style="display: flex; align-items: center">
                <div class="flex">
                  <div class="address-tag" style="min-width: 60px">备案信息</div>
                  <div>{{ registerAddress.address_text }}</div>
                </div>
              </Col>
              <Col :span="2" style="display: flex; align-items: center">
                <span v-if="editMobileVisible" style="cursor: pointer; margin-right: 4px" @click="cancelEditMobile">
                  取消
                </span>
                <el-button type="text" class="el-edit-mobile" v-loading="updatePhoneLoading" @click="editMobile">
                  {{ !editMobileVisible ? '编辑' : '确定' }}
                </el-button>
              </Col>
            </Row>
          </Form>
        </div>
      </div>

      <div class="receive-title-header">采购明细(药品)</div>
      <Table
        :columns="presTableCols"
        :row-class-name="rowClassName"
        class="tc-table-style"
        row-key="sku_code"
        :indent-size="100"
        :data="presExpandList"
        size="small"
        style="margin-bottom: 20px"
        border
      >
        <template slot="index" slot-scope="{ row, index }" style="display: flex">
          <div v-if="row.isChild">-</div>
          <div v-else>{{ index + 1 }}</div>
        </template>
        <!--商品名-->
        <template slot="generic_name" slot-scope="{ row }">
          <div>
            <span v-if="row.item_type == 'TAOCAN'" class="tc-tag">套餐</span>
            <span>{{ row.generic_name }}</span>
            <span v-if="row.is_activity_product" class="activity-tag">特惠加购</span>
          </div>
        </template>

        <template slot="grand_desc" slot-scope="{ row, index }">
          <div v-if="row.is_taocan_goods == 1">-</div>
          <div v-else style="white-space: pre-wrap">{{ row.grand_desc || '-' }}</div>
        </template>

        <template slot="img" slot-scope="{ row }">
          <!--				<viewer v-if="row.img" :images="[row.img]" class="flex  flex-item-center" style="margin: 8px 0;">-->
          <!--					<img :src="row.img"-->
          <!--					     class="image" style="width: 60px;height: auto;max-height: 100px;">-->
          <!--				</viewer>-->
          <!--				<span v-else>-</span>-->
          <div class="flex flex-item-center">
            <viewer
              v-if="row.desc_imgs.length"
              :images="row.desc_imgs"
              class="flex flex-item-center"
              style="margin: 6px 0"
            >
              <img
                v-for="(img, index) in row.desc_imgs"
                v-show="index === 0"
                :key="img"
                :src="img"
                class="image"
                style="width: 30px; height: auto; max-height: 70px"
              />
            </viewer>
            <span style="vertical-align: middle" v-else>-</span>
          </div>
        </template>

        <!--规格-->
        <template slot="prod_spec" slot-scope="{ row }">
          <div v-if="row.item_type == 'TAOCAN'">-</div>
          <p v-else>{{ row.prod_spec || '-' }}</p>
        </template>

        <!-- 供应商 -->
        <template slot-scope="{ row, index }" slot="supplier_name">
          <div v-if="row.isChild">-</div>
          <p v-else>{{ row.supplier_name || '-' }}</p>
        </template>

        <!-- 生产厂家 -->
        <template slot="manufacturer" slot-scope="{ row }">
          {{ row.manufacturer || '-' }}
        </template>
        <!-- 产地 -->
        <template slot="prod_area" slot-scope="{ row }">
          {{ row.prod_area || '-' }}
        </template>

        <!-- 经销商 -->
        <template slot-scope="{ row, index }" slot="seller">
          <div v-if="row.isChild">-</div>
          <p v-else>{{ row.seller || '-' }}</p>
        </template>

        <!-- 采购单价 -->
        <template slot-scope="{ row, index }" slot="price">
          <div v-if="row.isChild">-</div>
          <p v-else v-text-format.number="row.price"></p>
        </template>

        <!-- 采购数量 -->
        <template slot-scope="{ row, index }" slot="num">
          <div v-if="row.isChild">x{{ row.num || '0' }}</div>
          <p v-else>{{ row.num || '0' }}</p>
        </template>

        <template #footer>
          <div class="table-footer">
            <div v-if="presGoodsList.length > 4">
              <div class="expand" v-if="!isPresExpand" @click="isPresExpand = true">
                <span class="total">共{{ presGoodsList.length }}种产品</span>展开订单
                <Icon type="ios-arrow-down" />
              </div>
              <div class="expand" v-else @click="isPresExpand = false">
                <span class="total">共{{ presGoodsList.length }}种产品</span>收起订单
                <Icon type="ios-arrow-up" />
              </div>
            </div>
            <div class="mr10">
              总额：<span style="color: red; font-size: 16px" v-text-format.number="presTotalAmount"></span>
            </div>
          </div>
        </template>
      </Table>

      <div class="receive-title-header">订单备注</div>
      <Input
        style="min-width: 650px"
        :autosize="{ minRows: 3, maxRows: 6 }"
        v-model="formData.tcm_remark"
        type="textarea"
        placeholder="请输入备注"
        class="mb20"
      ></Input>
    </div>
    <div class="interval-block" v-if="presGoodsList.length > 0 && otherGoodsList.length > 0"></div>
    <!--  非药品采购  -->
    <div v-if="otherGoodsList.length > 0">
      <div class="receive-title-header">
        <div class="line"></div>
        <span
        >收货信息<span v-if="presGoodsList.length > 0 && otherGoodsList.length > 0" style="padding-left: 0"
        >2</span
        ></span
        >
      </div>
      <Select
        v-if="isJiShanChannel"
        v-model="formData.yizhan_id"
        placeholder="请选择收货主体"
        filterable
        clearable
        @on-change="selectYiZhan"
        class="mb10"
      >
        <Option v-for="(item, index) in healthStationList" :key="item.id" :label="item.name" :value="item.id"></Option>
      </Select>
      <div class="receive-box">
        <div class="receive-content mb10">
          <Row class-name="receive-header receive-pd">
            <Col :span="4" style="padding-left: 20px">姓名</Col>
            <Col :span="4">手机号</Col>
            <Col :span="isEditAddress ? 6 : 14">收件地址</Col>
            <Col :span="8" v-if="isEditAddress">详细地址</Col>
            <Col :span="2">操作</Col>
          </Row>
          <!--   编辑地址     -->
          <Row class-name="receive-pd" v-if="isEditAddress">
            <Col :span="4" style="padding-right: 20px">
              <Input v-model="formData.consignee_info.consignee" placeholder="请输入收件人姓名" clearable></Input>
            </Col>
            <Col :span="4" style="padding-right: 20px">
              <Input v-model="formData.consignee_info.mobile" placeholder="请输入收件人手机号" clearable></Input>
            </Col>
            <Col :span="6" style="padding-right: 20px">
              <el-cascader
                v-model="selectedAddress"
                :options="options"
                clearable
                placeholder="请选择收件地址"
                size="small"
                popper-class="address-com"
                style="width: 100%; line-height: 31px"
                @change="regionChange"
              >
              </el-cascader>
            </Col>
            <Col :span="8" style="padding-right: 20px">
              <Input v-model="formData.consignee_info.address.detail" placeholder="详细地址" clearable></Input>
            </Col>
            <Col :span="2">
              <a @click="saveAddress" style="line-height: 33px; text-align: center">保存</a>
            </Col>
          </Row>
          <!--   展示地址     -->
          <Row class-name="receive-pd" v-if="!isEditAddress">
            <Col :span="4" style="padding-left: 20px">
              {{ formData.consignee_info.consignee }}
            </Col>
            <Col :span="4">{{ formData.consignee_info.mobile }}</Col>
            <Col :span="14">
              <div class="flex">
                <div class="address-tag">常用</div>
                <div>{{ receiveAddress }}</div>
              </div>
            </Col>
            <Col :span="2">
              <a @click="isEditAddress = true">编辑</a>
            </Col>
          </Row>
        </div>
      </div>

      <div class="receive-title-header">采购明细</div>
      <Table
        :columns="tableCols"
        :row-class-name="rowClassName"
        class="tc-table-style"
        row-key="sku_code"
        :indent-size="100"
        :data="otherExpandList"
        size="small"
        style="margin-bottom: 10px"
        border
      >
        <template slot="index" slot-scope="{ row, index }" style="display: flex">
          <div v-if="row.isChild">-</div>
          <div v-else>{{ index + 1 }}</div>
        </template>
        <!--商品名-->
        <template slot="generic_name" slot-scope="{ row }">
          <div>
            <span v-if="row.item_type == 'TAOCAN'" class="tc-tag">套餐</span>
            <span>{{ row.generic_name }}</span>
            <span v-if="row.is_activity_product" class="activity-tag">特惠加购</span>
          </div>
        </template>

        <template slot="grand_desc" slot-scope="{ row, index }">
          <div v-if="row.is_taocan_goods == 1">-</div>
          <div v-else style="white-space: pre-wrap">{{ row.grand_desc || '-' }}</div>
        </template>

        <template slot="img" slot-scope="{ row }">
          <!--				<viewer v-if="row.img" :images="[row.img]" class="flex  flex-item-center" style="margin: 8px 0;">-->
          <!--					<img :src="row.img"-->
          <!--					     class="image" style="width: 60px;height: auto;max-height: 100px;">-->
          <!--				</viewer>-->
          <!--				<span v-else>-</span>-->
          <div style="max-height: 100px; height: 30px" class="flex flex-item-center">
            <viewer
              v-if="row.desc_imgs.length"
              :images="row.desc_imgs"
              class="flex flex-item-center"
              style="margin: 6px 0"
            >
              <img
                v-for="(img, index) in row.desc_imgs"
                v-show="index === 0"
                :key="img + index"
                :src="img"
                class="image"
                style="width: 30px; height: auto; max-height: 70px"
              />
            </viewer>
            <span style="vertical-align: middle" v-else>-</span>
          </div>
        </template>

        <!--规格-->
        <template slot="prod_spec" slot-scope="{ row }">
          <div v-if="row.item_type == 'TAOCAN'">-</div>
          <p v-else>{{ row.prod_spec || '-' }}</p>
        </template>

        <!-- 供应商 -->
        <template slot-scope="{ row, index }" slot="supplier_name">
          <div v-if="row.isChild">-</div>
          <p v-else>{{ row.supplier_name || '-' }}</p>
        </template>

        <!-- 生产厂家 -->
        <template slot="manufacturer" slot-scope="{ row }">
          {{ row.manufacturer || '-' }}
        </template>
        <!-- 产地 -->
        <template slot="prod_area" slot-scope="{ row }">
          {{ row.prod_area || '-' }}
        </template>

        <!-- 经销商 -->
        <template slot-scope="{ row, index }" slot="seller">
          <div v-if="row.isChild">-</div>
          <p v-else>{{ row.seller || '-' }}</p>
        </template>

        <!-- 采购单价 -->
        <template slot-scope="{ row, index }" slot="price">
          <div v-if="row.isChild">-</div>
          <p v-else v-text-format.number="row.price"></p>
        </template>

        <!-- 采购数量 -->
        <template slot-scope="{ row, index }" slot="num">
          <div v-if="row.isChild">x{{ row.num || '0' }}</div>
          <p v-else>{{ row.num || '0' }}</p>
        </template>
        <template #footer>
          <div class="table-footer">
            <div v-if="otherGoodsList.length > 4">
              <div class="expand" v-if="!isOtherExpand" @click="isOtherExpand = true">
                <span class="total">共{{ otherGoodsList.length }}种产品</span>展开订单
                <Icon type="ios-arrow-down" />
              </div>
              <div class="expand" v-else @click="isOtherExpand = false">
                <span class="total">共{{ otherGoodsList.length }}种产品</span>收起订单
                <Icon type="ios-arrow-up" />
              </div>
            </div>
            <div class="mr10">
              总额：<span style="color: red; font-size: 16px" v-text-format.number="otherTotalAmount"></span>
            </div>
          </div>
        </template>
      </Table>

      <div class="receive-title-header">订单备注</div>
      <Input
        style="min-width: 650px"
        :autosize="{ minRows: 3, maxRows: 6 }"
        v-model="formData.remark"
        type="textarea"
        placeholder="请输入备注"
        class="mb20"
      ></Input>
    </div>

    <div v-if="isDisplayHealth === '1'">
      <div class="receive-title-header"><span style="color: red; margin-right: 5px">*</span>采购单所属医疗事业部</div>
      <RadioGroup v-model="formData.is_belong_health">
        <Radio label="1">是</Radio>
        <Radio label="0">否</Radio>
      </RadioGroup>
    </div>

    <!-- 活动期间内 && 满足常繁活动价格 && 未追加   -->
    <div class="activity-tip" v-if="cf_activity?.is_activity_on === '1' && Number(cf_activity?.gift_num)">
      <div class="activity-note">
        本单已满{{ cf_activity?.order_money }}元，已为您自动加购
        <span class="activity-bold">{{ cf_activity?.gift_num }}份</span>「{{ cf_activity?.gift_name }}」，仅需<span
        class="activity-bold"
      >{{ cf_activity?.attach_pay }}元</span
      >
      </div>
    </div>

    <div class="block_30"></div>
    <div class="fixed-bottom-wrapper">
      <Button class="marginRight10" @click="back">返回</Button>
      <Button type="primary" @click="createOrder('goodPayForm')" :loading="createLoading">下单并提交审核</Button>
    </div>
    <select-receive-modal
      :visible.sync="receiveVisible"
      :defaultAddress="defaultAddress"
      :registerAddress="registerAddress"
      @onOk="confirmRegisterAddress"
    ></select-receive-modal>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { CodeToText, regionData, TextToCode } from '@/libs/chinaMap';
import renderHeader from '@/mixins/renderHeader';
import { isJiShanChannel } from '@/libs/runtime';
import { cloneDeep } from 'lodash-es';
import selectReceiveModal from './components/selectReceiveModal.vue';
import { $operator } from '@/libs/operation';
import S from '@/libs/util';

const init_form_data = {
  listof: {},
  tc_listof: {},
  yizhan_id: '',
  tcm_remark: '',
  remark: '',
  consignee_info: {
    consignee: '',
    mobile: '',
    address: {
      prov: {
        name: '',
        code: '',
      },
      city: {
        name: '',
        code: '',
      },
      county: {
        name: '',
        code: '',
      },
      detail: '',
    },
  },
};
export default {
  name: 'detail',
  components: { selectReceiveModal },
  mixins: [renderHeader],
  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };
    return {
      createLoading: false, //下单loading
      payVisible: false,
      single: true,
      selectedAddress: [],
      options: regionData,
      tableCols: [
        { title: '序号', slot: 'index', resizable: true, tree: true, width: 60, align: 'center' },
        { title: '图片', slot: 'img', resizable: true, align: 'center', width: 80 },
        { title: '商品名称', slot: 'generic_name', align: 'center', width: 100 },
        { title: '规格	', slot: 'prod_spec', align: 'center' },
        { title: '类型', key: 'prod_type_text', align: 'center', width: 80 },
        { title: '供应商', slot: 'supplier_name', align: 'center', minWidth: 80 },
        {
          title: '经销商',
          slot: 'seller',
          minWidth: 80,
          tooltip: true,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具'),
        },
        { title: '单位	', key: 'pack_unit', align: 'center', width: 80 },
        { title: '数量', slot: 'num', align: 'center', width: 80 },
        {
          title: '单价',
          slot: 'price',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '采购单价 = 货品金额 + 平台使用费'),
        },
      ],
      ruleValidate: {
        yizhan_id: [{ required: true, message: '请选择收货主体', trigger: 'change' }],
        is_belong_health: [{ required: true, message: '请选择采购单所属医疗事业部', trigger: 'change' }],
        'consignee_info.consignee': [{ required: true, message: '请填写收件人姓名', trigger: 'change' }],
        'consignee_info.mobile': [{ required: true, message: '请填写收件人电话号码', trigger: 'change' }],
        'consignee_info.address.city.code': [{ required: true, message: '请选择收件地址', trigger: 'change' }],
      },
      formData: cloneDeep(init_form_data),
      isDisplayHealth: '',
      payInfo: {
        balance: '',
        goods_num: '',
        goods_types: '',
        gunion_enabled_transfer: '',
        payment_fee: '',
        is_belong_health: '',
        autoPay: false,
      },
      return_select_goods: [],

      // 审核历史
      examineCols: [
        { title: '编号', key: 'sku_code', resizable: true },
        { title: '审核状态', key: 'sku_code', resizable: true },
        { title: '审核时间', key: 'sku_code', resizable: true },
        { title: '审核人', key: 'sku_code', resizable: true },
        { title: '审核内容', key: 'sku_code', resizable: true, minWidth: 300 },
      ],
      examineList: [],
      healthStationList: [],
      isJiShanChannel: isJiShanChannel(),
      isAddActivityProduct: false,
      cf_activity: {},
      receiveVisible: false,
      isEditAddress: false,
      presGoodsList: [], // 过滤中药饮片列表
      otherGoodsList: [], // 过滤非中药饮片列表
      procurement_tcm_status: '', // 是否使用过备案地址下过药 1:下过 2:没下过
      defaultAddress: {}, // 默认常用地址
      registerAddress: {
        mobile: '',
      }, // 备案地址
      oldRegisterAddress: {
        mobile: '',
      }, // 备案地址
      editMobileVisible: false,
      updatePhoneLoading: false,
      editMobileRules: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { required: true, validator: validateMobile, trigger: ['change', 'blur'] },
        ],
      },
      presTableCols: [
        { title: '序号', slot: 'index', tree: true, width: 60, align: 'center' },
        { title: '图片', slot: 'img', align: 'center', width: 80 },
        { title: '商品名称', slot: 'generic_name', resizable: true, align: 'center', width: 100 },
        { title: '规格	', slot: 'prod_spec', resizable: true, align: 'center', width: 100 },
        { title: '品级', slot: 'grand_desc', resizable: true, align: 'center', width: 55 },
        { title: '类型', key: 'prod_type_text', resizable: true, align: 'center', width: 90 },
        { title: '产地', slot: 'prod_area', width: 120, align: 'center' },
        { title: '厂家', slot: 'manufacturer', width: 100, align: 'center' },
        { title: '供应商', slot: 'supplier_name', resizable: true, minWidth: 50, align: 'center' },
        {
          title: '经销商',
          slot: 'seller',
          minWidth: 50,
          tooltip: true,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具'),
        },
        { title: '单位	', key: 'pack_unit', resizable: true, align: 'center' },
        {
          title: '单价',
          slot: 'price',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '采购单价 = 货品金额 + 平台使用费'),
          width: 100,
        },
        { title: '数量', slot: 'num', resizable: true, align: 'center' },
      ],
      isPresExpand: false,
      isOtherExpand: false,
    };
  },
  computed: {
    ...mapState('purchase', {
      // select_goods: state => state.selectGoodsList,
      covidListof: state => state.covidListof,
      ysHomologyListof: state => state.ysHomologyListof,
    }),
    ...mapGetters('purchase', { select_goods: 'selectGoodsList' }),

    // hasEnoughMoney(){
    // 	// !TODO 目前余额 后端未做校验  去除该校验(已完成)
    // 	return( Number(this.payInfo.balance) > Number(this.payInfo.payment_fee)|| this.payInfo.autoPay)
    // },
    // 内容是否只读
    // 如果采购订单在审核中,展示只读
    isDisabled() {
      return false;
    },
    receiveAddress() {
      let provName = this.formData.consignee_info.address.prov.name;
      let cityName = this.formData.consignee_info.address.city.name;
      let countyName = this.formData.consignee_info.address.county.name;
      let detail = this.formData.consignee_info.address.detail;
      return `${provName}${cityName}${countyName}${detail}`;
    },
    presExpandList() {
      return this.isPresExpand ? this.presGoodsList : this.presGoodsList.slice(0, 4);
    },
    otherExpandList() {
      return this.isOtherExpand ? this.otherGoodsList : this.otherGoodsList.slice(0, 4);
    },
    presTotalAmount() {
      return this.presGoodsList.reduce((prev, curr) => {
        return prev + $operator.multiply(curr.num, curr.price);
      }, 0);
    },
    otherTotalAmount() {
      return this.otherGoodsList.reduce((prev, curr) => {
        return prev + $operator.multiply(curr.num, curr.price);
      }, 0);
    },
  },
  watch: {},
  created() {
    this.$router.onReady(() => {
      if (this.$route.query.type == 'covid') {
        //  新冠
        this.purchaseConfirm(this.covidListof);
        return;
      }

      if (this.$route.query.type == 'ys-homology') {
        //  药食同源
        this.purchaseConfirm({ listof: this.ysHomologyListof, tc_listof: {} });
        return;
      }
      const listof = {};
      const tc_listof = {};
      this.select_goods.map(item => {
        if (item.is_taocan_goods == 1) {
          tc_listof[item.id] = item.quantity;
        } else {
          listof[item.id] = item.quantity;
        }
      });
      this.purchaseConfirm({ listof, tc_listof });
      if (this.isJiShanChannel) {
        this.getHealthStationList();
      }
    });
  },
  mounted() {},
  methods: {
    cancelEditMobile() {
      this.editMobileVisible = false;
      this.registerAddress.mobile = this.oldRegisterAddress?.mobile;
    },
    editMobile() {
      if (!this.editMobileVisible) {
        this.editMobileVisible = true;
        return;
      }
      this.$refs.mobileForm.validate(valid => {
        if (valid) {
          this.updatePhoneLoading = true;
          this.$api
            .updatecfmobile({
              mobile: this.registerAddress.mobile,
            })
            .then(() => {
              this.$set(this.formData.consignee_info, 'mobile', this.registerAddress.mobile);
              this.$set(this.oldRegisterAddress, 'mobile', this.registerAddress.mobile);
              this.$Message.success('修改成功');
            })
            .catch(() => {
              this.registerAddress.mobile = this.oldRegisterAddress?.mobile;
            })
            .finally(() => {
              this.editMobileVisible = false;
              this.updatePhoneLoading = false;
            });
        }
      });
    },
    // 给表格行设置样式
    rowClassName(row, index) {
      if (row.children && row.children.length) {
        return 'ivu-table-row-tree';
      }
      if (row.isChild) {
        return 'ivu-table-row-tree-child';
      }
    },

    createOrder(formName) {
      if (this.editMobileVisible) {
        this.$Message.error('请确认是否修改收货人手机号');
        return;
      }
      // 如果有药品并且未点击备案的收货地址确认，需拦截
      if (this.presGoodsList.length > 0 && this.procurement_tcm_status === '2') {
        this.$Message.error('请先选择收货地址');
        return;
      }
      // this.$refs[formName].validate(valid => {
      if (!this.formData.consignee_info.address.detail) {
        this.$Message.error('请完善收件地址信息');
        return;
      }
      if (this.isDisplayHealth === '1' && !this.formData.is_belong_health) {
        this.$Message.error('请选择采购单位所属医疗事业部');
        return;
      }
      // 三周年庆典 追加成功新增参数
      if (this.isAddActivityProduct) {
        this.formData.with_cf_gift = 1;
      }
      // if (valid) {
      this.createLoading = true;
      this.$api
        .createTwicePurchase(this.formData)
        .then(
          res => {
            this.$store.commit('purchase/CLEAR_GOODS_LIST');
            this.$router.replace('/purchase/order/list');
          },
          err => {
            {
            }
          }
        )
        .finally(() => (this.createLoading = false));
      // } else {
      //   // this.$Message.error('请先完善收货信息');
      // }
      // });
    },

    // 采购常繁商品时，需判断是否达到常繁设置的包邮价格

    // confirmPayment(){
    // 	this.$api.confirmPayment(this.formData).then(res=>{
    // 		this.$store.commit('purchase/CLEAR_GOODS_LIST')
    // 		this.$Message.success('支付成功')
    // 		this.payVisible = false
    // 		this.$router.replace('/purchase/stock/list')
    // 	},err=>{})
    // },
    // confirmPay(name) {
    // 	this.$refs[name].validate(valid=>{
    // 		if(!this.formData.consignee_info.address.detail){
    // 			this.$Message.error('请完善收件地址信息')
    // 			return
    // 		}
    // 		if(valid){
    // 			this.payVisible = true
    // 		}else {
    // 			this.$Message.error('请先完善收货信息')
    // 		}
    // 	})
    // },

    // 返回
    back() {
      let path = this.$route.query.type == 'ys-homology' ? '/purchase/stock/ys-homology' : '/purchase/stock/list';
      this.$router.replace(path);
    },

    purchaseConfirm({listof, tc_listof}) {
      console.log('-> %c listof  ===    %o', 'font-size: 15px;color: #fa8c16 ;', listof);
      let params = {
        listof,
        tc_listof,
      };
      this.formData.listof = listof;
      this.formData.tc_listof = tc_listof;
      this.$api.getPurchaseConfirmInfo(params).then(
        res => {
          this.return_select_goods = res.list;
          this.return_select_goods.forEach(item => {
            if (item.item_type == 'TAOCAN') {
              item.tc_skus?.forEach(c_item => (c_item.isChild = true));
              item.children = item.tc_skus;
              item._showChildren = false;
            }
          });
          // 中药饮片:2 ，中药饮片（非处方）:16，中成药:3、贵细药材:15
          this.presGoodsList = this.return_select_goods.filter(item => {
            return (
              item.prod_type === '2' || item.prod_type === '3' || item.prod_type === '15' || item.prod_type === '16'
            );
          });
          this.otherGoodsList = this.return_select_goods.filter(item => {
            return (
              item.prod_type !== '2' && item.prod_type !== '3' && item.prod_type !== '15' && item.prod_type !== '16'
            );
          });
          console.log('return_select_goods', this.return_select_goods);

          this.payInfo.balance = res.balance;
          this.payInfo.goods_num = res.goods_num;
          this.payInfo.goods_types = res.goods_types;
          this.payInfo.payment_fee = res.payment_fee;
          this.payInfo.autoPay = !!res?.auto_pay;
          this.isDisplayHealth = res?.is_display_health || '0';
          this.procurement_tcm_status = res.procurement_tcm_status || '2';
          this.defaultAddress = res.consignee_info;
          this.registerAddress = res.register_address;
          this.oldRegisterAddress = cloneDeep(res?.register_address || {});
          // 拼接备案地址
          this.registerAddress.address_text = !S.isEmptyObject(res.register_address?.address)
            ? `${res.register_address?.address?.prov?.name || ''}${res.register_address?.address?.city?.name || ''}${
              res.register_address?.address?.county?.name || ''
            }${res.register_address?.address?.detail || ''}`
            : '';
          // 非己善渠道诊所继续回显收货信息，己善渠道诊所需优先选择收货主体
          if (!this.isJiShanChannel) {
            this.formData.consignee_info = res.consignee_info;
            // if (!res.consignee_info?.address_text) {
            this.formData.consignee_info.address_text = `${res.consignee_info?.address?.prov?.name}${res.consignee_info?.address?.county?.name}${res.consignee_info?.address?.city?.name}${res.consignee_info?.address?.detail}`;
            // }
            let provName = res.consignee_info.address.prov.name;
            let cityName = res.consignee_info.address.city.name;
            let countyName = res.consignee_info.address.county.name;
            let provCode = res.consignee_info.address.prov.code || TextToCode[provName].code;
            let cityCode = res.consignee_info.address.city.code || TextToCode[provName][cityName].code;
            let countyCode = res.consignee_info.address.county.code || TextToCode[provName][cityName][countyName].code;
            this.selectedAddress = [provCode, cityCode, countyCode];
            this.regionChange(this.selectedAddress);
          }

          // 三周年活动
          this.cf_activity = res.cf_activity;
          if (this.cf_activity?.is_activity_on === '1' && Number(this.cf_activity?.gift_num)) {
            this.activityAddPurchase();
          }
        },
        err => {
          {
          }
        }
      );
    },
    regionChange(address) {
      if (address.length) {
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.consignee_info.address.prov = prov;
        this.formData.consignee_info.address.city = city;
        this.formData.consignee_info.address.county = county;
      } else {
        this.formData.consignee_info.address = {
          prov: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          detail: '',
        };
      }
    },

    getHealthStationList() {
      this.$api
        .getHealthStationList()
        .then(res => {
          this.healthStationList = res.list;
          console.log('=>(detail.vue:242) res', res);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    selectYiZhan(val) {
      if (val) {
        let currentSelect = this.healthStationList.find(item => item.id === val);
        console.log('=>(billingDetail.vue:477) currentSelect', currentSelect);
        this.formData.consignee_info.consignee = currentSelect.consignee_info.consignee;
        this.formData.consignee_info.mobile = currentSelect.consignee_info.mobile;
        this.formData.consignee_info.address.detail = currentSelect.consignee_info.other;
        let provName = currentSelect.consignee_info.province;
        let cityName = currentSelect.consignee_info.city;
        let countyName = currentSelect.consignee_info.county;
        let provCode = TextToCode[provName]?.code;
        let cityCode = TextToCode[provName][cityName]?.code;
        let countyCode = TextToCode[provName][cityName][countyName]?.code;
        this.selectedAddress = [provCode, cityCode, countyCode];
        console.log('=>(billingDetail.vue:484) this.selectedAddress', this.selectedAddress);
        this.regionChange(this.selectedAddress);
      } else {
        this.formData.consignee_info.consignee = '';
        this.formData.consignee_info.mobile = '';
        this.formData.consignee_info.address.detail = '';
        this.selectedAddress = [];
        this.regionChange(this.selectedAddress);
      }
    },

    activityAddPurchase() {
      const listof = {};
      const tc_listof = {};
      this.select_goods.map(item => {
        if (item.is_taocan_goods == 1) {
          tc_listof[item.id] = item.quantity;
        } else {
          listof[item.id] = item.quantity;
        }
      });

      this.$api.confirmCFActivity({listof, tc_listof}).then(res => {
        let activityProduct = this.cf_activity?.gift_sku;
        activityProduct.is_activity_product = '1';
        this.return_select_goods.push(activityProduct);
        this.isAddActivityProduct = true;
      });
    },

    showReceive() {
      this.receiveVisible = true;
      this.addRegisterAddressTime();
    },
    saveAddress() {
      // if()
      this.isEditAddress = false;
      this.formData.consignee_info.address_text = this.receiveAddress;
    },
    confirmRegisterAddress(val) {
      this.procurement_tcm_status = val;
    },
    addRegisterAddressTime() {
      let params = {};
      this.$api
        .addRegisterAddressTime(params)
        .then(res => {
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
  },
};
</script>

<style lang="less" scoped>
.addressInput {
  flex: 1;
}

.goods-price {
  justify-content: flex-end;
  align-items: flex-end;

  .goods-item {
    margin-top: 4px;
    text-align: right;
  }

  .label {
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .money {
    font-size: 16px;
    font-weight: 600;
  }
}

.modal-title {
  text-align: center;
  margin-bottom: 15px;
  margin-top: -30px;
  font-size: 16px;
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

::v-deep .ivu-modal-header {
  border-bottom: none;
}

.red {
  color: red;
}

.activity-tag {
  display: inline-block;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
  background: #d63232;
  border-radius: 3px;
  border: 1px solid #d63232;
  padding: 1px 3px;
  min-width: 45px;
  height: 23px;
  line-height: 20px;
  text-align: center;
  margin-left: 10px;
}

.activity-tip {
  display: flex;
  align-items: center;

  .activity-note {
    padding: 7px 10px;
    background: #fce9e8;
    border-radius: 2px;
    border: 1px solid #f3a8a2;
    margin-right: 10px;
    color: #e6372b;
    font-size: 14px;

    .activity-bold {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.receive-title-header {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 12px;
  color: #333333;
  margin-bottom: 13px;

  .line {
    width: 2px;
    height: 14px;
    background: #175bd4;
    margin-right: 10px;
  }
}

.receive-button {
  ::v-deep .ivu-icon {
    vertical-align: baseline;
  }
}

.address-tag {
  background-color: #155bd5;
  //width: 30px;
  height: 17px;
  min-width: 30px;
  max-width: 100px;
  color: #ffffff;
  text-align: center;
  border-radius: 2px;
  margin-right: 6px;
}

.receive-header {
  background-color: #f2f2f2;
}

.receive-content {
  width: 800px;
}

.receive-pd {
  padding: 11px 0;
}

.table-footer {
  position: relative;
  display: flex;
  justify-content: flex-end;

  .expand {
    position: absolute;
    top: 0;
    left: 50%;
    cursor: pointer;
    color: #155bd4;
    transform: translateX(-50%);

    .total {
      //color: red;
      margin-right: 10px;
    }
  }
}

.interval-block {
  width: calc(~'100% + 32px');
  height: 10px;
  margin-left: -16px;
  background-color: #f2f2f2;
  box-sizing: border-box;
  margin-bottom: 20px;
}

::v-deep .ivu-table-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .tc-table-style .ivu-table-cell-tree {
  margin-left: -20px;
  margin-right: 5px;
}

.editMobile :deep(.ivu-form-item) {
  margin-bottom: 0;
}

:deep(.el-edit-mobile.el-button--text) {
  font-size: 12px;
  color: #115bd4;
}

:deep(.el-edit-mobile.el-button--text:hover) {
  color: rgba(17, 91, 212, 0.7);
}
</style>
<style lang="less">
p {
  margin: 0px;
}
</style>

