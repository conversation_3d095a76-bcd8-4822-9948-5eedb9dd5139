<template>
  <div>
    <Modal :value="value" title="申请售后" width="980px" :mask-closable="false" @on-visible-change="changeVisible">
      <div class="modal-content">
        <Form ref="formData" :label-width="80" :label-colon="true" :model="formData">
          <!-- v-if="!getBackMap.includes(detail.type)" -->
          <FormItem label="售后类型" required>
            <RadioGroup v-model="formData.refund_type" @on-change="typeChange">
              <Radio label="M" v-if="!getBackMap.includes(detail.type)">仅退款</Radio>
              <Radio label="GM" :disabled="isAllBack">退货退款</Radio>
            </RadioGroup>
          </FormItem>

          <FormItem label="收货情况" required v-if="formData.refund_type == 'GM'">
            <RadioGroup v-model="formData.delivery_way">
              <Radio label="HAS">已收到货</Radio>
              <Radio label="NOT">未收到货</Radio>
            </RadioGroup>
          </FormItem>

          <FormItem label="售后商品明细" required :label-width="100">
            <div class="flex flex-item-end">
              <Select
                transfer-class-name="after-sale-select"
                v-model="formData.goods"
                style="width: 200px"
                v-if="!isAllBack"
                :placeholder="moneyHeader.get(formData.refund_type)"
                @on-change="changeClear"
                :clearable="true"
                ref="resetSelect"
              >
                <Option
                  v-for="(item, index) in showOptionList"
                  :key="item.goods_sku_id + index"
                  :value="item.is_taocan == 1 ? item.goods_id : item.goods_sku_id"
                  :label="item.generic_name"
                >
                  <div class="flex flex-item-align">
                    <span class="tc-tag" style="margin-right: 2px" v-if="item.is_taocan == 1">套餐</span>
                    <div style="width: 170px">{{ item.generic_name }}</div>
                  </div>
                </Option>
              </Select>
            </div>
          </FormItem>

          <Table
            :row-class-name="rowClassName"
            row-key="sku_code"
            :columns="goodsColumns"
            :data="showGoodList"
            :loading="goodsLoading"
            :show-summary="true"
            :summary-method="handleSummary"
            class="ml21 tc-table-style"
          >
            <template slot-scope="{ row, index }" slot="addNum">
              <span v-if="row.isChild">{{ dynamicCalcQuantity(row) }}</span>
              <Input-Number
                v-else
                :min="1"
                :max="getMaxNum(index)"
                v-model="showGoodList[index].addNum"
                placeholder="请输入数量"
                :precision="0"
                @on-change="numberChange($event, index)"
                style="width: 64px"
                :active-change="false"
                :disabled="isAllBack"
              />
            </template>

            <!-- 状态 -->
            <template slot-scope="{ row, index }" slot="shipped_status">
              {{ row.shipped_status || '-' }}
            </template>

            <template slot-scope="{ row, index }" slot="shipped_wait_num">
              <div>
                <span v-if="row.isChild == 1">{{ dynamicCalcWaitNum(row) }}</span>
                <span v-else>{{ getMaxNum(index) }}</span>
              </div>
            </template>

            <template slot-scope="{ row, index }" slot="clinic_price">
              <div>
                {{ getPurPrice(row) }}
              </div>
            </template>

            <template slot-scope="{ row, index }" slot="priceNum">
              <div>
                <span v-if="row.isChild">-</span>
                <span v-else>¥{{ row.priceNum || 0 }}</span>
              </div>
            </template>

            <template slot-scope="{ row, index }" slot="setting">
              <div>
                <!-- <Button type="text">删除</Button> -->
                <a v-if="!isAllBack && !row.isChild" style="color: red" @click="delCol(index)">删除</a>
                <span v-else>-</span>
              </div>
            </template>
          </Table>

          <FormItem label="诊所退款金额" :label-width="104">
            <span>{{ allPrice ? `￥${allPrice}` : '￥0.00' }}</span>
            <!-- {{ allPrice }} -->
          </FormItem>
          <FormItem label="售后原因" required>
            <Select v-model="formData.refund_reason">
              <Option v-for="item in reasonOptions" :key="item.kw" :value="item.kw" :label="item.desc"></Option>
            </Select>
          </FormItem>

          <FormItem label="售后说明">
            <Input
              v-model="formData.refund_desc"
              type="textarea"
              :maxlength="200"
              :show-word-limit="true"
              :autosize="{ minRows: 3, maxRows: 5 }"
              placeholder="请说明诊所的退款原因"
            ></Input>
          </FormItem>

          <FormItem label="附件">
            <MaterialPicture v-model="formData.imgs" :limit="9" />
            <div>支持上传照片，单张图片大小不超过3.0M，最多9张</div>
          </FormItem>
        </Form>
      </div>

      <div slot="footer" class="edit-footer">
        <Button type="default" @click="closeModal">取消</Button>
        <Button type="primary" :loading="confirmLoading" @click="confirm">确定</Button>
      </div>
    </Modal>

    <!--  提示框  -->
    <k-tip-modal v-model="tipVisible" text="售后申请提交成功，请等待审核"></k-tip-modal>
  </div>
</template>

<script>
import kTipModal from '@/components/k-tip-modal';
import { cloneDeep  } from 'lodash-es';
import { $operator } from '@/libs/operation';

export default {
  name: 'apply-after-sale',
  components: {
    kTipModal,
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false,
    },
    detail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tipVisible: false,
      formData: {
        // type: "",
        // receipts: "",
        // return_money: "",
        // reason: "",
        // remark: "",
        // images: []
      },
      getBackMap: ['WAIT_SHIP', 'COM_W_PAY'],
      companyMap: ['WAIT'],
      // 售后商品明细
      goodsColumns: [
        { title: '商品编号', key: 'sku_code', align: 'center', tree: true },
        { title: '商品', key: 'generic_name', align: 'center' },
        { title: '单位', key: 'pack_unit', align: 'center' },
        { title: '状态', slot: 'shipped_status', align: 'center' },
        { title: '采购单价', slot: 'clinic_price', align: 'center' },
        {
          // title: moneyMap.get(this.formData.refund_type),
          title: '可退数量',
          slot: 'shipped_wait_num',
          key: 'shipped_wait_num',
          align: 'center',
        },
        // {
        //   title: "可退货数量",
        //   slot: "return_number",
        //   key: "return_number",
        //   align: "center"
        // },
        { title: '退款数量', key: 'addNum', slot: 'addNum', align: 'center' },
        {
          title: '诊所退款金额',
          slot: 'priceNum',
          align: 'center',
          width: 100,
        },
        { title: '操作', key: 'setting', slot: 'setting', align: 'center' },
      ],
      goodsList: [],
      showGoodList: [],
      optionList: [],
      showOptionList: [],
      goodsLoading: false,
      isAllBack: false,
      reasonOptions: [],
      allPrice: null,
      confirmLoading: false,
      moneyHeader: new Map([
        ['M', '添加未发货商品'],
        ['GM', '添加已发货商品'],
      ]),
    };
  },
  computed: {
    // 动态计算套餐商品的退款数量
    dynamicCalcQuantity() {
      return row => {
        let addNum = this.showGoodList[row.parentIndex].addNum;
        return $operator.multiply(Number(row.taocan_buy_times), Number(addNum));
      };
    },
    // 动态计算套餐商品的可退数量
    dynamicCalcWaitNum() {
      return row => {
        let can_apply_num = this.showGoodList[row.parentIndex]['can_apply_num_info'][this.formData['refund_type']];
        return $operator.multiply(Number(row.taocan_buy_times), Number(can_apply_num));
      };
    },
    getMaxNum() {
      return function (index) {
        console.log('-> index', index);
        return Number(this.showGoodList[index]['can_apply_num_info'][this.formData['refund_type']]);
      };
    },
    getPurPrice() {
      return function ({ clinic_price }) {
        return `￥${$operator.toPrecision(clinic_price, 2)}`;
      };
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 给表格行设置样式
    rowClassName(row, index) {
      console.log('-> row, index', row, index);
      if (row.children && row.children.length) {
        return 'ivu-table-row-tree';
      }
      if (row.isChild) {
        return 'ivu-table-row-tree-child';
      }
    },

    // TODO 表单相关
    initData() {
      this.$api
        .getOrderFundgoods({
          order_code: this.detail.order_code,
        })
        .then(res => {
          // this.goodsList = res.list;
          this.optionList = res.list.map((item, index) => {
            if (item.is_taocan == 1) {
              item.attrs.forEach(attrs_item => {
                attrs_item.isChild = true;
                attrs_item.parentIndex = index;
              });
              return {
                ...item,
                children: item.attrs,
                _showChildren: false,
              };
            }
            return item;
          });
          console.log('-> optionList', this.optionList);
          this.showOptionList = res.list;
          // this.showGoodList = res.list;

          this.formatForm();
          // this.formData.return_money = res.data.return_money;
        })
        .catch(err => {
          console.log(err);
          {
          }
        });

      this.$api
        .getOrderFundoptions({
          order_code: this.detail.order_code,
        })
        .then(res => {
          console.log(res, 'ssss');
          let keys = res.refundReasonDesc;
          let arr = Object.keys(res.refundReasonDesc)?.map(v => ({
            desc: keys[v].desc,
            kw: keys[v].kw,
          }));
          console.log(arr, 'this is arr');
          this.reasonOptions = arr;
          // this.goodsList = res.list;
          // this.optionList = res.list;
          // this.formData.return_money = res.data.return_money;
        })
        .catch(err => {
          console.log(err);
          {
          }
        });
    },
    formatForm() {
      let refund_type = 'M';
      if (this.getBackMap.includes(this.detail.type)) {
        refund_type = 'GM';
      }
      // 针对省公司待付款状态的处理
      this.formData = {
        refund_type,
        delivery_way: 'HAS',
      };
      console.log(this.formData, 'this.formData----------');
      if (this.companyMap.includes(this.detail.com_pay_status)) {
        this.isAllBack = true;
        console.log(refund_type, 'refund_type');
        this.showGoodList = this.getOptionList(refund_type);
        this.getNumPrice(true);
      }
      // this.goodsColumns[5].title = moneyMap.get(refund_type);
      // console.log(this.getOptionList("GM"), "arrrrrrrrrrrrr");
      this.showOptionList = this.getOptionList(refund_type);
    },
    /*
     * @description: 初始化表单数据
     * @param {Object} data 表单数据
     * @param {Array} keys 需要清空的字段
     * @return: void
     * ps:什么都不传就是清空表单
     */
    formDataInit(data, keys = []) {
      if (!data) {
        this.formData = {};
      }
      this.formData = {
        ...this.formData,
        ...data,
      };
      keys.length &&
        keys.forEach(key => {
          this.formData[key] = '';
        });
    },
    typeChange(val) {
      this.formDataInit({ refund_goods: [], goods: '' });
      this.showOptionList = this.getOptionList(val);
      this.showGoodList = [];
      this.getNumPrice();
      // this.goodsColumns[5].title = moneyMap.get(this.formData.refund_type);
    },
    getOptionList(key) {
      let data = cloneDeep(this.optionList);
      return data?.filter(item => Number(item.can_apply_num_info[key]) > 0) || [];
    },
    changeClear(value) {
      this.$refs.resetSelect.clearSingleSelect();

      const item =
        this.showOptionList?.find(v => {
          console.log('-> v', v, value);
          if (v.is_taocan == 1) {
            return v.goods_id === value;
          } else {
            return v.goods_sku_id === value;
          }
        }) || null;

      let exist = this.showGoodList.findIndex(item => {
        if (item.is_taocan == 1) {
          return item.goods_id === value;
        } else {
          return item.goods_sku_id == value;
        }
      });
      if (exist > -1) {
        this.$Message.error('该商品已添加');
        return;
      }
      if (item) {
        this.showGoodList.push(item);
        this.getNumPrice();
      }
    },

    //TODO 表格相关
    // 合计
    handleSummary({ columns, data }) {
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计',
          };
          return;
        }
        let whiteList = [6, 7];
        let docList = [7];
        if (whiteList.includes(index)) {
          const values = data.map(item => Number(item[key]));
          if (!values.every(value => isNaN(value))) {
            const v = values.reduce((prev, curr) => {
              const value = Number(curr).toFixed(2);
              if (!isNaN(value)) {
                return $operator.add(prev, curr);
              } else {
                return prev;
              }
            }, 0);
            console.log(v, 'vvvvv');
            if (docList.includes(index)) {
              sums[key] = {
                key,
                value: `￥${v}`,
              };
            } else {
              sums[key] = {
                key,
                value: v,
              };
            }
          }
        } else {
          sums[key] = {
            key,
            value: '',
          };
        }
      });

      return sums;
    },
    addLogistics() {
      this.formData.return_logistics.push({ express_delivery: '', code: '' });
    },
    deleteLogistics(index) {
      this.formData.return_logistics.splice(index, 1);
    },
    // todo 商品数量改变，对应合计发生变化
    numberChange(e, index) {
      console.log(this.showGoodList);
      this.getNumPrice();
      // this.add_goods[index].num = e
      // this.caclTotalPrice( index )
    },
    delCol(index) {
      console.log(234);
      this.showGoodList.splice(index, 1);
      this.getNumPrice();
    },
    getNumPrice(isAllnum) {
      console.log('addwadw', isAllnum);
      let newArr = this.showGoodList.length ? this.showGoodList : [];
      let nums = 0;
      newArr.forEach(item => {
        let num = null;
        if (!item.hasOwnProperty('addNum')) {
          this.$set(item, 'addNum', null);
        }
        console.log('item.priceNum');
        item.priceNum = $operator.multiply(Number(item.addNum), Number(item.clinic_price));
        if (isAllnum) {
          item.addNum = Number(item['can_apply_num_info'][this.formData['refund_type']]);
        }
        num = $operator.multiply(Number(item.addNum), Number(item.clinic_price));
        item?.priceNum || this.$set(item, 'priceNum', num);
        nums = $operator.add(Number(nums), Number(item.priceNum));
      });
      console.log('-> newArr', newArr);
      this.allPrice = nums.toFixed(2);
      this.showGoodList = newArr;
    },

    // TODO 提交相关
    // 校验
    validateFormData() {
      if (!this.formData.refund_type) {
        this.$Message.error('请选择收货类型');
        return false;
      }
      if (this.formData.refund_type == 'GM' && !this.formData.delivery_way) {
        this.$Message.error('请选择收货情况');
        return false;
      }
      if (!this.showGoodList.length) {
        this.$Message.error('请选择售后商品');
        return false;
      }

      if (!this.showGoodList.every(v => Number(v.addNum) > 0)) {
        this.$Message.error('商品退款数量不能为空');
        return false;
      }

      if (!this.formData.refund_reason) {
        this.$Message.error('请选择售后原因');
        return false;
      }
      // if (!this.goodsList.length) {
      //   this.$Message.error("请选择售后商品");
      //   return false;
      // }
      return true;
    },
    confirm() {
      if (this.validateFormData()) {
        // 提交数据和表单数据做拆分，无强关联
        const subData = this.getSubmitData(this.formData);
        console.log(subData);
        this.confirmLoading = true;

        this.$api
          .getOrderFundsubmit({ ...subData })
          .then(res => {
            console.log(res);
            this.confirmLoading = false;
            if (res) {
              this.closeModal();
              // this.$Message.success("提交成功");
              this.tipVisible = true;
              this.$emit('successFunc');
            }
          })
          .catch(err => {
            console.log(err);
            {
            }
          })
          .finally(() => (this.confirmLoading = false));
      }
    },

    /*
     * @Description: 设置提交数据
     * return: {Object} 提交数据
     */
    getSubmitData(formData) {
      let subData = cloneDeep(formData);
      subData.refund_goods = [];
      this.showGoodList.map(item => {
        if (item.is_taocan == 1) {
          item.attrs.map(attrs_item => {
            subData.refund_goods.push({
              cli_attr_id: attrs_item.id,
              num: $operator.multiply(Number(attrs_item.taocan_buy_times), Number(item.addNum)),
              refund_money: $operator.multiply(Number(attrs_item.clinic_price), Number(item.addNum)),
            });
          });
        } else {
          subData.refund_goods.push({
            cli_attr_id: item.id,
            num: item.addNum,
            refund_money: item.priceNum,
          });
        }
      });

      if (subData.refund_type === 'M') {
        delete subData.delivery_way;
      }

      subData.order_code = this.detail.order_code;

      delete subData.goods;

      return subData;
    },

    // TODO Model相关
    changeVisible(visible) {
      console.log(cloneDeep(this.detail), 'detail');
      this.showGoodList = [];
      this.formDataInit();
      this.allPrice = 0;
      // console.log(this.showGoodList, "showGoodList");
      visible && this.initData();
      !visible && this.closeModal();
    },
    // 关闭弹窗
    closeModal() {
      this.$emit('input', false);
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  max-height: 600px;
  min-height: 400px;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  .ivu-form-item {
    margin-bottom: 10px;
  }
}

.ml21 {
  margin-left: 21px;
}

.after-sale-select {
  .ivu-select-item {
    max-width: 200px;
    white-space: normal !important;
    word-break: break-all;
  }
}
</style>
