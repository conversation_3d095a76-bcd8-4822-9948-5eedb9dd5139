<template>
  <div class="detail-wrapper">
    <div class="block-header"><span>订单详情</span></div>
    <div class="basic-info">
      <div class="basic-info-item">
        <span class="item-label">采购单号</span>
        <span class="item-content">{{ echoData.order_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">交易号</span>
        <span class="item-content">{{ echoData.trade_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单类型</span>
        <span class="item-content">{{ echoData.type_text || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单状态</span>
        <span class="item-content" style="text-indent: 0; margin-left: 30px"
          >{{ echoData.status_text || '-'
          }}<span v-if="echoData.reject_reason" class="reject-reason"
            >( 驳回原因：{{ echoData.reject_reason }} )</span
          ></span
        >
      </div>

      <div class="basic-info-item">
        <span class="item-label">诊所编号</span>
        <span class="item-content">{{ echoData.clinic_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购诊所</span>
        <span class="item-content">{{ echoData.clinic_name || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">所属企业名称</span>
        <span class="item-content">{{ echoData.organization_name || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">统一社会信用代码</span>
        <span class="item-content">{{ echoData.organization_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">诊所负责人</span>
        <span class="item-content">{{ echoData.leading_person || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">负责人电话</span>
        <span class="item-content">{{ echoData.leading_mobile || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单总件数</span>
        <span class="item-content">{{ echoData.total_num || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单生成时间</span>
        <span class="item-content">{{ echoData.create_time || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单支付时间</span>
        <span class="item-content">{{ echoData.paid_at || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单支付金额</span>
        <span class="item-content">￥{{ echoData.total_amount || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单支付方式</span>
        <span class="item-content">{{ echoData.pay_type_text || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">货款金额</span>
        <span class="item-content">
          <span v-if="echoData.goods_fee">￥{{ echoData.goods_fee }}</span>
          <span v-else></span>
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">平台服务费</span>
        <span class="item-content">
          <span v-if="echoData.service_fee">￥{{ echoData.service_fee }}</span>
          <span v-else></span>
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">收件人姓名</span>
        <span class="item-content">{{ echoData.consignee }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">收件人手机号</span>
        <span class="item-content">{{ echoData.mobile }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">收货地址</span>
        <span class="item-content">{{ echoData.address_text }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">诊所入库状态</span>
        <span class="item-content">{{ echoData.store_status_text }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">签收状态</span>
        <span class="item-content">{{ echoData.sign_status_desc || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">订单备注</span>
        <span class="item-content">{{ echoData.remark || '-' }}</span>
      </div>

      <!-- <div class="basic-info-item">
        <span class="item-label">入库状态</span>
        <span class="item-content">{{ echoData.store_status_text }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">支付方式</span>
        <span class="item-content">{{ echoData.pay_type_text }}</span>
      </div>


      <div class="basic-info-item">
        <span class="item-label">订单金额</span>
        <span class="item-content">￥{{ echoData.total_amount }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">货款金额</span>
        <span class="item-content"></span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">平台服务费</span>
        <span class="item-content">￥{{ echoData.service_fee }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">下单时间</span>
        <span class="item-content">{{ echoData.create_time }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">收件人信息</span>
        <span class="item-content">{{ echoData.consignee }} / {{ echoData.mobile }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">收件地址</span>
        <span class="item-content">{{ echoData.address_text }}</span>
      </div> -->
    </div>
    <div class="goods-list">
      <div class="goods-list-title custom-sticky" style="top: 56px">
        <div class="panel-nav flex flex-item-between">
          <div ref="tabList">
            <a
              class="nav"
              :class="{ active: activeGoodsType == type_item.key }"
              v-for="(type_item, type_index) in getShowTypeList"
              :key="type_index + 'type'"
              @click="goodsTabChange(type_item.key)"
              >{{ type_item.desc }}</a
            >
          </div>
        </div>
        <div>
          <Button
            class="ml10"
            type="primary"
            @click="confirmPass"
            v-if="echoData.can_sign === '1' && activeGoodsType === 'express_list'"
            >全部确认收货
          </Button>
          <Button
            v-if="Number(orderDetail.can_refund_apply) && !isJiShanChannel"
            type="default"
            @click="applyAfterSale"
            class="ml10"
            >申请售后</Button
          >
          <Button type="default" v-if="invoice_status === 'NORMAL'" class="ml10" @click="applyInvoicing"
            >申请开票
          </Button>
          <Button type="default" v-if="invoice_status === 'PROCESSING'" class="ml10" disabled>开票处理中</Button>
          <Button type="default" v-if="invoice_status === 'FINISHED'" class="ml10" @click="applyInvoicing"
            >发票明细
          </Button>
          <Button type="default" v-if="invoice_status === 'REJECTED'" class="ml10" @click="applyInvoicing"
            >开票驳回
          </Button>
          <Button
            type="primary"
            class="ml10"
            @click="shipment"
            v-if="orderStatus === 'PART_SHIP' || orderStatus === 'HAS_SHIP'"
            :disabled="Number(echoData.can_put_stock) !== 1"
            >采购入库
          </Button>
        </div>
      </div>
      <div class="table-wrapper">
        <Table
          :row-class-name="rowClassName"
          class="tc-table-style sticky-table-head"
          row-key="sku_code"
          :columns="tableColumns"
          v-if="activeGoodsType == 'goodsList'"
          :data="orderGoodsList"
          :loading="tableLoading"
        >
          <!-- 名称 -->
          <template slot="generic_name" slot-scope="{ row }">
            <span v-if="row.is_taocan == 1"> <span class="tc-tag">套餐</span> </span>{{ row.generic_name }}
          </template>
          <template slot-scope="{ row, index }" slot="img">
            <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
              <p v-if="row.img" class="flex flex-item-center">
                <img style="width: 60px; height: auto; margin: 10px 0" :src="row.img | imageStyle" class="image" />
              </p>
            </viewer>
            <span v-else>-</span>
          </template>
          <!-- 品级 -->
          <template slot="grand_desc" slot-scope="{ row, index }">
            <div v-if="row.is_taocan == 1">-</div>
            <div v-else style="white-space: pre-wrap">{{ row.grand_desc || '-' }}</div>
          </template>
          <!-- 规格 -->
          <template slot="prod_spec" slot-scope="{ row }">
            <span v-if="row.is_taocan == 1">-</span>
            <span v-else>{{ row.prod_spec || '-' }}</span>
          </template>

          <!-- 供应商 -->
          <template slot="supplier_name" slot-scope="{ row }">
            <span v-if="row.isChild">-</span>
            <span v-else>{{ row.supplier_name || '-' }}</span>
          </template>
          <!-- 生产厂家 -->
          <template slot="manufacturer" slot-scope="{ row }">
            {{ row.manufacturer || '-' }}
          </template>
          <!-- 产地 -->
          <template slot="prod_area" slot-scope="{ row }">
            {{ row.prod_area || '-' }}
          </template>

          <!-- 销售商 -->
          <template slot="seller" slot-scope="{ row }">
            <span v-if="row.isChild">-</span>
            <span v-else>{{ row.seller || '-' }}</span>
          </template>

          <!-- 采购数量 -->
          <template slot="num" slot-scope="{ row }">
            <span v-if="row.isChild">x{{ row.num || '0' }}</span>
            <span v-else>{{ row.num || '0' }}</span>
          </template>

          <!-- 采购总价 -->
          <template slot="payment_fee" slot-scope="{ row }">
            <span v-if="row.isChild">-</span>
            <span v-else>￥{{ row.payment_fee || '-' }}</span>
          </template>

          <!-- 入库状态 -->
          <template slot="store_status" slot-scope="{ row }">
            <span v-if="row.is_taocan === '1'">
              <span v-if="row.isChild">{{ row.store_status || '-' }}</span>
              <span v-else>-</span>
            </span>
            <span v-else>{{ row.store_status || '-' }}</span>
          </template>

          <!-- 已入数量 -->
          <template slot="stored_num" slot-scope="{ row }">
            <span v-if="row.is_taocan === '1'">
              <span v-if="row.isChild">{{ row.stored_num || '-' }}</span>
              <span v-else>-</span>
            </span>
            <span v-else>{{ row.stored_num || '-' }}</span>
          </template>

          <!-- 审核数量 -->
          <template slot="stocking_num" slot-scope="{ row }">
            <span v-if="row.is_taocan === '1'">
              <span v-if="row.isChild">{{ row.stocking_num || '-' }}</span>
              <span v-else>-</span>
            </span>
            <span v-else>{{ row.stocking_num || '-' }}</span>
          </template>

          <!-- 待入数量 -->
          <template slot="can_store_num" slot-scope="{ row }">
            <span v-if="row.is_taocan === '1'">
              <span v-if="row.isChild">{{ row.can_store_num || '-' }}</span>
              <span v-else>-</span>
            </span>
            <span v-else>{{ row.can_store_num || '-' }}</span>
          </template>

          <!-- <template slot="batch_code" slot-scope="{row}">
            <span>{{ row.batch_code || '-' }}</span>
          </template>
          <template slot="prod_date" slot-scope="{row}">
            <span>{{ row.prod_date | data_format("YYYY-MM-DD") }}</span>
          </template>
          <template slot="expire_date" slot-scope="{row}">
            <span>{{ row.expire_date | data_format("YYYY-MM-DD") }}</span>
          </template> -->
        </Table>
        <Table
          :columns="afterSaleColumns"
          v-if="activeGoodsType == 'afterSalesDetails'"
          :data="afterSaleList"
          stripe
          border
          class="tc-table-style sticky-table-head"
        >
          <template slot-scope="{ row }" slot="out_goods">
            {{ row.list.length ? rowMuty(row.list) : '-' }}
          </template>
          <template slot-scope="{ row }" slot="cli_receive_money">
            {{ row.cli_receive_money ? `¥${row.cli_receive_money}` : '-' }}
          </template>
          <template slot-scope="{ row }" slot="time">
            {{ row.create_time | data_format }}
          </template>
          <!-- 售后说明 -->
          <template slot-scope="{ row }" slot="refund_desc">
            {{ row.refund_desc ? row.refund_desc : '-' }}
          </template>
          <!-- 售后说明 -->
          <template slot-scope="{ row }" slot="refund_reason">
            {{ row.refund_reason ? row.refund_reason : '-' }}
          </template>
          <template slot-scope="{ row }" slot="action">
            <!-- <a @click="jump(row)">详情</a> -->
            <KLink
              :to="{
                path: '/purchase/after-sale/online-detail',
                query: {
                  status: row.status,
                  apply_code: row.apply_code,
                },
              }"
              target="_blank"
              >详情
            </KLink>
          </template>
        </Table>
        <Table
          :columns="examineCols"
          v-if="ostocks.length && activeGoodsType === 'ostocks'"
          :data="ostocks"
          stripe
          class="tc-table-style sticky-table-head"
        >
          <template slot-scope="{ row, index }" slot="audit_time">
            {{ row.audit_time || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="operator">
            {{ row.operator || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="reason">
            {{ row.reason || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <div v-if="row.status === '70' || row.status === '10'">
              <!--            <a v-if="row.status === '70' " @click="editExamin(row.code)">修改入库</a>-->
              <a class="margin-left10" @click="cancelExamin(row.code)">取消入库</a>
            </div>
            <span v-else>-</span>
          </template>
        </Table>
        <Table
          :columns="expressCols"
          v-if="express_list.length && activeGoodsType === 'express_list'"
          :data="express_list"
          border
          :span-method="handleSpan"
          class="tc-table-style sticky-table-head"
        >
          <template slot="express_no" slot-scope="{ row }">
            <div>
              <div>
                {{ row.express_name || '-' }}
                {{ row.express_no }}
              </div>
            </div>
          </template>

          <template slot-scope="{ row }" slot="action">
            <a v-if="row.normal_express === '1'" @click="toLogisticsDetail(row)" style="margin-right: 10px">查看物流</a>
            <span v-else>-</span>
            <!-- <a @click="confirmExpress(row)" v-if="echoData.can_sign === '1' && row.is_sign !== '1'">确认收货</a> -->
            <Button
              type="primary"
              size="small"
              @click="confirmExpress(row)"
              v-if="echoData.can_sign === '1' && row.can_sign === '1'"
              >确认收货</Button
            >
          </template>
        </Table>
      </div>
    </div>
    <div style="margin-top: 20px" v-if="orderStatus === 'WAIT_PAY' || orderStatus === 'REJECTED'">
      <!--    收货信息-->
      <div class="block-header"><span>收货信息</span></div>
      <div class="receipt-info">
        <Form ref="goodPayForm" :label-width="110" :model="formData" :rules="ruleValidate" label-colon>
          <div>
            <Row>
              <Col span="8">
                <FormItem label="收件人" prop="consignee_info.consignee">
                  <Input v-model="formData.consignee_info.consignee" placeholder="请输入收件人姓名"></Input>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="手机号" prop="consignee_info.mobile">
                  <Input v-model="formData.consignee_info.mobile" placeholder="请输入收件人手机号"></Input>
                </FormItem>
              </Col>
              <Col span="16">
                <FormItem label="收件地址" prop="consignee_info.address.city.code">
                  <div class="flex">
                    <div class="address" style="width: 55%">
                      <el-cascader
                        v-model="selectedAddress"
                        :options="options"
                        clearable
                        placeholder="请选择收件地址"
                        size="small"
                        popper-class="address-com"
                        style="width: 100%"
                        @change="regionChange"
                      >
                      </el-cascader>
                    </div>
                    <div class="addressInput ml10">
                      <Input v-model="formData.consignee_info.address.detail" placeholder="详细地址"></Input>
                    </div>
                  </div>
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    </div>

    <div class="pay-type" v-if="orderStatus === 'WAIT_PAY'">
      <div class="block-header"><span>支付方式</span></div>
      <div class="pay-type" style="margin-left: 40px">
        <div class="flex">
          <RadioGroup v-model="formData.pay_type" vertical>
            <Radio label="GUNION" :disabled="payInfo.allow_yzt === '1'"
              >银联钱包
              <span style="line-height: 18px"> （当前余额：{{ payInfo.balance }}）</span>
            </Radio>
            <Radio label="YZT" v-if="payInfo.allow_yzt === '1'"
              >云直通钱包
              <span style="line-height: 18px"> （当前余额：{{ payInfo.yzt_balance }}）</span>
            </Radio>
          </RadioGroup>
        </div>
        <div class="goods-price" style="margin-left: 4px; margin-top: 10px">
          <div>
            <div class="goods-item flex">
              <span class="label">采购商品种类：</span>
              <span class="value">{{ payInfo.goods_types }}</span>
            </div>
            <div class="goods-item flex">
              <span class="label">采购商品总数：</span>
              <span>{{ payInfo.goods_num }}</span>
            </div>
            <div class="goods-item flex">
              <span class="label">待支付总额：</span>
              <span class="money">￥{{ payInfo.payment_fee }}</span>
            </div>
          </div>
        </div>
      </div>
      <div>
        <Row>
          <Col span="8" class="flex flex-item-align" style="margin-top: 20px">
            <p style="text-align: right; width: 80px">备注：</p>
            <p style="width: 100%">
              <Input v-model="formData.remark" placeholder="请添加服装类商品尺码标注或其他补充信息"></Input>
            </p>
          </Col>
        </Row>
      </div>
    </div>
    <!--    {-->
    <!--    "10": {-->
    <!--    "desc": "待审核",-->
    <!--    "kw": "STATUS_WAIT"-->
    <!--    },-->
    <!--    "20": {-->
    <!--    "desc": "审核通过",-->
    <!--    "kw": "STATUS_PASS"-->
    <!--    },-->
    <!--    "70": {-->
    <!--    "desc": "审核未通过",-->
    <!--    "kw": "STATUS_NOT_PASS"-->
    <!--    },-->
    <!--    "80": {-->
    <!--    "desc": "已撤销",-->
    <!--    "kw": "STATUS_REVOKE"-->
    <!--    }-->
    <!--    }-->
    <!-- todo 入库审核详情 -->
    <!--    <div class="table-wrapper" v-if="ostocks.length && activeGoodsType === 'ostocks'">-->
    <!--      &lt;!&ndash; <h3 class="f-title margin-bottom10">入库审核详情</h3> &ndash;&gt;-->
    <!--      <Table :columns="examineCols" :data="ostocks" stripe class="tc-table-style sticky-table-head">-->
    <!--        <template slot-scope="{ row, index }" slot="audit_time">-->
    <!--          {{ row.audit_time || '-' }}-->
    <!--        </template>-->
    <!--        <template slot-scope="{ row, index }" slot="operator">-->
    <!--          {{ row.operator || '-' }}-->
    <!--        </template>-->
    <!--        <template slot-scope="{ row, index }" slot="reason">-->
    <!--          {{ row.reason || '-' }}-->
    <!--        </template>-->
    <!--        <template slot-scope="{ row, index }" slot="action">-->
    <!--          <div v-if="row.status === '70' || row.status === '10'">-->
    <!--            &lt;!&ndash;            <a v-if="row.status === '70' " @click="editExamin(row.code)">修改入库</a>&ndash;&gt;-->
    <!--            <a class="margin-left10" @click="cancelExamin(row.code)">取消入库</a>-->
    <!--          </div>-->
    <!--          <span v-else>-</span>-->
    <!--        </template>-->
    <!--      </Table>-->
    <!--    </div>-->
    <!-- todo   物流信息-->
    <div class="table-wrapper" v-if="express_list.length && activeGoodsType === 'express_list'">
      <!-- <h3 class="f-title">物流信息</h3> -->
      <!-- <div class="flex flex-warp"> -->
      <!--   <div v-for="(item, index) in express_list" :key="'express_no' + index" class="logistics-info-box"> -->
      <!--     <div class="flex flex-item-between"> -->
      <!--       <h4 class="title"> -->
      <!--         {{ `包裹${index + 1}：${item.express_name}` }} -->
      <!--       </h4> -->
      <!--       <Button type="default" size="small" class="radius-btn" @click="toLogisticsDetail(item)">包裹详情</Button> -->
      <!--     </div> -->
      <!--     <div class=""> -->
      <!--       <span class="logistics-label">单号：</span> -->
      <!--       <span class="logistics-content">{{ item.express_no || '-' }}</span> -->
      <!--     </div> -->
      <!--     <div> -->
      <!--       <span class="logistics-label">时间：</span> -->
      <!--       <span class="logistics-content">{{ item.time }}</span> -->
      <!--     </div> -->
      <!--     <div class="info-item flex"> -->
      <!--       <span class="logistics-label min36" v-if="item.remark">备注：</span> -->
      <!--       <span class="logistics-content wrap" v-if="item.remark">{{ item.remark }}</span> -->
      <!--     </div> -->
      <!--   </div> -->
      <!-- </div> -->

      <!--      <Table :columns="expressCols" :data="express_list" border :span-method="handleSpan" class="tc-table-style sticky-table-head">-->
      <!--        <template slot="express_no" slot-scope="{ row }">-->
      <!--          <div>-->
      <!--            <div>-->
      <!--              {{ row.express_name || '-' }}-->
      <!--              {{ row.express_no }}-->
      <!--            </div>-->
      <!--          </div>-->
      <!--        </template>-->

      <!--        <template slot-scope="{ row }" slot="action">-->
      <!--          <a v-if="row.normal_express === '1'" @click="toLogisticsDetail(row)" style="margin-right: 10px">查看物流</a>-->
      <!--          <span v-else>-</span>-->
      <!--          &lt;!&ndash; <a @click="confirmExpress(row)" v-if="echoData.can_sign === '1' && row.is_sign !== '1'">确认收货</a> &ndash;&gt;-->
      <!--          <Button-->
      <!--            type="primary"-->
      <!--            size="small"-->
      <!--            @click="confirmExpress(row)"-->
      <!--            v-if="echoData.can_sign === '1' && row.can_sign === '1'"-->
      <!--            >确认收货</Button-->
      <!--          >-->
      <!--        </template>-->
      <!--      </Table>-->
    </div>
    <!-- 操作记录 -->
    <div class="logistics-information" v-if="audit_records.length">
      <h3 class="f-title margin-bottom10">操作记录</h3>
      <Table :columns="supExamineCols" :data="audit_records" stripe border>
        <template slot-scope="{ row, index }" slot="content">
          {{ row.content || '-' }}
        </template>
        <template slot-scope="{ row, index }" slot="operator_source">
          {{ row.operator_source || '-' }}
        </template>
        <template slot-scope="{ row, index }" slot="operator">
          {{ row.operator_name || '-' }}
          <span v-if="row.operator_role_name">({{ row.operator_role_name }})</span>
        </template>
        <template slot-scope="{ row }" slot="create_time">
          {{ row.create_time | data_format }}
        </template>
      </Table>
    </div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Poptip
        confirm
        title="是否要取消采购?"
        @on-ok="cancelPurchase"
        v-if="
          purchase_type === 'CLI_PUR' &&
          (orderStatus === 'WAIT_AUDIT' || orderStatus === 'REJECTED' || orderStatus === 'WAIT_PAY')
        "
      >
        <Button style="margin-left: 10px" :loading="cancelPurLoading">取消采购</Button>
      </Poptip>

      <Button
        v-if="orderStatus === 'REJECTED'"
        style="margin-left: 10px"
        type="primary"
        :loading="editLoading"
        @click="editOrder"
      >
        <div style="font-size: 12px; line-height: 1.1; display: flex; flex-direction: column; justify-content: center">
          <span>修改采购单</span>
          <span>{{ getTimeRemain('audit') }}</span>
        </div>
      </Button>
      <Button
        v-if="orderStatus === 'WAIT_PAY'"
        style="margin-left: 10px"
        type="primary"
        :loading="editLoading"
        @click="confirmPay('goodPayForm')"
      >
        <div style="font-size: 12px; line-height: 1.1; display: flex; flex-direction: column; justify-content: center">
          <span>余额确认抵扣 </span>
          <span>{{ getTimeRemain() }}</span>
        </div>
      </Button>
      <Button
        type="primary"
        @click="RejoinToCart"
        v-if="allow_repurchase === '1' && (orderStatus === 'FINISHED' || orderStatus === 'CLOSED')"
        style="margin-left: 12px"
      >
        重新加入采购单
      </Button>
      <!--      <Button v-if="orderStatus === 'PART_SHIP'||orderStatus === 'WAIT_SHIP'" style="margin-left:20px;" type="primary"-->
      <!--              @click="shipment">发货-->
      <!--      </Button>-->
      <!--      <Button v-if="" style="marginLeft:20px;" type="primary"-->
      <!--              @click="shipment">发货-->
      <!--      </Button>-->
    </div>
    <Modal v-model="shipmentVisible" :mask-closable="false" title="采购入库单" transfer width="80">
      <div slot="footer" class="modal-foot">
        <Button @click="closeModal('shipmentVisible', false)">取消</Button>
        <!-- <Button type="primary" @click="submitForm('shipForm')" >确认入库</Button> -->
        <Button type="primary" :loading="confirmStoreLoading" @click="confirmSubmit">确认入库</Button>
      </div>
      <div class="purchase-modal-wrapper">
        <div class="table-wrapper">
          <Table :columns="tableColumns_order" :data="wareHouseList">
            <template slot="batch_code" slot-scope="{ row }">
              <span>{{ row.batch_code || '-' }}</span>
            </template>
            <template slot="prod_date" slot-scope="{ row }">
              <span>{{ row.prod_date || '-' }}</span>
            </template>

            <template slot="prod_spec" slot-scope="{ row }">
              <span>{{ row.prod_spec || '-' }}</span>
            </template>

            <template slot="expire_date" slot-scope="{ row }">
              <span>{{ row.expire_date || '-' }}</span>
            </template>
            <template slot="current_num" slot-scope="{ row, index }">
              <InputNumber
                :min="0"
                :max="Number(wareHouseList[index].can_store_num)"
                :disabled="Number(wareHouseList[index].can_store_num) === 0"
                :precision="0"
                style="width: 100px"
                :value="Number(wareHouseList[index].current_num)"
                placeholder="数量"
                @on-change="e => changeNum(e, index)"
              />
            </template>
          </Table>
        </div>
        <div class="tips">
          <p>待入库数量=已发货数量-已入库数量</p>
          <p>商品发生退货退款售后，不在此处扣减；建议在订单做全部入库，后续选择系统或手工出库</p>
        </div>
      </div>
    </Modal>
    <Modal
      v-model="payVisible"
      :mask-closable="false"
      class-name="vertical-center-modal pay-pur-modal"
      title=" "
      width="340px"
    >
      <div v-if="hasEnoughMoney">
        <h3 class="modal-title">是否确认用余额抵扣</h3>
        <p>确认用余额抵扣后，待付金额将自动从钱包余额中扣减。</p>
      </div>
      <div v-else>
        <h3 class="modal-title">当前钱包余额不足</h3>
        <p>请先向{{ payInfo.allow_yzt === '1' ? '云直通' : '银联' }}钱包账户充值。确保余额充足再进行购买订单</p>
      </div>
      <div slot="footer">
        <div v-if="hasEnoughMoney">
          <Button @click="payVisible = false">我再想想</Button>
          <Button type="primary" :loading="payLoading" @click="confirmPayment">确认支付</Button>
        </div>
        <Button v-else type="primary" @click="payVisible = false"> 我知道了</Button>
      </div>
    </Modal>
    <!-- 修改采购单 -->
    <edit-order
      :cancelPurchase="cancelPurchase"
      :visible.sync="editOrderVisible"
      :list="editGoodsList"
      :editOrderInfo="editOrderInfo"
      :consignee_info="formData.consignee_info"
    ></edit-order>

    <!-- 申请开票 -->
    <applyInvoicModal
      v-model="applyVisible"
      :order_code="order_code"
      :status="invoice_status"
      @success="getOrderDetailInfo"
    ></applyInvoicModal>

    <!-- 发票信息未完善提示 -->
    <invoice-info-tip
      v-model="invoiceInfoVisible"
      :tips-content="invoiceTipsContent"
      :tipsType="invoiceTipsType"
    ></invoice-info-tip>

    <!-- 物流详情 -->
    <!-- <KLogisticsDetail v-model="logisticsVisible" :logisticsData="logisticsObj"></KLogisticsDetail> -->
    <kLogisticsProgress
      v-model="logisticsVisible"
      :is-logistics-detail="false"
      :express_detail="progress_express_detail"
    />
    <cancel-modal v-model="cancelVisible" @ok="cancelPurchase"></cancel-modal>
    <!--    重新下单确认弹窗-->
    <!--    <confirmModal :confirmVisible.sync="confirmVisible" confirmText="合并到当前采购清单" cancelText="体会当前购物清单" content="系统检测到您当前购物列表已有商品，请选择合并到当前购物列表或者替换当前购物列表"></confirmModal>-->
    <!--  申请售后  -->
    <apply-after-sale
      v-model="applyAfterSaleVisible"
      :detail="orderDetail"
      @successFunc="setTagFunc"
    ></apply-after-sale>
    <tips-modal
      v-model="commonTipsVisible"
      :show-cancel="false"
      confirm-text="好的"
      contentText="检测到订单有关联的退货退款售后单，入库完成后请注意做商品出库更新库存"
    ></tips-modal>
    <k-tip-modal v-model="tipVisible" :text="placeHol" :showIcon="showIcon"></k-tip-modal>
  </div>
</template>

<script>
import io from 'libs/io'; // Http request
import S from 'libs/util'; // Some commonly used tools
import editOrder from './components/edit-order';
import { CodeToText, regionData, TextToCode } from '@/libs/chinaMap';
import KLogisticsDetail from '@/components/k-logistics-detail';
import kLogisticsProgress from '@/components/k-logistics-progress/k-logistics-progress';
import CancelModal from './components/cancelModal';
import applyInvoicModal from './components/apply-invoic-modal';
import renderHeader from '@/mixins/renderHeader';
import applyAfterSale from './components/apply-after-sale.vue';
import kTipModal from '@/components/k-tip-modal';
import { cloneDeep  } from 'lodash-es';
import TipsModal from '@/components/confirmModal/TipsModal.vue';
import { $operator } from '@/libs/operation';
import { data_format } from 'libs/filters';
import { isJiShanChannel } from '@/libs/runtime';
import Vue from 'vue';
import invoiceInfoTip from '@/components/InvoiceModal/invoice-info-tip.vue';

const init_order_form_data = {
  remark: '',
  pay_type: '',
  consignee_info: {
    consignee: '',
    mobile: '',
    address: {
      prov: {
        name: '',
        code: '',
      },
      city: {
        name: '',
        code: '',
      },
      county: {
        name: '',
        code: '',
      },
      detail: '',
    },
  },
};
export default {
  name: 'detail',
  components: {
    invoiceInfoTip,
    editOrder,
    // KLogisticsDetail, // 物流包裹详情
    kLogisticsProgress,
    applyInvoicModal, // 发票相关
    CancelModal,
    applyAfterSale,
    kTipModal,
    TipsModal,
    // confirmModal: () => import('_c/confirmModal/confirmModal'),
  },
  mixins: [renderHeader],
  data() {
    const init_form_data = {
      ship_type: 'SHIP',
      pur_code: '',
      clinic_name: '',
    };
    return {
      confirmVisible: false, //重新下单确认弹窗
      logisticsVisible: false, // 物流弹窗详情
      logisticsObj: {
        pack_index: '',
        order_code: '',
      }, // 物流相关数据
      /* 发票数据 */
      applyVisible: false, // 申请发票
      invoice_status: '',
      invoiceInfoVisible: false,
      invoiceTipsContent: '',
      invoiceTipsType: '',

      payVisible: false, //支付弹窗
      cancelPurLoading: false, //取消采购
      single: true, //是否余额支付
      payInfo: {
        balance: '',
        goods_num: '',
        goods_types: '',
        gunion_enabled_transfer: '',
        payment_fee: '',
        autoPay: '',
        allow_yzt: '',
        yzt_balance: '',
      },
      ruleValidate: {
        'consignee_info.consignee': [{ required: true, message: '请填写收件人姓名', trigger: 'blur' }],
        'consignee_info.mobile': [{ required: true, message: '请填写收件人电话号码', trigger: 'blur' }],
        'consignee_info.address.city.code': [{ required: true, message: '请选择收件地址', trigger: 'blur' }],
      },
      formData: {
        ...init_order_form_data,
      },
      orderStatus: this.$route.query.status,
      express_list: [], // 物流清单
      shipmentVisible: false,
      submitFormData: {
        ...init_form_data,
      },
      options: regionData,
      selectedAddress: [],
      echoData: {
        order_code: '', // 采购单号
        trade_code: '', // 交易号
        status_text: '', // 采购单状态
        store_status: '', // 入库状态
        pay_type_text: '', // 支付方式
        clinic_code: '', // 诊所编号
        clinic_name: '', // 下单诊所
        organization_name: '',
        organization_code: '',
        creator: '', // 下单人
        leading_person: '', // 诊所负责人
        leading_mobile: '', // 负责人电话
        total_amount: '', // 订单金额
        total_num: '', // 订单金额
        goods_fee: '', // 货款金额
        service_fee: '', // 服务费
        create_time: '', // 下单时间
        address_text: '', //收件地址
        consignee: '', //收件人
        mobile: '', //收件人电话
        remark: '', // 备注,
        finished_at: '',
        paid_at: '', //支付时间
        is_cf_supplier: '1', // 是否是常繁采购订单，非常繁的采购单可以重新加入采购单，为了初始化不出现按钮的闪烁，默认不先显示按钮
        can_sign: '',
      }, // 基础信息
      tableColumns: [
        { title: '编号', key: 'sku_code', align: 'center', tree: true, width: 100 },
        { title: '商品名', slot: 'generic_name', align: 'center', width: 100 },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center', width: 80 },
        { title: '类型', key: 'prod_type_desc', align: 'center', width: 100 },
        { title: '品级', slot: 'grand_desc', align: 'center', width: 100 },
        { title: '规格', slot: 'prod_spec', align: 'center', minWidth: 100 },
        { title: '单位', key: 'pack_unit', align: 'center', width: 100 },
        { title: '产地', slot: 'prod_area', width: 100 },
        { title: '生产厂家', slot: 'manufacturer', width: 100 },
        { title: '供应商', slot: 'supplier_name', align: 'center', width: 100 },
        {
          title: '销售商',
          slot: 'seller',
          align: 'center',
          minWidth: 100,
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具'),
        },
        // {title: '批号', slot: 'batch_code', align: 'center'},
        // {title: '生产日期', slot: 'prod_date', align: 'center'},
        // {title: '过期时间', slot: 'expire_date', align: 'center'},
        { title: '采购数量', slot: 'num', align: 'center', width: 100 },
        {
          title: '已发货数量',
          key: 'shipped_num',
          align: 'center',
          minWidth: 100,
        },
        { title: '采购总价', slot: 'payment_fee', align: 'center', width: 100 },
        {
          title: '取消数量',
          key: 'cancel_num',
          align: 'center',
          minWidth: 100,
          renderHeader: (h, params) => this._renderHeader(h, params, '商品已完成仅退款售后的数量'),
        },
        { title: '入库状态', slot: 'store_status', align: 'center', width: 100 },
        { title: '已入数量', slot: 'stored_num', align: 'center', width: 100 },
        { title: '审核数量', slot: 'stocking_num', align: 'center', width: 100 },
        { title: '待入数量', slot: 'can_store_num', align: 'center', width: 100 },
      ],
      orderGoodsList: [], // 采购商品数据
      wareHouseList: [], // 通过新接口获取的入库数据列表
      confirmStoreLoading: false, // 确认入库loading
      tableLoading: false,
      tableColumns_order: [],
      tableColumns_order_need: [
        { title: '编号', key: 'sku_code', align: 'center' },
        { title: '商品名', key: 'generic_name', align: 'center' },
        { title: '类型', key: 'prod_type_desc', align: 'center' },
        { title: '规格', slot: 'prod_spec', align: 'center' },
        { title: '单位', key: 'pack_unit', align: 'center' },
        { title: '批号', slot: 'batch_code', align: 'center' },
        { title: '生产日期', slot: 'prod_date', align: 'center' },
        { title: '过期时间', slot: 'expire_date', align: 'center' },
        { title: '采购数量', key: 'purchase_num', align: 'center' },
        {
          title: '取消数量',
          key: 'cancel_num',
          align: 'center',
          minWidth: 40,
          renderHeader: (h, params) => this._renderHeader(h, params, '商品已完成仅退款售后的数量'),
        },
        {
          title: '已发货数量',
          key: 'shipped_num',
          align: 'center',
          minWidth: 10,
        },
        { title: '金额', key: 'shipped_price', align: 'center' },
        { title: '已入库数量', key: 'stored_num', align: 'center' },
        {
          title: '待入库数量',
          slot: 'current_num',
          align: 'center',
          width: 110,
        },
      ],
      tableColumns_order_noNeed: [
        { title: '编号', key: 'sku_code', align: 'center' },
        { title: '商品名', key: 'generic_name', align: 'center' },
        { title: '类型', key: 'prod_type_desc', align: 'center' },
        { title: '规格', slot: 'prod_spec', align: 'center' },
        { title: '单位', key: 'pack_unit', align: 'center' },
        { title: '采购数量', key: 'purchase_num', align: 'center' },
        {
          title: '取消数量',
          key: 'cancel_num',
          align: 'center',
          minWidth: 40,
          renderHeader: (h, params) => this._renderHeader(h, params, '商品已完成仅退款售后的数量'),
        },
        {
          title: '已发货数量',
          key: 'shipped_num',
          align: 'center',
          minWidth: 10,
        },
        { title: '金额', key: 'shipped_price', align: 'center' },
        { title: '已入库数量', key: 'stored_num', align: 'center' },
        {
          title: '待入库数量',
          slot: 'current_num',
          align: 'center',
          width: 110,
        },
      ],

      // 入库审核列表
      examineCols: [
        { title: '入库单号', key: 'code', align: 'center' },
        { title: '商品', key: 'goods_names', align: 'center' },
        { title: '商品种类', key: 'kind_num', align: 'center' },
        { title: '申请入库数', key: 'num', align: 'center' },
        { title: '审核状态', key: 'status_text', align: 'center' },
        { title: '审核意见', slot: 'reason', align: 'center' },
        { title: '审核人', slot: 'operator', align: 'center' },
        { title: '审核时间', slot: 'audit_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      expressCols: [
        {
          title: '发货单号',
          key: 'ship_code',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '此处发货单号指流转到ERP销售订单的平台单号'),
        },
        {
          title: '商品',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.goods_text || '-'),
        },
        {
          title: '物流单号',
          slot: 'express_no',
          align: 'center',
          // render: (h, { row }) => h('span', {}, row.express_no || '-')
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
        },
        {
          title: '签收状态',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.is_sign === '1' ? '已签收' : '未签收' || '-'),
        },
        {
          title: '签收时间',
          align: 'center',
          render: (h, { row }) => h('span', {}, data_format(row.sign_time) || '-'),
          // render: (h, { row }) => h('span', {}, row.express_no || '-')
        },
        {
          title: '签收方式',
          key: 'code',
          align: 'center',
          renderHeader: (h, params) =>
            h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItem: 'center',
                },
              },
              [
                h(
                  'Tooltip',
                  {
                    props: {
                      theme: 'dark',
                      placement: 'top',
                    },
                  },
                  [
                    h('Icon', {
                      props: {
                        type: 'md-help-circle',
                      },
                      style: {
                        cursor: 'pointer',
                        fontSize: '16px',
                        marginRight: '4px',
                      },
                    }),
                    h(
                      'div',
                      {
                        slot: 'content',
                        class: 'text-inner',
                        style: {
                          maxWidth: '500px',
                        },
                      },
                      [
                        h('div', {}, '物流签收：物流快递单签收。'),
                        h('div', {}, '手动签收：点击“确认收货”按钮，完成签收。'),
                        h('div', {}, '入库签收：手动完成入库操作，系统自动签收。'),
                        h('div', {}, '超时签收：商品出库后7天，系统自动签收。'),
                        h('div', {}, '无需签收：当商品的发货方式为自提或无需发货时，无需签收。'),
                      ]
                    ),
                  ]
                ),
                params.column.title,
              ]
            ),
          render: (h, { row }) => h('span', {}, row.sign_type_desc || '-'),
        },
        {
          title: '物流回传/录入时间',
          key: 'code',
          align: 'center',
          render: (h, { row }) => h('span', {}, data_format(row.ship_time) || '-'),
        },
      ],
      // 供应商下单审核详情
      supExamineCols: [
        // { title: "编号", key: "id", align: "center" },
        {
          title: '时间',
          slot: 'create_time',
          key: 'create_time',
          align: 'center',
        },
        { title: '操作主体', slot: 'operator_source', align: 'center' },
        { title: '操作人', slot: 'operator', align: 'center' },
        { title: '操作记录', slot: 'content', align: 'center', minWidth: 200 },
      ],
      // 修改采购单
      editOrderVisible: false, // 修改采购单弹窗是否显示
      ostocks: [], //入库审核列表
      audit_records: [], //供应商审核列表
      audit_remain: '', //剩余修改时间
      pay_remain: '', //剩余支付时间
      timer: null,
      order_code: this.$route.query.order_code,
      editOrderInfo: {
        //编辑信息
        total_num: 0,
        total_money: 0,
        recommit_enable: '1', //是否可以重新提交
        goods_status_change: '',
      },
      editGoodsList: [], //编辑商品列表
      editLoading: false,
      editOstock: false, //是否是编辑入库
      reJoinOrderGoodsList: [], //重新提交商品列表
      purchase_type: '',
      cancelVisible: false,

      // 申请售后
      applyAfterSaleVisible: false,
      activeGoodsType: 'goodsList',
      afterSaleColumns: [
        { title: '售后单号', key: 'apply_code', align: 'center' },
        { title: '售后类型', key: 'refund_type_text', align: 'center' },
        { title: '退款商品', slot: 'out_goods', align: 'center' },
        { title: '退款金额', slot: 'cli_receive_money', align: 'center' },
        // {
        //   title: '换货情况',
        //   key: 'change_state_desc',
        //   align: 'center',
        //   renderHeader: (h, params) => this._renderHeader(h, params, '系统暂不支持换货功能，这里仅起到标记作用。')
        // },
        { title: '售后原因', slot: 'refund_reason', align: 'center' },
        { title: '售后说明', slot: 'refund_desc', align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '申请时间', slot: 'time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      afterSaleList: [],
      orderDetail: {},
      logList: [],
      typeList: [
        { desc: '商品清单', key: 'goodsList', sort: 0 },
        { desc: '售后明细', key: 'afterSalesDetails', sort: 1 },
        { desc: '入库详情', key: 'ostocks', sort: 2 },
        { desc: '包裹物流', key: 'express_list', sort: 3 },
      ],
      showTypeList: [],
      tipVisible: false,
      commonTipsVisible: false,
      remindRelation: false, //是否提醒关联
      placeHol: '',
      showIcon: false,
      progress_express_detail: [],
      isJiShanChannel: isJiShanChannel(),
      payLoading: false,
      allow_repurchase: '',
    };
  },
  computed: {
    hasEnoughMoney() {
      // !TODO 目前余额 后端未做校验  去除该校验(已完成)
      if (this.formData.pay_type === 'GUNION') {
        return Number(this.payInfo.balance) >= Number(this.payInfo.payment_fee) || this.payInfo.autoPay;
      }
      if (this.formData.pay_type === 'YZT') {
        return Number(this.payInfo.yzt_balance) >= Number(this.payInfo.payment_fee);
      }
    },
    formatDate() {
      return (date, formatType = 'YYYY.MM.DD HH:mm') => {
        if (date) {
          return S.moment(date * 1000).format(formatType);
        }
      };
    },
    rowMuty() {
      return function (list) {
        let str = '';
        list.forEach((v, i) => {
          if (i !== list.length - 1) {
            str += `${v.goods_name}*${v.num}，`;
          } else {
            str += `${v.goods_name}*${v.num}`;
          }
        });

        return str;
      };
    },
    getShowTypeList() {
      if (this.showTypeList.length) {
        return this.showTypeList.sort((a, b) => a.sort - b.sort);
      }
      return [];
    },
  },
  watch: {
    // 当弹窗关闭，入库数据重置为0
    shipmentVisible(val) {
      if (!val) {
        this.resetCurrentNum();
      }
    },
  },
  created() {
    // 获取采购详情
    this.getOrderDetailInfo();
    this.getApplyTableList();
  },
  mounted() {
    // if (this.$route.query.openType === 'receipt') {
    //   this.goodsTabChange(this.typeList[3].key);
    // }
    // this.goodsTabChange(this.typeList[3].key);
  },
  methods: {
    // 给表格行设置样式
    rowClassName(row, index) {
      console.log('-> row, index', row, index);
      if (row.children && row.children.length) {
        return 'ivu-table-row-tree';
      }
      if (row.isChild) {
        return 'ivu-table-row-tree-child';
      }
    },

    goodsTabChange(type) {
      this.activeGoodsType = type;
    },
    // 申请售后
    applyAfterSale() {
      if (this.orderDetail.can_refund_apply_num === '0') {
        this.placeHol = '当前订单中没有商品可用于发起售后';
        this.showIcon = true;
        this.tipVisible = true;
        return;
      }
      if (this.orderDetail.can_refund_apply_tip === '1') {
        // this.$Message.error("该订单状态不可申请售后");
        this.placeHol = '暂不支持线上发起售后';
        this.showIcon = false;
        this.tipVisible = true;
        return;
      }
      this.applyAfterSaleVisible = true;
    },
    getApplyTableList() {
      this.$api.getApplyTableList({ order_code: this.order_code }).then(res => {
        console.log(res, 'resssss');
        this.afterSaleList = res.list;
        if (res.list.length) {
          this.setShowTypeList(this.typeList[0]);
          this.setShowTypeList(this.typeList[1]);
        } else {
          this.setShowTypeList(this.typeList[0]);
        }
      });
    },
    RejoinToCart() {
      const ids = [];
      const taocan_goods_ids = [];
      this.reJoinOrderGoodsList.map(item => {
        if (item.is_taocan == 1) {
          taocan_goods_ids.push(item.goods_id);
        } else {
          ids.push(item.goods_sku_id);
        }
      });
      let params = {
        ids,
        taocan_goods_ids,
        order_code: this.$route.query.order_code,
      };
      this.$api.confirmGoodsStatus(params).then(
        res => {
          console.log('=>(detail.vue:1318) this.reJoinOrderGoodsList', this.reJoinOrderGoodsList);
          console.log('=>(detail.vue:1318) res', res);

          let isReplace = false;
          let values = Object.values(res.goods_map);
          console.log('=>(detail.vue:1321) values', values);

          // 套餐数据
          let tc_values = Object.values(res.tc_goods_map);
          this.reJoinOrderGoodsList = this.reJoinOrderGoodsList.map(item => {
            if (item.is_taocan !== '1' && item.goods_sku_id !== res.goods_map[item.goods_sku_id]?.id) {
              isReplace = true;
              return {
                ...item,
                goods_sku_id: res.goods_map[item.goods_sku_id].id,
                manufacturer: res.goods_map[item.goods_sku_id].manufacturer,
                prod_area: res.goods_map[item.goods_sku_id].prod_area,
              };
            } else {
              return item;
            }
          });
          // 两者均失效，说明当前订单商品全部失效
          if (
            values.every(item => !item || item.is_effective === '0') &&
            tc_values.every(item => !item || item.is_effective === '0')
          ) {
            this.$Message.error('该订单商品已全部失效，请重新下单');
          } else {
            this.$Message.success('重新提交成功');
            console.log('进入重新提交功能，当前是即将被加入的数据', this.reJoinOrderGoodsList);
            this.$store.commit('purchase/REJOIN', {
              list: this.reJoinOrderGoodsList,
              router: this.$router,
              dispatch: this.$store.dispatch,
              isReplace: isReplace,
            });
          }
        },
        err => {
          {
          }
        }
      );
    },
    /* 查看包裹详情 */
    toLogisticsDetail(row) {
      console.log('row', row);
      console.log([row]);
      this.progress_express_detail = [row];
      this.logisticsVisible = true;
    },

    /* 发票 */
    // 申请开票
    applyInvoicing() {
      if (this.invoice_status === 'NORMAL') {
        this.$api.checkInvoiceStatus().then(res => {
          if (res.not_pass !== '1') {
            this.applyVisible = true;
          } else {
            this.invoiceTipsContent = res.msg;
            this.invoiceTipsType = res.type;
            this.invoiceInfoVisible = true;
          }
        });
      } else {
        this.applyVisible = true;
      }
    },
    getTimeRemain(type) {
      if (type === 'audit') {
        return this.secondToDate(this.audit_remain);
      } else {
        return this.secondToDate(this.pay_remain);
      }
    },
    secondToDate(result) {
      let h =
        String(Math.floor(result / 3600)).length < 2 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
      let m =
        String(Math.floor((result / 60) % 60)).length < 2
          ? '0' + Math.floor((result / 60) % 60)
          : Math.floor((result / 60) % 60);
      let s = String(Math.floor(result % 60)).length < 2 ? '0' + Math.floor(result % 60) : Math.floor(result % 60);
      if (h || m || s) {
        return (result = h + ':' + m + ':' + s + '秒后失效');
      } else {
        return '已失效';
      }
    },
    //修改订单
    editOrder() {
      this.editLoading = true;
      this.$api
        .getEditOrderInfo({ order_code: this.order_code })
        .then(
          res => {
            console.log('-> %c res  === %o ', 'font-size: 15px', res);
            this.editGoodsList = res.list;
            this.editOrderInfo = {
              total_num: res.total_num,
              total_money: res.total_money,
              recommit_enable: res.recommit_enable,
              goods_status_change: res.goods_status_change,
            };
            this.editOrderVisible = true;
          },
          err => {}
        )
        .finally(() => {
          this.editLoading = false;
        });
    },
    //确认支付
    confirmPayment() {
      const params = {
        order_code: this.order_code,
        ...this.formData,
      };
      this.payLoading = true;
      this.$api
        .confirmPayment(params)
        .then(
          res => {
            this.$Message.success('支付成功');
            this.payVisible = false;
            this.$router.replace('/purchase/order/list');
          },
          err => {}
        )
        .finally(() => (this.payLoading = false));
    },
    //校验支付信息
    confirmPay(name) {
      console.log(this.formData);
      this.$refs[name].validate(valid => {
        console.log(valid);
        if (!this.formData.consignee_info.address.detail) {
          this.$Message.error('请完善收件地址信息');
          return;
        }
        if (valid) {
          this.payVisible = true;
        } else {
          this.$Message.error('请先完善收货信息');
        }
      });
    },
    cancelPurchase() {
      this.$api.cancelPurchaseOrder({ order_code: this.order_code }).then(
        res => {
          this.$Message.success('取消采购成功');
          this.editOrderVisible = false;
          this.$router.replace('/purchase/order/list');
        },
        err => {}
      );
    },
    // 入库审核详情-修改
    editExamin(ostock_code) {
      this.$api
        .getPurstockInfo({ order_code: this.order_code, ostock_code })
        .then(res => {
          // 将两组数据组合成新的入库数据
          this.wareHouseList = this.handleOrderGoodList(res.list);
          this.isShowBatch();
          this.shipmentVisible = true;
          this.editOstock = true;
        })
        .catch(err => {
          {
          }
        });
    },
    // 入库审核详情-取消
    cancelExamin(ostock_code) {
      console.log('-> %c ostock_code  === %o ', 'font-size: 15px', ostock_code);
      this.$Modal.confirm({
        title: '是否确定取消入库',
        closable: true,
        onOk: () => {
          this.$api.cancelOstock({ order_code: this.order_code, ostock_code }).then(
            res => {
              this.editOstock = false;
              this.getOrderDetailInfo();
            },
            err => {
              {
              }
            }
          );
        },
        onCancel: () => {},
      });
    },

    // 二次弹框确认
    // confirm() {
    //   this.$Modal.confirm({
    //     title: '是否确定取消入库',
    //     onOk: () => {
    //       this.$Message.info('Clicked ok');
    //     },
    //     onCancel: () => {
    //       this.$Message.info('Clicked cancel');
    //     }
    //   });
    // },

    changeNum(e, index) {
      this.wareHouseList[index].current_num = e;
      if (Number(this.wareHouseList[index].can_store_num) < e) {
        this.$Message.error(`${this.wareHouseList[index].generic_name}入库数量不得大于待入库数量`);
      }
    },
    // 开启入库弹框
    shipment() {
      // 获取即将入库的数据
      this.getWareHouseData();
    },
    // 是否显示批号表头
    isShowBatch() {
      console.log('this.wareHouseList', this.wareHouseList);
      let flag = this.wareHouseList.some(item => {
        return item.need_batch_code === '1';
      });
      // flag为true是,需要显示批号的表头,为false时,隐藏批号等表头
      if (flag) {
        this.tableColumns_order = this.tableColumns_order_need;
      } else {
        this.tableColumns_order = this.tableColumns_order_noNeed;
      }
    },
    // 将待入库数据展开成一层
    expandWareHouseData(data) {
      let waitData = JSON.parse(JSON.stringify(data));
      let resultData = [];
      waitData.forEach(item => {
        item.batches.forEach(bat_item => {
          resultData.push(bat_item);
        });
      });
      return resultData;
    },
    // 将两组数据组合成新的入库数据
    handleOrderGoodList(data) {
      let resultData = JSON.parse(JSON.stringify(data));

      // 针对于套餐的商品，要和正常sku比对，套餐的要和套餐的attrs数据比对
      this.orderGoodsList.map((item, index) => {
        // 套餐的attrs数据比对
        if (item.is_taocan === '1') {
          item.attrs.forEach(attrs_item => {
            data.map((ite, ind) => {
              if (attrs_item.sku_code === ite.sku_code) {
                ite.batches.map((bat_item, bat_index) => {
                  resultData[ind].batches[bat_index] = {
                    ...attrs_item,
                    ...bat_item,
                    ...{
                      current_num: bat_item.can_store_num,
                      need_batch_code: ite.need_batch_code,
                    },
                  };
                });
              }
            });
          });
        } else {
          // 正常sku比对
          data.map((ite, ind) => {
            if (item.sku_code === ite.sku_code) {
              ite.batches.map((bat_item, bat_index) => {
                resultData[ind].batches[bat_index] = {
                  ...item,
                  ...bat_item,
                  ...{
                    current_num: bat_item.can_store_num,
                    need_batch_code: ite.need_batch_code,
                  },
                };
              });
            }
          });
        }
      });
      // 展开批次数据
      resultData = this.expandWareHouseData(resultData);
      console.log('resultData', resultData);
      return resultData;
    },
    // 点击采购入库，获取待入库的数据
    async getWareHouseData() {
      await io
        .get('/clinic/pms.order.putstockinfo', {
          data: { order_code: this.$route.query.order_code },
        })
        .then(res => {
          console.log('-> %c res  ===    %o', 'font-size: 15px;color: #F56C6C ;', res);
          // 将两组数据组合成新的入库数据
          this.wareHouseList = this.handleOrderGoodList(res.list);
          this.isShowBatch();
          this.shipmentVisible = true;
        })
        .catch(err => {
          {
          }
        });
    },
    handleSummary({ columns, data }) {
      console.log('-> data', data);
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计',
          };
          return;
        }
        let whiteList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 11];
        if (!whiteList.includes(index)) {
          sums[key] = {
            key,
            value: '',
          };
          // return
        }
        const values = data.map(item => Number(item[key]));
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr).toFixed(2);
            if (!isNaN(value)) {
              return $operator.add(Number(prev), Number(curr));
            } else {
              return prev;
            }
          }, 0);
          if (key == 'num') {
            sums[key] = {
              key,
              value: v,
            };
          }
          if (key == 'stored_num') {
            sums[key] = {
              key,
              value: v,
            };
          }
          if (key == 'can_store_num') {
            sums[key] = {
              key,
              value: v,
            };
          }
        } else {
          sums[key] = {
            key,
            value: '',
          };
        }
      });
      return sums;
    },
    jump(row) {
      let query = {
        status: row.status,
        apply_code: row.apply_code,
      };
      this.$router.push({
        path: '/purchase/after-sale/detail',
        query,
      });
    },
    // *api 获取采购订单详情
    getOrderDetailInfo(order_code = '') {
      this.tableLoading = true;
      io.get('/clinic/pms.order.detail', {
        data: { order_code: this.$route.query.order_code },
      }).then(
        res => {
          this.tableLoading = false;
          let order = res.order;
          this.orderDetail = res.order;
          this.invoice_status = order.invoice_status;
          this.echoData.order_code = order.order_code;
          this.echoData.trade_code = order.trade_code;
          this.echoData.finished_at = order.finished_at;
          this.echoData.status_text = order.status_text;
          this.orderStatus = order.status;
          this.echoData.store_status_text = order.store_status_text;
          this.echoData.pay_type_text = order.pay_type_text;
          this.echoData.clinic_code = order.clinic_code;
          this.echoData.clinic_name = order.clinic_name;
          this.echoData.organization_name = order.organization_name;
          this.echoData.organization_code = order.organization_code;
          this.echoData.creator = order.creator;
          this.echoData.leading_mobile = order.leading_mobile;
          this.echoData.leading_person = order.leading_person;
          this.echoData.total_num = order.total_num;
          this.echoData.total_amount = order.money_info.total_amount;
          this.echoData.goods_fee = order.money_info.goods_fee;
          this.echoData.service_fee = order.money_info.service_fee;
          this.echoData.create_time = order.create_time;
          this.echoData.remark = order.remark;
          this.echoData.paid_at = order.paid_at;
          this.echoData.can_put_stock = order.can_put_stock;
          this.echoData.address_text = order.consignee_info.address_text;
          this.echoData.mobile = order.consignee_info.mobile;
          this.echoData.consignee = order.consignee_info.consignee;
          this.echoData.type_text = order.type_text;
          this.echoData.is_cf_supplier = order.is_cf_supplier;
          this.echoData.sign_status_desc = order.sign_status_desc;
          this.echoData.can_sign = order.can_sign;
          this.echoData.reject_reason = order.reject_reason;
          this.payInfo.balance = res.pay_info.gunion_balance;
          this.payInfo.goods_num = res.pay_info.goods_num;
          this.payInfo.goods_types = res.pay_info.goods_types;
          this.payInfo.payment_fee = res.pay_info.payment_fee;
          this.payInfo.autoPay = !!res.pay_info?.auto_pay;
          this.payInfo.allow_yzt = res.pay_info?.allow_yzt;
          this.formData.pay_type = this.payInfo.allow_yzt === '1' ? 'YZT' : 'GUNION';
          this.payInfo.yzt_balance = res.pay_info?.yzt_balance;
          // 2024.7.10 list新增disable_repurchase，过滤不可重新采购商品
          this.reJoinOrderGoodsList = this.$lodash.cloneDeep(res.list).filter(item => item.disable_repurchase !== '1');
          this.orderGoodsList = res.list?.map(item => {
            if (item.is_taocan == 1) {
              item.attrs?.forEach(attrs_item => {
                attrs_item.isChild = true;
              });
            }
            return {
              ...item,
              children: item.attrs,
              _showChildren: false,
            };
          });

          // 获取物流信息
          if (res.express_info?.length) {
            this.express_list = res.express_info;

            this.setShowTypeList(this.typeList[3]);
            // this.goodsTabChange('express_list');
            if (this.$route.query.openType === 'receipt') {
              this.goodsTabChange(this.typeList[3].key);
              this.setTabListToTop(this.$refs.tabList);
            }
          }
          // 获取入库列表
          if (res.ostocks?.length) {
            this.ostocks = res.ostocks;

            this.setShowTypeList(this.typeList[2]);
            // this.goodsTabChange('ostocks');
          }
          console.log(this.showTypeList);
          // 获取供应商审核
          // this.audit_records = res.audit_records;
          this.audit_records = res.operation_log;

          this.purchase_type = order.type;
          console.log('=>(detail.vue:1040) this.purchase_type', this.purchase_type);
          // if ( order.consignee_info.address.county.code ) {
          //   this.selectedAddress = [order.consignee_info.address.prov.code, order.consignee_info.address.city.code, order.consignee_info.address.county.code]
          // } else {
          //   this.selectedAddress = [order.consignee_info.address.prov.code, order.consignee_info.address.city.code]
          // }
          this.formData.consignee_info = order.consignee_info;
          let provName = order.consignee_info.address.prov.name;
          let cityName = order.consignee_info.address.city.name;
          let countyName = order.consignee_info.address.county.name;
          let provCode = order.consignee_info.address.prov.code || TextToCode[provName].code;
          let cityCode = order.consignee_info.address.city.code || TextToCode[provName][cityName].code;
          let countyCode = order.consignee_info.address.county.code || TextToCode[provName][cityName][countyName].code;
          this.selectedAddress = [provCode, cityCode, countyCode];
          this.regionChange(this.selectedAddress); // 针对小程序未传code, 与普通情况一并处理

          this.formData.remark = order.remark;
          this.pay_remain = Number(order.pay_remain);
          this.audit_remain = Number(order.audit_remain);
          this.allow_repurchase = res.allow_repurchase;
          this.timer = setInterval(() => {
            this.pay_remain--;
            this.audit_remain--;
          }, 1000);
        },
        rej => {
          this.tableLoading = false;
        }
      );
    },
    setShowTypeList(setItemObj) {
      let index = this.showTypeList.findIndex(item => item.key === setItemObj.key);
      if (index === -1) {
        this.showTypeList.push(setItemObj);
      }
    },
    setTabListToTop(ele) {
      this.$nextTick(() => {
        function getElementTop(element) {
          let actualTop = element.offsetTop;
          let current = element.offsetParent;

          while (current !== null) {
            actualTop += current.offsetTop;
            current = current.offsetParent;
          }

          return actualTop;
        }

        const elementTop = getElementTop(ele);
        window.scrollTo({
          top: elementTop - 56 - 5,
        });
      });
    },
    // // 处理入库参数
    // handlerListof () {
    // 	let arr = this.wareHouseList.filter( item => { return Number(item.current_num) > 0 } ) || []
    // 	let resultObj = {}
    // 	arr.map( item => {
    // 		console.log('item', item.id);
    // 		resultObj[item.id] = item.current_num
    // 	} )
    // 	if (!Object.keys(resultObj).length) {
    // 		this.$Message.error('待入库数量至少有一个大于0')
    // 		return
    // 	}
    // 	return resultObj || {}
    // },
    handlerListNewOf() {
      let arr =
        this.wareHouseList.filter(item => {
          return Number(item.current_num) > 0;
        }) || [];
      let resultArray = [];
      arr.map(item => {
        resultArray.push({
          sku_code: item.sku_code, // 商品批号
          batch_code: item.batch_code, // 批号
          prod_date: item.prod_date, // 生产日期
          expire_date: item.expire_date, // 过期时间
          num: item.current_num, // 入库数量
        });
      });
      if (!resultArray.length) {
        this.$Message.error('待入库数量至少有一个大于0');
        return;
      }
      return resultArray || {};
    },
    // *api 确认入库
    confirmSubmit() {
      console.log('waitHouse', this.wareHouseList);
      let listof = this.handlerListNewOf();
      if (listof) {
        this.confirmStoreLoading = true;
        let query = {
          order_code: this.$route.query.order_code,
          listof,
        };
        if (this.editOstock) {
          this.$api.editOstock(query).then(
            res => {
              this.$Message.success('修改入库成功');
              this.getOrderDetailInfo();
              this.confirmStoreLoading = false;
              this.shipmentVisible = false;
              this.remindRelation = res.relation;
              this.remindRelation && this.remindRelation.length && (this.commonTipsVisible = true);
            },
            rej => {
              this.confirmStoreLoading = false;
            }
          );
        } else {
          io.post('/clinic/pms.order.putstock', query).then(
            res => {
              this.$Message.success('申请入库成功');
              this.getOrderDetailInfo();
              this.confirmStoreLoading = false;
              this.shipmentVisible = false;
              this.remindRelation = res.relation;
              this.remindRelation && this.remindRelation.length && (this.commonTipsVisible = true);
            },
            rej => {
              this.confirmStoreLoading = false;
            }
          );
        }
      }
    },
    resetCurrentNum() {
      this.wareHouseList = this.wareHouseList.map(item => {
        return { ...item, current_num: item.can_store_num };
      });
    },
    //关闭弹窗
    closeModal(visible, show) {
      this.editOstock = false;
      this[visible] = false;
    },
    regionChange(address) {
      console.log('-> address', address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.consignee_info.address.prov = prov;
        this.formData.consignee_info.address.city = city;
        this.formData.consignee_info.address.county = county;
        console.log(this.formData);
      } else {
        this.formData.consignee_info.address = {
          prov: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          detail: '',
        };
      }
    },

    confirmPMS() {
      this.cancelVisible = true;
    },
    confirmExpress(item) {
      console.log(item);
      let { ship_code, batch_id, pack_index, deliver_index } = item;
      ship_code = item.ship_code_data;
      console.log({
        order_code: this.order_code,
        ship_code,
        batch_id,
        pack_index,
        deliver_index,
      });
      this.$Modal.confirm({
        title: '是否确认收货？',
        loading: true,
        onOk: () => {
          io.post('/clinic/pms.order.signDeliver', {
            order_code: this.order_code,
            ship_code,
            batch_id,
            pack_index,
            deliver_index,
          }).then(
            res => {
              this.$Message.success('确认收货成功');
              this.$Modal.remove();
              this.getOrderDetailInfo();
              this.getApplyTableList();
            },
            err => {
              {
              }
              this.$Modal.remove();
            }
          );
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel');
        },
      });
    },
    confirmPass() {
      this.$Modal.confirm({
        title: '是否确认全部收货？',
        loading: true,
        onOk: () => {
          io.post('/clinic/pms.order.signWhole', {
            order_code: this.order_code,
          }).then(
            res => {
              this.$Message.success('全部确认收货成功');
              this.$Modal.remove();
              this.getOrderDetailInfo();
              this.getApplyTableList();
            },
            err => {
              {
              }
              this.$Modal.remove();
            }
          );
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel');
        },
      });
    },
    handleSpan({ row, column, rowIndex, columnIndex }) {
      // 合并单元格起始位置
      let firstBiginColsIndex = 0;
      // 合并单元格数量
      let firstColsNum = 1;
      // 合并单元格起始位置
      let secondBiginColsIndex = 0;
      // 合并单元格数量
      let secondColsNum = 1;

      // 一级单元格合并
      // [0,1,2]代表从第一行开始合并三行
      // [2,3,4,5]代表从第三行开始合并四行
      let includeFirstIndex = [];
      this.express_list.forEach((item, index) => {
        if (item.ship_unique === row.ship_unique) {
          includeFirstIndex.push(index);
        }
      });
      if (includeFirstIndex.includes(rowIndex)) {
        firstBiginColsIndex = includeFirstIndex[0];
        firstColsNum = includeFirstIndex.length;
      }
      // 二级单元格合并
      let includeSecondIndex = [];
      this.express_list.forEach((item, index) => {
        if (item.goods_unique === row.goods_unique) {
          includeSecondIndex.push(index);
        }
      });
      if (includeSecondIndex.includes(rowIndex)) {
        secondBiginColsIndex = includeSecondIndex[0];
        secondColsNum = includeSecondIndex.length;
      }

      //---------------------------------------------------------------------这部分代码不用动-
      //合并一级单元格
      if (firstColsNum + firstBiginColsIndex > this.express_list.length) {
        console.error(new Error('合并列数超出表格列数'));
        return;
      }

      if (columnIndex === 0 && rowIndex === firstBiginColsIndex) {
        return [firstColsNum, 1];
      } else {
        if (columnIndex === 0 && rowIndex > firstBiginColsIndex && rowIndex < firstColsNum + firstBiginColsIndex) {
          return [0, 0];
        }
      }

      //合并二级单元格
      if (secondColsNum + secondBiginColsIndex > this.express_list.length) {
        console.error(new Error('合并列数超出表格列数'));
        return;
      }

      if (columnIndex === 1 && rowIndex === secondBiginColsIndex) {
        return [secondColsNum, 1];
      } else {
        if (columnIndex === 1 && rowIndex > secondBiginColsIndex && rowIndex < secondColsNum + secondBiginColsIndex) {
          return [0, 0];
        }
      }
    },
    setTagFunc() {
      this.getApplyTableList();
      this.getOrderDetailInfo();
      this.goodsTabChange(this.typeList[1].key);
    },
    backFunc() {
      if (window.history.length === 1) {
        this.$router.push({ path: '/purchase/after-sale/list' });
      } else {
        this.$router.back();
      }
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
  },
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  padding-bottom: 50px;

  .basic-info {
    border: 1px solid #b7b7b7;

    .basic-info-item {
      display: flex;
      line-height: 22px;
      border-bottom: 1px solid #b7b7b7;

      &:last-of-type {
        border-bottom: none;
      }

      text-align: center;

      .item-label {
        width: 180px;
        border-right: 1px solid #b7b7b7;
        background: #e6e6e6;
      }

      .item-content {
        flex: 1;
        text-align: left;
        text-indent: 30px;
      }
    }
  }

  .goods-list {
    padding-top: 20px;

    .title {
      padding-bottom: 0 !important;
    }
    .goods-list-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .footer {
    text-align: center;
    padding: 20px;
  }
}

.logistics-information {
  margin-bottom: 30px;
  padding-top: 20px;

  .logistics-info-box {
    padding: 12px 12px 6px;
    border: 1px solid #e7e7e7;
    width: 260px;
    margin: 12px;
    background: rgba(225, 225, 225, 0.3);

    .title {
      font-size: 15px;
      font-weight: 600;
      line-height: 22px;
    }

    .info-item {
      margin-bottom: 20px;
    }

    .foot-tip {
      line-height: 20px;
    }
  }
}

.reject-reason {
  color: red;
}
</style>

<style lang="less" scoped>
.margin-bottom10 {
  margin-bottom: 10px;
}

.margin-left10 {
  margin-left: 10px;
}

.addressInput {
  flex: 1;
}

.radius-btn {
  border-radius: 30px;
  font-size: 12px;
}

.ml10 {
  margin-left: 10px;
}

.wrap {
  white-space: pre-wrap;
}

.min36 {
  min-width: 36px;
}

.table-wrapper {
  position: relative;
  ::v-deep .ivu-table-wrapper {
    overflow: unset;
  }
}
.sticky-table-head {
  ::v-deep .ivu-table {
    overflow: unset;
    .ivu-table-header {
      position: sticky;
      top: 95px;
      z-index: 3;
    }
    .ivu-table-fixed {
      z-index: 4;
    }
  }
}
</style>
<style lang="less">
.pay-pur-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-title {
    text-align: center;
    margin-bottom: 15px;
    font-size: 16px;
  }

  .ivu-modal {
    top: -10vh;
  }

  .ivu-modal-footer {
    border-top: none;
  }

  .ivu-modal-body {
    padding-top: 0;
    margin-top: -10px;
  }

  .ivu-modal-header {
    border-bottom: none;
  }
}

.default-title {
  color: #333;
  cursor: pointer;
  font-size: 12px;
  line-height: 21px;
}

.active-title {
  color: #155bd4;
  font-size: 16px;
  line-height: 18px !important;
  //border-bottom: 1px solid #155BD4;
}

.tips {
  color: #333333;
  font-size: 13px;
  line-height: 21px;
  padding: 10px 0;

  p {
    margin-bottom: 0;
  }
}
</style>

<style>
.ivu-tooltip-inner {
  max-width: 400px;
}
</style>
