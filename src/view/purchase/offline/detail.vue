<template>
  <div class="detail-wrapper">
    <h3 class="f-title">基础信息</h3>
    <div class="basic-info">
      <div class="basic-info-item" style="justify-content: center; background: #cccccc">采购单信息</div>
      <div class="basic-info-item">
        <span class="item-label">采购单号</span>
        <span class="item-content">{{ echoData.code }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单状态</span>
        <span class="item-content"
          >{{ echoData.status_text }}
          <span style="color: red; margin-left: 6px" v-if="echoData.status_text === '已驳回'"
            >( 驳回原因：{{ echoData.reject_reason }} )</span
          >
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购主体</span>
        <span class="item-content">{{ echoData.pur_main_name }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">营业执照名称</span>
        <span class="item-content">{{ echoData.organization_name || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单类型</span>
        <span class="item-content">{{ echoData.type_text }}</span>
      </div>
      <!-- <div class="basic-info-item" v-if="type === 'P_MEETING'">
        <span class="item-label">关联活动名称</span>
        <span class="item-content">{{ promotion.name }}</span>
      </div>
      <div class="basic-info-item" v-if="type === 'P_MEETING'">
        <span class="item-label flex flex-item-center ">参会人</span>
        <span class="item-content">
          <div
            v-for="(item, index) in promotion.meeting_user_list"
            :key="index"
          >
            参会人{{ index + 1 }}: {{ item.name }}({{ item.sex_text }})
            {{ item.mobile }} {{ item.company_name }}
          </div>
        </span>
      </div> -->

      <!-- <div class="basic-info-item" v-if="type !== 'P_MEETING'">
        <span class="item-label">收件人姓名</span>
        <span class="item-content">{{ echoData.consignee }}</span>
      </div>
      <div class="basic-info-item" v-if="type !== 'P_MEETING'">
        <span class="item-label">收件人手机号</span>
        <span class="item-content">{{ echoData.mobile }}</span>
      </div>
      <div class="basic-info-item" v-if="type !== 'P_MEETING'">
        <span class="item-label">收货地址</span>
        <span class="item-content">{{ echoData.address }}</span>
      </div> -->

      <div class="basic-info-item">
        <span class="item-label">收件人姓名</span>
        <span class="item-content">{{ echoData?.consignee || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">收件人手机号</span>
        <span class="item-content">{{ echoData?.mobile || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">收货地址</span>
        <span class="item-content">{{ echoData?.address || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">下单时间</span>
        <span class="item-content">{{ echoData.create_time }}</span>
      </div>
      <!-- <div class="basic-info-item" v-if="echoData.status === 'WAIT_SHIP'">
        <span class="item-label">付款单号</span>
        <span class="item-content">{{ echoData.pack_order_code }}</span>
      </div> -->

      <div class="basic-info-item" v-if="type === 'COM_PERSONAL'">
        <span class="item-label">个人支付金额</span>
        <span class="item-content">￥{{ echoData.personal_price }}</span>
      </div>
      <div class="basic-info-item" v-if="type === 'COM_PERSONAL'">
        <span class="item-label flex flex-item-center">个人支付方式</span>
        <span
          class="item-content"
          v-if="echoData.pay_info && echoData.pay_info.personal && echoData.pay_info.personal.desc_list.length"
        >
          <span v-for="(item, ind) in echoData.pay_info.personal.desc_list" :key="ind">{{ item + '&nbsp;' }}</span>
          <br />
          <viewer
            v-if="echoData.pay_info.personal.imgs.length"
            :images="echoData.pay_info.personal.imgs"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 45px; height: auto; margin-right: 10px"
              v-for="(item, index) in echoData.pay_info.personal.imgs"
              :key="index"
              :src="item | imageStyle"
              alt="支付凭证"
              class="image cursor scale"
            />
          </viewer>
        </span>
        <span class="item-content flex flex-item-align" v-else>-</span>
      </div>

      <div class="basic-info-item" v-if="!isOpcPay && (type === 'CLI_ADDITION' || type === 'CLI_OPENING')">
        <span class="item-label">诊所支付金额</span>
        <span class="item-content">￥{{ echoData.cli_price }}</span>
      </div>
      <div class="basic-info-item" v-if="isOpcPay && (type === 'CLI_ADDITION' || type === 'CLI_OPENING')">
        <span class="item-label">直营运营中心支付金额</span>
        <span class="item-content">￥{{ echoData.com_price }}</span>
      </div>

      <div class="basic-info-item" v-if="!isOpcPay && (type === 'CLI_ADDITION' || type === 'CLI_OPENING')">
        <span class="item-label flex flex-item-center">诊所支付方式</span>
        <span
          class="item-content"
          v-if="echoData.pay_info && echoData.pay_info.clinic && echoData.pay_info.clinic.desc_list.length"
        >
          <span v-for="(item, ind) in echoData.pay_info.clinic.desc_list" :key="ind">{{ item + '&nbsp;' }}</span>
          <br />
          <viewer
            v-if="echoData.pay_info.clinic.imgs.length && type !== 'CLI_ADDITION'"
            :images="echoData.pay_info.clinic.imgs"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 45px; height: auto; margin-right: 10px"
              v-for="(item, index) in echoData.pay_info.clinic.imgs"
              :key="index"
              :src="item | imageStyle"
              alt="支付凭证"
              class="image cursor scale"
            />
          </viewer>
        </span>
        <span class="item-content flex flex-item-align" v-else>-</span>
      </div>
      <div class="basic-info-item" v-if="isOpcPay && (type === 'CLI_ADDITION' || type === 'CLI_OPENING')">
        <span class="item-label flex flex-item-center">直营运营中心支付方式</span>
        <span
          class="item-content"
          v-if="echoData.pay_info && echoData.pay_info.company && echoData.pay_info.company.desc_list.length"
        >
          <span v-for="(item, ind) in echoData.pay_info.company.desc_list" :key="ind">{{ item + '&nbsp;' }}</span>
          <br />
          <viewer
            v-if="echoData.pay_info.company.imgs.length && type !== 'CLI_ADDITION'"
            :images="echoData.pay_info.company.imgs"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 45px; height: auto; margin-right: 10px"
              v-for="(item, index) in echoData.pay_info.company.imgs"
              :key="index"
              :src="item | imageStyle"
              alt="支付凭证"
              class="image cursor scale"
            />
          </viewer>
        </span>
        <span class="item-content flex flex-item-align" v-else>-</span>
      </div>

      <!-- 养疗馆支付 todo -->
      <div class="basic-info-item" v-if="type === 'RXJ_ADDITION' || type === 'RXJ_OPENING'">
        <span class="item-label">养疗馆支付金额</span>
        <span class="item-content">￥{{ echoData.cli_price }}</span>
      </div>
      <div class="basic-info-item" v-if="type === 'RXJ_ADDITION' || type === 'RXJ_OPENING'">
        <span class="item-label flex flex-item-center">养疗馆支付方式</span>
        <span
          class="item-content"
          v-if="echoData.pay_info && echoData.pay_info.rxj && echoData.pay_info.rxj.desc_list.length"
        >
          <span v-for="(item, ind) in echoData.pay_info.rxj.desc_list" :key="ind">{{ item + '&nbsp;' }}</span>
          <br />
          <viewer
            v-if="echoData.pay_info.rxj.imgs.length"
            :images="echoData.pay_info.rxj.imgs"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 45px; height: auto; margin-right: 10px"
              v-for="(item, index) in echoData.pay_info.rxj.imgs"
              :key="index"
              :src="item"
              alt="支付凭证"
              class="image cursor scale"
            />
          </viewer>
        </span>
        <span class="item-content flex flex-item-align" v-else>-</span>
      </div>

      <!-- <div class="basic-info-item">
        <span class="item-label">省公司支付金额</span>
        <span class="item-content">￥{{ echoData.com_price }}</span>
      </div> -->
      <!-- <div class="basic-info-item">
        <span class="item-label flex flex-item-center">省公司支付方式</span>
        <span
          class="item-content"
          v-if="
            echoData.pay_info &&
              echoData.pay_info.company &&
              echoData.pay_info.company.desc_list.length
          "
        >
          <span
            v-for="(item, ind) in echoData.pay_info.company.desc_list"
            :key="ind"
            >{{ item + "&nbsp;" }}</span
          >
          <br />
          <viewer
            v-if="echoData.pay_info.company.imgs.length"
            :images="echoData.pay_info.company.imgs"
            class="flex  flex-item-align"
            style="margin: 10px 10px 10px 30px;"
          >
            <img
              style="width: 45px;height: auto;margin-right: 10px;"
              v-for="(item, index) in echoData.pay_info.company.imgs"
              :key="index"
              :src="item"
              alt="支付凭证"
              class="image cursor scale"
            />
          </viewer>
        </span>
        <span class="item-content flex flex-item-align" v-else>-</span>
      </div> -->
      <!--			<div class="basic-info-item">-->
      <!--				<span class="item-label">平台服务费</span>-->
      <!--				<span class="item-content">¥{{echoData.service_price}}</span>-->
      <!--			</div>-->

      <div class="basic-info-item">
        <span class="item-label">签收状态</span>
        <span class="item-content">{{ echoData.sign_status_desc || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">备注</span>
        <span class="item-content">{{ echoData.remark ? echoData.remark : '-' }}</span>
      </div>
    </div>
    <div class="goods-list">
      <!-- <h3 class="f-title">
        商品清单
      </h3> -->
      <div class="table-wrapper">
        <div class="panel-nav flex flex-item-between">
          <div ref="tabList">
            <a
              class="nav"
              :class="{ active: activeGoodsType == type_item.key }"
              v-for="(type_item, type_index) in showTypeList"
              :key="type_index + 'type'"
              @click="goodsTabChange(type_item.key)"
              >{{ type_item.desc }}</a
            >
            <!-- <a
              :class="{active: tableTab === 'goodsList'}"
              class="nav"
              @click.prevent.capture="tabChange('goodsList')"
            >
              商品清单
            </a>
            <a
              :class="{active: tableTab === 'express'}"
              class="nav"
              @click.prevent.capture="tabChange('express')"
            >
              包裹物流
            </a>
            <a
              :class="{active: tableTab === 'afterDetail'}"
              class="nav"
              @click.prevent.capture="tabChange('afterDetail')"
            >
              售后明细
            </a> -->
          </div>

          <div>
            <Button
              class="ml10"
              type="primary"
              @click="confirmPass"
              v-if="echoData.can_sign === '1' && activeGoodsType === 'express'"
              >全部确认收货
            </Button>
            <!-- <Button @click="pringFunc">打印进货单</Button> -->
            <Button v-if="Number(orderInfo.can_refund_apply) && !isJiShanChannel" class="ml10" @click="applyAfterSale"
              >申请售后</Button
            >
          </div>
        </div>
        <div v-if="activeGoodsType === 'goodsList'">
          <!-- 诊所 -->
          <Table
            v-if="
              type === 'CLI_ADDITION' || type === 'CLI_OPENING' || type === 'RXJ_ADDITION' || type === 'RXJ_OPENING'
            "
            :columns="tableColumns"
            :data="goods_items"
            :summary-method="handleSummary"
            border
            show-summary
          >
            <template slot-scope="{ row, index }" slot="img">
              <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
                <img style="width: 80px; height: auto" :src="row.img" class="image" />
              </viewer>
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row, index }" slot="specs">
              <div v-if="row.specs && row.specs.length">
                <div v-for="item in row.specs" :key="item">
                  <span>{{ item.name + ' * ' + item.num }}、</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row }" slot="cli_price">
              {{ row.cli_price ? `¥${row.cli_price}` : '-' }}
            </template>
            <!-- 取消数量 -->
            <template slot-scope="{ row }" slot="cancel_num">
              {{ row.cancel_num ? row.cancel_num : '-' }}
            </template>
          </Table>

          <!-- 省公司 -->
          <!-- <Table
            v-if="type === 'COM_OWN'"
            :columns="purchase_columns_company"
            :data="goods_items"
            :summary-method="handleSummary"
            border
            show-summary
          >
            <template slot-scope="{row, index}" slot="img">
              <viewer
                :images="[row.img]"
                v-if="row.img"
                class="flex  flex-item-center"
              >
                <img
                  style="width: 80px;height: auto"
                  :src="row.img"
                  class="image"
                />
              </viewer>
              <span v-else>-</span>
            </template>
            <template slot-scope="{row, index}" slot="specs">
              <div v-if="row.specs && row.specs.length">
                <div v-for="item in row.specs" :key="item">
                  <span>{{ item.name + " * " + item.num }}、</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </Table> -->

          <!-- 省公司对个人销售 -->
          <!-- <Table
            v-if="type === 'COM_PERSONAL'"
            :columns="purchase_columns_person"
            :data="goods_items"
            :summary-method="handleSummary"
            border
            show-summary
          >
            <template slot-scope="{row, index}" slot="img">
              <viewer
                :images="[row.img]"
                v-if="row.img"
                class="flex  flex-item-center"
              >
                <img
                  style="width: 80px;height: auto"
                  :src="row.img"
                  class="image"
                />
              </viewer>
              <span v-else>-</span>
            </template>
            <template slot-scope="{row, index}" slot="specs">
              <div v-if="row.specs && row.specs.length">
                <div v-for="item in row.specs" :key="item">
                  <span>{{ item.name + " * " + item.num }}、</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </Table> -->
          <!-- <Table
            v-if="type === 'P_MEETING'"
            :columns="purchase_columns_meeting"
            :data="goods_items"
            :summary-method="handleSummary"
            border
            show-summary
          >
            <template slot-scope="{row, index}" slot="img">
              <viewer
                :images="[row.img]"
                v-if="row.img"
                class="flex  flex-item-center"
              >
                <img
                  style="width: 80px;height: auto"
                  :src="row.img"
                  class="image"
                />
              </viewer>
              <span v-else>-</span>
            </template>
            <template slot-scope="{row, index}" slot="specs">
              <div v-if="row.specs && row.specs.length">
                <div v-for="item in row.specs" :key="item">
                  <span>{{ item.name + " * " + item.num }}、</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </Table> -->
        </div>
        <!-- 包裹物流 -->
        <div v-if="activeGoodsType === 'express'">
          <Table
            :columns="expressColumns"
            :data="express_info"
            :loading="tableLoading"
            border
            :span-method="handleSpan"
          >
            <!-- <template slot="express_desc" slot-scope="{ row }"> -->
            <!--   <div class="expressBox"> -->
            <!--     <div style="text-align: left; flex: 1"> -->
            <!--       <div v-for="(item, index) in row.express_desc" :key="index"> -->
            <!--         {{ item.express_name }} {{ item.express_no }} -->
            <!--       </div> -->
            <!--     </div> -->
            <!--   </div> -->
            <!-- </template> -->
            <!-- <template slot="ship_time" slot-scope="{ row }"> -->
            <!--   {{ row.ship_time | data_format }} -->
            <!-- </template> -->
            <!-- <template slot="action" slot-scope="{ row }"> -->
            <!--   <div> -->
            <!--     <a style="margin-right: 10px" @click="showExpress(row)"> -->
            <!--       <!-- <svg-icon iconClass="express" class="helpIcon cursor"></svg-icon> -->
            -->
            <!--       查看物流</a -->
            <!--     > -->
            <!--     <a @click="confirmExpress(row)">确认收货</a> -->
            <!--   </div> -->
            <!-- </template> -->

            <template slot="express_no" slot-scope="{ row }">
              <div>
                <div>
                  {{ row.express_name || '-' }}
                  {{ row.express_no }}
                </div>
              </div>
            </template>
            <template slot-scope="{ row }" slot="action">
              <a v-if="row.normal_express === '1'" @click="showExpress(row)" style="margin-right: 10px">查看物流</a>
              <span v-else>-</span>
              <Button
                type="primary"
                size="small"
                @click="confirmExpress(row)"
                v-if="echoData.can_sign === '1' && row.can_sign === '1'"
                >确认收货
              </Button>
            </template>
          </Table>
        </div>
        <div v-if="activeGoodsType === 'afterDetail'">
          <!-- <Table
            :columns="afterSaleColumns"
            :data="afterSaleList"
            :loading="tableLoading"
            border
          >
            <template slot="express_desc" slot-scope="{row}">
              <div class="expressBox">
                <div style="text-align: left; flex: 1;">
                  <div v-for="(item, index) in row.express_desc" :key="index">
                    {{ item.express_name }} {{ item.express_no }}
                  </div>
                </div>
                <a style="width:70px" @click="showExpress(row)"
                  ><svg-icon
                    iconClass="express"
                    class="helpIcon cursor"
                  ></svg-icon>
                  查看物流</a
                >
              </div>
            </template>
            <template slot="ship_time" slot-scope="{row}">
              {{ row.ship_time | data_format }}
            </template>
          </Table> -->

          <Table :columns="afterSaleColumns" :data="afterSaleList" stripe border>
            <template slot-scope="{ row }" slot="out_goods">
              {{ row.list && row.list.length ? rowMuty(row.list) : '-' }}
            </template>
            <template slot-scope="{ row }" slot="refund_money">
              {{ row.refund_money ? `¥${row.refund_money}` : '-' }}
            </template>
            <template slot-scope="{ row }" slot="time">
              {{ row.create_time | data_format }}
            </template>
            <!-- 售后说明 -->
            <template slot-scope="{ row }" slot="refund_desc">
              {{ row.refund_desc ? row.refund_desc : '-' }}
            </template>
            <template slot-scope="{ row }" slot="status_text">
              {{ row.status_text ? row.status_text : '-' }}
            </template>
            <template slot-scope="{ row }" slot="action">
              <!-- <a @click="jump(row)">详情</a> -->
              <KLink
                :to="{
                  path: '/purchase/after-sale/offline-detail',
                  query: {
                    status: row.status,
                    apply_code: row.apply_code,
                  },
                }"
                target="_blank"
                >详情
              </KLink>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <div class="fixed-bottom-wrapper">
      <!-- <Button @click="$router.back()">返回</Button> -->
      <back-button style="margin-right: 10px"></back-button>
      <!--			<Button  type="primary" style="margin:0 20px;" v-if="echoData.status==='REJECTED'" @click="editPurchaseOrder">修改</Button>-->
      <!--			<Button  type="primary" style="margin:0 20px;" @click="editPurchaseOrder">修改采购单</Button>-->
    </div>

    <purchase-modal
      :showModal.sync="editModalVisible"
      :orderInfo="orderInfo"
      :goods_items="local_goods_items"
      :pack_order_code="echoData.pack_order_code"
    ></purchase-modal>
    <k-logistics-progress
      v-model="logisticsVisible"
      :is-logistics-detail="false"
      :express_detail="progress_express_detail"
    ></k-logistics-progress>
    <apply-after-sale
      v-model="applyAfterSaleVisible"
      :detail="orderInfo"
      @successFunc="setTagFunc"
      :isOpcPay="isOpcPay"
    ></apply-after-sale>
    <k-tip-modal v-model="tipVisible" :text="placeHol" :showIcon="showIcon"></k-tip-modal>
  </div>
</template>

<script>
import S from '@/libs/util';
import io from 'libs/io'; // Http request
import search from '@/mixins/search';
import purchaseModal from './components/purchase-modal';
import renderHeader from '@/mixins/renderHeader';
import kLogisticsProgress from '@/components/k-logistics-progress/k-logistics-progress';
import applyAfterSale from './components/apply-after-sale.vue';
import kTipModal from '@/components/k-tip-modal';
import { cloneDeep  } from 'lodash-es';
import { $operator } from '../../../libs/operation';
import { data_format } from 'libs/filters';
import { isJiShanChannel } from '@/libs/runtime';

export default {
  name: 'detail',
  mixins: [search, renderHeader],
  components: { purchaseModal, kLogisticsProgress, applyAfterSale, kTipModal },
  data() {
    return {
      type: '', // 诊所采购： 'CLI_ADDITION'/'CLI_OPENING' ,省公司采购自用 :'COM_OWN' ,省公司对个人销售(含秒杀):  'COM_PERSONAL'
      goods_status: '',
      selectedAddress: {},
      echoData: {
        status: '',
        code: '',
        type_text: '',
        pur_main_name: '', // 采购主体
        logistics_status_text: '',
        clinic_name: '',
        create_time: '',
        pack_order_code: '',
        cli_price: '',
        com_price: '',
        personal_price: '',
        service_price: '',
        remark: '',
        address: '',
        mobile: '',
        consignee: '',
        reject_reason: '',
        pay_info: {},
        organization_name: '',
        can_sign: '',
      },
      showTypeList: [],
      typeList: [
        { desc: '商品清单', key: 'goodsList' },
        { desc: '包裹物流', key: 'express' },
        { desc: '售后明细', key: 'afterDetail' },
      ],
      tableColumns: [
        {
          title: '序号',
          type: 'index',
          key: 'index',
          align: 'center',
          width: 80,
        },
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center' },
        {
          title: '规格',
          slot: 'specs',
          key: 'specs',
          align: 'center',
          minWidth: 100,
        },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '采购单价', key: 'single_cli_price', align: 'center' },
        // {title: "省公司进货单价", key: "single_com_price", align: "center"},
        { title: '数量', key: 'num', align: 'center' },
        {
          title: '采购总价',
          slot: 'cli_price',
          key: 'cli_price',
          align: 'center',
        },
        // {title: "省公司进货合计(元)", key: "com_price", align: "center"},
        { title: '已发货数量', key: 'shipped_num', align: 'center' },
        {
          title: '取消数量',
          key: 'cancel_num',
          slot: 'cancel_num',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '商品已完成仅退款售后的数量'),
        },
        { title: '未发货数量', key: 'can_ship_num', align: 'center' },
      ],

      purchase_columns_company: [
        {
          title: '序号',
          type: 'index',
          key: 'index',
          align: 'center',
          width: 80,
        },
        { title: '商品编号', key: 'spu_code', align: 'center', width: 75 },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', align: 'center', width: 120 },
        {
          title: '采购规格',
          slot: 'specs',
          key: 'specs',
          align: 'center',
          minWidth: 100,
        },
        { title: '类型', key: 'type_text', align: 'center' },
        {
          title: '省公司进货单价(元)',
          key: 'single_com_price',
          align: 'center',
        },
        { title: '数量', key: 'num', align: 'center' },
        // {title: "省公司进货合计(元)", key: "com_price", align: "center"},
      ],

      purchase_columns_person: [
        {
          title: '序号',
          type: 'index',
          key: 'index',
          align: 'center',
          width: 80,
        },
        { title: '商品编号', key: 'spu_code', align: 'center', width: 75 },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', align: 'center', width: 120 },
        {
          title: '采购规格',
          slot: 'specs',
          key: 'specs',
          align: 'center',
          minWidth: 100,
        },
        { title: '类型', key: 'type_text', align: 'center' },
        // { title: '个人采购单价', key: 'guide_single_personal_price', align: 'center', },
        // { title: '省公司进货单价', key: 'guide_single_com_price', align: 'center', },
        {
          title: '个人采购单价',
          key: 'single_personal_price',
          align: 'center',
          width: 100,
        },
        {
          title: '省公司进货单价',
          key: 'single_com_price',
          align: 'center',
          width: 120,
        },
        { title: '数量', key: 'num', align: 'center' },
        { title: '个人采购合计(元)', key: 'personal_price', align: 'center' },
        // {title: "省公司进货合计(元)", key: "com_price", align: "center"},
        { title: '已发货数量', key: 'shipped_num', align: 'center' },
        { title: '未发货数量', key: 'can_ship_num', align: 'center' },
      ],
      purchase_columns_meeting: [
        {
          title: '序号',
          type: 'index',
          key: 'index',
          align: 'center',
          width: 80,
        },
        { title: '商品编号', key: 'spu_code', align: 'center', width: 75 },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', align: 'center', width: 120 },
        {
          title: '采购规格',
          slot: 'specs',
          key: 'specs',
          align: 'center',
          minWidth: 100,
        },
        { title: '类型', key: 'type_text', align: 'center' },
        // { title: '省公司进货指导单价', key: 'guide_single_com_price', align: 'center', },
        {
          title: '省公司进货单价',
          key: 'single_com_price',
          align: 'center',
          width: 120,
        },
        { title: '数量', key: 'num', align: 'center' },
        // {title: "省公司进货合计(元)", key: "com_price", align: "center"},
        { title: '已发货数量', key: 'shipped_num', align: 'center' },
        { title: '未发货数量', key: 'can_ship_num', align: 'center' },
      ],

      editModalVisible: false,
      model1: '',
      pur_code: '',
      express_list: [],
      goods_items_copy: [],
      goods_items: [],
      orderInfo: {},
      local_goods_items: [],
      tableTab: 'goodsList',
      expressColumns: [
        {
          title: '发货单号',
          key: 'ship_code',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '此处发货单号指流转到ERP销售订单的平台单号'),
        },
        {
          title: '商品',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.goods_text || '-'),
        },
        {
          title: '物流单号',
          slot: 'express_no',
          align: 'center',
          // render: (h, { row }) => h('span', {}, row.express_no || '-')
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
        },
        {
          title: '签收状态',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.is_sign === '1' ? '已签收' : '未签收' || '-'),
        },
        {
          title: '签收时间',
          align: 'center',
          render: (h, { row }) => h('span', {}, data_format(row.sign_time) || '-'),
          // render: (h, { row }) => h('span', {}, row.express_no || '-')
        },
        {
          title: '签收方式',
          key: 'code',
          align: 'center',
          renderHeader: (h, params) =>
            h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItem: 'center',
                },
              },
              [
                h(
                  'Tooltip',
                  {
                    props: {
                      theme: 'dark',
                      placement: 'top',
                    },
                  },
                  [
                    h('Icon', {
                      props: {
                        type: 'md-help-circle',
                      },
                      style: {
                        cursor: 'pointer',
                        fontSize: '16px',
                        marginRight: '4px',
                      },
                    }),
                    h(
                      'div',
                      {
                        slot: 'content',
                        class: 'text-inner',
                        style: {
                          maxWidth: '500px',
                        },
                      },
                      [
                        h('div', {}, '物流签收：物流快递单签收。'),
                        h('div', {}, '手动签收：点击“确认收货”按钮，完成签收。'),
                        h('div', {}, '入库签收：手动完成入库操作，系统自动签收。'),
                        h('div', {}, '超时签收：商品出库后7天，系统自动签收。'),
                        h('div', {}, '无需签收：当商品的发货方式为自提或无需发货时，无需签收。'),
                      ]
                    ),
                  ]
                ),
                params.column.title,
              ]
            ),
          render: (h, { row }) => h('span', {}, row.sign_type_desc || '-'),
        },
        {
          title: '物流回传/录入时间',
          key: 'code',
          align: 'center',
          render: (h, { row }) => h('span', {}, data_format(row.ship_time) || '-'),
        },
      ],
      afterSaleColumns: [
        { title: '售后单号', key: 'apply_code', align: 'center' },
        { title: '售后类型', key: 'refund_type_text', align: 'center' },
        { title: '退款商品', slot: 'out_goods', align: 'center' },
        { title: '退款金额', slot: 'refund_money', align: 'center' },
        {
          title: '换货情况',
          key: 'change_state_desc',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '系统暂不支持换货功能，这里仅起到标记作用。'),
          isExchange: true,
        },
        { title: '售后原因', key: 'refund_reason', align: 'center' },
        { title: '售后说明', slot: 'refund_desc', align: 'center' },
        { title: '状态', slot: 'status_text', align: 'center' },
        { title: '申请时间', slot: 'time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      afterSaleList: [],
      express_info: [],
      logisticsVisible: false,
      progress_express_detail: [],
      promotion: {},
      placeHol: '',
      order_code: this.$route.query.order_code,
      tipVisible: false,
      applyAfterSaleVisible: false,
      activeGoodsType: 'goodsList',
      showIcon: false,
      orderType: '',
      isJiShanChannel: isJiShanChannel(),
    };
  },
  computed: {
    showExchange() {
      let orderTypes = ['RXJ_ADDITION', 'CLI_ADDITION', 'COM_OWN', 'COM_PERSONAL'];
      return orderTypes.includes(this.orderType);
    },
    rowMuty() {
      return function (list) {
        let str = '';
        list.length &&
          list.forEach((v, i) => {
            if (i !== list.length - 1) {
              str += `${v.goods_name}*${v.num}，`;
            } else {
              str += `${v.goods_name}*${v.num}`;
            }
          });

        return str;
      };
    },
    isOpcPay() {
      return this.orderInfo.is_opc_pay === '1';
    },
  },
  watch: {},
  created() {
    console.log(this.order_code);
    const { pur_code } = this.$route.query;
    if (pur_code) {
      this.pur_code = pur_code;
      this.getDetail();
    }
    this.getApplyTableList();
  },
  mounted() {
    if (this.$route.query.openType === 'receipt') {
      this.goodsTabChange(this.typeList[1].key);
    }
    // this.goodsTabChange(this.typeList[1].key);
  },
  methods: {
    getDetail() {
      this.$api.getPurchaseInfo({ pur_code: this.pur_code }).then(({ info }) => {
        console.log('-> info', info);
        this.orderInfo = info;
        this.type = info.type;
        if (info.type === 'RXJ_ADDITION') {
          this.tableColumns[6].title = '采购单价';
          this.tableColumns[8].title = '采购合计';
        }
        this.orderType = info.type;
        this.echoData.status_text = info.status_text;
        this.echoData.status = info.status;
        this.echoData.pay_info = info.pay_info;
        this.echoData.clinic_name = info.clinic_name;
        this.echoData.code = info.code;
        this.echoData.type_text = info.type_text;
        this.echoData.pur_main_name = info.pur_main_name;
        this.echoData.com_price = info.com_price;
        this.echoData.cli_price = info.cli_price;
        this.echoData.personal_price = info.personal_price;
        this.echoData.service_price = info.price;
        this.echoData.pack_order_code = info.pack_order_code;
        this.echoData.remark = info.remark;
        this.echoData.create_time = info.create_time;
        this.echoData.status = info.status;
        this.echoData.reject_reason = info.reject_reason;
        this.echoData.organization_name = info.organization_name;
        this.echoData.sign_status_desc = info.sign_status_desc;
        this.echoData.can_sign = info.can_sign;
        this.goods_items = info.items;
        this.express_list = info.express_list;
        this.echoData.mobile = info.consignee_info.mobile;
        this.echoData.consignee = info.consignee_info.consignee;
        this.goods_items_copy = cloneDeep(info.items);
        if (!this.showExchange) {
          this.afterSaleColumns = this.afterSaleColumns.filter(item => !item.isExchange);
        }
        if (this.type !== 'P_MEETING') {
          this.echoData.address = `${info.consignee_info.prov.name} ${info.consignee_info.city.name} ${
            info.consignee_info.county.name || ''
          }  ${info.consignee_info.detail}`;
        }
        // if (info.pack_order_code) {
        // this.getPackageInfo(info.pack_order_code);
        // }
        if (this.type === 'P_MEETING') {
          this.promotion = info.promotion;
        }
        this.express_info = info.express_info;
        if (this.$route.query.openType === 'receipt') {
          this.setTabListToTop(this.$refs.tabList);
        }
      });
    },
    setTabListToTop(ele) {
      this.$nextTick(() => {
        function getElementTop(element) {
          let actualTop = element.offsetTop;
          let current = element.offsetParent;

          while (current !== null) {
            actualTop += current.offsetTop;
            current = current.offsetParent;
          }

          return actualTop;
        }

        const elementTop = getElementTop(ele);
        window.scrollTo({
          top: elementTop - 56 - 5,
        });
      });
    },
    getTag(indexArr) {
      if (indexArr !== undefined) {
        // this.showTypeList = [this.typeList[index]];
        this.showTypeList = this.typeList.filter((v, i) => {
          return indexArr.includes(i);
        });
        // this.goodsTabChange(this.typeList[indexArr[0]].key);
      } else {
        this.showTypeList = this.typeList;
      }
    },
    goodsTabChange(type) {
      this.activeGoodsType = type;
    },
    // getPackageInfo(order_code) {
    //   this.$api.getPackageInfo({order_code}).then(res => {
    //     console.log("-> res", res);
    //     this.local_goods_items = res.items;
    //   });
    // },
    getApplyTableList() {
      this.$api.getApplyTableList({ order_code: this.pur_code }).then(res => {
        console.log(res, 'resssss');
        this.afterSaleList = res.list;

        if (res.list && res.list.length) {
          this.getTag();
        } else {
          this.getTag([0, 1]);
        }
      });
    },
    editPurchaseOrder() {
      this.editModalVisible = true;
    },
    handleSummary({ columns, data }) {
      const sums = {};

      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          console.log('index', index, key);
          sums[key] = {
            key,
            value: '合计',
          };
          return;
        }

        let whiteList = [];
        let type = this.type;
        let whiteTypeList = ['CLI_ADDITION', 'CLI_OPENING', 'RXJ_OPENING', 'RXJ_ADDITION'];
        let sumAdd = [];
        if (whiteTypeList.includes(type)) {
          // whiteList = [9, 10, 11, 12];
          whiteList = [7, 8, 9, 10, 11];
          sumAdd = [7, 9, 10];
        } else if (type === 'COM_OWN') {
          whiteList = [7, 8];
        } else if (type === 'COM_PERSONAL') {
          whiteList = [10, 11, 12];
        } else if (type === 'P_MEETING') {
          whiteList = [8, 9, 10, 11];
        }
        console.log(whiteList);

        if (whiteList.indexOf(index) > -1) {
          const values = data.map(item => Number(item[key]));
          console.log(values, 'value', key);
          if (!values.every(value => isNaN(value))) {
            const v = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return $operator.add(prev, curr);
              } else {
                return prev;
              }
            }, 0);
            if (sumAdd.includes(index)) {
              sums[key] = {
                key,
                value: v,
              };
            } else {
              sums[key] = {
                key,
                value: $operator.add(v, 0),
              };
            }
          } else {
            sums[key] = {
              key,
              value: '-',
            };
          }
        } else {
          sums[key] = {
            key,
            value: '',
          };
        }
      });
      return sums;
    },
    tabChange(tab) {
      this.tableTab = tab;
    },

    showExpress(row) {
      console.log('row', row);
      this.progress_express_detail = [row];
      this.logisticsVisible = true;
    },
    pringFunc() {
      console.log(this.$refs.print);
      this.$print(this.$refs.print);
    },
    applyAfterSale() {
      if (this.orderInfo.can_refund_apply_num === '0') {
        this.placeHol = '当前订单全部商品已完成售后或正在售后中，暂时无法重复发起';
        this.showIcon = true;
        this.tipVisible = true;
        return;
      }
      if (this.orderInfo.can_refund_apply_tip === '1') {
        // this.$Message.error("该订单状态不可申请售后");
        this.placeHol = '暂不支持线上发起售后';
        this.showIcon = false;
        this.tipVisible = true;
        return;
      }
      this.applyAfterSaleVisible = true;
    },
    setTagFunc() {
      this.getApplyTableList();
      this.getDetail();
      this.goodsTabChange(this.typeList[2].key);
    },
    handleSpan({ row, column, rowIndex, columnIndex }) {
      // 合并单元格起始位置
      let firstBiginColsIndex = 0;
      // 合并单元格数量
      let firstColsNum = 1;
      // 合并单元格起始位置
      let secondBiginColsIndex = 0;
      // 合并单元格数量
      let secondColsNum = 1;

      // 一级单元格合并
      // [0,1,2]代表从第一行开始合并三行
      // [2,3,4,5]代表从第三行开始合并四行
      let includeFirstIndex = [];
      this.express_info.forEach((item, index) => {
        if (item.ship_unique === row.ship_unique) {
          includeFirstIndex.push(index);
        }
      });
      if (includeFirstIndex.includes(rowIndex)) {
        firstBiginColsIndex = includeFirstIndex[0];
        firstColsNum = includeFirstIndex.length;
      }
      // 二级单元格合并
      let includeSecondIndex = [];
      this.express_info.forEach((item, index) => {
        if (item.goods_unique === row.goods_unique) {
          includeSecondIndex.push(index);
        }
      });
      if (includeSecondIndex.includes(rowIndex)) {
        secondBiginColsIndex = includeSecondIndex[0];
        secondColsNum = includeSecondIndex.length;
      }

      //---------------------------------------------------------------------这部分代码不用动-
      //合并一级单元格
      if (firstColsNum + firstBiginColsIndex > this.express_info.length) {
        console.error(new Error('合并列数超出表格列数'));
        return;
      }

      if (columnIndex === 0 && rowIndex === firstBiginColsIndex) {
        return [firstColsNum, 1];
      } else {
        if (columnIndex === 0 && rowIndex > firstBiginColsIndex && rowIndex < firstColsNum + firstBiginColsIndex) {
          return [0, 0];
        }
      }

      //合并二级单元格
      if (secondColsNum + secondBiginColsIndex > this.express_info.length) {
        console.error(new Error('合并列数超出表格列数'));
        return;
      }

      if (columnIndex === 1 && rowIndex === secondBiginColsIndex) {
        return [secondColsNum, 1];
      } else {
        if (columnIndex === 1 && rowIndex > secondBiginColsIndex && rowIndex < secondColsNum + secondBiginColsIndex) {
          return [0, 0];
        }
      }
    },
    confirmExpress(item) {
      console.log(item);
      let { ship_code, pack_index, deliver_index } = item;
      console.log({
        ship_code,
        pack_index,
        deliver_index,
      });
      this.$Modal.confirm({
        title: '是否确认收货？',
        loading: true,
        onOk: () => {
          io.post('/clinic/joinin.purchase.signDeliver', {
            ship_code,
            pack_index,
            deliver_index,
          }).then(
            res => {
              this.$Message.success('确认收货成功');
              this.$Modal.remove();
              this.getDetail();
              this.getApplyTableList();
            },
            err => {
              {
              }
              this.$Modal.remove();
            }
          );
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel');
        },
      });
    },
    confirmPass() {
      this.$Modal.confirm({
        title: '是否确认全部收货？',
        loading: true,
        onOk: () => {
          io.post('/clinic/joinin.purchase.signWhole', {
            pur_code: this.pur_code,
          }).then(
            res => {
              this.$Message.success('全部确认收货成功');
              this.$Modal.remove();
              this.getDetail();
              this.getApplyTableList();
            },
            err => {
              {
              }
              this.$Modal.remove();
            }
          );
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel');
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  .basic-info {
    border: 1px solid #b7b7b7;

    .basic-info-item {
      display: flex;
      line-height: 22px;
      border-bottom: 1px solid #b7b7b7;

      &:last-of-type {
        border-bottom: none;
      }

      text-align: center;

      .item-label {
        width: 180px;
        border-right: 1px solid #b7b7b7;
        background: #e6e6e6;
      }

      .item-content {
        flex: 1;
        text-align: left;
        text-indent: 30px;
      }
    }
  }

  .goods-list {
    padding-top: 20px;
  }

  .logistics-information {
    margin-bottom: 30px;

    .logistics-info-box {
      padding: 12px 12px 6px;
      border: 1px solid #e7e7e7;
      width: 260px;
      margin: 12px;
      background: rgba(225, 225, 225, 0.3);

      .title {
        font-size: 15px;
        font-weight: 600;
        line-height: 22px;
      }

      .info-item {
        margin-bottom: 20px;
      }

      .foot-tip {
        line-height: 20px;
      }
    }
  }
}

.table-wrapper {
  padding: 10px 0 20px;
}

.cursor {
  cursor: pointer;
}

.scale {
  &:hover {
    transform: scale(1.2);
  }
}

.expressBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;

  .search {
    cursor: pointer;
  }
}

.ml10 {
  margin-left: 10px;
}
</style>
<style>
.ivu-tooltip-inner {
  max-width: 400px;
}
</style>
