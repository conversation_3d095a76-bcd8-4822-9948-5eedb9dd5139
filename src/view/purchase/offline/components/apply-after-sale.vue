<template>
  <div>
    <Modal :value="value" title="申请售后" width="980px" :mask-closable="false" @on-visible-change="changeVisible">
      <div class="modal-content">
        <Form ref="formData" :label-width="80" :label-colon="true" :model="formData">
          <!-- v-if="!getBackMap.includes(detail.type)" -->
          <FormItem label="售后类型" required>
            <RadioGroup v-model="formData.refund_type" @on-change="typeChange">
              <Radio label="M" v-if="!getBackMap.includes(detail.type)">仅退款</Radio>
              <Radio label="GM" :disabled="isAllBack">退货退款</Radio>
            </RadioGroup>
          </FormItem>

          <FormItem label="收货情况" required v-if="formData.refund_type == 'GM'">
            <RadioGroup v-model="formData.delivery_way">
              <Radio label="HAS">已收到货</Radio>
              <Radio label="NOT">未收到货</Radio>
            </RadioGroup>
          </FormItem>

          <FormItem label="售后商品明细" required :label-width="100">
            <div class="flex flex-item-end">
              <Select
                v-model="formData.goods"
                style="width: 280px"
                v-if="!isAllBack"
                :placeholder="moneyHeader.get(formData.refund_type)"
                @on-change="changeClear"
                :clearable="true"
                ref="resetSelect"
              >
                <Option v-for="item in showOptionList" :key="item.spu_code" :value="item.spu_code" :label="item.name">
                  <div class="flex">
                    <span>{{ item.name }}</span>
                    <span style="font-size: 12px; color: #333333" v-show="item.spec_desc">
                      <span style="margin: 0 4px">-</span> {{ item.spec_desc }}</span
                    >
                  </div>
                </Option>
              </Select>
            </div>
          </FormItem>

          <Table
            :columns="goodsColumns"
            :data="showGoodList"
            :loading="goodsLoading"
            :show-summary="true"
            :summary-method="handleSummary"
            stripe
            border
            class="ml21"
          >
            <template slot-scope="{ index }" slot="addNum">
              <Input-Number
                :min="1"
                :max="getMaxNum(index)"
                v-model="showGoodList[index].addNum"
                placeholder="请输入数量"
                :precision="0"
                @on-change="numberChange($event, index)"
                style="width: 64px"
                :disabled="isAllBack"
                :active-change="false"
              />
            </template>
            <template slot-scope="{ row, index }" slot="pur_price">
              {{ getPurPrice(row) }}
            </template>
            <template slot-scope="{ row }" slot="spec_desc">
              <div>
                {{ row.spec_desc || '-' }}
              </div>
            </template>

            <template slot-scope="{ index }" slot="shipped_wait_num">
              <div>
                {{ getMaxNum(index) }}
              </div>
            </template>

            <template slot-scope="{ index }" slot="setting">
              <div>
                <!-- <Button type="text">删除</Button> -->
                <a v-if="!isAllBack" style="color: red" @click="delCol(index)">删除</a>
                <span v-else>-</span>
              </div>
            </template>
          </Table>

          <FormItem label="诊所退款金额" :label-width="104" v-if="!isOpcPay">
            {{ allPrice ? `￥${allPrice}` : '￥0.00' }}
            <!-- {{ allPrice }} -->
          </FormItem>
          <FormItem label="直营运营中心退款金额" :label-width="140" v-if="isOpcPay">
            {{ allPrice ? `￥${allPrice}` : '￥0.00' }}
            <!-- {{ allPrice }} -->
          </FormItem>
          <FormItem label="售后原因" required>
            <Select v-model="formData.refund_reason">
              <Option v-for="item in reasonOptions" :key="item.kw" :value="item.kw" :label="item.desc"></Option>
            </Select>
          </FormItem>

          <FormItem label="售后说明">
            <Input
              v-model="formData.refund_desc"
              type="textarea"
              :maxlength="200"
              :show-word-limit="true"
              :autosize="{ minRows: 3, maxRows: 5 }"
              placeholder="请说明诊所的退款原因"
            ></Input>
          </FormItem>

          <FormItem label="附件">
            <MaterialPicture v-model="formData.imgs" :limit="9" />
            <div>支持上传照片，单张图片大小不超过3.0M，最多9张</div>
          </FormItem>
        </Form>
      </div>

      <div slot="footer" class="edit-footer">
        <Button type="default" @click="closeModal">取消</Button>
        <Button type="primary" @click="confirm">确定</Button>
      </div>
    </Modal>

    <!--  提示框  -->
    <k-tip-modal v-model="tipVisible" text="售后申请提交成功，请等待审核"></k-tip-modal>
  </div>
</template>

<script>
import kTipModal from '@/components/k-tip-modal';
import { cloneDeep  } from 'lodash-es';
import { $operator } from '@/libs/operation';

const ourderreFound = 'getOrderFund';

export default {
  name: 'apply-after-sale',
  components: {
    kTipModal,
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false,
    },
    detail: {
      type: Object,
      default: () => {},
    },
    isOpcPay: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      tipVisible: false,
      formData: {
        // type: "",
        // receipts: "",
        // return_money: "",
        // reason: "",
        // remark: "",
        // images: []
      },
      getBackMap: ['WAIT_SHIP', 'COM_W_PAY'],
      companyMap: ['WAIT'],
      // 售后商品明细
      goodsColumns: [
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '商品类型', key: 'type_text', align: 'center' },
        // 单位线下售后暂无,弹窗复用暂留
        { title: '规格', key: '', slot: 'spec_desc', align: 'center' },
        { title: '状态', key: 'shipped_status', align: 'center' },
        { title: '采购单价', slot: 'pur_price', align: 'center' },
        {
          title: '可退数量',
          slot: 'shipped_wait_num',
          key: 'shipped_wait_num',
          align: 'center',
        },
        { title: '退款数量', key: 'addNum', slot: 'addNum', align: 'center' },
        {
          // title: '诊所退款金额',
          key: 'priceNum',
          align: 'center',
          width: 100,
          renderHeader: (h, params) => {
            return h('span', {}, `${this.isOpcPay ? '直营运营中心退款金额' : '诊所退款金额'}`);
          },
          // renderHeader: ()
        },
        { title: '操作', key: 'setting', slot: 'setting', align: 'center' },
      ],
      goodsList: [],
      showGoodList: [],
      optionList: [],
      showOptionList: [],
      goodsLoading: false,
      isAllBack: false,
      reasonOptions: [],
      allPrice: null,
      moneyHeader: new Map([
        ['M', '添加未发货商品'],
        ['GM', '添加已发货商品'],
      ]),
    };
  },
  computed: {
    getMaxNum() {
      return function (index) {
        return Number(this.showGoodList[index]['can_apply_num_info'][this.formData['refund_type']]);
      };
    },
    getPurPrice() {
      return row => {
        if (this.isOpcPay) {
          return `￥${$operator.toPrecision(row.com_price, 2)}`;
        } else {
          return `￥${$operator.toPrecision(row.cli_price, 2)}`;
        }
      };
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // TODO 表单相关
    initData() {
      // console.log(this.detail, "detail");
      this.$api
        .getOfflineGoods({
          pur_code: this.detail.code,
        })
        .then(res => {
          // this.goodsList = res.list;
          this.optionList = res.list;
          this.showOptionList = res.list;
          // this.showGoodList = res.list;

          this.formatForm();
          // this.formData.return_money = res.data.return_money;
        })
        .catch(err => {
          console.log(err);
          {
          }
        });

      this.$api[`${ourderreFound}options`]({
        order_code: this.detail.order_code,
      })
        .then(res => {
          console.log(res, 'ssss');
          let keys = res.refundReasonDesc;
          let arr = Object.keys(res.refundReasonDesc)?.map(v => ({
            desc: keys[v].desc,
            kw: keys[v].kw,
          }));
          console.log(arr, 'this is arr');
          this.reasonOptions = arr;
          // this.goodsList = res.list;
          // this.optionList = res.list;
          // this.formData.return_money = res.data.return_money;
        })
        .catch(err => {
          console.log(err);
          {
          }
        });
    },
    formatForm() {
      let refund_type = 'M';
      if (this.getBackMap.includes(this.detail.type)) {
        refund_type = 'GM';
      }
      // 针对省公司待付款状态的处理
      this.formData = {
        refund_type,
        delivery_way: 'HAS',
      };
      console.log(this.formData, 'this.formData----------');
      if (this.companyMap.includes(this.detail.com_pay_status)) {
        this.isAllBack = true;
        console.log(refund_type, 'refund_type');
        this.showGoodList = this.getOptionList(refund_type);
        this.getNumPrice(true);
      }
      // this.goodsColumns[5].title = moneyMap.get(refund_type);
      // console.log(this.getOptionList("GM"), "arrrrrrrrrrrrr");
      this.showOptionList = this.getOptionList(refund_type);
    },
    /*
     * @description: 初始化表单数据
     * @param {Object} data 表单数据
     * @param {Array} keys 需要清空的字段
     * @return: void
     * ps:什么都不传就是清空表单
     */
    formDataInit(data, keys = []) {
      if (!data) {
        this.formData = {};
      }
      this.formData = {
        ...this.formData,
        ...data,
      };
      keys.length &&
        keys.forEach(key => {
          this.formData[key] = '';
        });
    },
    typeChange(val) {
      this.formDataInit({ refund_goods: [], goods: '' });
      this.showOptionList = this.getOptionList(val);
      this.showGoodList = [];
      this.getNumPrice();
      // this.goodsColumns[5].title = moneyMap.get(this.formData.refund_type);
    },
    getOptionList(key) {
      let data = cloneDeep(this.optionList);
      return data?.filter(item => Number(item.can_apply_num_info[key]) > 0) || [];
    },
    changeClear(value) {
      this.$refs.resetSelect.clearSingleSelect();
      const item = this.showOptionList?.find(v => v.spu_code === value) || null;
      console.log(item);
      console.log(!!this.showGoodList?.find(v => v.spu_code === value));
      if (!!this.showGoodList?.find(v => v.spu_code === value)) {
        this.$Message.error('该商品已添加');
        return;
      }
      console.log(item, 'item');
      if (item) {
        this.showGoodList.push(item);
        this.getNumPrice();
      }
    },

    //TODO 表格相关
    // 合计
    handleSummary({ columns, data }) {
      const sums = {};
      console.log(columns);
      console.log(data);
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计',
          };
          return;
        }
        let whiteList = [7, 8];
        let docList = [8];
        if (whiteList.includes(index)) {
          const values = data.map(item => Number(item[key]));
          console.log(values);
          if (!values.every(value => isNaN(value))) {
            const v = values.reduce((prev, curr) => {
              const value = Number(curr).toFixed(2);
              // console.log(
              //   "-> %c value  ===    %o",
              //   "font-size: 15px;color: #F56C6C ;",
              //   value
              // );
              if (!isNaN(value)) {
                return $operator.add(Number(prev), Number(curr));
              } else {
                return prev;
              }
            }, 0);
            console.log(v, 'vvvvv');
            if (docList.includes(index)) {
              sums[key] = {
                key,
                value: `￥${v}`,
              };
            } else {
              sums[key] = {
                key,
                value: v,
              };
            }
          }
        } else {
          sums[key] = {
            key,
            value: '',
          };
        }
      });
      console.log(sums);

      return sums;
    },
    addLogistics() {
      this.formData.return_logistics.push({ express_delivery: '', code: '' });
    },
    deleteLogistics(index) {
      this.formData.return_logistics.splice(index, 1);
    },
    // todo 商品数量改变，对应合计发生变化
    numberChange(e, index) {
      console.log(this.showGoodList);
      this.getNumPrice();
      // this.add_goods[index].num = e
      // this.caclTotalPrice( index )
    },
    delCol(index) {
      console.log(234);
      this.showGoodList.splice(index, 1);
      this.getNumPrice();
    },
    getNumPrice(isAllnum) {
      let newArr = this.showGoodList.length ? this.showGoodList : [];
      let nums = 0;
      newArr.forEach(item => {
        let num = null;
        item?.priceNum || this.$set(item, 'priceNum', num);
        if (!item.hasOwnProperty('addNum')) {
          this.$set(item, 'addNum', null);
        }
        item.priceNum = this.isOpcPay
          ? $operator.multiply(Number(item.addNum), Number(item.com_price))
          : $operator.multiply(Number(item.addNum), Number(item.cli_price));
        if (isAllnum) {
          item.addNum = Number(item['can_apply_num_info'][this.formData['refund_type']]);
        }
        num = $operator.multiply(Number(item.addNum), Number(item.clinic_price));
        nums = $operator.add(Number(nums), Number(item.priceNum));
      });
      this.allPrice = nums.toFixed(2);
      console.log(newArr, 'newArr');
      this.showGoodList = newArr;
    },

    // TODO 提交相关
    // 校验
    validateFormData() {
      if (!this.formData.refund_type) {
        this.$Message.error('请选择收货类型');
        return false;
      }
      if (this.formData.refund_type == 'GM' && !this.formData.delivery_way) {
        this.$Message.error('请选择收货情况');
        return false;
      }
      if (!this.showGoodList.length) {
        this.$Message.error('请选择售后商品');
        return false;
      }

      if (!this.showGoodList.every(v => Number(v.addNum) > 0)) {
        this.$Message.error('商品退款数量不能为空');
        return false;
      }

      if (!this.formData.refund_reason) {
        this.$Message.error('请选择售后原因');
        return false;
      }
      // if (!this.goodsList.length) {
      //   this.$Message.error("请选择售后商品");
      //   return false;
      // }
      return true;
    },
    confirm() {
      if (this.validateFormData()) {
        // 提交数据和表单数据做拆分，无强关联
        const subData = this.getSubmitData(this.formData);
        console.log(subData);

        this.$api
          .submitOfflineGoods({ ...subData })
          .then(res => {
            console.log(res);
            if (res) {
              this.closeModal();
              // this.$Message.success("提交成功");
              this.tipVisible = true;
              this.$emit('successFunc');
            }
          })
          .catch(err => {
            console.log(err);
            {
            }
          });
      }
    },

    /*
     * @Description: 设置提交数据
     * return: {Object} 提交数据
     */
    getSubmitData(formData) {
      let subData = cloneDeep(formData);
      subData.refund_goods = this.showGoodList.map(item => {
        return {
          spu_code: item.spu_code,
          num: item.addNum,
          refund_money: item.priceNum,
        };
      });

      if (subData.refund_type === 'M') {
        delete subData.delivery_way;
      }

      subData.pur_code = this.detail.code;

      delete subData.goods;

      return subData;
    },

    // TODO Model相关
    changeVisible(visible) {
      console.log(cloneDeep(this.detail), 'detail');
      this.showGoodList = [];
      this.formDataInit();
      this.allPrice = 0;
      // console.log(this.showGoodList, "showGoodList");
      visible && this.initData();
      !visible && this.closeModal();
    },
    // 关闭弹窗
    closeModal() {
      this.$emit('input', false);
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  max-height: 600px;
  min-height: 400px;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  .ivu-form-item {
    margin-bottom: 10px;
  }
}

.ml21 {
  margin-left: 21px;
}
</style>
