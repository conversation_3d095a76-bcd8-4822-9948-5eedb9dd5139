<template>
  <div class="create-order-wrapper">
    <div class="goods-box-wrapper">
      <div class="search-box" ref="searchBoxRef">
        <div class="current-user">
          <div class="current-user-left">
            <div class="avatar-box" :class="{ 'vip-avatar-box': current_user_info.is_vip == 1 }">
              <img
                v-if="current_user_info.is_vip == 1"
                class="vip-icon"
                src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0927/151759_70656.png"
              />
              <img
                class="avatar"
                :src="
                  current_user_info.avatar
                    | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
                "
              />
            </div>

            <div class="user-info">
              <div class="user-info-top">
                <div class="user-info-name">{{ current_user_info.real_name }}</div>
                <div class="user-info-sex">
                  <span>{{ current_user_info.sex_text }}</span>
                  <span v-if="current_user_info.sex_text && current_user_info.age">｜</span>
                  <span>{{ current_user_info.age ? `${current_user_info.age}岁` : current_user_info.age }}</span>
                </div>
              </div>
              <div class="user-info-mobile-box">
                <div class="info-mobile">{{ current_user_info.mobile }}</div>
                <div class="info-stage-mobile" v-if="current_user_info.show_staging_mobile == '1'">
                  <span class="stage-tag">暂存</span>
                  <span class="stage-mobile">{{ current_user_info.staging_mobile }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="clinic-goods-area" :style="getGoodsBoxStyle">
        <div>
          <div class="goods-tab-box">
            <Tabs v-model="tabActive" @on-click="tabClick">
              <TabPane
                v-for="item in tabList"
                :label="item.label"
                :name="item.name"
                :key="item.name"
                :index="item.sort"
              >
              </TabPane>
            </Tabs>
            <div class="search-goods-box">
              <Input
                v-model="search_keyword"
                placeholder="搜索输入服务名称"
                prefix="ios-search"
                clearable
                autocomplete="off"
                style="width: 240px"
              />
              <div style="position: fixed; left: -200px; top: -200px">
                <Input type="text" name="username"></Input>
              </div>
            </div>
          </div>
        </div>

        <div class="goods-box" ref="goodsArea">
          <div class="goods-box-nav hidden-scroll" ref="navScrollRef" v-if="!search_keyword">
            <div
              class="goods-box-nav-item"
              :class="{
                'goods-box-nav-item--active': index == goodsTabActive,
              }"
              v-for="(item, index) in goodsTabList"
              :key="index"
              :ref="'nav' + index"
              @click="goodsTabChange(index)"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="goods-box-area">
            <Scroll class="goods-source-box" :height="scrollHeight">
              <!-- 服务 -->
              <service-content
                ref="serviceRef"
                v-if="tabActive === 'service' && (serviceList.length || loading)"
                :list="serviceList"
                :search_keyword="search_keyword"
                :scrollTop="scrollTop"
                :add_goods="add_goods"
                @addService="addService"
                @getIndex="getIndex"
                :disabled="isCreateOrder"
              ></service-content>
              <div class="empty-img-box" v-else>
                <img class="empty-img" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/180054_55080.png" />
              </div>
            </Scroll>
          </div>
        </div>
      </div>
    </div>
    <div class="car-box-wrapper" ref="carBoxRef">
      <!--      <div class="order-info" v-if="detailInfo.status === '1100'">-->
      <!--        <div class="info-item">订单编号：{{ detailInfo.shop_order || '-' }}</div>-->
      <!--        <div class="info-item">-->
      <!--          订单状态：{{ detailInfo.status_desc }}<span style="margin-left: 6px; color: red">({{ formattedTime }})</span>-->
      <!--        </div>-->
      <!--        <div class="info-item">下单时间：{{ detailInfo.create_at }}</div>-->
      <!--      </div>-->
      <div class="car-top">
        <div class="title">消费明细（{{ add_goods.length }}）</div>
        <Poptip confirm width="180" title="确定清空所有服务?" @on-ok="clearAllGoods">
          <div class="car-clear-all" v-if="!isCreateOrder">清空</div>
        </Poptip>
      </div>

      <Scroll class="car-item-box" :height="carScrollHeight">
        <div class="car-item-box-content" ref="carScrollRef">
          <div class="car-item-box-goods" v-if="add_goods.length">
            <div class="car-item" v-for="(item, index) in add_goods" :key="index">
              <div class="delete-icon-box" v-if="!isCreateOrder">
                <Tooltip content="移除" placement="top">
                  <img
                    class="delete-icon"
                    @click="deleteAddGoods(index)"
                    src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/102709_78163.png"
                  />
                </Tooltip>
              </div>
              <div class="car-item-top">
                <div class="car-item-left">
                  <div class="car-item-l-name-box">
                    <div class="car-item-l-name ecs ecs-2">{{ item.name }}</div>
                  </div>
                </div>

                <div class="car-item-right">
                  <div class="car-item-count">
                    <InputNumber
                      class="custom-input-number"
                      :min="1"
                      v-model="item.num"
                      disabled
                      :precision="0"
                      controls-outside
                      autocomplete="off"
                    />
                  </div>

                  <div class="car-item-price-box">
                    <div class="car-item-price">
                      <span class="price-icon">¥</span>
                      <span>{{ calcCarPirce(item.num, item.price) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="physical-box">
                <div class="physical-label">{{ getPhysioName }}</div>
                <div class="physical-name">
                  <div v-if="isCreateOrder">
                    <span v-if="item.physical_name">{{ item.physical_name }}</span>
                    <span v-else style="color: #ccc">请选择服务</span>
                    <Icon type="ios-arrow-down" />
                  </div>
                  <physic-poptip
                    v-else
                    :reserve_id="detailInfo.id"
                    :id="item.physio_id"
                    :service_id="item.id"
                    :reserve_time="detailInfo.reserve_time"
                    :date="detailInfo.reserve_date"
                    :duration="detailInfo.duration.minute"
                    @change="physicalChange($event, index)"
                  >
                    <div class="cursor">
                      <span v-if="item.physical_name">{{ item.physical_name }}</span>
                      <span v-else style="color: #ccc">请选择服务</span>
                      <Icon type="ios-arrow-down" />
                    </div>
                  </physic-poptip>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-img-box car-item-box-goods">
            <img class="empty-img" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/180054_55080.png" />
          </div>
        </div>
      </Scroll>

      <div class="fixed-btn-box" ref="fixedBtnRef">
        <div class="fixed-btn-left">
          <div class="fixed-btn-num">
            <span class="num">共{{ addGoodsCalc.num }}件，</span>总计：
          </div>
          <div class="fixed-btn-price">
            <div class="fixed-total-price">
              <span>¥</span>
              <span class="store-price">{{ Number(addGoodsCalc.price || 0).toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <div class="fixed-btn-right">
          <BackButton>取消</BackButton>
          <Button
            type="primary"
            style="margin-left: 10px"
            v-if="detailInfo.is_suspend !== '1' && detailInfo.status === '1000'"
            @click="hangingOrder"
            :loading="hangingOrderLoading"
            >挂单
          </Button>
          <Button
            type="primary"
            style="margin-left: 10px"
            :loading="payLoading"
            v-if="detailInfo.status === '1000' || detailInfo.status === '1100'"
            @click="submit"
            >收款</Button
          >
        </div>
      </div>
    </div>

    <!-- v2版本支持优惠券的支付弹窗 -->
    <!-- v2版本支持优惠券的支付弹窗 -->
    <template v-if="is_rst">
      <rst-pay-dialog v-model="rstPayVisible" :orderId="order_id"></rst-pay-dialog>
    </template>
    <template v-else>
      <k-pay-dialog v-model="payVisible" :order_id="order_id" :is_rst="is_rst"></k-pay-dialog>
    </template>
    <!-- 确认到店 -->
    <confirm-arrival v-model="arrivalVisible" :id="detailInfo.id" @success="arrivalSuccess"></confirm-arrival>
  </div>
</template>

<script>
import moment from 'moment';
import { getPhysioName } from '@/libs/runtime';
import S from '@/libs/util';
import { $operator } from '@/libs/operation';
import { cloneDeep } from 'lodash-es';
import { debounce } from 'lodash-es';
import KPayDialog from '@/components/k-pay-dialog/index.vue';
import serviceContent from './components/serviceContent.vue';
import physicPoptip from '@/components/addReserveCom/components/physicPoptip';
import confirmArrival from './components/confirmArrival.vue';
import { isRstClinic } from '@/libs/runtime';
import RstPayDialog from '@/components/k-pay-dialog/rst-pay-dialog.vue';

export default {
  name: '',
  components: { serviceContent, KPayDialog, physicPoptip, confirmArrival, RstPayDialog },
  mixins: [],
  data() {
    return {
      goodsTabList: [],
      scrollHeight: 300,
      carScrollHeight: 300,
      add_goods: [], // 表格加入的商品

      current_user_info: {}, // 当前用户

      payVisible: false, // 支付弹窗显示的标识
      rstPayVisible: false, // 支付弹窗显示的标识
      order_id: '', // 订单id,
      loading: false,

      scrollTop: 0,

      // tab
      tabList: [{ label: '服务', name: 'service', sort: 2 }],
      tabActive: 'service',
      goodsTabActive: 0,

      serviceList: [], // 服务列表
      search_keyword: '',
      hangingOrderLoading: false, // 挂单loading
      detailInfo: {},
      payLoading: false,
      arrivalVisible: false,
      freeze_count_down: 0, // 冻结的倒计时
      count_down: 0,
      timer: null, // 定时器
    };
  },
  computed: {
    // 倒计时是否有变化，用户刷新接口
    // countDownIsChange() {
    //   if (this.freeze_count_down != 0 && this.count_down == 0 && !this.payVisible) {
    //     return true;
    //   }
    //   return false;
    // },
    // 将秒数改为指定格式
    // formattedTime() {
    //   return moment.utc(this.count_down * 1000).format('mm:ss');
    // },
    getPhysioName() {
      return getPhysioName();
    },
    is_rst() {
      return isRstClinic();
    },
    isCreateOrder() {
      return Boolean(Number(this.detailInfo.order_id));
    },
    calcCarPirce() {
      return (num, price) => {
        return $operator.multiply(Number(num || 0), Number(price || 0))?.toFixed(2);
      };
    },
    // 暂时留着，当选择用户盒子高度不一致时，可以利用当前方法调整高度
    getGoodsBoxStyle() {
      return {
        height: 'calc(100vh - 230px)',
      };
    },
    // 商品总计算
    addGoodsCalc() {
      let num = 0;
      let price = 0;
      this.add_goods.forEach(item => {
        num = $operator.add(num, Number(item.num || 0));

        let item_price = $operator.multiply(Number(item.price || 0), Number(item.num || 0));
        price = $operator.add(price, item_price);
      });
      return {
        num,
        price,
      };
    },
  },
  watch: {
    payVisible(val) {
      if (!val) {
        this.getReservev2ReserveShow();
      }
    },
    rstPayVisible(val) {
      if (!val) {
        this.getReservev2ReserveShow();
      }
    },
  },
  created() {},
  mounted() {
    // 获取详情
    this.getReservev2ReserveShow();
    this.goodsTabChange(this.goodsTabActive);
    this.getScrolllHeight();
    this.getCarScrollHeight();

    window.addEventListener('resize', this.getScrolllHeight);
    window.addEventListener('resize', this.getCarScrollHeight);

    this.$nextTick(() => {
      // 注册监听服务的滚动
      let overflowY = this.$el.getElementsByClassName('ivu-scroll-container')[0];
      overflowY.addEventListener('scroll', this.serviceScroll);
    });
  },
  methods: {
    // startCountdown() {
    //   // 每秒更新一次倒计时
    //   this.timer = setInterval(() => {
    //     if (this.count_down > 0) {
    //       this.count_down--;
    //     } else {
    //       this.count_down = 0;
    //       // 倒计时结束，清除定时器
    //       clearInterval(this.timer);
    //       if (this.countDownIsChange) {
    //         this.getReservev2ReserveShow();
    //       }
    //     }
    //   }, 1000);
    // },
    // 确认到店成功
    arrivalSuccess() {
      this.collection();
    },
    // 获取理疗师
    physicalChange(val, index) {
      let item = {
        physical_name: val.name || '',
        physio_id: val.id || '',
      };
      this.$set(this.add_goods, index, { ...this.add_goods[index], ...item });
    },
    // 获取详情
    getReservev2ReserveShow() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      let params = {
        id: this.$route.query.id,
      };
      this.add_goods = [];
      this.detailInfo = {};
      this.$api.getReservev2ReserveShow(params).then(res => {
        this.current_user_info = {
          ...res.reserve_user,
          real_name: res.reserve_user.name,
          sex_text: res.reserve_user.sex_desc,
        };
        this.freeze_count_down = cloneDeep(res.count_down);
        this.count_down = res.count_down;
        // this.startCountdown();
        this.detailInfo = res;
        this.handlerEcho(res);
      });
    },
    handlerEcho(data) {
      let services = data.services;
      services?.forEach(item => {
        if (Boolean(Number(item.goods_service_id))) {
          this.add_goods.push({
            id: item.goods_service_id,
            name: item.goods_service.name,
            price: item.goods_service.price,
            physio_id: item.physio_id,
            physical_name: item.physio.name,
            num: 1,
          });
        }
      });
    },
    getIndex(index) {
      if (this.tabActive === 'service' && !this.search_keyword) {
        this.goodsTabActive = index;
        let ref = `nav${index}`;
        this.$refs[ref][0].scrollIntoView({ behavior: 'smooth' });
      }
    },
    serviceScroll() {
      if (this.tabActive === 'service') {
        let overflowY = this.$el.getElementsByClassName('ivu-scroll-container')[0];
        this.scrollTop = overflowY.scrollTop;
        this.$refs.serviceRef.handleNodeScroll();
      }
    },

    // 选中的服务
    addService(item) {
      if (this.add_goods.length > 0) {
        this.$Message.error('只允许添加一个服务');
        return;
      }
      this.addGoods(item);
    },
    tabClick(val) {
      this.getScrolllHeight();
      this.goodsTabActive = 0;
      if (val === 'service') {
        this.getPhysicalTServiceTree();
      }
    },

    // 提交之前对页面必填数据判断
    isSubmit() {
      if (!this.current_user_info.uid) {
        this.$Message.error('请选择下单用户');
        return false;
      }
      if (!this.add_goods.length) {
        this.$Message.error('购买服务不可为空');
        return false;
      }
      for (let item of this.add_goods) {
        if (!Boolean(Number(item.physio_id))) {
          this.$Message.error(`请选择${getPhysioName()}`);
          return false;
        }
      }

      let flag = this.add_goods.some(item => {
        if (item.num === 0 || item.num === null) {
          this.$Message.error(`${item.name}的数量最小为1`);
          return true;
        }
      });

      if (flag) {
        return false;
      }
      return true;
    },

    // 挂单
    hangingOrder() {
      if (this.isSubmit()) {
        this.hangingOrderLoading = true;
        let params = {
          id: this.$route.query.id,
          services: this.handlerServices(),
        };
        this.$api
          .getReservev2ReserveSuspend(params)
          .then(res => {
            this.$Message.success('订单挂单成功');
            // this.$router.push('/reserve/listing/list');
            this.$router.back();
          })
          .finally(() => (this.hangingOrderLoading = false));
      }
    },
    // 收款
    collection() {
      if (this.isSubmit()) {
        this.payLoading = true;
        let params = {
          id: this.$route.query.id,
          services: this.handlerServices(),
        };
        this.$api
          .getReservev2ReserveConfirm(params)
          .then(res => {
            this.order_id = res.order_id;
            if (this.is_rst) {
              this.rstPayVisible = true;
            } else {
              this.payVisible = true;
            }
          })
          .finally(() => (this.payLoading = false));
      }
    },

    handlerServices() {
      let list = [];
      this.add_goods.forEach(item => {
        list.push({
          goods_service_id: item.id,
          physio_id: item.physio_id,
          quantity: item.num,
        });
      });
      return list;
    },
    submit() {
      if (this.isSubmit()) {
        if (this.detailInfo.is_arrival === '1') {
          if (Number(this.detailInfo.order_id)) {
            this.order_id = this.detailInfo.order_id;
            if (this.is_rst) {
              this.rstPayVisible = true;
            } else {
              this.payVisible = true;
            }
          } else {
            this.collection();
          }
        } else {
          this.arrivalVisible = true;
        }
      }
    },
    deleteAddGoods(index) {
      this.add_goods.splice(index, 1);
    },

    clearAllGoods() {
      this.add_goods = [];
    },

    scrollEvent() {
      this.$nextTick(() => {
        let ref = this.goodsTabList[this.goodsTabActive]?.name;
        this.$refs.serviceRef.scrollView(ref);
      });
    },
    goodsTabChange(index) {
      this.goodsTabActive = index;
      if (this.tabActive === 'service') {
        this.scrollEvent();
        this.getPhysicalTServiceTree();
      }
    },
    getScrolllHeight() {
      this.$nextTick(() => {
        let other_distance = 20;
        this.scrollHeight = this.$refs.goodsArea.offsetHeight - other_distance;
      });
    },
    getCarScrollHeight() {
      this.$nextTick(() => {
        this.carScrollHeight = this.$refs.carBoxRef.offsetHeight - this.$refs.fixedBtnRef.offsetHeight - 40;
      });
    },

    //  获取服务列表
    getPhysicalTServiceTree() {
      let params = {};
      this.loading = true;
      this.$api
        .getPhysicalTServiceTree(params)
        .then(res => {
          let list = res.tree[0]?.children || [];
          this.serviceList = list;
          this.goodsTabList = this.handleTabList(list);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理服务的左侧菜单数据
    handleTabList(res = []) {
      let list = [];
      res &&
        res.forEach(item => {
          list.push({
            label: item.name,
            name: item.id,
          });
        });
      return list || [];
    },

    addGoods(item) {
      this.add_goods.unshift({
        ...item,
        num: 1,
      });
    },
  },
  beforeDestroy() {
    // 在组件销毁时清除定时器，避免内存泄漏
    if (this.timer) {
      clearInterval(this.timer);
    }
    window.removeEventListener('resize', this.getScrolllHeight);
    window.removeEventListener('resize', this.getCarScrollHeight);
    let overflowY = this.$el.getElementsByClassName('ivu-scroll-container')[0];
    if (overflowY) {
      overflowY.addEventListener('scroll', this.serviceScroll);
    }
  },
};
</script>
<style lang="less" scoped>
@import url('./style/create.less');
</style>
