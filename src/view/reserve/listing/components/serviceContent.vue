<template>
  <div>
    <div class="type-box" v-for="(box_item, box_index) in list" :key="'serivce' + box_index">
      <div :ref="box_item.id" v-if="secondLevelHasData(box_item)">
        <div class="type-name">{{ box_item.name }}</div>
        <div class="card-box">
          <div
            class="card-item"
            :class="{ 'card-item--hover': !disabled, 'card-item--disabled': add_goods.length == '1' }"
            v-for="(item, index) in box_item.children"
            :key="'item' + index"
            @click="disabled ? '' : addService(item)"
            v-if="searchShow(item.name)"
          >
            <div class="name-box">
              <div class="name ecs ecs-2">{{ item.name }}</div>
              <div class="price-box">
                <div class="price">¥{{ Number(item.price || 0).toFixed(2) }}</div>
              </div>
            </div>
            <div class="tag-box">
              <div class="card-tag">约{{ item.duration }}分钟</div>
              <div style="flex: 1; text-align: right" v-if="item.sale_promotion_price > 0 && is_rst">
                <Tooltip theme="light" placement="top" v-if="item.sale_promotion_price > 0">
                  <div slot="content">
                    980专享 <span style="color: red">￥{{ item.sale_promotion_price }}</span>
                  </div>
                  <div class="vip-tag-box">
                    <svg-icon name="980_service_tag" class="vip-tag"></svg-icon>
                    <span class="vip-price">￥{{ item.sale_promotion_price }}</span>
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce  } from 'lodash-es';
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'serviceContent',
  components: {},
  mixins: [],
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    add_goods: {
      type: Array,
      default: () => [],
    },
    scrollTop: {
      type: [String, Number],
      default: 0,
    },
    search_keyword: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    is_rst() {
      return isRstClinic();
    },
    // 整个二级是否包含搜索的服务
    secondLevelHasData() {
      return item => {
        let list = item.children;
        let isHas = list.some(item => {
          if (item.name.includes(this.search_keyword)) {
            return true;
          }
        });
        return isHas;
      };
    },
    searchShow() {
      return item => {
        return item?.includes(this.search_keyword);
      };
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 检测滚动
    handleNodeScroll: debounce(function () {
      this.$nextTick(() => {
        if (this.search_keyword) return;
        this.list.some((title_item, title_index) => {
          const ref = this.$refs[title_item.id] && this.$refs[title_item.id][0];
          if (ref) {
            let top = this.$refs[title_item.id][0].getBoundingClientRect().top;
            if (top <= 270 && top !== 0) {
              this.$emit('getIndex', title_index);
            }
          }
        });
      });
    }, 100),
    // 添加服务
    addService(item) {
      this.$emit('addService', { ...item, physio_id: '' });
    },
    // 滚动到指定服务区域
    scrollView(ref) {
      this.$nextTick(() => {
        if (this.search_keyword) return;
        if (this.$refs[ref] && this.$refs[ref][0]) {
          this.$refs[ref][0].scrollIntoView({ behavior: 'smooth' });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.type-box {
  .type-name {
    margin-bottom: 16px;
  }

  .card-box {
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    margin-right: 10px;

    .card-item--hover {
      cursor: pointer;

      &:hover {
        background: rgba(48, 136, 255, 0.04);
      }
    }

    .card-item--disabled {
      background: #fbfbfb !important;
      cursor: not-allowed;

      div,
      p,
      span {
        color: #ccc !important;
      }
    }

    .card-item {
      width: 49%;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #ebedf0;
      padding: 10px 12px;
      margin-bottom: 10px;

      &:nth-child(2n) {
        margin-left: 2%;
      }

      .name-box {
        display: flex;
        justify-content: space-between;

        .name {
          font-weight: 400;
          font-size: 13px;
          color: #303133;
          line-height: 20px;
        }

        .price-box {
          margin-left: 10px;

          .price {
            font-size: 14px;
            color: #303133;
            line-height: 20px;
          }
        }
      }

      .tag-box {
        margin-top: 12px;
        display: flex;
        align-items: center;

        .card-tag {
          background: #f5f6f8;
          border-radius: 2px;
          padding: 0px 4px;
          font-weight: 300;
          font-size: 12px;
          color: #909399;
          margin-right: 4px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:last-child {
            margin-right: 0px;
          }
        }
        .vip-tag-box {
          position: relative;
          .vip-price {
            position: absolute;
            right: 6px;
            top: 0;
            color: #fff;
          }
        }
      }
    }

    .has-add-item {
      border: 1px solid #3088ff !important;
    }
  }
}

.vip-tag {
  width: 90px;
  height: 18px;
}
</style>
