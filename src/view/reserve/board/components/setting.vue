<template>
  <Modal v-model="modelVisible" width="647" class="setting-modal" :mask-closable="false" title="看板设置">
    <Form :model="form" :label-width="100">
      <FormItem label="员工显示设置">
        <el-switch v-model="form.use_member_setting" active-value="1" inactive-value="2" active-color="#155bd4" />
      </FormItem>
      <template v-if="form.use_member_setting === '1'">
        <FormItem label="" :label-width="18">
          <Checkbox v-model="form.show_has_reserve_member" true-value="1" false-value="2"> 有预约的员工 </Checkbox>
          <Checkbox v-model="form.not_show_rest_member" @on-change="change" true-value="1" false-value="2">不显示休假员工</Checkbox>
        </FormItem>
        <FormItem label="" :label-width="18">
          <div class="setting-modal-box">
            <div class="header">拖拽可进行员工排序</div>
            <draggable v-model="form.physical_therapist_list">
              <div class="draggable-item" v-for="item in form.physical_therapist_list" :key="item.id">
                {{ item.name }}
              </div>
            </draggable>
          </div>
        </FormItem>
      </template>
      <FormItem label="看板时间间隔" />
      <FormItem label="" :label-width="18">
        <div class="flex">
          <div
            v-for="item in timeList"
            :key="item.key"
            :class="`radio-box ${+form.interval_min === +item.key && 'radio-box-active'}`"
            @click="changeTime(item.key)"
          >
            {{ item.value }}
          </div>
        </div>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button @click="onCancel">取消</Button>
      <Button type="primary" v-loading="okLoading" @click="onOK">确定</Button>
    </div>
  </Modal>
</template>

<script>
import draggable from 'vuedraggable';
import { cloneDeep } from 'lodash-es';
const initForm = {
  id: void 0,
  use_member_setting: '2',
  show_has_reserve_member: '2',
  not_show_rest_member: '2',
  physical_therapist_list: [],
  interval_min: '15',
};
export default {
  name: 'boardSetting',
  components: {
    draggable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    modelVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  data() {
    return {
      form: { ...initForm },
      cloneForm: {},
      timeList: [
        { key: '15', value: '15分钟' },
        { key: '30', value: '30分钟' },
        { key: '60', value: '1小时' },
        { key: '120', value: '2小时' },
      ],
      okLoading: false,
    };
  },
  mounted() {},
  watch: {
    visible(val) {
      this.$set(this, 'form', cloneDeep(initForm));
      if (val) {
        this.handleQueryInfo();
      }
    },
  },
  methods: {
    onCancel() {
      this.$emit('update:visible', false);
    },
    change(val) {
      this.form.not_show_rest_member = val;
      this.onOK('', true);
    },
    onOK(e, flag) {
      const {
        id,
        use_member_setting,
        interval_min,
        show_has_reserve_member,
        not_show_rest_member,
        physical_therapist_list,
      } = this.form;
      const params = {
        id,
        use_member_setting,
        interval_min,
        show_has_reserve_member,
        not_show_rest_member,
        physical_therapist_list,
        physical_therapist_sort: physical_therapist_list?.map(item => item.id) || [],
      };
      if (use_member_setting !== '1') {
        params.show_has_reserve_member = this.cloneForm.show_has_reserve_member;
        params.not_show_rest_member = this.cloneForm.not_show_rest_member;
        params.physical_therapist_sort = this.cloneForm.physical_therapist_list?.map(item => item.id) || [];
      }
      this.okLoading = true;
      this.$api
        .reservePanelSetting(params)
        .then(() => {
          if (flag) {
            this.handleQueryInfo();
            return;
          };
          this.$Message.success('保存成功');
          this.$emit('init', true, true);
          this.onCancel();
        })
        .finally(() => {
          this.okLoading = false;
        });
    },
    changeTime(key) {
      this.$set(this.form, 'interval_min', key);
    },
    handleQueryInfo() {
      this.$api.getReservePanelSettingInfo().then(res => {
        // this.form = res || {};
        this.form.id = res?.id || initForm.id;
        this.form.physical_therapist_list = res?.physical_therapist_list || initForm.physical_therapist_list;
        this.form.interval_min = res?.interval_min || initForm.interval_min;
        this.form.not_show_rest_member = res?.not_show_rest_member || initForm.not_show_rest_member;
        this.form.show_has_reserve_member = res?.show_has_reserve_member || initForm.show_has_reserve_member;
        this.form.use_member_setting = res?.use_member_setting || initForm.use_member_setting;
        this.cloneForm = res || {};
      });
    },
  },
};
</script>

<style scoped lang="less">
.setting-modal :deep(.ivu-form-item) {
  margin-bottom: 6px;
}
.setting-modal-box {
  width: 100%;
  height: fit-content;
  overflow-y: auto;
  padding: 10px;
  background: #f7f8fa;
  .header {
    color: #969799;
    line-height: 14px;
    margin-bottom: 5px;
  }
  .draggable-item {
    background-image: url(../../../../assets/image/point.png);
    background-repeat: no-repeat;
    width: 108px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    margin: 3px 8px 0px 0px;
    padding: 0px 12px 0px 24px;
    border: 1px solid #dcdee0;
    background-size: 16px;
    background-position: 5px 7px;
    background-color: #fff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    text-align: center;
  }
  .draggable-item:nth-child(5n) {
    margin-right: 0;
  }
}
.radio-box {
  width: 80px;
  padding: 8px 16px;
  display: flex;
  line-height: 14px;
  justify-content: center;
  align-items: center;
  border: 1px solid #ccc;
  margin-right: 10px;
  border-radius: 3px;
  cursor: pointer;
}
.radio-box:hover {
  border-color: rgba(21, 91, 211, 0.5);
}
.radio-box-active {
  border-color: #155bd4;
  background-color: #155bd4;
  color: #fff;
}
</style>
