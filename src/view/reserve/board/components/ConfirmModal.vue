<template>
  <Modal
    v-model="visible"
    :width="480"
    :mask-closable="false"
    :closable="false"
    class="confirm-modal"
  >
    <div class="confirm-content">
      <div class="confirm-icon">
        <Icon type="md-alert" size="24" color="#ff9900" />
      </div>
      <div class="confirm-text">
        <div class="confirm-title">{{ title }}</div>
        <div class="confirm-message">{{ content }}</div>
      </div>
    </div>

    <div slot="footer" class="confirm-footer">
      <Button @click="handleCancel" size="default" class="cancel-btn">
        {{ cancelText }}
      </Button>
      <Button
        @click="handleConfirm"
        type="primary"
        size="default"
        class="confirm-btn"
        :loading="loading"
      >
        {{ okText }}
      </Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ConfirmModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    content: {
      type: String,
      required: true
    },
    okText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('on-ok');
    },
    handleCancel() {
      this.visible = false;
      this.$emit('on-cancel');
    }
  }
};
</script>

<style lang="less" scoped>
.confirm-modal {
  /deep/ .ivu-modal-body {
    padding: 32px 24px 0;
  }

  /deep/ .ivu-modal-footer {
    border-top: none;
  }
}

.confirm-content {
  display: flex;
  align-items: flex-start;

  .confirm-icon {
    margin-right: 16px;
    margin-top: 4px;
  }

  .confirm-text {
    flex: 1;
    .confirm-title {
      margin-top: 4px;
      font-size: 16px;
      font-weight: 500;
      color: #17233d;
      line-height: 24px;
      margin-bottom: 12px;
    }

    .confirm-message {
      font-size: 14px;
      color: #515a6e;
      line-height: 22px;
    }
  }
}

.confirm-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 22px;

  .cancel-btn {
    min-width: 88px;
  }

  .confirm-btn {
    min-width: 120px;
  }
}
</style>
