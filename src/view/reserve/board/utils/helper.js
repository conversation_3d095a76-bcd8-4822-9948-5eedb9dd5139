import moment from 'moment';
import { cloneDeep  } from 'lodash-es';

function parseTimeIntervals(timeRanges, splitTime) {
  // 辅助函数：将时间字符串转换为分钟
  const timeToMinutes = timeStr => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // 辅助函数：将分钟转换为时间字符串
  const minutesToTime = minutes => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
  };

  // 解析分割时间段
  const [splitNumber, splitUnit] = splitTime.match(/(\d+)([hm])/).slice(1);
  const splitInMinutes = splitUnit === 'h' ? splitNumber * 60 : splitNumber;

  // 处理每个时间区间并生成时间点数组
  return timeRanges
    .map(range => {
      const startMinutes = timeToMinutes(range.start);
      const endMinutes = timeToMinutes(range.end);

      // 生成时间点数组，包含最后一个时间段
      const intervals = [];
      for (let i = 0; i < Math.ceil((endMinutes - startMinutes) / splitInMinutes); i++) {
        const start = minutesToTime(startMinutes + i * splitInMinutes);
        const end = minutesToTime(Math.min(startMinutes + (i + 1) * splitInMinutes, endMinutes));
        intervals.push({
          start: start,
          end: end,
          m: minutesSinceMidnight(start),
          em: minutesSinceMidnight(end),
        });
      }

      return intervals;
    })
    .flat(); // 使用 flat() 将二维数组展平为一维数组
}

function minutesSinceMidnight(day) {
  if (!day) return 0;
  const midnight = moment('2020-01-01 00:00:00');
  const now = moment(`2020-01-01 ${day}`);

  const diff = now - midnight; // 计算时间差
  const minutes = diff / 1000 / 60; // 转换为分钟

  return minutes;
}
function culHeight(duration, start, list = []) {
  // 获取起点到格子起点的时间差
  const startTime = minutesSinceMidnight(start);
  const endTime = startTime + duration;
  const newlist = list.filter(item => {
    if (startTime >= item.m && startTime <= item.em) return true;
    if (startTime <= item.m && endTime >= item.m) return true;
    if (endTime >= item.m && endTime <= item.em) return true;
    return false;
  });
  const total = newlist
    ?.map(item => {
      const h_m = item.height / (item.em - item.m); // 每一分钟的高度
      // 计算在当前时间格子中实际占用的时间
      const actualStartTime = Math.max(startTime, item.m);
      const actualEndTime = Math.min(endTime, item.em);
      const actualDuration = actualEndTime - actualStartTime;

      return actualDuration * h_m;
    })
    .reduce((acc, cur) => acc + cur, 0);
  return total;
}
function culAxisY(duration, start, list = []) {
  // 获取起点到格子起点的时间差
  const startTime = minutesSinceMidnight(start);
  const newlist = list.filter(item => {
    if (item.em <= startTime) return true;
    if (startTime >= item.m && startTime <= item.em) return true;
    return false;
  });
  const total = newlist
    ?.map((item, i) => {
      const h_m = item.height / (item.em - item.m); // 每一分钟的高度
      if (i === newlist.length - 1) {
        return (startTime - item.m) * h_m;
      }
      return item.height;
    })
    .reduce((acc, cur) => acc + cur, 0);
  return total;
}
// 根据高度和开始时间算出结束时间在哪一格， 返回该格的开始时间
function culStartTimeByHeight(h, st, list = []) {
  console.log(h, st, '11111');
  list = cloneDeep(list);
  const startTime = minutesSinceMidnight(st);
  const newlist = list.filter(item => startTime <= item.em);
  let start = '00:00';
  let remain = +h || 0;
  console.log(newlist, 'newlist');
  for (let i = 0; i < newlist.length; i++) {
    const item = newlist[i];
    remain -= item.height;
    if (remain <= 0) {
      start = item.start;
      break;
    }
  }
  return start;
}

function dragMouseTopOfStart(list = [], y, mouseY, targetMouseY) {
  if (!Array.isArray(list)) return '00:00';
  list = cloneDeep(list);
  y = +y;
  if (y === 0 || !y || isNaN(y)) return '00:00';
  let remain = targetMouseY - mouseY;
  if (remain < 0) return list?.find((item, index) => index === y)?.start || '00:00';
  let start = '00:00';
  list = list.filter((item, index) => index < y).reverse();
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    remain -= item.height;
    if (remain <= 0) {
      start = item.start;
      break;
    }
  }
  return start;
}

function handleCollisions(list) {
  if (list.length === 0) return list; // 如果列表为空，直接返回
  list = cloneDeep(list);
  // 获取总宽度，假设所有item的w都相同
  const totalWidth = list[0].w;

  // 辅助函数：判断两个矩形是否在y方向上碰撞
  function isCollidingVertically(item1, item2) {
    return !(item1.y + item1.h <= item2.y || item1.y >= item2.y + item2.h);
  }

  // 找出所有碰撞的item组
  let collidingGroups = [];
  let visited = new Set(); // 用于记录已访问的item索引

  for (let i = 0; i < list.length; i++) {
    if (visited.has(i)) continue; // 跳过已访问的item

    // 当前item作为碰撞组的起点
    let collidingGroup = [list[i]];
    visited.add(i);

    // DFS深度优先搜索，找出所有碰撞的item
    const dfs = j => {
      for (let k = 0; k < list.length; k++) {
        if (!visited.has(k) && isCollidingVertically(list[j], list[k])) {
          collidingGroup.push(list[k]);
          visited.add(k);
          dfs(k); // 递归搜索与当前item碰撞的其他item
        }
      }
    };

    dfs(i); // 从起点开始深度优先搜索

    // 如果碰撞组中有多个item，则记录该组，并按y坐标排序（如果需要）
    if (collidingGroup.length > 1) {
      collidingGroup.sort((a, b) => a.y - b.y); // 可选：按y坐标排序
      collidingGroups.push(collidingGroup);
    }
  }
  collidingGroups.forEach(group => {
    const newWidth = totalWidth / group.length;
    // 对每组碰撞的item平分宽度并重新分配x坐标
    let currentX = list[0].x; // 用于跟踪当前分配的x坐标
    group.forEach(item => {
      item.w = newWidth;
      item.x = currentX; // 分配新的x坐标
      currentX += newWidth; // 更新当前x坐标以供下一个item使用
    });
  });

  // 对于未碰撞的item，保持其x坐标不变（但可能需要根据已分配的碰撞组调整）
  // 注意：这里我们假设未碰撞的item不会与已重新分配的碰撞组在x方向上重叠，
  // 或者我们需要一个额外的步骤来确保这一点（例如，通过平移未碰撞的item组）。
  // 由于题目没有提供足够的信息来处理这种情况，我们在这里省略了这个步骤。
  // 如果需要，可以添加一个额外的循环来检查并调整未碰撞item的x坐标。

  return list;
}

// 获取鼠标落点到目标顶点占用多少分钟

export {
  parseTimeIntervals,
  minutesSinceMidnight,
  handleCollisions,
  culHeight,
  culAxisY,
  dragMouseTopOfStart,
  culStartTimeByHeight,
};
