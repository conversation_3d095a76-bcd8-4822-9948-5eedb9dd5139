<template>
  <div class="container">
    <el-image style="width: 300px; height: 230px" :src="img" fit="cover"></el-image>
    <div class="tip-text">
      <span v-if="isChrome">
        您当前的浏览器可能无法正常使用素灵AI辅助诊疗系统，为保障您的使用体验，请升级至新版本，或更换我们推荐的浏览器
      </span>
      <span v-else>
        您当前的浏览器可能无法正常使用素灵AI辅助诊疗系统，为保障您的使用体验，请更换为我们推荐的浏览器
      </span>
    </div>
    <div class="recommend">
      <div class="recommend-item" v-for="item in list" :key="item.browser" @click="handleClick(item.downLink)">
        <el-image style="width: 45px; height: 45px" :src="item.img" fit="cover"></el-image>
        <div class="text">{{ item.text }}</div>
        <div class="tips" v-if="item.type === 'update'">* 升级完成后，请重启浏览器</div>
      </div>
    </div>
  </div>
</template>

<script>
import img from '@/assets/image/version/version.png';
import update360 from '../../assets/image/version/update360.png';
import uploadChrome from '../../assets/image/version/uploadChrome.png';
import down360 from '../../assets/image/version/down360.png';
import downChrome from '../../assets/image/version/downChrome.png';

export default {
  name: 'recommend',
  data() {
    return {
      img,
      cardList: [
        {
          browser: 'update360',
          img: update360,
          text: '升级360浏览器',
          downLink: 'https://browser.360.cn',

          type: 'update',
        },
        {
          browser: 'uploadChrome',
          img: uploadChrome,
          text: '升级Chrome浏览器',
          downLink: 'https://www.google.cn/chrome',
          type: 'update',
        },
        { browser: 'down360', img: down360, text: '使用360浏览器', downLink: 'https://browser.360.cn' },
        {
          browser: 'downChrome',
          img: downChrome,
          text: '使用Chrome浏览器',
          downLink: 'https://www.google.cn/chrome',
        },
      ],
      browser: [],
    };
  },
  computed: {
    list() {
      return this.cardList.filter(item => this.browser.includes(item.browser)) || [];
    },
    // 是否是chrome内核 最保险的方案
    isChrome() {
      return navigator.userAgent.toLowerCase().includes('chrome');
    },
  },
  mounted() {
    this.handlerRecommend();
  },
  methods: {
    handleClick(downLink) {
      window.open(downLink, '_blank');
    },
    handlerRecommend() {
      const userAgent = navigator.userAgent.toLowerCase();
      if (userAgent.includes('360ee') || userAgent.includes('360se')) {
        // 360浏览器
        this.browser = ['update360', 'downChrome'];
      } else if (userAgent.includes('chrome') && !userAgent.includes('360')) {
        // Chrome浏览器
        this.browser = ['uploadChrome', 'down360'];
      } else {
        // 其他浏览器
        this.browser = ['downChrome', 'down360'];
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.tip-text {
  width: 480px;
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  text-align: center;
  margin-top: -35px;
}

.recommend {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;

  .recommend-item {
    width: 280px;
    height: 110px;
    background: #fafafb;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 16px;
    cursor: pointer;

    .text {
      font-size: 13px;
      color: #333333;
      line-height: 18px;
    }

    .tips {
      font-size: 12px;
      color: red;
      margin-top: 4px;
    }
  }

  .recommend-item:last-child {
    margin-right: 0;
  }
}
</style>
