<template>
  <div class="user-wrap">
    <div class="content-wrap" v-loading="loading">
      <div class="content-wrap-scroll" ref="userScroll" @mouseenter="visible = false">
        <div class="header-wrap" style="padding: 0">
          <div class="header-title">用户信息</div>
          <div class="header-action">
            <div class="base-info-edit" @click="queryDetail('', 'openEdit')">
              <img src="../../../assets/image/user/edit1.png" alt="" />
              修改
            </div>
          </div>
        </div>
        <div class="user-content">
          <div class="user-content-item yCenter width50">
            <div class="user-content-label w-26">姓名</div>
            <div class="user-content-text">{{ detail.real_name || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width50">
            <div class="user-content-label">手机号</div>
            <div class="user-content-text">{{ detail.mobile || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width50">
            <div class="user-content-label w-26">性别</div>
            <div class="user-content-text">{{ detail.sex_text || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width50">
            <div class="user-content-label">注册名</div>
            <div class="user-content-text">{{ detail.nickname || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width50">
            <div class="user-content-label w-26">生日</div>
            <div class="user-content-text">{{ detail.birthday || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width50">
            <div class="user-content-label">年龄</div>
            <div class="user-content-text">{{ detail.age || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width50">
            <div class="user-content-label w-26">ID</div>
            <div class="user-content-text">{{ detail.uid || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width50">
            <div class="user-content-label">来源</div>
            <div class="user-content-text">{{ detail.source_text || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width100">
            <div class="user-content-label w-26">证件</div>
            <div class="user-content-text">{{ detail.card_no || '-' }}</div>
          </div>
          <div class="user-content-item yCenter width100">
            <div class="user-content-label w-26">地址</div>
            <div class="user-content-text">{{ calcAddress }}</div>
          </div>
        </div>
        <div class="content-line"></div>
        <div class="header-wrap" style="padding: 0">
          <div class="header-title">用户标签</div>
          <div class="header-action" />
        </div>
        <div class="user-label" v-loading="deleteLoading">
          <div class="user-label-item" v-for="item in selectedTagList" :key="item.id">
            {{ item.tag_name }}
            <Icon
              class="user-label-item-close"
              type="ios-close-circle"
              color="#abb0b8"
              :size="18"
              @click="deleteTag(item)"
            />
          </div>
          <div class="user-label-add" @click="showUserTag"><Icon size="16" color="#155bd4" type="ios-add" /></div>
        </div>
        <div class="content-line"></div>
        <div class="header-wrap" style="padding: 0">
          <div class="header-title">用户跟随</div>
          <div class="header-action">
            <div class="base-info-edit" @click="addUserFollow()">
              <img src="../../../assets/image/user/edit.png" alt="" />
              记一笔
            </div>
          </div>
        </div>
        <div class="user-follow">
          <el-empty
            class="user-pop-empty"
            :image-size="40"
            description="暂无数据"
            v-if="followList?.length === 0"
          ></el-empty>
          <div class="user-follow-item" v-for="item in followList.filter((item, i) => i < 2)" :key="item.id">
            <div class="user-follow-time">
              <div v-if="+item?.update_operator_info?.operator_time">
                {{ item.update_operator_info.operator_time | data_format }}&nbsp;&nbsp;由&nbsp;&nbsp;
                {{ item?.update_operator_info?.operator || '-' }} 更新
              </div>
              <div v-else>
                {{ item.create_time | data_format }}&nbsp;&nbsp;由&nbsp;&nbsp;
                {{ item?.operator_info?.operator || '-' }} 创建
              </div>
            </div>
            <div class="user-follow-text">{{ item.description || '-' }}</div>
          </div>
          <div class="more" v-if="followList?.length > 2" @click="queryDetail('11')">查看更多</div>
        </div>
        <div class="content-line" style="margin: 0"></div>
        <div
          :class="{
            'header-wrap': true,
            'header-action-wrap': true,
          }"
          :style="{
            cursor: !isVip980 || !isVipRf ? 'pointer' : 'not-allowed',
          }"
          @click="openVip()"
        >
          <div class="header-title sub-header-title">
            <img src="../../../assets/image/user/member.png" alt="" />
            <!--  1. 普通诊所展示980会员 2.榕树堂诊所但已经是980会员展示980会员   -->
            <!--            <div v-if="isVip980 || !isRstClinic">980会员</div>-->
            <!--            &lt;!&ndash; 榕树堂展示青铜榕粉 &ndash;&gt;-->
            <!--            <div v-if="isRstClinic && !isVip980">青铜榕粉</div>-->
            <div>会员权益</div>
            <img
              v-if="isVip980"
              src="https://img-sn01.rsjxx.com/image/2025/0728/150234_20867.png"
              style="width: 63px; height: 22px; margin-left: 8px"
            />
          </div>
          <div class="header-action">
            <!--  980会员权益展示 -->
            <div class="sub-header-action-content" v-if="isVip980 || !isRstClinic">
              <Tooltip :disabled="!isVip980 || isWillExpire" placement="top" :content="`${expireTime} 前有效`">
                <div class="sub-header-action-content" style="margin-right: 0">
                  <div class="member-valid-time" v-if="isVip980 && isWillExpire">{{ expireTime }} 前有效</div>
                  <div v-if="!isVip980" class="sub-header-action-content-text" style="color: #ee3838">立即开通</div>
                  <div
                    v-if="isVip980 && !isWillExpire"
                    class="sub-header-action-content-text"
                    style="color: #115bd4; text-align: right"
                  >
                    权益生效中
                  </div>
                  <div v-if="isCurrentClinic" class="sub-header-action-content-text">非当前诊所开通</div>
                </div>
              </Tooltip>
            </div>
            <!--  青铜榕粉权益展示 -->
            <div class="sub-header-action-content" v-if="!isVip980 && isRstClinic">
              <Tooltip :disabled="!isVipRf || isRfWillExpire" placement="top" :content="`${rfExpireTime} 前有效`">
                <div class="sub-header-action-content" style="margin-right: 0">
                  <div class="member-valid-time" v-if="isVipRf && isRfWillExpire">{{ rfExpireTime }} 前有效</div>
                  <div v-if="!isVipRf" class="sub-header-action-content-text" style="color: #ee3838">立即开通</div>
                  <div
                    v-if="isVipRf && !isRfWillExpire"
                    class="sub-header-action-content-text"
                    style="color: #115bd4; text-align: right"
                  >
                    权益生效中
                  </div>
                  <div v-if="isRfCurrentClinic" class="sub-header-action-content-text">非当前诊所开通</div>
                </div>
              </Tooltip>
            </div>
            <div v-if="!detail.vip_info || detail.vip_info?.length === 0" class="base-info-edit" style="padding: 5px 0">
              <img style="margin-right: 0" src="../../../assets/image/user/right-arrow.png" alt="" />
            </div>
          </div>
        </div>
        <div class="content-line" style="margin: 0"></div>
        <div class="header-wrap header-action-wrap cursor-pointer" @click="queryDetail('')">
          <div class="header-title sub-header-title">
            <img src="../../../assets/image/user/balance.png" alt="" />
            {{ isRstClinic ? '云储值余额' : '储值余额' }}
          </div>
          <div class="header-action">
            <div class="sub-header-action-content">
              <div class="sub-header-action-content-text" v-if="isRstClinic">
                ¥{{ detail.recharge_yzt?.total_money | number_format }}
              </div>
              <div class="sub-header-action-content-text" v-if="!isRstClinic">
                ¥{{ detail.recharge?.total_money | number_format }}
              </div>
            </div>
            <div class="base-info-edit" style="padding: 5px 0">
              <img style="margin-right: 0" src="../../../assets/image/user/right-arrow.png" alt="" />
            </div>
          </div>
        </div>
        <div class="content-line" style="margin: 0"></div>
        <div class="header-wrap header-action-wrap cursor-pointer" @click="queryDetail('4')">
          <div class="header-title sub-header-title">
            <img src="../../../assets/image/user/coupon.png" alt="" />
            服务卡券
          </div>
          <div class="header-action">
            <div class="sub-header-action-content">
              <div class="sub-header-action-content-text">
                {{ detail.xn_service_info?.service_card_count || 0 }}次可服务
              </div>
            </div>
            <div class="base-info-edit" style="padding: 5px 0">
              <img style="margin-right: 0" src="../../../assets/image/user/right-arrow.png" alt="" />
            </div>
          </div>
        </div>
        <div class="content-line" style="margin: 0"></div>
        <div class="header-wrap header-action-wrap cursor-pointer" @click="queryDetail('5')">
          <div class="header-title sub-header-title">
            <img src="../../../assets/image/user/circulate.png" alt="" />
            通兑券
          </div>
          <div class="header-action">
            <div class="sub-header-action-content">
              <div class="sub-header-action-content-text">
                {{ detail.tdj_service_info?.service_card_count || 0 }}次可兑换
              </div>
            </div>
            <div class="base-info-edit" style="padding: 5px 0">
              <img style="margin-right: 0" src="../../../assets/image/user/right-arrow.png" alt="" />
            </div>
          </div>
        </div>
        <div class="content-line" style="margin: 0 0 10px 0"></div>
      </div>
    </div>
    <div class="action-wrap" v-if="visible">
      <div class="arrow-wrap">
        <div class="arrow-left"></div>
      </div>
      <div class="action-content-box">
        <div class="action-content" v-loading="addLoading">
          <el-empty
            class="user-pop-empty"
            style="width: 250px"
            :image-size="40"
            description="暂无数据"
            v-if="tagList?.length === 0"
          ></el-empty>
          <div v-for="item in tagList" :key="item.id">
            <div class="action-content-header">
              <div class="action-content-header-label">{{ item.name }}</div>
            </div>
            <div class="action-content-container">
              <div
                :class="{
                  'action-content-container-item': true,
                  active: selectedTagList.some(label => tag.id === label.tag_id),
                }"
                v-for="tag in item.tag_info"
                :key="tag.id"
                @click="saveUserTag(tag)"
              >
                {{ tag.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import { cloneDeep  } from 'lodash-es';
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'user-tooltip',
  components: {},
  props: {
    detail: {
      type: Object,
      default: () => ({}),
    },
    selectedRowPatientDetail: {
      type: Object,
      default: () => ({}),
    },
    tagList: {
      type: Array,
      detail: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isRstClinic: isRstClinic(),
      moment,
      visible: false,
      deleteLoading: false,
      addLoading: false,
      tagsList: [],
      selectedTagList: [],
    };
  },
  computed: {
    calcAddress() {
      const { city, county, prov, other } = this.detail?.address || {};
      const provName = prov?.name || '';
      const cityName = city?.name || '';
      const countyName = county?.name || '';
      const otherName = other || '';
      return provName || cityName || countyName || otherName ? provName + cityName + countyName + otherName : '-';
    },
    isVip980() {
      const vip_info = this.detail?.vip_info;
      return vip_info?.some(item => item.user_type === '1');
    },
    isVipRf() {
      const vip_info = this.detail?.vip_info;
      return vip_info?.some(item => item.user_type === '4');
    },
    isWillExpire() {
      const vip_info = this.detail?.vip_info;
      const vip980 = vip_info?.find(item => item.user_type === '1');
      const expireTime = vip980?.end_time * 1000;
      if (Number.isNaN(expireTime)) return false;
      const now = moment();
      const expire = moment(expireTime).subtract(30, 'days');
      return now.isAfter(expire);
    },
    isRfWillExpire() {
      const vip_info = this.detail?.vip_info;
      const vip980 = vip_info?.find(item => item.user_type === '4');
      const expireTime = vip980?.end_time * 1000;
      if (Number.isNaN(expireTime)) return false;
      const now = moment();
      const expire = moment(expireTime).subtract(30, 'days');
      return now.isAfter(expire);
    },
    expireTime() {
      const vip_info = this.detail?.vip_info;
      const vip980 = vip_info?.find(item => item.user_type === '1');
      const expireTime = vip980?.end_time * 1000;
      if (Number.isNaN(expireTime)) return '-';
      const expire = moment(expireTime).format('YYYY-MM-DD HH:mm:ss');
      return expire || '-';
    },
    rfExpireTime() {
      const vip_info = this.detail?.vip_info;
      const vip980 = vip_info?.find(item => item.user_type === '4');
      const expireTime = vip980?.end_time * 1000;
      if (Number.isNaN(expireTime)) return '-';
      const expire = moment(expireTime).format('YYYY-MM-DD HH:mm:ss');
      return expire || '-';
    },
    isCurrentClinic() {
      const vip_info = this.detail?.vip_info;
      if (!this.isVip980) return false;
      const vip980 = vip_info?.find(item => item.user_type === '1');
      return vip980.is_current_clinic !== '1';
    },
    isRfCurrentClinic() {
      const vip_info = this.detail?.vip_info;
      if (!this.isVipRf) return false;
      const vipRf = vip_info?.find(item => item.user_type === '4');
      return vipRf.is_current_clinic !== '1';
    },
    followList() {
      return this.detail?.follow_up_tips_list || [];
    },
  },
  watch: {
    'detail.uid': {
      handler(flag) {
        if (flag) {
          this.selectedTagList = this.detail?.label_list || [];
        }
        this.visible = false;
        if (this.$refs.userScroll) {
          this.$refs.userScroll.scrollTop = 0; // 将滚动条重置到顶部
        }
      },
    },
  },
  methods: {
    queryDetail(key, openEdit) {
      const { uid } = this.detail;
      if (!uid) {
        this.$Message.error('用户详情查询失败！');
        return;
      }
      const href = this.$router.resolve({
        path: '/user/detail',
        query: {
          uid,
          routeTabId: key,
          openEdit,
        },
      }).href;
      window.open(href, '_blank');
      this.$emit('hidePop');
    },
    addUserFollow() {
      this.$emit('addUserFollow', this.detail);
      this.$emit('hidePop');
    },
    showUserTag() {
      this.visible = !this.visible;
    },
    openVip() {
      if (this.isVip980 || this.isVipRf) return;
      this.$emit('hidePop');
      this.$nextTick(() => {
        this.$emit('openVip', this.detail);
      });
    },
    success() {
      this.$emit('hidePop');
      this.$nextTick(() => {
        this.$emit('success');
      });
    },
    deleteTag(row) {
      const list = this.selectedTagList.filter(item => item.tag_id !== row.tag_id);
      const ids = list?.map(item => item.tag_id);
      const { uid } = this.detail;
      this.deleteLoading = true;
      this.$api
        .saveUserTag({
          uid,
          tag_ids: ids,
        })
        .then(() => {
          this.selectedTagList = list;
        })
        .finally(() => {
          this.deleteLoading = false;
        });
    },
    saveUserTag(row) {
      const isAdd = this.selectedTagList.every(item => item.tag_id !== row.id);
      if (isAdd) {
        const list = cloneDeep(this.selectedTagList);
        list.push({
          ...row,
          tag_id: row.id,
          tag_name: row.name,
        });
        const ids = list?.map(item => item.tag_id);
        const { uid } = this.detail;
        this.addLoading = true;
        this.$api
          .saveUserTag({
            uid,
            tag_ids: ids,
          })
          .then(() => {
            this.selectedTagList = list;
          })
          .finally(() => {
            this.addLoading = false;
          });
        return;
      }
      if (!isAdd) {
        const list = this.selectedTagList.filter(item => item.tag_id !== row.id);
        const ids = list?.map(item => item.tag_id);
        const { uid } = this.detail;
        this.addLoading = true;
        this.$api
          .saveUserTag({
            uid,
            tag_ids: ids,
          })
          .then(() => {
            this.selectedTagList = list;
          })
          .finally(() => {
            this.addLoading = false;
          });
      }
    },
  },
};
</script>

<style scoped lang="less">
.user-wrap {
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
  .content-wrap {
    width: 320px;
    flex-shrink: 0;
    background: #ffffff;
    height: 450px;
    border-radius: 2px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    .content-wrap-scroll {
      width: 100%;
      height: 100%;
      padding: 16px 16px 16px 20px;
      overflow-y: auto;
      overflow-x: hidden;
      .header-wrap {
        display: flex;
        align-items: center;
        padding: 10px 0;
        .header-title {
          flex-shrink: 0;
          font-weight: 600;
          font-size: 13px;
          color: #303133;
          display: flex;
          align-items: center;
          > img {
            width: 18px;
            height: 18px;
            margin-right: 6px;
          }
        }
        .sub-header-title {
          font-size: 12px;
          font-weight: 400;
          color: #303133;
          line-height: 20px;
        }
        .header-action {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .base-info-edit {
            padding-left: 9px;
            font-size: 12px;
            color: #115bd4;
            line-height: 18px;
            display: flex;
            align-items: center;
            cursor: pointer;
            > img {
              width: 12px;
              height: 12px;
              margin-right: 2px;
            }
          }
          .base-info-edit:hover {
            color: #447cdd;
          }
          .sub-header-action-content {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: center;
            margin-right: 4px;
            .member-valid-time {
              font-size: 12px;
              color: #ee3838;
              line-height: 18px;
            }
            .sub-header-action-content-text {
              font-size: 12px;
              color: #909399;
              line-height: 16px;
            }
          }
        }
      }
      .header-action-wrap:hover {
        background: #f3f3f3;
      }
      .user-content {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin-top: 12px;
        .user-content-item {
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          .user-content-label {
            width: 40px;
            height: 18px;
            flex-shrink: 0;
            font-size: 12px;
            color: #909399;
            line-height: 18px;
            text-align: right;
            margin-right: 8px;
          }
          .user-content-text {
            width: calc(100% - 20px);
            font-weight: 400;
            font-size: 12px;
            color: #303133;
            cursor: default;
            line-height: 18px;
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden; /* 隐藏溢出的文本 */
            text-overflow: ellipsis; /* 显示省略号 */
          }
        }
        .user-content-item:last-child {
          margin-bottom: 0;
        }
      }
      .content-line {
        width: 100%;
        height: 1px;
        background: #ebedf0;
        margin: 10px 0;
      }
      .user-label {
        width: 100%;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding-top: 8px;

        .user-label-item {
          padding: 3px 16px;
          font-size: 13px;
          color: #155bd4;
          line-height: 18px;
          background: rgba(21, 91, 212, 0.06);
          border-radius: 14px;
          position: relative;
          cursor: default;
          margin-bottom: 8px;
          margin-right: 6px;

          .user-label-item-close {
            display: none;
            position: absolute;
            right: -4px;
            top: -4px;
            cursor: pointer;
            color: rgba(20, 90, 210, 0.5);
          }
        }
        .user-label-item:hover {
          background: rgba(21, 91, 212, 0.2);
        }
        .user-label-item:hover .user-label-item-close {
          display: block;
        }
        .user-label-add {
          width: 49px;
          height: 24px;
          background: #f6f6f6;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          margin-bottom: 8px;
        }
        .user-label-add:hover {
          cursor: pointer;
          background: rgba(21, 91, 212, 0.2);
        }
      }
      .user-follow {
        width: 100%;
        min-height: 40px;
        margin: 8px 0 12px 0;
        .user-follow-item {
          width: 100%;
          margin-bottom: 10px;
          .user-follow-time {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #909399;
            line-height: 18px;
          }
          .user-follow-text {
            word-break: break-all;
            white-space: pre-wrap;
            font-size: 13px;
            color: #303133;
            line-height: 18px;
          }
        }
        .user-follow-item:last-child {
          margin-bottom: 0;
        }

        .more {
          font-size: 12px;
          color: #115bd4;
          cursor: pointer;
          display: inline-block;
          padding: 5px 12px;
          border: 1px solid #dcdde0;
          border-radius: 14px;
          width: auto;
        }
      }
    }
    .content-wrap-scroll::-webkit-scrollbar {
      width: 4px;
    }
  }
  .action-wrap {
    width: 292px;
    height: fit-content;
    display: flex;
    .arrow-wrap {
      width: 12px;
      min-height: 200px;
      display: flex;
      align-items: center;
      background: transparent;
      .arrow-left {
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        border-width: 7px 7px 7px 0;
        border-right-color: rgba(217, 217, 217, 0.5);
      }
    }
    .action-content-box {
      display: flex;
      flex-direction: column;
      height: 400px;
      .action-content {
        height: 400px;
        flex-shrink: 0;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 16px 16px 0 16px;
        background: #ffffff;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
        .action-content-header {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          .action-content-header-label {
            font-size: 12px;
            color: #303133;
            line-height: 20px;
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden; /* 隐藏溢出的文本 */
            text-overflow: ellipsis; /* 显示省略号 */
          }
        }
        .action-content-container {
          width: 100%;
          display: flex;
          flex-wrap: wrap; /* 允许换行 */
          gap: 0 10px; /* 设置间距 */
          margin-bottom: 10px;
          .action-content-container-item {
            width: fit-content;
            padding: 5px 16px;
            background: rgba(21, 91, 212, 0.06);
            border-radius: 14px;
            font-size: 13px;
            color: #155bd4;
            line-height: 18px;
            margin-bottom: 8px;
            cursor: pointer;
          }
          .action-content-container-item.active {
            background: #115bd4;
            color: #fff;
          }
          .action-content-container-item:hover {
            background: rgba(21, 91, 212, 0.2);
          }
          .action-content-container-item:hover.active {
            background: rgba(21, 91, 212, 0.2);
            color: #155bd4;
          }
        }
      }
    }
  }
}
.width100 {
  width: 100%;
}
.width50 {
  width: 50%;
}
.w-26 {
  width: 26px !important;
}
.xCenter {
  display: flex;
  justify-content: center;
}
.yCenter {
  display: flex;
  align-items: center;
}
.cursor-pointer {
  cursor: pointer;
}
.user-pop-empty {
  padding: 20px 0;
}
.user-pop-empty :deep(.el-empty__description) {
  margin-top: 10px;
}
.user-pop-empty :deep(.el-empty__description p) {
  font-size: 12px;
  color: #909399;
}
</style>
