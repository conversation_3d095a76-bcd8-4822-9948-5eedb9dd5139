<template>
  <Modal :value="visible" title="添加家庭成员" width="520" :mask-closable="false" @on-visible-change="onVisibleChange">
    <div class="flex flex-item-l-center">
      <Form
        style="width: 360px; margin-top: 4px"
        :model="formData"
        :rules="formRules"
        label-position="right"
        ref="memberForm"
        :label-width="80"
        label-colon
      >
        <FormItem label="选择成员" prop="member_uid">
          <el-select
            v-model="formData.member_uid"
            placeholder="请选择成员"
            filterable
            remote
            :remote-method="getUsers"
            @change="selectUser"
            clearable
            size="medium"
            style="width: 100%"
            @clear="clearUser"
            popper-class="modal-select"
            :popper-append-to-body="false"
            @blur="checkUser"
          >
            <el-option
              v-for="item in userList"
              :tag="item.mobile"
              :key="item.uid"
              :value="item.uid"
              :label="`${item.patient_name} (${item.sex_text}/${item.mobile})`"
              :disabled="memberIdList.includes(item.uid)"
            >
              <Tooltip
                content="当前用户已是家庭组成员，无法添加"
                placement="top"
                style="width: 100%"
                :disabled="!memberIdList.includes(item.uid)"
              >
                <div :class="['user-info', memberIdList.includes(item.uid) ? 'disabled' : '']">
                  <div class="user-name">{{ item.patient_name }}</div>
                  <div class="user-base">{{ item.sex_text }}｜{{ item.mobile }}</div>
                </div>
              </Tooltip>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem label="成员关系" prop="role_type">
          <Select v-model="formData.role_type" placeholder="请选择成员与你的关系">
            <Option v-for="item in userRoles" :key="item.kw" :value="item.kw" :label="item.desc"></Option>
          </Select>
        </FormItem>
        <FormItem label="创建人手机">
          <Input v-model="formData.mobile" disabled placeholder="请输入手机号码"></Input>
          <span class="mobile-tip" v-if="$attrs.isTemporaryMobile">临时号无法添加家庭组成员，请先修改真实手机号</span>
        </FormItem>
        <FormItem label="创建验证码" prop="authcode">
          <div class="flex">
            <Input
              clearable
              class="flex-1 code-input"
              v-model="formData.authcode"
              placeholder="请输入验证码"
              maxlength="4"
            >
            </Input>
            <vac ref="vac" :auto-start="false" :left-time="60000" style="width: 92px; text-align: center">
              <Button disabled slot="process" slot-scope="{ timeObj }" style="width: 100%">
                {{ timeObj.ceil.s }} 重新获取
              </Button>
              <Button
                slot="before"
                type="primary"
                :disabled="!formData.mobile || $attrs.isTemporaryMobile"
                @click="sendAuthCode"
                >获取验证码
              </Button>
              <Button slot="finish" type="primary" :disabled="!formData.mobile" @click="sendAuthCode"
                >获取验证码
              </Button>
            </vac>
          </div>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button
        type="primary"
        class="ml-12"
        @click="handleSubmit"
        :loading="submitLoading"
        :disabled="$attrs.isTemporaryMobile"
        >提交
      </Button>
    </div>
  </Modal>
</template>

<script>
import { debounce  } from 'lodash-es';

export default {
  name: 'AddGroupMember',
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    memberIdList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      key: 'value',
      formData: {
        uid: this.$route.query.uid,
        member_uid: '',
        role_type: '',
        authcode: undefined,
        mobile: '',
      },
      formRules: {
        member_uid: [{ required: true, message: '请选择成员', trigger: 'change' }],
        role_type: [{ required: true, message: '请选择成员关系', trigger: 'change' }],
      },
      userList: [],
      userRoles: {},
      currentUser: '',
      submitLoading: false,
    };
  },
  created() {
    this.getMemberOptions();
  },
  methods: {
    onVisibleChange(v) {
      if (v) {
        this.getMemberOptions();
        console.log(this.$attrs);
        this.getUsers('');
      } else {
        this.closeModal();
      }
    },
    closeModal() {
      this.clearUser();
      this.$emit('changeVisible', false);
    },
    async getMemberOptions() {
      const res = await this.$api.getFamilyGroupOption();
      console.log('%c=>(add-group-member.vue:59) res', 'font-size: 18px;color: #FF7043 ;', res);
      this.userRoles = res.role_type_desc;
    },
    handleSubmit() {
      this.$refs.memberForm.validate(valid => {
        if (valid) {
          this.submitLoading = true;
          this.$api
            .addFamilyGroupMember(this.formData)
            .then(res => {
              this.$Message.success('添加家庭组成员成功');
              this.$emit('freshList');
              this.clearUser();
              this.closeModal();
            })
            .finally(() => {
              this.submitLoading = false;
            });
        } else {
          this.$Message.error('请按提示完善表单信息');
        }
      });
    },
    getUsers: debounce(
      function (search) {
        // if (this.currentUser === search && this.userList.length > 0) {
        //   return;
        // }
        this.$api.getUserList({ search }).then(res => {
          console.log('%c=>(add-group-member.vue:106) res', 'font-size: 18px;color: #FF7043 ;', res);
          this.userList = res.users;
        });
      },
      30,
      {
        leading: false,
        trailing: true,
      }
    ),
    selectUser(val) {
      console.log(val);
      this.$refs.memberForm.validateField('member_uid');
      // this.currentUser = val.patient_name;
      // this.formData.mobile = val.tag;
    },
    clearUser() {
      this.currentUser = '';
      this.getUsers('');
      // this.formData.mobile = '';
      this.$refs.memberForm.resetFields();
    },
    checkUser() {
      console.log(this.formData.member_uid)
      if (!this.formData.member_uid) {
        this.getUsers('');
      }
    },
    sendAuthCode() {
      this.$api.sendAuthCode({ mobile: this.formData.mobile }).then(res => {
        this.$Message.success('发送成功');
        this.$refs.vac.startCountdown(true);
      });
    },
    onCountDownFinish() {},
  },
  watch: {
    $attrs: {
      handler(val) {
        console.log(val);
        this.formData.mobile = val.mobile;
      },
    },
  },
};
</script>

<style lang="less" scoped>
.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  //padding: 4px 6px;

  .user-name {
    font-size: 14px;
    color: #303133;
    line-height: 20px;
  }

  .user-base {
    font-size: 12px;
    color: #909399;
    line-height: 18px;
  }

  &.disabled {
    opacity: 0.6;
    background: #f5f5f5;
  }
}

.mobile-tip {
  color: #ff2641;
}

::v-deep .code-input {
  .ivu-input::-webkit-inner-spin-button {
    display: none;
  }
}
</style>
