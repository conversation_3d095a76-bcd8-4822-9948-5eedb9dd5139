<template>
  <div class="custom-tabs-wrap">
    <div class="more-search-tabs">
      <div
        :class="{
          'more-search-tabs-item': true,
          active: item.key === activeKey,
        }"
        v-for="(item, i) in options"
        :key="item.key + i"
        @click="changeTabs(item.key)"
      >
        <span v-if="!item.img">{{ item.name }}</span>
        <DatePicker
          v-else
          :transfer="transfer"
          :editable="editable"
          :open="open"
          :value.sync="value"
          :type="dateType"
          :placement="placement"
          :format="format"
          @on-change="handleChange"
          @on-clickoutside="open = false"
          @on-open-change="closeDatePicker"
        >
          <div class="more-search-tabs-item-img">
            <img :src="item.img" alt="#" />
          </div>
        </DatePicker>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep  } from 'lodash-es';

export default {
  name: 'custom-tabs',
  model: {
    prop: 'activeKey',
    event: 'change',
  },
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    activeKey: {
      type: String,
      default: '',
    },
    dateType: {
      type: String,
      default: 'date',
    },
    placement: {
      type: String,
      default: 'bottom',
    },
    date: {
      type: [String, Array],
      default: '',
    },
    format: {
      type: String,
      default: 'yyyy-MM-dd',
    },
    editable: {
      type: Boolean,
      default: true,
    },
    transfer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      open: false,
      value: '',
    };
  },
  watch: {
    open: {
      handler(val) {
        if (!val && !this.value.length) {
          this.$emit('change', '');
          this.$emit('update:date', []);
        }
      },
    },
  },
  methods: {
    changeTabs(key) {
      this.$emit('change', key);
      if (this.activeKey !== key) {
        this.$emit('update:date', []);
      }
      if (key === 'custom') {
        this.open = true;
      } else {
        this.open = false;
        const date = this.options.find(item => item.key === key)?.date || [];
        this.$emit('update:date', date);
      }
    },
    handleChange(date) {
      let dates = [];
      if (this.activeKey === 'custom' && Array.isArray(date)) {
        dates = date;
      }
      if (this.activeKey === 'custom' && !Array.isArray(date)) {
        dates = [date];
      }
      this.open = false;
      this.value = date;
      this.$emit('update:date', dates);
    },
    closeDatePicker(flag) {
      if (flag) {
        this.value = Array.isArray(this.date) ? cloneDeep(this.date) : [];
      }
    },
  },
};
</script>

<style scoped lang="less">
.custom-tabs-wrap {
  width: 100%;
  height: 100%;
  .more-search-tabs {
    width: 100%;
    display: flex;
    align-items: center;
    .more-search-tabs-item {
      flex: 1;
      min-width: fit-content;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      border: 1px solid #dcdfe6;
      border-left: 1px solid transparent;
      transition: all 0.2s ease-in-out;
      cursor: pointer;
      :deep(.ivu-date-picker) {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        :deep(.ivu-date-picker-rel) {
          width: 100%;
          height: 100%;
        }
      }
      .more-search-tabs-item-img {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 16px;
          height: 16px;
        }
      }
      > span {
        font-size: 13px;
        color: #606266;
        line-height: 22px;
      }
    }
    .more-search-tabs-item:first-child {
      border-left: 1px solid #dcdfe6;
      border-radius: 4px 0 0 4px;
    }
    .more-search-tabs-item:last-child {
      border-radius: 0 4px 4px 0;
    }
    .more-search-tabs-item.active {
      background: rgba(21, 91, 212, 0.06);
      border: 1px solid #155bd4;
      box-shadow: 0 0 3px 0 rgba(21, 91, 212, 0.5);
    }
  }
}
</style>
