<template>
  <Modal
    :value="searchVisible"
    title="筛选用户"
    width="740"
    :mask-closable="false"
    lock-scroll
    @on-cancel="onCancel"
    class-name="more-search-model"
    @on-visible-change="changeVisible"
  >
    <div class="search-wrap">
      <Form class="more-search-form">
        <Row>
          <Col span="24">
            <div class="more-search-form-header">
              <div class="more-search-form-header-title">按基础信息</div>
            </div>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="性别">
              <Select v-model="formData.sex" :transfer="false" placeholder="不指定">
                <Option v-for="(item, index) in options.sexDesc" :value="item.id" :key="index">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="年龄">
              <div class="width100 flex yCenter">
                <InputNumber
                  :min="0"
                  :max="formData.age_et || 9999"
                  :precision="0"
                  v-model="formData.age_st"
                  placeholder="0"
                  :active-change="false"
                  @on-focus="event => event.currentTarget.select()"
                />
                <div class="mx-10">至</div>
                <InputNumber
                  :min="formData.age_st || 0"
                  :precision="0"
                  v-model="formData.age_et"
                  placeholder="不限"
                  :active-change="false"
                  @on-focus="event => event.currentTarget.select()"
                />
              </div>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="生日">
              <div class="form-item-label" slot="label">
                <div class="form-item-label-title">生日</div>
                <div class="form-item-label-action" v-if="formData.birthday_md_type">
                  {{ showDateFormat(formData.birthday_md, 'M月D日') }}
                </div>
              </div>
              <custom-tabs
                v-model="formData.birthday_md_type"
                :date.sync="formData.birthday_md"
                :options="options.birthday_keys"
                dateType="daterange"
                @on-change-date="date => changeDate(date, 'birthday')"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="出生日期">
              <div class="width100 flex yCenter">
                <DatePicker
                  :value="formData.birthday_st"
                  :editable="false"
                  :options="{
                    disabledDate: date => {
                      if (!date) return false;
                      if (moment().isBefore(date)) return true;
                      if (!formData.birthday_et) return false;
                      return moment(formData.birthday_et).isBefore(date);
                    },
                  }"
                  :transfer="false"
                  type="date"
                  placeholder="选择日期"
                  @on-change="date => handleChangeDate(date, 'birthday_st')"
                />
                <div class="mx-10">至</div>
                <DatePicker
                  :value="formData.birthday_et"
                  :editable="false"
                  :options="{
                    disabledDate: date => {
                      if (!date) return false;
                      if (moment().isBefore(date)) return true;
                      if (!formData.birthday_st) return false;
                      return moment(formData.birthday_st).isAfter(date);
                    },
                  }"
                  placement="bottom-end"
                  :transfer="false"
                  type="date"
                  placeholder="选择日期"
                  @on-change="date => handleChangeDate(date, 'birthday_et')"
                />
              </div>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="来源">
              <Select v-model="formData.source" :transfer="false" placeholder="不指定">
                <Option v-for="(item, index) in options.sourceList" :value="item.id" :key="index">{{
                  item.desc
                }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="创建时间">
              <div class="form-item-label" slot="label">
                <div class="form-item-label-title">创建时间</div>
                <div class="form-item-label-action" v-if="formData.createTimeType">
                  {{ showDateFormat(formData.createTime) }}
                </div>
              </div>
              <custom-tabs
                v-model="formData.createTimeType"
                :options="options.createday_keys"
                :date.sync="formData.createTime"
                dateType="daterange"
                placement="bottom-end"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="会员类型">
              <Select v-model="formData.vip_type" :transfer="false" multiple placeholder="不指定">
                <Option
                  v-for="(item, index) in options.vip_status_desc"
                  :disabled="isVipDisable(item.kw)"
                  :value="item.id"
                  :key="index"
                >
                  {{ item.desc }}
                </Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="会员有效开通途径">
              <Select v-model="formData.vip_source" :transfer="false" placeholder="不指定">
                <Option v-for="item in options.source_clinic_this" :value="item.id" :key="item.id">
                  {{ item.desc }}
                </Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem>
              <div class="form-item-label" slot="label">
                <div class="form-item-label-title">会员生效</div>
                <div class="form-item-label-action" v-if="formData.vip_start_time_type">
                  {{ showDateFormat(formData.vip_start_time) }}
                </div>
              </div>
              <custom-tabs
                v-model="formData.vip_start_time_type"
                :options="options.createday_keys"
                :date.sync="formData.vip_start_time"
                dateType="daterange"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem>
              <div class="form-item-label" slot="label">
                <div class="form-item-label-title">会员截止</div>
                <div class="form-item-label-action" v-if="formData.vip_end_time_type">
                  {{ showDateFormat(formData.vip_end_time) }}
                </div>
              </div>
              <custom-tabs
                v-model="formData.vip_end_time_type"
                :options="options.createday_keys"
                :date.sync="formData.vip_end_time"
                dateType="daterange"
                placement="bottom-end"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="用户标签">
              <Select v-model="formData.tag_ids" :transfer="false" multiple placeholder="不指定">
                <Option v-for="item in options.tag_desc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <div class="more-search-form-header">
              <div class="more-search-form-header-title">按消费信息</div>
            </div>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem>
              <div class="form-item-label" slot="label">
                <div class="form-item-label-title">最近消费</div>
                <div class="form-item-label-action" v-if="formData.last_consume_time_type">
                  {{ showDateFormat(formData.last_consume_time) }}
                </div>
              </div>
              <custom-tabs
                v-model="formData.last_consume_time_type"
                :options="options.createday_keys"
                :date.sync="formData.last_consume_time"
                dateType="daterange"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="消费次数">
              <div class="width100 flex yCenter">
                <InputNumber
                  :max="formData.pay_num_max || 9999"
                  :min="0"
                  :precision="0"
                  v-model="formData.pay_num_min"
                  :active-change="false"
                  placeholder="0"
                  @on-focus="event => event.currentTarget.select()"
                />
                <div class="mx-10">至</div>
                <InputNumber
                  :min="formData.pay_num_min || 0"
                  :precision="0"
                  v-model="formData.pay_num_max"
                  :active-change="false"
                  placeholder="不限"
                  @on-focus="event => event.currentTarget.select()"
                />
              </div>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="累计消费">
              <div class="width100 flex yCenter">
                <custom-input-number
                  :max="formData.total_consume_price_max || 999999"
                  :min="0"
                  :precision="2"
                  v-model="formData.total_consume_price_min"
                  placeholder="0"
                />
                <div class="mx-10">至</div>
                <custom-input-number
                  :min="formData.total_consume_price_min || 0"
                  :precision="2"
                  v-model="formData.total_consume_price_max"
                  placeholder="不限"
                />
              </div>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="客单价">
              <div class="width100 flex yCenter">
                <custom-input-number
                  :max="formData.order_per_turnover_max || 999999"
                  :min="0"
                  :precision="2"
                  v-model="formData.order_per_turnover_min"
                  placeholder="0"
                />
                <div class="mx-10">至</div>
                <custom-input-number
                  :min="formData.order_per_turnover_min || 0"
                  :precision="2"
                  v-model="formData.order_per_turnover_max"
                  placeholder="不限"
                />
              </div>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <div class="more-search-form-header">
              <div class="more-search-form-header-title">其他信息</div>
            </div>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem>
              <div class="form-item-label" slot="label">
                <div class="form-item-label-title">最近预约</div>
                <div class="form-item-label-action" v-if="formData.reserve_time_type">
                  {{ showDateFormat(formData.reserve_time) }}
                </div>
              </div>
              <custom-tabs
                v-model="formData.reserve_time_type"
                :options="options.createday_keys"
                :date.sync="formData.reserve_time"
                dateType="daterange"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="预约次数">
              <div class="width100 flex yCenter">
                <InputNumber
                  :max="formData.reserve_times_max || 9999"
                  :min="0"
                  :precision="0"
                  v-model="formData.reserve_times_min"
                  :active-change="false"
                  placeholder="0"
                  @on-focus="event => event.currentTarget.select()"
                />
                <div class="mx-10">至</div>
                <InputNumber
                  :min="formData.reserve_times_min || 0"
                  :precision="0"
                  v-model="formData.reserve_times_max"
                  :active-change="false"
                  placeholder="不限"
                  @on-focus="event => event.currentTarget.select()"
                />
              </div>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem>
              <div class="form-item-label" slot="label">
                <div class="form-item-label-title">最近就诊</div>
                <div class="form-item-label-action" v-if="formData.his_order_time_type">
                  {{ showDateFormat(formData.his_order_time) }}
                </div>
              </div>
              <custom-tabs
                v-model="formData.his_order_time_type"
                :options="options.createday_keys"
                :date.sync="formData.his_order_time"
                dateType="daterange"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="就诊次数">
              <div class="width100 flex yCenter">
                <InputNumber
                  :max="formData.his_order_num_max || 9999"
                  :min="0"
                  :precision="0"
                  v-model="formData.his_order_num_min"
                  :active-change="false"
                  placeholder="0"
                  @on-focus="event => event.currentTarget.select()"
                />
                <div class="mx-10">至</div>
                <InputNumber
                  :min="formData.his_order_num_min || 0"
                  :precision="0"
                  v-model="formData.his_order_num_max"
                  :active-change="false"
                  placeholder="不限"
                  @on-focus="event => event.currentTarget.select()"
                />
              </div>
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem>
              <div class="form-item-label" slot="label">
                <div class="form-item-label-title">最近到店</div>
                <div class="form-item-label-action" v-if="formData.arrival_manual_time_type">
                  {{ showDateFormat(formData.arrival_manual_time) }}
                </div>
              </div>
              <custom-tabs
                v-model="formData.arrival_manual_time_type"
                :options="options.createday_keys"
                :date.sync="formData.arrival_manual_time"
                dateType="daterange"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="到店次数">
              <div class="width100 flex yCenter">
                <InputNumber
                  :max="formData.arrival_manual_num_max || 9999"
                  :min="0"
                  :precision="0"
                  v-model="formData.arrival_manual_num_min"
                  :active-change="false"
                  placeholder="0"
                  @on-focus="event => event.currentTarget.select()"
                />
                <div class="mx-10">至</div>
                <InputNumber
                  :min="formData.arrival_manual_num_min || 0"
                  :precision="0"
                  v-model="formData.arrival_manual_num_max"
                  :active-change="false"
                  placeholder="不限"
                  @on-focus="event => event.currentTarget.select()"
                />
              </div>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>
    <div class="footer" slot="footer">
      <Button style="margin-right: auto" type="primary" ghost @click="resetMoreSearch">清空筛选条件</Button>
      <Button @click="onCancel">取消</Button>
      <Button type="primary" @click="onSearch">确定</Button>
    </div>
  </Modal>
</template>

<script>
import CustomTabs from '@/view/user/components/more-search/custom-tabs.vue';
import moment from 'moment';
import CustomInputNumber from '@/components/CustomInputNumber/index.vue';
import { cloneDeep  } from 'lodash-es';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '',
  user_type: '',
  gift_status: '',
  sex: '',
  age_st: null,
  age_et: null,
  birthday_st: '',
  birthday_et: '',
  birthday_md_type: '',
  birthday_md: [],
  source: '',
  createTimeType: '',
  createTime: [],
  vip_type: [],
  vip_start_time: '',
  vip_end_time: '',
  vip_source: '',
  tag_ids: [],
  last_consume_time_type: '',
  last_consume_time: [],
  pay_num_max: null,
  pay_num_min: null,
  total_consume_price_min: null,
  total_consume_price_max: null,
  order_per_turnover_min: null,
  order_per_turnover_max: null,
  reserve_time_type: '',
  reserve_time: [],
  reserve_times_min: null,
  reserve_times_max: null,
  his_order_time_type: '',
  his_order_time: [],
  his_order_num_min: null,
  his_order_num_max: null,
  arrival_manual_time_type: '',
  arrival_manual_time: [],
  arrival_manual_num_min: null,
  arrival_manual_num_max: null,
  r: '',
};
export default {
  name: 'more-search',
  components: { CustomInputNumber, CustomTabs },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    options: {
      type: Object,
      default: () => ({
        sourceList: [],
        sexDesc: [],
        vip_status_desc: [],
        source_clinic_this: [],
        tag_desc: [],
      }),
    },
    queryFormData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      moment,
      formData: {},
    };
  },
  computed: {
    searchVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
    isVipDisable() {
      const nonVipId = this.options.vip_status_desc.find(item => item.is_vip === '0')?.kw;
      return id => {
        if (!this.formData.vip_type.length) return false;
        const isNonVipSelected = this.formData.vip_type.includes(nonVipId);
        return isNonVipSelected ? id !== nonVipId : id === nonVipId;
      };
    },
  },
  watch: {
    queryFormData: {
      handler(val) {
        this.formData = cloneDeep(val);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    resetMoreSearch() {
      this.formData = cloneDeep(init_query_form_data);
      const form = cloneDeep(this.queryFormData);
      delete form.id;
      // this.onCancel();
    },
    changeVisible(visible) {
      if (visible) {
        console.log(this.queryFormData, 'this.queryFormData')
        this.formData = cloneDeep(this.queryFormData);
      }
    },
    onSearch() {
      this.$emit('onSearch', this.formData);
      this.onCancel();
    },
    changeDate(date, type) {
      if (type === 'birthday') {
        this.formData.birthday = date;
      }
    },
    showDateFormat(date, format = 'YY/MM/DD') {
      if (Array.isArray(date)) {
        return date.map(day => moment(day).format(format)).join(' - ');
      }
      return date;
    },
    onCancel() {
      this.$emit('update:visible', false);
    },
    handleChangeDate(date, field) {
      this.formData[field] = date;
    },
  },
};
</script>

<style scoped lang="less">
.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.more-search-form :deep(.ivu-row) {
  justify-content: space-between;
}
.more-search-form :deep(.ivu-col-span-12) {
  flex: 0 0 48.5%;
}
.more-search-form :deep(.ivu-form-item) {
  width: 100%;
  margin-bottom: 16px;
}
.more-search-form :deep(.ivu-form-item-label) {
  text-align: left;
  width: 100%;
  padding-right: 0;
}
.more-search-form :deep(.ivu-input-number) {
  flex: 1;
}
.mx-10 {
  margin: 0 10px;
}
.width100 {
  width: 100%;
}
.form-item-label {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .form-item-label-action {
    font-size: 12px;
    color: #a8abb2;
    line-height: 12px;
  }
}
.more-search-form-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  .more-search-form-header-title {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
  }
  .more-search-form-header-title::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    background: #155bd4;
    margin-right: 10px;
    margin-bottom: -2px;
  }
}
</style>
<style lang="less">
.more-search-model .ivu-modal-body {
  max-height: 70vh;
  min-height: 300px;
  overflow-y: auto;
  padding: 16px 20px;
}
.more-search-model {
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-search-model .ivu-modal{
  top: 0;
}
</style>
