<template>
  <div class="EditUserModal-wrapper">
    <Modal
      :value="editModalVisible"
      title="修改用户信息"
      width="520"
      :mask-closable="false"
      ok-text="保存"
      @on-visible-change="changeVisible"
      class-name="user-auth-modal"
    >
      <template #header>
        <div class="EditUserModal-wrapper-header">
          <div class="EditUserModal-wrapper-header-title">修改用户信息</div>
          <div class="EditUserModal-wrapper-header-desc">
            <img src="@/assets/image/user/warning.png" alt="" />
            <div>灰色背景区域的信息需要短信验证后才能成功修改</div>
          </div>
        </div>
      </template>
      <Form
        ref="userModalForm"
        class="editUserModel"
        :model="formData"
        :rules="ruleValidate"
        label-colon
        :label-width="120"
        v-if="editModalVisible"
      >
        <div class="form-wrap">
          <Row>
            <Col span="24">
              <FormItem label="姓名" prop="real_name">
                <Input
                  v-model="formData.real_name"
                  placeholder="请输入用户真实姓名"
                  maxlength="16"
                  show-word-limit
                  style="width: 280px"
                ></Input>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem label="注册名" prop="real_name">
                <Input v-model="formData.nickname" placeholder="请输入注册名" disabled style="width: 280px"></Input>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem label="性别" prop="sex">
                <RadioGroup v-model="formData.sex" style="width: 280px">
                  <Radio
                    v-for="item in sexyTypes.filter(item => +item.id !== 3)"
                    :label="item.id"
                    :key="item.id"
                    :disabled="checkCard || validateCard"
                    :vertical="false"
                  >
                    {{ item.desc }}
                  </Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem label="身高" prop="mail">
                <Input v-if="+formData.height > 0" v-model="formData.height" disabled style="width: 280px">
                  <template #suffix> <div style="height: 100%; display: flex; align-items: center">cm</div> </template>
                </Input>
                <Input v-else :value="'-'" disabled style="width: 280px">
                  <template #suffix> <div style="height: 100%; display: flex; align-items: center">cm</div> </template>
                </Input>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem label="出生日期" prop="birthday">
                <div class="flex" style="align-items: center">
                  <DatePicker
                    type="date"
                    clearable
                    placeholder="请选择出生日期"
                    @on-change="changeBirdth"
                    :disabled="checkCard || validateCard"
                    :value="formData.birthday"
                    style="width: 280px"
                  ></DatePicker>
                  <span style="margin-left: 10px; color: #999999">{{ formatDuration(formData.birthday) }}</span>
                </div>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem label="农历生辰">
                <Input :value="getSolarBirth" placeholder="根据出生日期自动生成" disabled style="width: 280px" />
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem label="出生时辰">
                <Select v-model="formData.time_of_birth" placeholder="请选择出生时辰" style="width: 280px">
                  <Option v-for="item in birthTimes" :value="item.value" :key="item.value" :label="item.label"></Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem label="证件/编号" prop="card_no" class="id-card">
                <Input
                  placeholder="请输入证件编号"
                  :disabled="!formData.card_type"
                  clearable
                  v-model="formData.card_no"
                  @on-blur="getBirthFromCard"
                  style="width: 280px"
                  class="customInputPrepend"
                >
                  <template #prepend>
                    <Select
                      v-model="formData.card_type"
                      class="not-hover"
                      placeholder="身份证"
                      disabled
                      clearable
                      style="width: 82px"
                    >
                      <Option
                        v-for="item in certificateTypes"
                        :value="item.id"
                        :key="item.id"
                        :label="item.desc"
                      ></Option>
                    </Select>
                  </template>
                </Input>
              </FormItem>
            </Col>
          </Row>
        </div>

        <FormItem label="" v-if="userInfo.is_vip === '1'">
          <Row>
            <Col span="20" style="margin-top: -15px">
              <div class="vip-box" v-for="item in userInfo.vip_info" :key="item.user_type_text">
                <img class="tip-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0927/123915_94370.png" />
                <div class="item-box flex">
                  <div class="label">会员名称:</div>
                  <div class="value">{{ item.user_type_text || '-' }}</div>
                </div>
                <div class="item-box flex">
                  <div class="label">会员开通渠道:</div>
                  <div class="value">{{ item.vip_source_text || '-' }}</div>
                </div>
              </div>
            </Col>
          </Row>
        </FormItem>

        <Row>
          <Col span="24">
            <FormItem label="联系地址">
              <Row>
                <Col span="24">
                  <el-cascader
                    v-model="selectedAddress"
                    :options="options"
                    clearable
                    placeholder="请选择联系地址"
                    size="small"
                    popper-class="address-com"
                    style="width: 280px"
                    @change="regionChange"
                  >
                  </el-cascader>
                </Col>
                <Col span="24">
                  <Input
                    v-model.trim="formData.address.other"
                    placeholder="详细地址"
                    style="width: 280px; margin-top: 10px"
                  ></Input>
                </Col>
              </Row>
            </FormItem>
          </Col>
        </Row>

        <Row style="margin-top: -6px">
          <Col span="24">
            <FormItem label="用户标签">
              <div class="tagList flex" style="width: 280px">
                <div class="tag" v-for="item in chronicList" :key="item.id">
                  <div>{{ item.tag_name }}</div>
                  <Icon
                    class="user-label-item-close"
                    type="ios-close-circle"
                    color="#abb0b8"
                    :size="18"
                    @click.stop="delUserTag(item)"
                  />
                </div>
                <Poptip v-model="tagPopVisible" placement="top">
                  <div class="user-label-add" @click.stop="handleOpenTagPop"><Icon size="16" type="ios-add" /></div>
                  <template #content>
                    <div class="action-content">
                      <div v-for="item in tag_list" :key="item.id">
                        <div class="action-content-header">
                          <div class="action-content-header-label">{{ item.name }}</div>
                        </div>
                        <div class="action-content-container">
                          <div
                            :class="{
                              'action-content-container-item': true,
                              active: chronicList.some(label => tag.id === label.tag_id),
                            }"
                            v-for="tag in item.tag_info"
                            :key="tag.id"
                            @click.stop="addUserTag(tag)"
                          >
                            {{ tag.name }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </Poptip>
              </div>
            </FormItem>
          </Col>
        </Row>
        <Row style="margin-top: -12px">
          <Col span="20">
            <FormItem label="用户来源" prop="source">
              <Select v-model="formData.source" placeholder="请选择用户来源">
                <Option v-for="item in sourceTypes" :value="item.id" :key="item.id" :label="item.desc"></Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div slot="footer">
        <div>
          <Button @click="cancel">取消</Button>
          <Button type="primary" :loading="saveLoading" @click="handleSubmit">保存</Button>
        </div>
      </div>
    </Modal>
    <Modal
      v-model="showConfirm"
      title=" "
      class="confirm-modal"
      :footer-hide="true"
      :closable="false"
      width="300"
      :mask-closable="false"
    >
      <div>
        <div>
          <p style="margin: 20px">用户信息已更新，历史数据将不做同步，请知悉。</p>
        </div>
        <div class="footer">
          <div class="case flex" style="margin-top: 40px; justify-content: flex-end">
            <Button type="primary" class="ml10" @click="confirm">确定</Button>
          </div>
        </div>
      </div>
    </Modal>

    <modify-user-mobile-modal
      v-model="modifyUserVisible"
      type="verify"
      @verifySuccess="verifySuccess"
      :phone="phone"
    ></modify-user-mobile-modal>
  </div>
</template>

<script>
import Vue from 'vue';
import VueAwesomeCountdown from 'vue-awesome-countdown';
import S from '@/libs/util';
import io from '@/libs/io'; // Http request
import { cloneDeep } from 'lodash-es';
import modifyUserMobileModal from './modifyUserMobileModal.vue';
import { CodeToText, regionData } from '@/libs/chinaMap';
import solarLunar from 'solarlunar';

Vue.use(VueAwesomeCountdown, 'vac');
const init_form_data = {
  sex: '',
  uid: '',
  real_name: '',
  birthday: '',
  time_of_birth: '', // 出生时辰
  card_type: 'ID',
  card_no: '',
  mobile: '',
  // authcode: '',
  address: {
    prov: {
      code: '',
      name: '',
    },
    city: {
      code: '',
      name: '',
    },
    county: {
      code: '',
      name: '',
    },
    other: '',
  },
  source: '',
  nickname: '',
  labels: [],
};
export default {
  name: 'EditUserModal',
  mixins: [],

  components: { modifyUserMobileModal },

  props: {
    editModalVisible: {
      type: Boolean,
      default: false,
    },
    sexyTypes: {
      type: Array,
      default: () => [],
    },
    sourceTypes: {
      type: Array,
      default: () => [],
    },
    userInfo: {
      type: Object,
      default: () => {},
    },
    getUserInfo: {
      type: Function,
    },
    labelTypes: {
      type: Array,
      default: () => [],
    },
    labelList: {
      type: Array,
      default: () => [],
    },
    phone: {
      type: [String, Number],
      default: '',
    },
    authStatus: {
      type: [String, Number],
      default: '',
    },
    tag_list: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    const validateCardPass = (rule, value, callback) => {
      console.log('-> %c value  === %o ', 'font-size: 15px', value);
      if (value === '') {
        callback();
      } else {
        // const reg = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/
        const reg =
          /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/;

        if (!reg.test(value)) {
          // 对第二个密码框单独验证
          callback(new Error('请输入有效的身份证号'));
        } else {
          callback();
        }
      }
    };
    return {
      selectedAddress: [],
      options: regionData,
      birthTimes: [
        { label: '子时(23点-1点)', value: '子时' },
        { label: '丑时(1点-3点)', value: '丑时' },
        { label: '寅时(3点-5点)', value: '寅时' },
        { label: '卯时(5点-7点)', value: '卯时' },
        { label: '辰时(7点-9点)', value: '辰时' },
        { label: '巳时(9点-11点)', value: '巳时' },
        { label: '午时(11点-13点)', value: '午时' },
        { label: '未时(13点-15点)', value: '未时' },
        { label: '申时(15点-17点)', value: '申时' },
        { label: '酉时(17点-19点)', value: '酉时' },
        { label: '戌时(19点-21点)', value: '戌时' },
        { label: '亥时(21点-23点)', value: '亥时' },
      ],
      certificateTypes: [
        {
          id: 'ID',
          desc: '身份证',
          kw: 'CARD_TYPE_ID',
        },
      ],
      formData: {
        ...init_form_data,
      },
      ruleValidate: {
        card_no: [{ validator: validateCardPass, trigger: 'blur' }],
        real_name: [
          { required: true, message: '请输入用户真实姓名', trigger: 'blur' },
          { max: 16, message: '用户姓名长度不得大于16', trigger: 'blur' },
        ],
        sex: [{ required: true, message: '请选择性别', trigger: 'blur' }],
        mobile: [{ required: true, message: '请输入手机号', trigger: 'change' }],
        birthday: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
      },
      isChangeMobile: false,
      showConfirm: false,
      checkCard: false,
      chronic: '',
      chronicList: [],
      freeze_user_info: {},
      modifyUserVisible: false,
      saveLoading: false,
      tagPopVisible: false,
      tagList: [],
    };
  },

  computed: {
    getSolarBirth() {
      const { birthday, time_of_birth } = this.formData;
      if (birthday) {
        let birthStr = '';
        const year = S.moment(birthday).get('year');
        const month = S.moment(birthday).get('month') + 1;
        const day = S.moment(birthday).get('date');
        const solarInfo = solarLunar.solar2lunar(year, month, day);
        birthStr = `${solarInfo.gzYear}(${solarInfo.animal})年  ${solarInfo.monthCn}${solarInfo.dayCn}`;
        if (time_of_birth) birthStr += time_of_birth;
        return birthStr;
      } else {
        return '-';
      }
    },

    splitMobile(mobile) {
      return mobile => {
        if (mobile) {
          console.log('-> %c mobile  === %o ', 'font-size: 15px', mobile);
          return mobile.replace(/^(.{3})(.*)(.{4})$/, '$1-$2-$3');
        } else {
          return '';
        }
      };
    },
    formatDuration(birdth) {
      return birdth => {
        return S.formatDuration(birdth);
      };
    },

    validateCard() {
      const reg =
        /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/;
      let is_verify_success = reg.test(this.formData.card_no);
      if (is_verify_success) {
        this.formData.sex = this.getGender(this.formData.card_no);
      }
      return is_verify_success;
    },
  },

  watch: {
    userInfo: {
      handler(val) {
        this.setFreezeData(val);
        const copyVal = this.$lodash.cloneDeep(val);

        this.chronicList = copyVal?.label_list || [];
        this.formData.real_name = copyVal.real_name || '';
        this.formData.birthday = copyVal.birthdayDate;
        this.formData.card_no = copyVal.card_no || '';
        // this.formData.card_type = copyVal.card_type || ''
        this.formData.nickname = copyVal.nickname || '';
        this.formData.sex = copyVal.sex || '';
        this.formData.time_of_birth = copyVal.time_of_birth || '';
        this.formData.source = copyVal.source || '';
        this.formData.height = copyVal.height;
        if (copyVal.address && copyVal.address.detail) {
          delete copyVal.address.detail;
        }
        if (copyVal.card_no) {
          this.checkCard = true;
        }
        this.formData.address = copyVal.address || {};
        this.formData.uid = copyVal.uid;
        if (copyVal.address && copyVal.address.county && copyVal.address.county.code) {
          this.selectedAddress = [copyVal.address.prov.code, copyVal.address.city.code, copyVal.address.county.code];
        } else if (copyVal.address && copyVal.address.prov && copyVal.address.prov.code) {
          this.selectedAddress = [copyVal.address.prov.code, copyVal.address.city.code];
        }
      },
      deep: true,
    },
  },

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    arrayIsEqual(arr1 = [], arr2 = []) {
      let is_equal = true;
      if (arr1.length === arr2.length) {
        arr1.some(arr1_item => {
          let equalObj = arr2.find(arr2_item => arr2_item.id === arr1_item.id);
          if (equalObj === undefined) {
            is_equal = false;
          }
        });
      } else {
        is_equal = false;
      }
      return is_equal;
    },
    setFreezeData(val) {
      let copyVal = this.$lodash.cloneDeep(val);
      this.freeze_user_info = this.$lodash.cloneDeep({
        real_name: copyVal.real_name || '',
        nickname: copyVal.nickname || '',
        card_no: copyVal.card_no || '',
        birthday: copyVal.birthdayDate || '',
        sex: copyVal.sex || '',
        time_of_birth: copyVal.time_of_birth || '',
      });
    },
    freezeDataIsChange() {
      let is_change = false;
      for (const key in this.freeze_user_info) {
        if (this.freeze_user_info[key] !== this.formData[key]) {
          is_change = true;
          return true;
        }
      }
      return is_change;
    },
    getGender(idNumber) {
      if (!idNumber) return;
      let genderCode = idNumber.charAt(16);
      console.log('-> genderCode', genderCode);
      if (parseInt(genderCode) % 2 == 0) {
        return '2';
      } else {
        return '1';
      }
    },
    getBirthFromCard() {
      console.log(this.formData.card_no);
      const reg =
        /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
      if (reg.test(this.formData.card_no)) {
        // 对第二个密码框单独验证
        this.formData.birthday = this.formData.card_no.substr(6, 8).replace(/(.{4})(.{2})/, '$1-$2-');
      }
    },
    confirm() {
      this.$Message.success('更新用户信息成功');
      this.getUserInfo();
      // this.showConfirm = false
    },
    changeMobileVisible() {
      this.isChangeMobile = true;
    },
    changeBirdth(date) {
      this.formData.birthday = date;
    },
    changeMobile() {
      this.isChangeMobile = !this.isChangeMobile;
    },
    cancel() {
      this.formData = { ...init_form_data };
      this.checkCard = false;
      this.$refs.userModalForm.resetFields();
      this.$emit('update:editModalVisible', false);
      this.$emit('updateUserInfo');
    },
    ok() {},
    changeVisible(flag) {
      if (flag) {
        this.chronicList = cloneDeep(this.labelList);
        this.formData.labels = this.chronicList.map(item => item.id);
        console.log('=>(EditUserModal.vue:417) this.chronicList', this.chronicList);
      }
      !flag && this.cancel();
    },
    verifySuccess() {
      this.$refs['userModalForm'].validate(valid => {
        if (valid) {
          this.saveUserInfo();
        }
      });
    },
    handleSubmit() {
      this.$refs['userModalForm'].validate(valid => {
        if (valid) {
          if (this.freezeDataIsChange()) {
            this.modifyUserVisible = true;
          } else {
            this.saveUserInfo();
          }
        }
      });
    },
    saveUserInfo() {
      const params = {
        ...this.formData,
      };
      this.saveLoading = true;
      this.$api
        .editUserInfo(params)
        .then(
          res => {
            this.saveLoading = false;
            this.cancel();
            this.confirm();
          },
          err => {}
        )
        .finally(() => (this.saveLoading = false));
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    // onCountDownFinish() {
    //   this.countdowning = false;
    // },
    // 获取验证码
    // onCountDownStart() {
    //   if (this.formData.mobile.trim() == '') {
    //     this.$Message.error('请输入手机号');
    //     return;
    //   }
    //   io.post('clinic/mobile.sendauthcode', { mobile: this.formData.mobile })
    //     .then(() => {
    //       this.$Message.success('发送成功');
    //       if (!this.forgetPassword) {
    //         this.$refs.vac.startCountdown(true);
    //       } else {
    //         this.$refs.vac2.startCountdown(true);
    //       }
    //       this.countdowning = true;
    //     })
    //     .catch(error => {
    //       {};
    //     });
    // },
    regionChange(address) {
      console.log(address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.address.prov = prov;
        this.formData.address.city = city;
        this.formData.address.county = county;
      } else {
        this.formData.address = {
          prov: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          other: '',
        };
      }

      // 手动触发校验
      // this.$refs.formData.validateField('selectedAddress')
    },
    handleOpenTagPop() {
      this.tagPopVisible = true;
    },
    delUserTag(row) {
      this.chronicList = this.chronicList.filter(item => item.tag_id !== row.tag_id);
      this.formData.labels = this.chronicList.map(item => item.tag_id);
    },
    addUserTag(row) {
      const isAdd = this.chronicList.every(item => item.tag_id !== row.id);
      if (isAdd) {
        this.chronicList.push({
          ...row,
          tag_id: row.id,
          tag_name: row.name,
        });
      } else {
        this.chronicList = this.chronicList.filter(item => item.tag_id !== row.id);
      }
      this.formData.labels = this.chronicList.map(item => item.tag_id);
    },
  },
};
</script>

<style lang="less">
.confirm-modal {
  .ivu-modal-header {
    display: none;
  }

  //.ivu-modal-body{
  //  height: 200px;
  //}
}

.id-card {
  .ivu-form-item-error-tip {
    margin-left: 135px;
  }
}
</style>

<style lang="less" scoped>

::v-deep .ivu-select-selection {
  .ivu-tag {
    display: none;
  }
}

.tagList {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-top: 4px;
  .tag {
    padding: 3px 16px;
    font-size: 13px;
    color: #155bd4;
    line-height: 18px;
    background: rgba(21, 91, 212, 0.06);
    border-radius: 14px;
    position: relative;
    cursor: default;
    margin-bottom: 8px;
    margin-right: 6px;

    .user-label-item-close {
      display: none;
      position: absolute;
      right: -4px;
      top: -4px;
      cursor: pointer;
      color: rgba(20, 90, 210, 0.5);
    }
  }

  .tag:hover {
    background: rgba(21, 91, 212, 0.2);
  }
  .tag:hover .user-label-item-close {
    display: block;
  }
  .user-label-add {
    width: 49px;
    height: 24px;
    background: #f6f6f6;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-bottom: 8px;
  }
  .user-label-add:hover {
    cursor: pointer;
    background: rgba(21, 91, 212, 0.2);
  }
}

.ml27 {
  margin-left: 27px;
}

.vip-box {
  background: rgba(48, 136, 255, 0.08);
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  &:first-child {
    margin-bottom: 8px;
  }
  .tip-icon {
    width: 16px;
    min-width: 16px;
    height: 16px;
    margin-right: 12px;
  }

  .item-box {
    font-weight: 400;
    font-size: 12px;
    color: #909399;
    line-height: 16px;

    .label {
    }

    .value {
      color: #3088ff;
      margin-left: 10px;
      margin-right: 10px;
    }
  }
}
.action-content {
  width: 280px;
  min-height: 100px;
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 16px 0 16px;
  background: #ffffff;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  .action-content-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .action-content-header-label {
      font-weight: 500;
      font-size: 14px;
      color: #303133;
      line-height: 20px;
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 隐藏溢出的文本 */
      text-overflow: ellipsis; /* 显示省略号 */
    }
  }
  .action-content-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    gap: 0 10px; /* 设置间距 */
    .action-content-container-item {
      width: fit-content;
      padding: 5px 16px;
      background: rgba(21, 91, 212, 0.06);
      border-radius: 14px;
      font-size: 13px;
      color: #155bd4;
      line-height: 18px;
      margin-bottom: 16px;
      cursor: pointer;
    }
    .action-content-container-item.active {
      background: #115bd4;
      color: #fff;
    }
  }
}
.form-wrap {
  background: #f9fafb;
  border-radius: 4px;
  padding-top: 16px;
  margin-bottom: 16px;
}
.EditUserModal-wrapper-header {
  display: flex;
  align-items: center;
}
.EditUserModal-wrapper-header-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  line-height: 24px;
  margin-right: 24px;
}
.EditUserModal-wrapper-header-desc {
  display: flex;
  align-items: center;
  background: #fff8eb;
  border-radius: 4px;
  padding: 8px 12px;
  > img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  > div {
    font-size: 12px;
    color: #ffa300;
    line-height: 16px;
  }
}
:deep(.not-hover .ivu-select-selection:hover) {
  background-color: inherit;
  margin: -1px;
  border: 1px solid transparent;
}
:deep(.customInputPrepend .ivu-input-group-prepend) {
  padding: 4px 0;
}
.editUserModel :deep(.el-input .el-input__inner) {
  border-radius: 4px !important;
}
</style>
<style lang="less">
.user-auth-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal-body {
    max-height: 520px;
    min-height: 150px;
    overflow-y: auto;
  }
  .ivu-modal {
    top: 0;
  }
}
</style>
