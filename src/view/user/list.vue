<!--这个页面不要加带pop的操作框， 如果要加，必须在下面的userListMouseOver去增加对应的igore-->
<template>
  <div class="user-wrap" ref="userListRef" @mouseover="userListMouseOver">
    <standard-table
      :loading="tableLoading"
      ref="user-table"
      class="user-table"
      :columns="tableCols"
      :data="list"
      @on-sort-change="sortChanged"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form class="table-form" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <FormItem label="">
            <Input v-model="queryFormData.keyword" placeholder="用户姓名/手机号" clearable />
          </FormItem>
          <FormItem label="">
            <Select v-model="queryFormData.user_type" placeholder="手机号状态" clearable>
              <Option v-for="(item, index) in userTypesDesc" :value="item.id" :key="index">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem label="">
            <Select v-model="queryFormData.gift_status" placeholder="会员赠品" clearable>
              <Option v-for="(item, index) in gift_status_desc" :value="item.id" :key="index">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <div class="flex">
              <Button @click="openMoreSearch" style="margin-right: 10px">高级筛选</Button>
              <Button type="primary" @click="onSearch" style="margin-right: 10px">筛选</Button>
              <span class="list-reset-btn" @click="onResetSearch(true)">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </div>
          </FormItem>
        </Form>
        <div ref="selectedSearchRef" class="selected-search" v-if="search_text.length > 0">
          <div class="selected-search-label">已选：</div>
          <div class="selected-search-item" v-for="(item, i) in search_text" :key="i">
            {{ item.name }}：{{ item.value }}
            <Icon class="cursor" type="md-close" color="#606266" :size="12" @click="clearSearch(item)" />
          </div>
        </div>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="creatConsumer">创建用户</Button>
          <Button
            type="default"
            style="margin-left: 10px"
            @click="exportExcel"
            :loading="exportLoading"
            v-eleControl="'Ezr6lb7dk6'"
          >
            导出
          </Button>
        </div>
      </template>

      <template #userinfo="{ row, index }">
        <div class="userinfo-wrap">
          <div class="avatar-wrapper">
            <div
              style="width: fit-content; height: fit-content; border-radius: 50%"
              @mouseenter="e => getUserDetail(e, row, index)"
              @mouseleave="clearUserDetailTimer"
            >
              <Avatar
                :src="
                  row.avatar | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
                "
                shape="square"
                icon="ios-person"
                class="avatar"
                :style="{
                  border: row.vip_info?.some(vip => vip.user_type === '1' || vip.user_type === '4')
                    ? '2px solid #b0c3dd'
                    : '2px solid  transparent',
                }"
                @on-error="imgOnLoadErr(index)"
              />
              <img
                v-if="row.vip_info?.some(vip => vip.user_type === '1' || vip.user_type === '4')"
                class="head980vip"
                :src="vip980Avatar"
                alt="#"
              />
            </div>
          </div>
          <div class="user-wrapper">
            <div class="username">
              <div
                class="name"
                @click="queryDetail(row)"
                :style="{
                  maxWidth: +row.auth_status === 2 ? 'calc(85% - 20px)' : '85%',
                  width: 'fit-content',
                }"
              >
                {{ row.real_name || '-' }}
              </div>
              <Tooltip
                v-if="+row.auth_status === 2"
                :transfer="false"
                content="用户中心已同步"
                placement="top"
                :offset="2"
              >
                <img class="tip" src="../../assets/image/user/async.png" alt="#" />
              </Tooltip>
            </div>
            <Tooltip content="粉丝金钻" placement="top">
              <div v-if="row.vip_info?.length > 0" class="user-desc">
                <img v-if="row.vip_info?.some(vip => vip.user_type === '3')" :src="vip9800" alt="#" />
              </div>
            </Tooltip>
            <div v-if="row.gift_status_text" @click="sendGoods(row)" class="user-desc cursor">
              {{ row.gift_status_text }}
            </div>
          </div>
        </div>
      </template>
      <template #mobile="{ row }">
        <Tooltip
          :content="calcPhoneLabel(row)"
          placement="top"
          :transfer="false"
          :offset="2"
          :disabled="+row.mobile_type === 1"
        >
          <div class="yCenter" style="font-size: 14px">
            <div class="phone-tab" v-if="+row.mobile_type === 2">
              <svg-icon iconClass="short-logo" size="28"></svg-icon>
            </div>
            <div class="phone-tab-box xEnd yCenter" v-if="+row.mobile_type === 4">
              <img class="phone-tab-box-img" src="../../assets/image/user/tip.png" alt="#" />
            </div>
            <div class="phone-tab" v-if="+row.mobile_type !== 2 && +row.mobile_type !== 4"></div>
            <div
              class="cursorDefault"
              :style="{ color: +row.mobile_type === 3 || +row.mobile_type === 4 ? '#909399' : '' }"
            >
              {{ row.mobile || '-' }}
            </div>
          </div>
        </Tooltip>
      </template>
      <template #card_coupons="{ row }">
        <div class="card_coupons link" @click="queryDetail(row, '4')">
          <div class="card_coupons-item">
            <div style="width: 70px">持卡：</div>
            <div>{{ row.xn_service_info.service_card_batch_count || 0 }}张</div>
          </div>
          <div class="card_coupons-item">
            <div style="width: 70px">可服务：</div>
            <div>{{ row.xn_service_info.service_card_count || 0 }}次</div>
          </div>
        </div>
      </template>
      <template #redeemable_coupons="{ row }">
        <div class="card_coupons link" @click="queryDetail(row, '5')">
          <div class="card_coupons-item">
            <div style="width: 42px">持卡：</div>
            <div>{{ row.tdj_service_info.service_card_batch_count || 0 }}张</div>
          </div>
          <div class="card_coupons-item">
            <div style="width: 42px">可兑：</div>
            <div>{{ row.tdj_service_info.service_card_count || 0 }}次</div>
          </div>
        </div>
      </template>
      <template #stored="{ row }">
        <div class="card_coupons" v-if="Number(row?.recharge_money) > 0 || Number(row?.recharge_yzt_money) > 0">
          <div class="card_coupons-item" v-if="Number(row?.recharge_yzt_money) > 0">
            <div style="width: 72px">云储值：</div>
            <div v-text-format.number="row?.recharge_yzt_money"></div>
          </div>
          <div class="card_coupons-item" v-if="Number(row?.recharge_money) > 0">
            <div style="width: 72px">门店储值：</div>
            <div v-text-format.number="row?.recharge_money"></div>
          </div>
        </div>
        <div v-else style="text-align: center">-</div>
      </template>
      <template #consumptions="{ row }">
        <div class="card_coupons">
          <div class="card_coupons-item">
            <div style="width: 70px">累计消费：</div>
            <div v-text-format.number="row?.user_statistics?.total_consume_price"></div>
          </div>
          <div class="card_coupons-item">
            <div style="width: 70px">消费次数：</div>
            <div>{{ row?.user_statistics?.total_consume_num || 0 }}次</div>
          </div>
          <div class="card_coupons-item">
            <div style="width: 70px">客单价：</div>
            <div v-text-format.number="row?.user_statistics?.order_per_price"></div>
          </div>
        </div>
      </template>
      <template #times="{ row }">
        <div class="card_coupons">
          <div class="card_coupons-item">
            <div style="width: 70px">注册时间：</div>
            <div>{{ row.create_time | data_format }}</div>
          </div>
          <div class="card_coupons-item">
            <div style="width: 70px">最近消费：</div>
            <div>{{ row.user_statistics?.last_consume_time | data_format }}</div>
          </div>
          <div class="card_coupons-item">
            <div style="width: 70px">最近到店：</div>
            <div>{{ row.arrival_time | data_format }}</div>
          </div>
        </div>
      </template>
      <template #action="{ row }">
        <a @click="createOrderNew(row)">开单</a>
        <Divider type="vertical" />
        <a @click="createReserve(row)">预约</a>
        <Divider type="vertical" />
        <Dropdown placement="bottom-end" transfer @on-click="key => handleClickAction(key, row)">
          <img style="width: 12px; height: 12px; cursor: pointer" src="../../assets/image/user/omit.png" alt="#" />
          <template #list>
            <DropdownMenu class="user-table-action">
              <DropdownItem v-if="isCanOpenVip(row)" name="1">开会员</DropdownItem>
              <!-- <DropdownItem name="2">预约</DropdownItem>-->
              <DropdownItem name="3">储值</DropdownItem>
              <DropdownItem name="4">记一笔</DropdownItem>
              <DropdownItem name="5">详情</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
      </template>
    </standard-table>

    <Poptip
      ref="userPop"
      popper-class="userinfo-pop"
      :placement="userPopPlacement"
      theme="light"
      :reference="userPopReference"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <user-tooltip
          ref="userToolPopRef"
          :detail="selectedRowDetail"
          :loading="selectedRowLoading"
          :tag-list="tag_list"
          @success="submitQueryForm"
          @openVip="openVip"
          @hidePop="hidePop"
          @addUserFollow="addUserFollow"
        />
      </template>
    </Poptip>

    <!-- 创建用户 -->
    <create-user-modal
      :showCreateInfo="optionChange"
      :source-list="sourceList"
      :visible.sync="consumerVisibleDia"
      :level-list="levelList"
      :name="creatName"
      :quickCreate="$route.params.quickCreate"
      :quickCreateInfo="$route.params.quickCreateInfo"
      @success="submitQueryForm"
    ></create-user-modal>

    <more-search
      :visible.sync="moreSearchVisible"
      :query-form-data="queryFormData"
      :options="keys_option"
      @onClear="onResetSearch"
      @onSearch="onMoreSearch"
    />
    <!-- 开通会员 -->
    <open-vip-modal
      v-model="vipVisible"
      :interestList="interestList"
      :row="currentRow"
      @success="getsList"
    ></open-vip-modal>
    <!-- 发货 -->
    <send-goods v-model="sendGoodsVisible" :row="currentRow" @success="submitQueryForm"></send-goods>
    <add-user-follow
      :visible.sync="addUserFollowVisible"
      :selected-row="{}"
      :user-info="currentRow"
      @success="submitQueryForm"
    />
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
/* eslint-disable */
// import CreateConsumerDia from '../daily/arrival/components/CreateConsumerDia'
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';
import search from '@/mixins/search';
import renderHeader from '@/mixins/renderHeader';
import { isRstClinic, isOpenjhPay } from '@/libs/runtime';
import UserTooltip from '@/view/user/components/user-tooltip.vue';
import MoreSearch from '@/view/user/components/more-search/more-search.vue';
import vip980 from '@/assets/image/user/980health.png';
import vip980Avatar from '@/assets/image/user/980vip.png';
import vip9800 from '@/assets/image/user/9800fans.png';
import vip9800Avatar from '@/assets/image/user/9800vip.png';
import openVipModal from '@/components/k-pay-dialog/openVipModal.vue';
import sendGoods from './components/sendGoods.vue';
import { cloneDeep, debounce  } from 'lodash-es';
import AddUserFollow from '@/view/user/components/add-user-follow.vue';
import moment from 'moment/moment';
import calender from '@/assets/image/user/calender.png';
import { isEmpty } from '@/utils/helper';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '',
  user_type: '',
  gift_status: '',
  sex: '',
  age_st: null,
  age_et: null,
  birthday_st: '',
  birthday_et: '',
  birthday_md_type: '',
  birthday_md: [],
  source: '',
  createTimeType: '',
  createTime: [],
  vip_type: [],
  vip_start_time_type: '',
  vip_start_time: [],
  vip_end_time_type: '',
  vip_end_time: [],
  vip_source: '',
  tag_ids: [],
  last_consume_time_type: '',
  last_consume_time: [],
  pay_num_max: null,
  pay_num_min: null,
  total_consume_price_min: null,
  total_consume_price_max: null,
  order_per_turnover_min: null,
  order_per_turnover_max: null,
  reserve_time_type: '',
  reserve_time: [],
  reserve_times_min: null,
  reserve_times_max: null,
  his_order_time_type: '',
  his_order_time: [],
  his_order_num_min: null,
  his_order_num_max: null,
  arrival_manual_time_type: '',
  arrival_manual_time: [],
  arrival_manual_num_min: null,
  arrival_manual_num_max: null,
  r: '',
};
// 反显搜索信息枚举 1 单值 2 区间 3 类型
const query_form_data_map = [
  //  [值类型，值field, 类型为3有值， 需要反显的枚举]
  // [1, 'keyword', '', '', '用户名'],
  // [1, 'user_type', '', 'userTypesDesc', '手机号状态'],
  // [1, 'gift_status', '', 'gift_status_desc', '会员赠品'],
  [1, 'sex', '', 'sexDesc', '性别'],
  [2, ['age_st', 'age_et'], '', '', '年龄'],
  [3, 'birthday_md', 'birthday_md_type', 'birthday_keys', '生日'],
  [2, ['birthday_st', 'birthday_et'], '', '', '出生日期'],
  [1, 'source', '', 'sourceList', '来源'],
  [3, 'createTime', 'createTimeType', 'createday_keys', '创建时间'],
  [1, 'vip_type', '', 'vip_status_desc', '会员类型'],
  [1, 'vip_source', '', 'source_clinic_this', '会员有效开通途径'],
  [3, 'vip_start_time', 'vip_start_time_type', 'createday_keys', '会员生效'],
  [3, 'vip_end_time', 'vip_end_time_type', 'createday_keys', '会员截止'],
  [1, 'tag_ids', '', 'tag_desc', '用户标签'],
  [3, 'last_consume_time', 'last_consume_time_type', 'createday_keys', '最近消费'],
  [2, ['pay_num_min', 'pay_num_max'], '', '', '消费次数'],
  [2, ['total_consume_price_min', 'total_consume_price_max'], '', '', '累计消费'],
  [2, ['order_per_turnover_min', 'order_per_turnover_max'], '', '', '客单价'],
  [3, 'reserve_time', 'reserve_time_type', 'createday_keys', '最近预约'],
  [2, ['reserve_times_min', 'reserve_times_max'], '', '', '预约次数'],
  [3, 'his_order_time', 'his_order_time_type', 'createday_keys', '最近就诊'],
  [2, ['his_order_num_min', 'his_order_num_max'], '', '', '就诊次数'],
  [3, 'arrival_manual_time', 'arrival_manual_time_type', 'createday_keys', '最近到店'],
  [2, ['arrival_manual_num_min', 'arrival_manual_num_max'], '', '', '到店次数'],
];
const mobile_tip =
  '' +
  '临时号：140/141号段均为临时号，在注册用户账号时如果不愿提供真实手机号并验证注册，可先选用临时号注册的方式来实现日常问诊、理疗\n' +
  '\n' +
  '暂存号：用户不愿提供短信验证码时，暂存的用户手机号。待用户认可后，可将临时号修改成常规号码\n' +
  '\n' +
  '原号：在互医端用户信息合并时，如果忽略某个诊所下的合并，该诊所下的原号会被释放为临时号，原号仅做展示';
const consumptions_tip =
  '' +
  '累计消费”和“消费次数”在此处不会剔除退款数据\n' +
  '\n' +
  '比如用户消费了3次，第一次消费了200元，第二次消费了300元（全部退款），第三次消费100元（退款50元）。最后计算所得\n' +
  '累计消费：600元\n' +
  '消费次数：3次\n' +
  '客单价：200元';
const time_tip =
  '' +
  '最近到店时间受以下几个因素影响：\n' +
  '1）日常模块中录入到店行为\n' +
  '2）消费订单下单\n' +
  '3）储值订单下单\n' +
  '4）核销理疗服务';
export default {
  name: 'list',
  components: {
    StandardTable,
    AddUserFollow,
    MoreSearch,
    UserTooltip,
    CreateUserModal,
    openVipModal,
    sendGoods,
  },
  mixins: [search, renderHeader],
  data() {
    return {
      isRstClinic: isRstClinic(),
      apiName: 'getUserListV2',
      listName: 'users',
      vip980,
      vip980Avatar,
      vip9800,
      vip9800Avatar,
      registrationTimes: [],
      consumptionTimes: [],
      vipEndTime: [],
      vipStartTime: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      queryFormData: { ...cloneDeep(init_query_form_data) },
      tableLoading: false,
      list: [],
      total: 0,
      roleDesc: {},
      userType: {},
      userTypeList: [],
      userTypesDesc: {},
      userStatistics: {},
      consumerVisibleDia: false,
      creatName: '',
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],
      sourceList: [],
      exportLoading: false,
      vip_status_desc: [],
      source_clinic_this: [],
      vipVisible: false,
      currentRow: {},
      sendGoodsVisible: false,
      addUserFollowVisible: false,
      moreSearchVisible: false,
      keys_option: {},
      gift_status_desc: [],
      interestList: [], // 980权益枚举
      tag_list: [],
      search_text: [],
      selectedRowLoading: false,
      userPopReference: null,
      userPopRef: null,
      userPopPlacement: 'right-start',
      selectedRowDetail: {},
      selectedSearchRefH: 0,
      userDetailTimer: null,
    };
  },
  created() {
    this.getUserTypes();
    this.queryFormData = S.merge(init_query_form_data, this.$route.query);
    this.handleInitQueryFormData();
    this.submitQueryForm(true);
    this.getArrivalOptions();
    this.getUserTagGroupList();
    this.$nextTick(() => {
      if (this.$route.params.quickCreate) {
        this.consumerVisibleDia = true;
      }
    });
  },
  computed: {
    getContent() {
      return item => {
        return `有效期：${this.$moment(item.start_time * 1000).format('YYYY-MM-DD')} 至 ${this.$moment(
          item.end_time * 1000
        ).format('YYYY-MM-DD')}`;
      };
    },
    isCanOpenVip() {
      return item => {
        let list = item.vip_info || [];
        return !list.some(item => item.user_type === '1' || item.user_type === '4');
      };
    },
    user_vip_type: {
      set(val) {
        this.queryFormData.user_vip_type = val.join(',');
      },
      get() {
        return this.queryFormData.user_vip_type ? this.queryFormData.user_vip_type.split(',') : [];
      },
    },
    tableCols() {
      const list = [
        {
          title: '姓名',
          slot: 'userinfo',
          minWidth: 180,
          align: 'left',
          renderHeader: (h, p) => this.customRender(h, p, 48),
        },
        {
          title: '手机号',
          slot: 'mobile',
          align: 'left',
          minWidth: 150,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, mobile_tip, 400, 'custom_white_space', {
              alignItem: 'left',
              marginLeft: '48px',
            }),
        },
        {
          title: '卡券',
          slot: 'card_coupons',
          align: 'left',
          minWidth: 120,
          renderHeader: (h, p) => this.customRender(h, p, 36),
        },
        {
          title: '通兑券',
          slot: 'redeemable_coupons',
          align: 'left',
          minWidth: 120,
          renderHeader: (h, p) => this.customRender(h, p, 22),
        },
        {
          title: '储值余额',
          slot: 'stored',
          align: 'center',
          minWidth: 200,
        },
        {
          title: '消费概况',
          slot: 'consumptions',
          align: 'left',
          minWidth: 180,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, consumptions_tip, 400, 'custom_white_space', {
              alignItem: 'left',
              marginLeft: '42px',
            }),
        },
        {
          title: '时间',
          slot: 'times',
          align: 'center',
          minWidth: 240,
          renderHeader: (h, params) => this._renderHeader(h, params, time_tip, 400, 'custom_white_space'),
        },
        { title: '操作', slot: 'action', width: 150, align: 'center', fixed: 'right' },
      ];
      // if (isRstClinic()) {
      //   return list.filter(item => item.slot !== 'redeemable_coupons');
      // }
      return list;
    },
    calcPhoneLabel() {
      return item => {
        if (+item.mobile_type === 4) {
          return `该号码为临时号码，由${item.before_mobile || '-'}释放所得`;
        }
        if (+item.mobile_type === 3) {
          return `该号码为临时号码，请尽快修改为正常号码`;
        }
        if (+item.mobile_type === 2) {
          return '该手机号为暂存状态，请尽快验证';
        }
      };
    },
  },
  methods: {
    customRender(h, params, width = 42) {
      return h(
        'span',
        {
          style: {
            marginLeft: typeof width === 'number' ? width + 'px' : width,
          },
        },
        [params.column.title]
      );
    },
    getUserDetail(e, row, index) {
      // 清除之前的定时器
      if (this.userDetailTimer) {
        clearTimeout(this.userDetailTimer);
      }

      // 设置新的定时器，500ms后执行
      this.userDetailTimer = setTimeout(() => {
        // console.log(e.target, 'e.target.className')
        const mouseY = e.clientY;
        const viewportHeight = window.innerHeight;
        const distanceToBottom = viewportHeight - mouseY;
        if (distanceToBottom <= 480) {
          this.userPopPlacement = 'right-end';
        } else {
          this.userPopPlacement = 'right-start';
        }
        this.showPop('userPop', e);
        this.selectedRowLoading = true;
        this.selectedRowDetail = {};
        this.selectedRowPatientDetail = {};
        this.$api
          .getUserInfo({
            uid: row.uid,
          })
          .then(res => {
            this.selectedRowDetail = res?.user || {};
          })
          .finally(() => {
            this.selectedRowLoading = false;
          });
      }, 500);
    },
    clearUserDetailTimer() {
      // 清除定时器，阻止弹窗显示
      if (this.userDetailTimer) {
        clearTimeout(this.userDetailTimer);
        this.userDetailTimer = null;
      }
    },
    showPop(ref, e) {
      this.hidePop();
      const newDom = e.target;
      let popStone = this.$refs[ref];
      popStone.visible = false;
      popStone.doDestroy();
      this.userPopReference = newDom;
      // popStone.updatePopper();
      setTimeout(() => {
        this.userPopRef = ref;
        popStone.visible = true;
      }, 50);
    },
    hidePop() {
      if (!this.userPopRef) return;
      this.$refs.userListRef.click();
      this.userPopRef = null;
    },
    userListMouseOver: debounce(function (e) {
      try {
        const ignore = [
          'ivu-select-placeholder',
          'table-form ivu-form ivu-form-label-right ivu-form-inline',
          'head980vip',
        ];
        if (e.relatedTarget?.className?.indexOf('avatar') > -1 && !e.target?.className) return;
        if (e.relatedTarget?.className?.indexOf('head980vip') > -1 && !e.target?.className) return;
        if (e.relatedTarget?.className === 'el-loading-spinner') return;
        if (ignore.includes(e.target?.className)) return;
        if (e?.relatedTarget?.className === 'user-wrap' || e.target?.className !== 'avatar-wrapper') {
          this.hidePop();
        }
      } catch (e) {
        console.log(e);
      }
    }, 0),
    addUserFollow(row = {}) {
      this.currentRow = row;
      this.addUserFollowVisible = true;
    },
    getUserTagGroupList() {
      this.$api
        .getUserTagGroupList({
          page: 1,
          pageSize: 99,
        })
        .then(res => {
          this.tag_list =
            res?.list
              ?.filter(item => item?.tag_info?.length > 0 && item.status === '1')
              .map(item => ({
                ...item,
                tag_info: item.tag_info?.filter(itm => itm.status === '1'),
              })) || [];
        });
    },
    createOrderNew(row) {
      const path = '/trade/order/create';
      const query = {
        type: 'new',
        uid: row.uid,
        from: '/user/list',
      };
      this.openNewPage(path, query);
    },
    createReserve(row) {
      const path = '/reserve/board/index';
      const query = {
        uid: row.uid,
        from: '/user/list',
      };
      this.openNewPage(path, query);
    },
    createGive(row) {
      const path = '/trade/give/create';
      const query = {
        uid: row.uid,
        from: '/user/list',
      };
      this.openNewPage(path, query);
    },
    openNewPage(path, query) {
      const href = this.$router.resolve({
        path,
        query,
      }).href;
      window.open(href, '_blank');
    },
    // 发货
    sendGoods(row) {
      this.currentRow = row;
      this.sendGoodsVisible = true;
    },
    // 开通会员
    openVip(row) {
      this.$refs?.[`user_table_list_${row.uid}`]?.handleClose();
      this.$refs?.[`user_table_list_${row.uid}`]?._data &&
        (this.$refs[`user_table_list_${row.uid}`]._data.visible = false);
      // this.
      if (row.user_type === '2' || row.user_type === '3') {
        this.$Message.error('临时号/暂存号暂不支持开通会员');
        return;
      }
      this.order_id = '';
      this.currentRow = row;
      this.vipVisible = true;
    },
    handleClickAction(key, row) {
      if (key === '1') {
        this.openVip(row);
      }
      if (key === '3') {
        this.createGive(row);
      }
      if (key === '4') {
        this.addUserFollow(row);
      }
      if (key === '5') {
        this.queryDetail(row);
      }
    },
    sortChanged({ column: { slot }, order }) {
      if (order === 'normal') {
        order = '';
      }

      if (slot) {
        this.queryFormData.order = slot + ' ' + order;
        this.getsList();
      } else {
        this.$Message.error('无效排序字段');
      }
    },
    getUserTypes() {
      this.$api.getUserType().then(res => {
        this.userTypeList = Object.keys(res.consume_type_desc);
        this.keys_option.userTypeList = Object.keys(res.consume_type_desc);
        this.userTypesDesc = S.descToArrHandle(res.user_type_desc);
        this.keys_option.userTypesDesc = S.descToArrHandle(res.user_type_desc);
        this.userType = res.consume_type_desc;
        this.keys_option.userType = res.consume_type_desc;
        // 会员信息
        this.vip_status_desc = S.descToArrHandle(res.vip_status_desc);
        this.keys_option.vip_status_desc = res.vip_status_desc?.map(item => ({ ...item, id: item.kw }));
        // 消费渠道
        this.source_clinic_this = S.descToArrHandle(res.source_clinic_this);
        this.keys_option.source_clinic_this = S.descToArrHandle(res.source_clinic_this);
        // 赠品状态
        this.gift_status_desc = res.gift_status_desc;
        this.keys_option.gift_status_desc = S.descToArrHandle(res.gift_status_desc);
        // 标签枚举
        this.tag_desc = S.descToArrHandle(res.tag_desc);
        this.keys_option.tag_desc = S.descToArrHandle(res.tag_desc);
        // 980 枚举
        this.interestList = res['980_vip_desc'] || [];
        this.keys_option.interestList = res['980_vip_desc'] || [];
        this.keys_option.birthday_keys = [
          { key: '', name: '不限', date: [] },
          { key: 'today', name: '今天', date: [moment().format('YYYY-MM-DD')] },
          {
            key: 'week',
            name: '本周',
            date: [moment().startOf('week').format('YYYY-MM-DD'), moment().endOf('week').format('YYYY-MM-DD')],
          },
          {
            key: 'month',
            name: '本月',
            date: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
          },
          { key: 'custom', img: calender },
        ];
        this.keys_option.createday_keys = [
          { key: '', name: '不限', date: [] },
          {
            key: '30',
            name: '近30天',
            date: [moment().subtract('30', 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          },
          {
            key: '90',
            name: '近90天',
            date: [moment().subtract('90', 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          },
          {
            key: '180',
            name: '近180天',
            date: [moment().subtract('180', 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          },
          { key: 'custom', img: calender },
        ];
      });
    },
    queryDetail({ uid }, key) {
      const path = '/user/detail';
      const query = {
        uid,
        routeTabId: key,
      };
      this.openNewPage(path, query);
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onMoreSearch(params) {
      this.queryFormData = cloneDeep(params);
      this.submitQueryForm(false, true);
    },
    openMoreSearch() {
      this.moreSearchVisible = true;
    },
    onResetSearch: function (flag) {
      this.queryFormData = { ...cloneDeep(init_query_form_data) };
      this.handlerSearchText(this.queryFormData);
      // this.$refs['user-table']?.handleSort(1, 'normal');
      flag && this.submitQueryForm();
    },
    clearSearch(item) {
      const field = item.field;
      const type = item.type;
      console.log(
        field,
        type,
        this.queryFormData[field],
        init_query_form_data[field],
        this.queryFormData[type],
        'field'
      );
      if (!isEmpty(field) && Array.isArray(field)) {
        field.forEach(key => {
          this.queryFormData[key] = init_query_form_data[key];
        });
      }
      if (!isEmpty(field) && typeof field === 'string') {
        this.queryFormData[field] = init_query_form_data[field];
      }
      if (!isEmpty(type)) {
        this.queryFormData[type] = init_query_form_data[type];
      }
      this.submitQueryForm();
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    onSetRole: function (uid, role) {
      io.post('clinic/user.user.setrole', { uid: uid, role: role })
        .then(() => {
          this.$Message.success(role === 'MEMBER' ? '撤销成功' : '设置成功');
          this.submitQueryForm();
        })
        .catch(error => {
          {
          }
        });
    },

    getsList(params) {
      this.tableLoading = true;
      this.$api[this.apiName]({
        ...this.queryFormData,
      })
        .then(data => {
          this.total = data.total;
          this.list = data.users?.map(user => ({ ...user, popVisible: false })) || [];
          this.roleDesc = data.roleDesc;
        })
        .finally(() => {
          this.tableLoading = false;
          this.handlerSearchText(this.queryFormData);
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },

    // 点击创建用户，显示弹窗
    creatConsumer(val) {
      this.consumerVisibleDia = true;
    },

    // 创建用户返回的数据
    optionChange(item) {
      this.getsList();
    },

    imgOnLoadErr(index) {
      this.$set(this.list[index], 'avatar', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png-B.w300');
    },
    // api-获取options
    getArrivalOptions() {
      this.$api.getArrivalOptions().then(res => {
        // 用户来源
        this.sourceList = S.descToArrHandle(res.userFromDesc);
        this.keys_option.sourceList = S.descToArrHandle(res.userFromDesc);
        // 用户性别
        this.sexDesc = S.descToArrHandle(res.sexDesc);
        this.keys_option.sexDesc = S.descToArrHandle(res.sexDesc);
        // 到店类型
        this.userTypeDesc = S.descToArrHandle(res.userTypeDesc);
        this.keys_option.userTypeDesc = S.descToArrHandle(res.userTypeDesc);
        // 大致年龄段
        this.ageGroupDesc = S.descToArrHandle(res.ageGroupDesc);
        this.keys_option.ageGroupDesc = S.descToArrHandle(res.ageGroupDesc);
        // 到店述求
        this.askTypeDesc = S.descToArrHandle(res.askTypeDesc);
        this.keys_option.askTypeDesc = S.descToArrHandle(res.askTypeDesc);
        // 消费类型
        this.consumeTypeDesc = S.descToArrHandle(res.consumeTypeDesc);
        this.keys_option.consumeTypeDesc = S.descToArrHandle(res.consumeTypeDesc);
      });
    },

    // 导出excel
    exportExcel() {
      this.exportLoading = true;
      this.$api
        .exportUserList(this.queryFormData)
        .then(res => {
          this.download(res.url);
        })
        .finally(() => (this.exportLoading = false));
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
    handleInitQueryFormData() {
      this.queryFormData.age_st = +this.queryFormData.age_st || null;
      this.queryFormData.age_et = +this.queryFormData.age_et || null;
      this.queryFormData.pay_num_min = +this.queryFormData.pay_num_min || null;
      this.queryFormData.pay_num_max = +this.queryFormData.pay_num_max || null;
      this.queryFormData.total_consume_price_min = +this.queryFormData.total_consume_price_min || null;
      this.queryFormData.total_consume_price_max = +this.queryFormData.total_consume_price_max || null;
      this.queryFormData.order_per_turnover_min = +this.queryFormData.order_per_turnover_min || null;
      this.queryFormData.order_per_turnover_max = +this.queryFormData.order_per_turnover_max || null;
      this.queryFormData.reserve_times_min = +this.queryFormData.reserve_times_min || null;
      this.queryFormData.reserve_times_max = +this.queryFormData.reserve_times_max || null;
      this.queryFormData.his_order_num_min = +this.queryFormData.his_order_num_min || null;
      this.queryFormData.his_order_num_max = +this.queryFormData.his_order_num_max || null;
      this.queryFormData.arrival_manual_num_min = +this.queryFormData.arrival_manual_num_min || null;
      this.queryFormData.arrival_manual_num_max = +this.queryFormData.arrival_manual_num_max || null;
    },
    handlerSearchText(val = {}) {
      const list = [];
      for (let i = 0; i < query_form_data_map.length; i++) {
        const item = query_form_data_map[i];
        const type = item?.[0]; // 1
        const field = item?.[1]; // 2
        const field_type = item?.[2]; // 3
        const map = item?.[3]; // 3
        const maps = this.keys_option?.[item?.[3]] || []; // 4
        const name = item?.[4];
        if (type === 1) {
          let value = '';
          const fieldValue = val[field];
          if (!map) {
            value = val[field];
          } else if (Array.isArray(fieldValue)) {
            value = fieldValue.map(val => maps.find(item => item.id === val)?.desc || '')?.join(', ');
          } else {
            value = maps.find(item => item.id === fieldValue)?.desc || '';
          }
          value &&
            list.push({
              name,
              value,
              field,
              type: field_type,
            });
        }
        if (type === 2 && Array.isArray(field)) {
          const field1 = field[0];
          const field2 = field[1];
          const fieldValue1 = val[field1];
          const fieldValue2 = val[field2];
          let value = '';
          if (!isEmpty(fieldValue1) || !isEmpty(fieldValue2)) {
            value = fieldValue1 || fieldValue2 || '';
          }
          if (!isEmpty(fieldValue1) && !isEmpty(fieldValue2)) {
            value = fieldValue1 + '～' + fieldValue2;
          }
          value &&
            list.push({
              name,
              value,
              field,
              type: field_type,
            });
        }

        if (type === 3 && val[field_type] === 'custom') {
          const fieldValue1 = val[field][0];
          const fieldValue2 = val[field][1];
          let value = '';
          if (!isEmpty(fieldValue1) || !isEmpty(fieldValue2)) {
            value = fieldValue1 || fieldValue2 || '';
          }
          if (!isEmpty(fieldValue1) && !isEmpty(fieldValue2)) {
            value = fieldValue1 + '～' + fieldValue2;
          }
          value &&
            list.push({
              name,
              value,
              field,
              type: field_type,
            });
        }
        if (type === 3 && !!val[field_type] && val[field_type] !== 'custom') {
          let value = maps.find(item => item.key === val[field_type])?.name || '';
          value &&
            list.push({
              name,
              value,
              field,
              type: field_type,
            });
        }
      }
      this.search_text = list;
      this.$nextTick(() => {
        this.selectedSearchRefH = this.$refs.selectedSearchRef?.clientHeight || 0;
      });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.handleInitQueryFormData();
    this.getsList();
    // if (!to.query.order) {
    //   this.$nextTick(() => {
    //     this.$refs['user-table'].$refs.standardTable.handleSort(1, 'normal');
    //   });
    // } else {
    //   this.getsList();
    // }
    next();
  },
};
</script>

<style lang="less" scoped>
.time-range {
  ::v-deep .ivu-input-wrapper {
    min-width: 250px;
  }
}
.userinfo-wrap {
  width: 100%;
  padding: 16px 0;
  display: flex;
  align-items: center;
  .avatar-wrapper {
    width: 56px;
    height: 48px;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    position: relative;
    .avatar {
      width: 48px;
      height: 48px;
      line-height: 48px;
      border-radius: 50%;
    }
    :deep(.avatar img) {
      vertical-align: unset;
      object-fit: cover;
    }
  }
  .head980vip {
    width: 33px;
    height: 14px;
    position: absolute;
    bottom: -2px;
    left: 6px;
    object-fit: cover;
  }
  .user-wrapper {
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    > div {
      display: flex;
      align-items: center;
    }
    > div:last-child {
      margin-bottom: 0;
    }
    .username > .name {
      max-width: calc(98% - 40px);
      width: 98%;
      font-size: 14px;
      color: #155bd4;
      line-height: 22px;
      text-align: left;
      cursor: pointer;
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 隐藏溢出的文本 */
      text-overflow: ellipsis; /* 显示省略号 */
    }
    .username .tip {
      width: 14px;
      height: 14px;
      margin-left: 4px;
      border-radius: 50%;
    }
    .user-desc {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #ee3838;
      line-height: 18px;
      > img {
        height: 16px;
        object-fit: cover;
      }
      > img:last-child {
        margin-right: 0;
      }
    }
    .user-desc:hover {
      color: rgba(238, 56, 56, 0.7);
    }
  }
}
.card_coupons {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .card_coupons-item {
    display: flex;
    align-items: center;
    > div {
      font-size: 14px;
      color: #303133;
      line-height: 22px;
    }
    > div:first-child {
      width: fit-content;
      color: #909399;
      text-align: right;
    }
  }
}
.card_coupons.link:hover div {
  cursor: pointer;
  color: #115bd4 !important;
}
.flex {
  display: flex;
}
.xCenter {
  display: flex;
  justify-content: center;
}
.xEnd {
  display: flex;
  justify-content: flex-end;
}
.yCenter {
  display: flex;
  align-items: center;
}
.phone-tab-box {
  width: 32px;
  height: 32px;
}
.phone-tab-box-img {
  width: 12px;
  height: 12px;
  object-fit: cover;
}
.phone-tab {
  width: 32px;
  flex-shrink: 0;
  margin-top: 2px;
}
.cursorDefault {
  cursor: default;
}
:deep(.table-form .ivu-form-item) {
  margin-bottom: 10px;
}
.mr10 {
  margin-right: 10px;
}
.cursor {
  cursor: pointer;
}
.selected-search {
  width: 100%;
  min-height: 22px;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8px;
  .selected-search-label {
    width: 36px;
    height: 16px;
    font-size: 12px;
    color: #303133;
    line-height: 16px;
    margin-top: 4px;
  }
  .selected-search-item {
    padding: 2px 8px;
    background: #f5f6f8;
    border-radius: 2px;
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
.user-table-action {
  min-width: 72px;
}
.user-table-action :deep(.ivu-dropdown-item) {
  font-size: 13px !important;
  color: #115bd4;
}
.user-wrap {
  background: #ffffff;
}
</style>
<style lang="less">
.userinfo-pop .ivu-poptip-inner {
  background: transparent;
  box-shadow: unset;
}
.userinfo-pop .ivu-poptip-body {
  background: transparent;
}
.userinfo-pop .user-wrap {
  background: transparent;
}
.userinfo-pop .ivu-poptip-content {
  margin-left: -20px;
}
.userinfo-pop .ivu-poptip-arrow {
  display: none;
}
.app-inner:has(.user-wrapper) {
  background: #fff !important;
  padding: 16px !important;
}

.app-container:has(.user-wrapper) {
  background: #f5f6f8;
}
</style>
