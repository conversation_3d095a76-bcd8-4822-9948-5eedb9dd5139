<template>
  <div style="min-width: 1100px; width: 50vw">
    <Form ref="tagGroupForm" :label-width="120" :label-colon="true" :model="detail" :rules="ruleValidate">
      <FormItem label="标签组名称" prop="name">
        <Input
          type="text"
          v-model="detail.name"
          :maxlength="10"
          show-word-limit
          :disabled="detail.source === '1'"
          style="width: 280px"
          placeholder="请输入标签名称"
        />
      </FormItem>
      <FormItem label="打标方式" prop="type">
        <RadioGroup v-model="detail.type" class="tag-group-style" vertical>
          <Radio label="1">
            <div class="tag-group-label">
              <div>手动打标</div>
              <div class="tag_desc">通过员工手动给客户打标签。</div>
            </div>
          </Radio>
          <!--          <Radio label="2">-->
          <!--            <div class="tag-group-label">-->
          <!--              <div>自动打标</div>-->
          <!--              <div class="tag_desc">通过自动打标规则为客户打标签、删除标签。</div>-->
          <!--            </div>-->
          <!--          </Radio>-->
        </RadioGroup>
      </FormItem>
      <FormItem label="标签值" prop="tag_list">
        <Table border :columns="tableCols" :data="detail.tag_list" :height="$store.state.app.clientHeight - 450">
          <template #sort="{ index }">
            {{ index + 1 }}
          </template>
          <template #name="{ row, index }">
            <div v-if="row.source === '1'">{{ row.name }}</div>
            <Input
              v-else
              :ref="'tagTable' + index"
              v-model="row.name"
              show-word-limit
              :maxlength="10"
              placeholder="请输入标签值"
              clearable
              style="min-width: 150px; width: 90%; text-align: center"
              @on-change="e => tagRowNameChange(e, index)"
              @on-keypress="handleKeyPress"
              @on-focus="event => event.currentTarget.select()"
            />
          </template>
          <template #status="{ row }">
            {{ status_option?.[+row.status] || '-' }}
          </template>
          <template #source="{ row }">
            {{ source_option?.[row.source] || '-' }}
          </template>
          <template #action="{ row, index }">
            <a class="mr10" @click="upMove(row, index)">上移</a>
            <a class="mr10" @click="downMove(row, index)">下移</a>
            <a v-if="index !== 0" class="mr10" @click="moveTop(index)">置顶</a>
            <a class="mr10" v-if="row.source !== '1'" @click="deleteRow(row, index)">删除</a>
            <a v-if="row.source !== '1'" @click="changeEnable(row, index)">
              {{ row.status === '1' ? '禁用' : '启用' }}
            </a>
          </template>
        </Table>

        <Button class="mt16" icon="md-add" @click="addTag">添加标签值</Button>
      </FormItem>
    </Form>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Dvd />
      <Dvd />
      <Button type="primary" @click="onSave" :loading="savaLoading">保存</Button>
    </div>

    <custom-dialog :visible.sync="openDialog" :content="dialogText" :loading="savaLoading" :on-ok="onOk" />
  </div>
</template>

<script>
import renderHeader from '@/mixins/renderHeader';
import { cloneDeep, debounce  } from 'lodash-es';
import CustomDialog from '@/components/custom-dialog/index.vue';

const init_query_form = {
  id: '',
  name: '',
  type: '1',
  status: '',
  tag_list: [],
};
const init_table_row = {
  name: '',
  status: '1',
  status_text: '已启用',
  sort: 1,
  source: '2',
  source_text: '自定义',
};
export default {
  name: 'tagGroupDetail',
  components: { CustomDialog },
  mixins: [renderHeader],
  data() {
    const validateTags = (rule, value, callback) => {
      if (!value || value?.length === 0) {
        callback(new Error('请选择标签值'));
      } else {
        callback();
      }
    };
    return {
      detail: { ...cloneDeep(init_query_form) },
      oldDetail: {},
      status_option: {
        1: '启用中',
        2: '禁用中',
      },
      source_option: {
        1: '系统内建',
        2: '自定义',
      },
      ruleValidate: {
        name: [{ required: true, message: '该输入标签组名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择打标方式', trigger: 'change' }],
        tag_list: [{ required: true, validator: validateTags, trigger: 'blur' }],
      },
      tableCols: [
        {
          title: '序号',
          slot: 'sort',
          align: 'center',
          width: 100,
          renderHeader: (h, params) =>
            this._renderHeader(
              h,
              params,
              '所有需要选择标签的场景(新建人群选择标签、客户列表页按标签查询等)，标签都按此顺序排列',
              300,
              'custom_white_space'
            ),
        },
        { title: '标签值', slot: 'name', align: 'center', minWidth: 180 },
        { title: '状态', slot: 'status', align: 'center' },
        { title: '来源', slot: 'source', align: 'center' },
        { title: '操作', slot: 'action', align: 'center', width: 200 },
      ],
      openDialog: false,
      dialogText: '',
      savaLoading: false,
      onOk: null,
    };
  },
  mounted() {
    if (this.$route.query.id) {
      this.getUserTagGroupInfo();
    }
  },
  methods: {
    getUserTagGroupInfo() {
      this.$api
        .getUserTagGroupInfo({
          id: this.$route.query.id,
        })
        .then(res => {
          this.detail = cloneDeep(res || {}) || cloneDeep(init_query_form);
          this.oldDetail = cloneDeep(res || {});
        });
    },
    addTag() {
      const row = cloneDeep(init_table_row);
      const curSort = this.detail.tag_list.length || 0;
      row.sort = curSort + 1;
      this.$set(this.detail.tag_list, curSort, row);
      this.$nextTick(() => {
        this.$refs['tagTable' + curSort]?.focus();
      });
    },
    handleKeyPress(event) {
      // 只允许输入字母和数字
      const charCode = event.charCode;
      if (!/[a-zA-Z0-9]/.test(String.fromCharCode(charCode))) {
        event.preventDefault(); // 阻止输入
      }
    },
    tagRowNameChange: debounce(function (event, index) {
      this.$set(this.detail.tag_list[index], 'name', event.target.value);
    }, 300),

    checkTagNameChanged() {
      if (!this.oldDetail?.tag_list?.length) return false;

      return this.detail.tag_list.some(newTag => {
        if (!newTag.id) return false;
        const oldTag = this.oldDetail.tag_list.find(item => item.id === newTag.id);
        return oldTag && oldTag.name !== newTag.name;
      });
    },
    saveAction() {
      this.savaLoading = true;
      return new Promise((resolve, reject) => {
        this.$api
          .saveUserTagGroup({
            ...this.detail,
            tag_list: this.detail.tag_list?.map((item, index) => ({ ...item, sort: index + 1 })),
          })
          .then(() => {
            resolve(true);
            this.dialogText = '';
            this.$Message.success(this.detail.id ? '修改成功！' : '新增成功！');
            this.$router.back();
          })
          .catch(err => {
            reject(err);
          })
          .finally(() => {
            this.savaLoading = false;
          });
      });
    },
    onSave() {
      this.$refs.tagGroupForm.validate(valid => {
        if (valid) {
          if (this.checkTagNameChanged()) {
            this.openDialog = true;
            this.dialogText = '修改标签会同时更新现有用户身上的标签，是否确认？';
            this.onOk = this.saveAction;
          } else {
            this.saveAction();
          }
        }
      });
    },
    changeEnable(row, index) {
      this.$set(this.detail.tag_list[index], 'status', row.status === '1' ? '2' : '1');
    },
    moveItem(arr, index, count) {
      arr = cloneDeep(arr);
      if (count > 0) {
        const item = arr.splice(index, 1)[0]; // 移除当前项
        arr.splice(index - 1, 0, item); // 插入到前一项的位置
      }
      if (count < 0) {
        const item = arr.splice(index, 1)[0]; // 移除当前项
        arr.splice(index + 1, 0, item); // 插入到后一项的位置
      }
      return arr;
    },
    upMove(row, index) {
      if (index === 0) return;
      this.detail.tag_list = this.moveItem(this.detail.tag_list, index, 1);
    },
    downMove(row, index) {
      if (index + 1 === this.detail.tag_list.length) return;
      this.detail.tag_list = this.moveItem(this.detail.tag_list, index, -1);
    },
    deleteRow(row, index) {
      if (row.id) {
        this.openDialog = true;
        this.dialogText = '删除标签的同时会删除用户身上的标签，且不可恢复，确认删除？';

        this.onOk = () =>
          new Promise((resolve, reject) => {
            this.savaLoading = true;
            this.$api
              .saveUserTagGroup({
                ...this.detail,
                tag_list: this.detail.tag_list
                  ?.filter(item => item.id !== row.id)
                  ?.map((item, index) => ({ ...item, sort: index + 1 })),
              })
              .then(() => {
                resolve(true);
                this.detail.tag_list.splice(index, 1);
                this.$Message.success('删除成功！');
              })
              .catch(err => {
                reject(err);
              })
              .finally(() => {
                this.dialogText = '';
                this.savaLoading = false;
              });
          });
        return;
      }
      this.detail.tag_list.splice(index, 1);
    },
    moveTop(index) {
      const list = cloneDeep(this.detail.tag_list);
      const item = list.splice(index, 1)[0];
      list.splice(0, 0, item);
      this.detail.tag_list = list;
    },
  },
};
</script>

<style scoped lang="less">
.tag_desc {
  font-size: 12px;
  color: #909399;
  line-height: 16px;
}
.tag-group-style :deep(.ivu-radio-wrapper) {
  display: flex;
  height: auto;
  line-height: unset;
  margin-bottom: 8px;
}
.tag-group-style :deep(.ivu-radio) {
  margin-top: 8px;
}
.tag-group-label {
  display: flex;
  flex-direction: column;
}
.mt16 {
  margin-top: 16px;
}
:deep(.ivu-table-cell input) {
  text-align: center;
}
</style>
