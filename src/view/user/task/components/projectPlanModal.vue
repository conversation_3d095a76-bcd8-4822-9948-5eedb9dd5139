<template>
  <div>
    <Modal
      ref="customModal"
      :value="value"
      width="1000px"
      title="专案计划"
      :footer-hide="false"
      :mask-closable="false"
      :lock-scroll="true"
      @on-visible-change="changeVisible"
    >
      <div class="content">
        <Form ref="formData" :model="formData" :rules="ruleValidate" label-position="top">
          <div class="form-warpper">
            <FormItem label="档案编号" class="form-item" prop="code">
              <Input v-model="formData.code" placeholder="请输入档案编号" disabled></Input>
            </FormItem>
            <FormItem label="用户姓名" class="form-item" prop="uid">
              <user-search v-model="formData.uid" ref="userSearchRef" disabled></user-search>
            </FormItem>
            <FormItem label="专病病症/部位" class="form-item">
              <Input v-model="formData.disease_group_text" placeholder="请输入专病病症/部位" disabled></Input>
            </FormItem>
            <FormItem label="客户状态" class="form-item" prop="client_status">
              <Select v-model="formData.client_status" placeholder="客户状态" clearable>
                <Option :value="item.id" v-for="item in clientStatusDesc" :key="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </div>
        </Form>
        <div class="">
          <div class="mb10 flex flex-item-align">
            <div class="task-title">跟进计划</div>
            <a class="ml10" style="display: block" @click="editPlan">+ 添加任务</a>
          </div>
          <Table :loading="tableLoading" :columns="tableCols" :data="task">
            <!-- 状态 -->
            <template slot-scope="{ row }" slot="status">
              <mark-status :type="getStatusTextColor(row.status)">{{ row.status_text }}</mark-status>
            </template>

            <!-- 任务情况 -->
            <template slot-scope="{ row }" slot="task_info">
              <div>
                <div class="item">
                  <div class="item-label">任务时间:</div>
                  <div class="item-value">{{ row.follow_time | data_format('YYYY-MM-DD') }}</div>
                </div>
                <div class="item">
                  <div class="item-label">任务内容:</div>
                  <Tooltip :content="row.content" :disabled="!row.content">
                    <div class="item-value ecs ecs-2 cursor">{{ row.content || '-' }}</div>
                  </Tooltip>
                </div>
                <div class="item">
                  <div class="item-label">备注:</div>
                  <Tooltip :content="row.remark" :disabled="!row.remark">
                    <div class="item-value ecs ecs-2 cursor">{{ row.remark || '-' }}</div>
                  </Tooltip>
                </div>
              </div>
            </template>

            <!-- 用药相关 -->
            <template slot-scope="{ row }" slot="drug_relate">
              <div>
                <div class="item">
                  <div class="item-label">发药时间:</div>
                  <div class="item-value">{{ row.dispensing_plan.time || '-' }}</div>
                </div>
                <div class="item">
                  <div class="item-label">发药地址:</div>
                  <div class="item-value">{{ row.dispensing_plan.address || '-' }}</div>
                </div>
                <div class="item">
                  <div class="item-label">中药明细:</div>
                  <Tooltip :content="row.dispensing_plan?.medicine_detail" :disabled="!row.dispensing_plan?.medicine_detail">
                    <div class="item-value ecs ecs-2 cursor">{{ row.dispensing_plan?.medicine_detail || '-' }}</div>
                  </Tooltip>
                </div>
              </div>
            </template>

            <!-- 用药用量 -->
            <template slot-scope="{ row }" slot="treatment_info">
              <div>
                <div class="item">
                  <div class="item-label item-label82">疗程:</div>
                  <Tooltip :content="row.dispensing_plan.treatment" :disabled="!row.dispensing_plan.treatment">
                    <div class="item-value ecs ecs-2 cursor">{{ row.dispensing_plan.treatment || '-' }}</div>
                  </Tooltip>
                </div>
                <div class="item">
                  <div class="item-label item-label82">总药份数:</div>
                  <Tooltip :content="row.dispensing_plan.all_times" :disabled="!row.dispensing_plan.all_times">
                    <div class="item-value ecs ecs-2 cursor">{{ row.dispensing_plan.all_times || '-' }}</div>
                  </Tooltip>
                </div>
                <div class="item">
                  <div class="item-label item-label82">本次发药份数:</div>
                  <Tooltip :content="row.dispensing_plan.this_times" :disabled="!row.dispensing_plan.this_times">
                    <div class="item-value ecs ecs-2 cursor">{{ row.dispensing_plan.this_times || '-' }}</div>
                  </Tooltip>
                </div>
                <div class="item">
                  <div class="item-label item-label82">剩余发药份数:</div>
                  <Tooltip :content="row.dispensing_plan.residual_times" :disabled="!row.dispensing_plan.residual_times">
                    <div class="item-value ecs ecs-2 cursor">{{ row.dispensing_plan.residual_times || '-' }}</div>
                  </Tooltip>
                </div>
              </div>
            </template>

            <!-- 相关人员 -->
            <template slot-scope="{ row }" slot="personnel">
              <div>
                <div class="item">
                  <div class="item-label">接诊人:</div>
                  <div class="item-value">{{ row.receive_name || '-' }}</div>
                </div>
                <div class="item">
                  <div class="item-label">责任人:</div>
                  <div class="item-value">{{ row.duty_name || '-' }}</div>
                </div>
              </div>
            </template>

            <!-- 创建时间 -->
            <template slot-scope="{ row }" slot="create">
              <div>
                <div class="item">
                  <div class="item-label">创建人:</div>
                  <div class="item-value">{{ row.operator_name || '-' }}</div>
                </div>
                <div class="item">
                  <div class="item-label">创建时间:</div>
                  <div class="item-value">{{ row.create_time | data_format }}</div>
                </div>
              </div>
            </template>

            <!-- 完成情况 -->
            <template slot-scope="{ row }" slot="progress">
              <div>
                <div class="item">
                  <div class="item-label">完成时间:</div>
                  <div class="item-value">{{ row.finished_time | data_format }}</div>
                </div>
                <div class="item">
                  <div class="item-label">任务反馈:</div>
                  <Tooltip :content="row.task_remark" :disabled="!row.task_remark">
                    <div class="item-value ecs ecs-2 cursor">{{ row.task_remark || '-' }}</div>
                  </Tooltip>
                </div>
              </div>
            </template>

            <template slot-scope="{ row }" slot="operate">
              <template v-if="row.status === '1' || row.status === '3'">
                <a @click="finishTask(row)">完成任务</a>
                <a class="ml10" @click="editPlan(row)">编辑</a>
                <Poptip confirm transfer title="确定删除吗?" @on-ok="deleteTask(row)">
                  <a class="ml10">删除</a>
                </Poptip>
              </template>
              <div v-else>-</div>
            </template>
          </Table>
        </div>
      </div>
      <div slot="footer">
        <Button @click="closeModal">取消</Button>
        <Button :loading="confirmLoading" type="primary" @click="confirm">保存</Button>
      </div>
    </Modal>

    <!-- 创建任务 -->
    <create-task ref="creatTask" v-model="createTaskVisible" :id="currentRow.id" @success="init"></create-task>
    <!-- 完成任务 -->
    <done-task-modal v-model="doneTaskVisible" :row="currentRow" @success="init"></done-task-modal>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import userSearch from './userSearch.vue';
import createTask from './createTask.vue';
import doneTaskModal from './doneTaskModal.vue';
import S from '@/libs/util';

export default {
  name: 'projectPlanModal',
  mixins: [],
  components: { userSearch, createTask, doneTaskModal },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    row: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      selectedOptions: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000;
        },
      },
      confirmLoading: false,
      formData: {
        code: '',
        uid: '',
        disease_group_text: '',
        client_status: '',
      },
      ruleValidate: {
        code: [{ required: true, message: '请输入档案编号', trigger: 'change' }],
        uid: [{ required: true, message: '请选择用户姓名', trigger: 'change' }],
        input3: [{ required: true, message: '请选择专病病症/部位', trigger: 'change' }],
        client_status: [{ required: true, message: '请选择客户状态', trigger: 'change' }],
      },
      tableCols: [
        { title: '序号', type: 'index', width: 60, align: 'center' },
        { title: '任务状态', slot: 'status', align: 'center', minWidth: 80 },
        { title: '任务情况', slot: 'task_info', align: 'center', minWidth: 240 },
        { title: '用药相关', slot: 'drug_relate', align: 'center', minWidth: 240 },
        { title: '用药用量', slot: 'treatment_info', align: 'center', minWidth: 200 },
        { title: '相关人员', slot: 'personnel', align: 'center', minWidth: 160 },
        { title: '创建时间', slot: 'create', align: 'center', minWidth: 200 },
        { title: '完成情况', slot: 'progress', align: 'center', minWidth: 240 },
        { title: '操作', slot: 'operate', align: 'center', fixed: 'right', width: 140 },
      ],
      task: [],
      tableLoading: false,
      createTaskVisible: false,
      doneTaskVisible: false,
      currentRow: {},
      clientStatusDesc: [],
      detail_info: {},
    };
  },

  computed: {
    getStatusTextColor() {
      return status => {
        switch (status) {
          case '1':
            return 'warn';
          case '2':
            return 'success';
          case '3':
            return 'reject';
        }
      };
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    // 完成任务
    finishTask(row) {
      this.currentRow = row;
      this.doneTaskVisible = true;
    },

    // 编辑
    editPlan(row) {
      if (row.id) {
        this.currentRow = row;
      } else {
        this.currentRow = {};
        let info = {
          ...this.formData,
          name: this.detail_info?.user?.name || '',
          mobile: this.detail_info?.user.mobile || '',
          uid: this.detail_info.uid,
          plan_id: this.detail_info.id,
        };
        console.log('info', info)
        this.$refs.creatTask.addTaskInit(info);
      }
      this.createTaskVisible = true;
    },
    // 删除
    deleteTask(row) {
      this.getUserPlanDeltetask(row.id)
    },
    init() {
      this.getUserPlanOptions();
      this.getUserPlanPlaninfo();
    },
    MODAL_SCROLL_TOP() {
      let MODAL_EL = this.$el.getElementsByClassName('ivu-modal-body')[0];
      if (MODAL_EL) {
        MODAL_EL.scrollTop = 0;
      }
    },
    /**
     * @description: 弹窗状态检测
     * @params  { Boolean } visible true: 弹窗打开 false:弹窗关闭
     * */
    changeVisible(visible) {
      if (visible) {
        this.init();
      } else {
        this.closeModal();
      }
    },

    /**
     * @description: 弹窗数据清除
     * */
    clearData() {
      this.task = [];
      this.$refs.formData.resetFields()
    },

    /**
     * @description: 弹窗关闭
     * */
    closeModal() {
      this.clearData();
      this.MODAL_SCROLL_TOP();
      this.$emit('input', false);
    },

    /**
     * @description: 确定事件
     * */
    confirm() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.getUserPlanEdit()
        }
      });
    },

    // 获取枚举
    getUserPlanOptions() {
      let params = {};
      this.$api.getUserPlanOptions(params).then(res => {
        this.clientStatusDesc = S.descToArrHandle(res.clientStatusDesc);
      });
    },

    getUserPlanPlaninfo() {
      let params = {
        id: this.row.id,
      };
      this.$api.getUserPlanPlaninfo(params).then(res => {
        this.detail_info = res;
        this.task = res.task;
        this.formData.code = res.code;
        this.formData.client_status = res.client_status;
        this.formData.disease_group_text = res.disease_group_text;
        this.$nextTick(() => {
          this.$refs.userSearchRef.echoUser({ real_name: res.user?.name,mobile: res.user?.mobile, uid: res.uid });
        });
      });
    },

    getUserPlanEdit() {
      this.confirmLoading = true;
      let params = {
        id: this.detail_info.id,
        client_status: this.formData.client_status
      }
      this.$api
        .getUserPlanEdit(params)
        .then(res => {
          this.$emit('success');
          this.closeModal();
        })
        .finally(() => (this.confirmLoading = false));
    },

    // 删除任务
    getUserPlanDeltetask(id) {
      let params = {id};
      this.$api.getUserPlanDeltetask(params).then(
        res => {
          this.$Message.success('删除成功')
          this.init()
        });
    },
  },
};
</script>

<style scoped lang="less">
.content {
  min-height: 400px;

  .form-warpper {
    display: flex;

    .form-item {
      margin-right: 20px;
      flex: 1;

      &:last-child {
        margin-right: 0px;
      }
    }
  }
}
</style>

<style lang="less" scoped>
::v-deep .ivu-modal-body {
  max-height: 500px;
  overflow-y: auto;
}
.ml10 {
  margin-left: 10px;
}

.mr20 {
  margin-right: 20px;
}

.item {
  display: flex;
  align-items: flex-start;

  .item-label {
    width: 60px;
    min-width: 60px;
    text-align: right;
    color: #aaa;
    margin-right: 4px;
  }
  .item-label82 {
    min-width: 82px !important;
  }

  .item-value {
    text-align: left;
  }
}
</style>

<style lang="less">
.custom-popper-cascader {
  .el-cascader-panel {
    .el-cascader-menu {
      &:first-child {
        .el-checkbox {
          display: none;
        }
      }

      &:nth-child(2) {
        .el-cascader-node {
          .el-checkbox {
            width: 100%;
            z-index: 1;
          }

          .el-cascader-node__label {
            position: absolute;
            left: 34px;
          }
        }
      }
    }
  }
}
</style>
