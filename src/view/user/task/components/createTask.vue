<template>
  <Modal
    ref="customModal"
    :value="value"
    width="1000px"
    :title="id ? '编辑任务' : '新增任务'"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <Form ref="formData" :model="formData" :rules="ruleValidate" label-position="top" v-if="value">
        <div class="form-warpper">
          <FormItem label="档案编号" class="form-item" prop="code">
            <Input
              v-model="formData.code"
              placeholder="请输入档案编号"
              maxlength="20"
              :disabled="isEdit || isPlanAddTask"
            ></Input>
          </FormItem>
          <FormItem label="用户姓名" class="form-item" prop="uid" v-if="value">
            <user-search v-model="formData.uid" ref="userSearchRef" :disabled="isEdit || isPlanAddTask"></user-search>
          </FormItem>
          <FormItem label="专病病症/部位" class="form-item" :prop="isEdit || isPlanAddTask ? '' : 'disease_id'">
            <Input v-if="isEdit || isPlanAddTask" v-model="disease_name" :disabled="isEdit || isPlanAddTask"></Input>
            <el-cascader
              v-else
              :disabled="isEdit || isPlanAddTask"
              style="width: 100%"
              popper-class="custom-popper-cascader"
              id="customCascader"
              :value="selectedOptions"
              :collapse-tags="true"
              :options="disease_list"
              :props="{ multiple: true, checkStrictly: true }"
              @change="handleChange"
              clearable
              class="cascader-style"
              placeholder="请选择专病病症/部位"
            >
            </el-cascader>
          </FormItem>
          <FormItem label="客户状态" class="form-item" prop="client_status">
            <Select
              v-model="formData.client_status"
              placeholder="客户状态"
              :disabled="isPlanAddTask || isEdit"
              clearable
            >
              <Option :value="item.id" v-for="item in clientStatusDesc" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </div>

        <div class="task-box">
          <div class="task-header flex flex-item-align">
            <div class="task-title">任务跟进</div>
            <Button type="default" size="small" class="ml10" @click="addTask" v-if="!id">新增任务</Button>
          </div>

          <div class="task-item" v-for="(item, index) in task" :key="index">
            <div class="item-header flex flex-item-align flex-item-between">
              <div class="header-left flex flex-item-align">
                <div>{{ index + 1 }}. <span style="color: red">* </span>任务时间</div>
                <DatePicker
                  class="ml10"
                  type="date"
                  size="default"
                  :options="disabledTime"
                  format="yyyy-MM-dd"
                  @on-change="task[index].follow_time = arguments[0]"
                  placeholder="请选择跟进时间"
                  v-model="task[index].follow_time"
                  style="width: 200px"
                ></DatePicker>
              </div>
              <div class="header-right">
                <a @click="addDrugPlan(index)" v-if="!item._isShowPlan">发药计划</a>
                <a class="ml10" @click="deleteTask(index)" v-if="task.length > 1">删除任务</a>
                <a></a>
              </div>
            </div>

            <!-- 任务计划数据 -->
            <div class="task-content">
              <FormItem label="" class="form-item">
                <div slot="label"><span style="color: red">*</span> 任务跟进内容</div>
                <Input
                  v-model="item.content"
                  type="textarea"
                  placeholder="请输入跟进内容"
                  :autosize="{ minRows: 2, maxRows: 2 }"
                ></Input>
              </FormItem>

              <Row>
                <Col span="6">
                  <FormItem label="" class="mr20">
                    <div slot="label">责任人</div>
                    <Select v-model="item.duty_id" placeholder="请选择责任人" clearable>
                      <Option :value="mem_item.id" v-for="(mem_item, mem_index) in member_list" :key="mem_index">
                        {{ mem_item.name }}
                      </Option>
                    </Select>
                  </FormItem>
                </Col>

                <Col span="6">
                  <FormItem label="" class="mr20">
                    <div slot="label">接诊人</div>
                    <Select v-model="item.receive_id" placeholder="请选择接诊人" clearable>
                      <Option :value="mem_item.id" v-for="(mem_item, mem_index) in member_list" :key="mem_index">
                        {{ mem_item.name }}
                      </Option>
                    </Select>
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="备注">
                    <Input v-model="item.remark" placeholder="请输入备注" clearable></Input>
                  </FormItem>
                </Col>
              </Row>
            </div>

            <!-- 发药计划 -->
            <div class="drug-content-wrapper" v-if="item._isShowPlan">
              <div class="drug-header flex flex-item-align flex-item-between">
                <div>发药计划</div>
                <a @click="deleteDrugPlan(index)">删除</a>
              </div>
              <div class="drug-content">
                <Row>
                  <Col span="5">
                    <FormItem label="发药时间" class="mr20">
                      <DatePicker
                        type="date"
                        size="default"
                        :options="disabledTime"
                        placeholder="请选择发药时间"
                        format="yyyy-MM-dd"
                        @on-change="task[index].dispensing_plan.time = arguments[0]"
                        v-model="item.dispensing_plan.time"
                      ></DatePicker>
                    </FormItem>
                  </Col>

                  <Col span="5">
                    <FormItem label="疗程" class="mr20">
                      <Input v-model="item.dispensing_plan.treatment" placeholder="请输入疗程"></Input>
                    </FormItem>
                  </Col>

                  <Col span="5">
                    <FormItem label="总药份数" class="mr20">
                      <Input v-model="item.dispensing_plan.all_times" placeholder="请输入总药份数"></Input>
                    </FormItem>
                  </Col>

                  <Col span="5">
                    <FormItem label="本次发药份数" class="mr20">
                      <Input v-model="item.dispensing_plan.this_times" placeholder="请输入本次发药份数"></Input>
                    </FormItem>
                  </Col>

                  <Col span="4">
                    <FormItem label="剩余发药份数">
                      <Input v-model="item.dispensing_plan.residual_times" placeholder="请输入剩余发药份数"></Input>
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span="10">
                    <FormItem label="中药明细" class="mr20">
                      <Input v-model="item.dispensing_plan.medicine_detail" placeholder="请输入中药明细"></Input>
                    </FormItem>
                  </Col>

                  <Col span="14">
                    <FormItem label="发药地址">
                      <Input v-model.trim="item.dispensing_plan.address" placeholder="请输入发药地址"></Input>
                    </FormItem>
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </div>
      </Form>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">保存</Button>
    </div>
  </Modal>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import userSearch from './userSearch.vue';
import S from '@/libs/util';

const init_task_data = {
  follow_time: '',
  content: '',
  duty_id: '',
  receive_id: '',
  remark: '',
  _isShowPlan: false,
  dispensing_plan: {
    time: '',
    treatment: '',
    all_times: '',
    this_times: '',
    residual_times: '',
    medicine_detail: '',
    address: '',
  },
};
export default {
  name: 'createTask',
  mixins: [],
  components: { userSearch },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      selectedOptions: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000;
        },
      },
      confirmLoading: false,
      formData: {
        code: '',
        uid: '',
        disease_id: '',
        client_status: '',
      },
      ruleValidate: {
        code: [{ required: true, message: '请输入档案编号', trigger: 'change' }],
        uid: [{ required: true, message: '请选择用户姓名', trigger: 'change' }],
        disease_id: [{ required: true, message: '请选择专病病症/部位', trigger: 'change' }],
        client_status: [{ required: true, message: '请选择客户状态', trigger: 'change' }],
      },
      clientStatusDesc: [], // 客户状态
      member_list: [], // 员工数据
      task: [{ ...init_task_data }],
      disease_list: [],
      disease_name: '', // 用户编辑回显的专病名称
      detail_info: {},
      delete_list: [], //删除的任务集合
      isPlanAddTask: false,
      plan_id: '',
    };
  },

  computed: {
    isEdit() {
      return !!this.id;
    },
  },

  watch: {
    selectedOptions(val) {
      if (!this.id) {
        this.setCascaderContent();
        let id_list = [];
        val.forEach(item => id_list.push(item[1]));
        this.formData.disease_id = id_list.join(',');
      }
    },
  },

  created() {},

  mounted() {},

  methods: {
    // 用户专案计划添加任务初始化
    addTaskInit(info) {
      this.isPlanAddTask = true;
      this.formData.code = info.code;
      this.formData.uid = info.uid;
      this.$nextTick(() => {
        this.$refs.userSearchRef.echoUser({ real_name: info.name, mobile: info.mobile, uid: info.uid });
      });
      this.formData.client_status = info.client_status;
      this.disease_name = info.disease_group_text;
      this.plan_id = info.plan_id;
    },
    handleChange(value) {
      let new_index = -1;
      value.some((item, index) => {
        if (!this.selectedOptions.some(s_item => item === s_item)) {
          new_index = index;
          return true;
        }
      });
      if (new_index == -1) {
        this.selectedOptions = value;
      } else {
        this.selectedOptions = value.filter(item => item[0] === value[new_index][0]);
      }
      setTimeout(() => {
        this.$refs.formData.validateField('disease_id');
      });
    },
    // 自定义内容
    setCascaderContent() {
      let parentElement = document.getElementById('customCascader');
      let node = parentElement?.getElementsByClassName('el-cascader__tags')[0];
      if (!node) return
      let pathName = this.selectedOptions.length ? this.selectedOptions[0][0] : '';
      let path_name_label = '';
      let current_option = this.disease_list.filter(item => item.value == pathName);
      path_name_label = current_option[0]?.label;
      if (!path_name_label) {
        node.innerHTML = '';
        return;
      }
      let text = '';
      this.selectedOptions.forEach((item, index) => {
        text +=
          index == 0
            ? `${getLabelByValue(item[1], current_option[0]?.children)}`
            : `、${getLabelByValue(item[1], current_option[0]?.children)}`;
      });
      node.innerHTML = path_name_label ? `${path_name_label}/${text}` : '';
      node.style =
        'line-height: 30px;left: 10px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;overflow-x: auto';

      function getLabelByValue(value, list) {
        let get_list = [];
        get_list = list?.filter(item => item.value === value);
        return get_list[0]?.label || '';
      }
    },
    // 新增任务
    addTask() {
      this.task.unshift(cloneDeep(init_task_data));
    },
    // 删除任务
    deleteTask(index) {
      this.delete_list.push({ id: this.task[index].id, is_delete: 1 });
      this.task.splice(index, 1);
    },
    // 发药计划
    addDrugPlan(index) {
      this.$set(this.task, index, {
        ...this.task[index],
        _isShowPlan: true,
        dispensing_plan: {
          time: '',
          treatment: '',
          all_times: '',
          this_times: '',
          residual_times: '',
          medicine_detail: '',
          address: '',
        },
      });
    },
    // 删除发药计划
    deleteDrugPlan(index) {
      this.$set(this.task, index, {
        ...this.task[index],
        _isShowPlan: false,
        dispensing_plan: {},
      });
    },
    init() {
      this.getUserPlanOptions();
      this.getUserDiseaseList();
      this.getMemberList();
      if (this.id) {
        this.getUserPlanTaskInfo();
      } else {
        this.task = [cloneDeep(init_task_data)];
      }
    },
    MODAL_SCROLL_TOP() {
      let MODAL_EL = this.$el.getElementsByClassName('ivu-modal-body')[0];
      if (MODAL_EL) {
        MODAL_EL.scrollTop = 0;
      }
    },
    /**
     * @description: 弹窗状态检测
     * @params  { Boolean } visible true: 弹窗打开 false:弹窗关闭
     * */
    changeVisible(visible) {
      if (visible) {
        this.init();
      } else {
        this.closeModal();
      }
    },

    /**
     * @description: 弹窗数据清除
     * */
    clearData() {
      this.$refs.formData.resetFields();
      this.task = [];
      this.selectedOptions = [];
      this.delete_list = [];
      this.isPlanAddTask = false;
      this.plan_id = '';
    },

    /**
     * @description: 弹窗关闭
     * */
    closeModal() {
      this.clearData();
      this.MODAL_SCROLL_TOP();
      this.$emit('input', false);
    },
    // 校验必填字段
    validFields() {
      let flag = true;
      this.task.some((item, index) => {
        if (!item.follow_time) {
          this.$Message.error(`【任务${index + 1}】的任务时间不可为空`);
          flag = false;
          return true;
        }

        if (!item.content) {
          this.$Message.error(`【任务${index + 1}】的任务跟进内容不可为空`);
          flag = false;
          return true;
        }
      });
      return flag;
    },

    /**
     * @description: 确定事件
     * */
    confirm() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.validFields()) {
            this.id || this.isPlanAddTask ? this.getUserPlanEdit() : this.getUserPlanCreate();
          }
        } else {
          this.MODAL_SCROLL_TOP();
        }
      });
    },
    handleTask() {
      let task = cloneDeep(this.task);
      task.forEach(item => {
        if (!item._isShowPlan) {
          item.dispensing_plan = {};
        }
      });
      console.log('task', task);
      return task || {};
    },
    // 创建任务
    getUserPlanCreate() {
      this.confirmLoading = true;
      let params = {
        id: this.id,
        ...this.formData,
        task: this.handleTask(),
      };
      this.$api
        .getUserPlanCreate(params)
        .then(res => {
          this.$emit('success');
          this.closeModal();
        })
        .finally(() => (this.confirmLoading = false));
    },

    // 编辑任务
    getUserPlanEdit() {
      this.confirmLoading = true;
      let params = {};
      if (this.isPlanAddTask) {
        params = {
          id: this.plan_id,
          task: this.handleTask(),
        };
      } else {
        params = {
          id: this.detail_info.id,
          client_status: this.formData.client_status,
          task: [...this.delete_list, ...this.handleTask()],
        };
      }
      this.$api
        .getUserPlanEdit(params)
        .then(res => {
          this.$emit('success');
          this.closeModal();
        })
        .finally(() => (this.confirmLoading = false));
    },

    // dispensing_plan 内容是否为空
    dispensingPlanIsEmpty(plan_obj = {}) {
      let list = Object.keys(plan_obj);
      return list.every(item => {
        if (item === 'create_time') {
          return !Number(plan_obj[item] || '');
        } else {
          return !plan_obj[item];
        }
      });
    },

    // 获取任务详情
    getUserPlanTaskInfo() {
      let params = {
        id: this.id,
      };
      this.$api.getUserPlanTaskInfo(params).then(res => {
        this.detail_info = res;
        this.formData.code = res.code;
        this.formData.client_status = res.client_status;
        this.task = res.task;
        this.task.forEach(item => {
          item.follow_time = Number(item.follow_time)
            ? this.$moment(Number(item.follow_time * 1000)).format('YYYY-MM-DD')
            : '';
          if (!this.dispensingPlanIsEmpty(item.dispensing_plan)) {
            item._isShowPlan = true;
          } else {
            item._isShowPlan = false;
          }
        });
        this.$nextTick(() => {
          this.$refs.userSearchRef.echoUser({ real_name: res.user.name, mobile: res.user?.mobile, uid: res.uid });
        });
        // 回显专病病症/部位
        this.disease_name = res.disease_group.disease_son_name
          ? `${res.disease_group.disease_name}/${res.disease_group.disease_son_name}`
          : res.disease_group.disease_group;
      });
    },

    // 获取枚举
    getUserPlanOptions() {
      let params = {};
      this.$api.getUserPlanOptions(params).then(res => {
        this.clientStatusDesc = S.descToArrHandle(res.clientStatusDesc);
      });
    },

    // 获取专病数据
    getUserDiseaseList() {
      let params = {
        page: 1,
        pageSize: 1000,
      };
      this.$api.getUserDiseaseList(params).then(res => {
        this.handleDiseaseList(res.list);
      });
    },

    // 获取员工数据
    getMemberList() {
      let params = {
        page: 1,
        pageSize: 1000,
      };
      this.$api.getMemberList(params).then(res => {
        this.member_list = res.members;
      });
    },

    // 处理专病数据
    handleDiseaseList(list) {
      this.disease_list = [];
      list.forEach(item => {
        item.children.forEach(c_item => {
          c_item.label = c_item.name;
          c_item.value = c_item.id;
        });
        this.disease_list.push({
          ...item,
          label: item.name,
          value: item.id,
        });
      });
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 450px;
  min-height: 450px;
  overflow-y: auto;
}

.form-warpper {
  display: flex;

  .form-item {
    margin-right: 20px;
    flex: 1;

    &:last-child {
      margin-right: 0px;
    }
  }
}

::v-deep .el-cascader {
  margin-top: 1px;
  line-height: 32px;

  .el-input__inner {
    height: 32px !important;
  }

  .el-cascader__tags {
    height: 30px;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>

<style lang="less" scoped>
::v-deep .el-input__icon {
  line-height: 30px;
}
.task-box {
  .task-header {
    margin-bottom: 20px;
  }

  .task-item {
    margin-bottom: 20px;
    border: 1px solid #eaeaea;

    .item-header {
      background: #f7f8fc;
      padding: 10px 16px;
    }

    .task-content {
      padding: 10px 16px 0px 16px;
    }

    .drug-content-wrapper {
      .drug-header {
        background: #f7f8fc;
        padding: 10px 16px;
      }

      .drug-content {
        padding: 10px 16px 0px 16px;
      }
    }
  }
}

.ml10 {
  margin-left: 10px;
}

.mr20 {
  margin-right: 20px;
}
</style>

<style lang="less">
.custom-popper-cascader {
  z-index: 9999999999 !important;
  .el-cascader-menu__wrap {
    min-height: 40px;
  }
  .el-cascader-panel {
    .el-cascader-menu {
      &:first-child {
        .el-checkbox {
          display: none;
        }
      }

      &:nth-child(2) {
        .el-cascader-node {
          .el-checkbox {
            width: 100%;
            z-index: 1;
          }

          .el-cascader-node__label {
            position: absolute;
            left: 34px;
          }
        }
      }
    }
  }
}
</style>
