<template>
  <div>
    <el-select
      style="width: 100%"
      popper-class="custom-el-remote-select"
      :value="value"
      value-key="uid"
      filterable
      remote
      :placeholder="placeholder"
      :remote-method="searchMethod"
      :loading="loading"
      @change="selectSup"
      @clear="clearSub"
      :disabled="disabled"
      clearable
    >
      <el-option
        v-for="item in user_list"
        :key="item.uid"
        :label="`${item.real_name} / ${item.mobile}`"
        :value="item.uid"
      >
        <span>{{ item.real_name }} / {{ item.mobile }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { debounce } from 'lodash-es'

export default {
  name: 'userSearch',
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '请选择用户姓名',
    },
    // 传入UID标识做回显
    uid: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      user_list: [],
      loading: false,
    }
  },
  watch: {
  },
  computed: {},
  created() {
    this.init()
  },
  mounted() {},
  methods: {
    init() {
      if (!this.uid) {
        this.searchMethod()
      }
    },
    // 调用此方法，进行数据回显
    echoUser(val) {
      this.user_list = [val] || [];
      this.$emit('input', val.uid);
    },
    selectSup(val) {
      this.$emit('input', val);
    },
    searchMethod: debounce(function (query = '') {
      this.loading = true
      let params = {
        search: query,
      }
      this.$api.getUserList(params).then(res => {
        this.loading = false
        this.user_list = res.users
      })
    }, 300),

    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .el-select {
  .el-input__inner {
    height: 33px;
    margin-top: 1px;
    padding-left: 9px;
  }
  .el-input__icon {
    line-height: 30px;
  }
}
::v-deep .el-input.is-disabled .el-input__inner {
  background: #f3f3f3;
  border-color: #bbb;
}
</style>
<style lang="less">
.custom-el-remote-select {
  z-index: 99999999 !important;
}
</style>
