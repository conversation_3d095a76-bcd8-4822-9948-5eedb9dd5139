<template>
  <div>
    <Button type="primary" class="mb10" @click="addClass">新 增</Button>

    <Table
      row-key="id"
      :indent-size="2"
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :height="$store.state.app.clientHeight - 230"
    >
      <template slot="tree" slot-scope="{ row }"></template>

      <template slot="name" slot-scope="{ row }">
        <div :style="{ marginLeft: row._type !== 'first' ? '20px' : '' }">{{ row.name }}</div>
      </template>
      <template slot="create_time" slot-scope="{ row }">
        {{ row.create_time | data_format }}
      </template>

      <template slot-scope="{ row }" slot="operate">
        <a class="mr10" @click="addSecondClass(row)" v-if="row._type === 'first'">添加子分类</a>
        <a @click="editClass(row)">编辑</a>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: center"
    />

    <!-- 新增一级分类 -->
    <add-first-classify v-model="addFirstVisible" :row="currentRow" @success="onSearch"></add-first-classify>
    <!-- 新增二级分类 -->
    <add-second-classify v-model="addSecondVisible" :row="currentRow" @success="onSearch"></add-second-classify>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import S from '@/libs/util';
import search from '@/mixins/search';
import addFirstClassify from './components/addFirstClassify.vue';
import addSecondClassify from './components/addSecondClassify.vue';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  r: '',
};
export default {
  name: 'list',
  components: { addFirstClassify, addSecondClassify },
  mixins: [search],
  data() {
    return {
      apiName: 'getUserDiseaseList',
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { title: ' ', key: 'tree', align: 'right', tree: true, width: 40 },
        { title: '一级分类', slot: 'name', align: 'left' },
        { title: '分类ID', key: 'id', align: 'center', minWidth: 160 },
        { title: '创建时间', slot: 'create_time', align: 'center', minWidth: 200 },
        { title: '操作', slot: 'operate', align: 'center', fixed: 'right', width: 120 },
      ],
      currentRow: {},
      addFirstVisible: false,
      addSecondVisible: false,
    };
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  computed: {},

  methods: {
    // 添加子分类
    addClass() {
      this.currentRow = {};
      this.addFirstVisible = true;
    },
    // 添加子分类
    addSecondClass(row) {
      this.currentRow = row;
      this.addSecondVisible = true;
    },
    // 编辑分类
    editClass(row) {
      this.currentRow = row;
      if (row._type === 'first') {
        this.addFirstVisible = true;
      } else {
        this.addSecondVisible = true;
      }
    },
    handlerListData() {
      this.list?.forEach((item, index) => {
        item.children.map(c_item => (c_item.f_item = { name: item.name, id: item.id }));
        item._showChildren = true;
        item._type = 'first';
      });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.ml10 {
  margin-left: 10px;
}
p {
  margin: 0;
}
.item {
  display: flex;
  align-items: flex-start;
  .item-label {
    width: 82px;
    min-width: 82px;
    text-align: right;
    color: #aaa;
    margin-right: 4px;
  }
  .item-value {
    text-align: left;
  }
}
</style>
