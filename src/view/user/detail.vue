<template>
  <div class="user-wrapper">
    <div class="user-info">
      <div class="user-detail">
        <div class="user-base flex-1 flex flex-item-between">
          <div class="user-avatar flex flex-item-align">
            <img :src="userInfo.avatar | imageStyle" v-if="userInfo.avatar" />
            <i class="fa fa-user-circle" v-else></i>
            <!--            <div>-->
            <!--              <div class="flex flex-item-align mb-6">-->
            <span class="user-name">{{ userInfo.real_name || '-' }}</span>
            <div class="user-async flex flex-item-align" v-if="userInfo.auth_status === '2'">
              <svg-icon size="12" class="mr-2" name="is-sync"></svg-icon>
              <span>已同步</span>
            </div>
            <!--              </div>-->
            <!--              <template v-if="isTemporaryMobile">-->
            <!--                <Button class="ml-16" size="small" type="primary"-->
            <!--                  >修改个人信息-->
            <!--                </Button>-->
            <!--                <Button class="ml10" type="default" style="height: 26px" @click="modifyMobile">修改手机号</Button>-->
            <!--              </template>-->
            <!--            </div>-->
          </div>
          <div class="user-btn">
            <Tooltip
              :content="isTemporaryMobile ? '当前用户为临时号，无法自定义管理通知' : '通知设置'"
              theme="dark"
              max-width="140"
            >
              <div
                :class="['msg-icon', 'cursor', isTemporaryMobile ? 'msg-icon-disabled' : '']"
                @click="messageSetting"
              >
                <svg-icon
                  size="20"
                  name="msg-sys"
                  class="cursor"
                  :color="isTemporaryMobile ? '#C0C4CC' : '#303133'"
                  v-if="userInfo.follow_sys === '1'"
                ></svg-icon>
                <svg-icon class="cursor" size="20" color="#303133" name="msg-user" v-else></svg-icon>
              </div>
            </Tooltip>
            <svg-icon
              name="user-edit"
              class="ml-24 cursor"
              size="20"
              @click="editUserInfo(userInfo.auth_status === '2' ? 'editAuthModalVisible' : 'editModalVisible')"
            ></svg-icon>
          </div>
        </div>
        <div class="user-content mt-20">
          <el-descriptions title="" :labelStyle="{ color: '#909399' }" :contentStyle="{ color: '#303133' }">
            <el-descriptions-item label="用户标签">
              <div v-if="userInfo.label_list?.length">
                <Tag v-for="item in userInfo.label_list" :key="item.kw" class="mr-4">
                  {{ item.tag_name }}
                </Tag>
              </div>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="用户年龄">{{ userInfo.birthday || '-' }}</el-descriptions-item>
            <el-descriptions-item label="注册名称">{{ userInfo.nickname || '-' }}</el-descriptions-item>
            <el-descriptions-item label="用户性别">
              {{ (userInfo.patient_sex === '1' && '男') || (userInfo.patient_sex === '2' && '女') || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="出生日期">{{ userInfo.birthdayDate || '-' }}</el-descriptions-item>
            <el-descriptions-item label="注册时间">{{ userInfo.create_time | data_format }}</el-descriptions-item>
            <el-descriptions-item label="联系电话"
              >{{ userInfo.mobile || '-' }}
              <svg-icon
                class="cursor ml-4"
                size="16"
                name="user-edit"
                @click="modifyMobile"
                v-if="userInfo.auth_status === '2'"
              ></svg-icon>
            </el-descriptions-item>
            <el-descriptions-item label="证件/编号">
              <span v-if="!patient.card_no">-</span>
              <span v-else>{{ patient.card_type_text }} {{ patient.card_no }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="用户来源">{{ patient.from_text || '-' }}</el-descriptions-item>
            <el-descriptions-item label="联系地址">{{ patient.addr_detail || '-' }}</el-descriptions-item>
            <el-descriptions-item label="会员名称" v-if="userInfo.is_vip == 1">
              {{ userInfo.vip_info.map(item => item.user_type_text).join('、') }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <!-- 非榕树堂 -->
      <div v-if="!is_rst" class="user-recharge">
        <div class="recharge-header">
          <h2 class="title">储值余额</h2>
          <span class="detail-link cursor" @click="checkChargeDetails">变动明细 <Icon type="ios-arrow-forward" /></span>
        </div>
        <div class="recharge-balance">￥{{ rechargeInfo.total_money }}</div>
        <div class="recharge-actions">
          <Button class="flex-1 mr-12" :disabled="rechargeInfo.total_money <= 0" size="large" @click="refund"
            >退款
          </Button>
          <Button
            class="flex-1"
            type="primary"
            size="large"
            :to="{ path: '/trade/give/create', query: { uid: userInfo.uid } }"
            >充值</Button
          >
        </div>
      </div>

      <!-- 榕树堂 -->
      <div v-else class="user-recharge-new">
        <div class="recharge-title">储值余额</div>
        <div class="recharge-balance-group">
          <!-- 储值余额 -->

          <template v-for="(item, key) in wallets">
            <div :key="key" class="recharge-balance-item">
              <div class="recharge-balance-header">
                <div class="balance-type active">
                  <div class="line"></div>
                  {{ item.is_yzt == 1 ? '云储值' : '门店储值' }}
                </div>
                <div class="balance-detail cursor" @click="checkChargeDetails(item)">
                  变动明细 <Icon type="ios-arrow-forward" />
                </div>
              </div>
              <div class="recharge-balance-num">
                <span class="recharge-icon">¥</span>
                <span class="recharge-balance" v-text-format.money="item.total_money"></span>
              </div>

              <!-- v-if="item.is_yzt == 1" -->
              <div class="recharge-balance-actions">
                <Button class="action-btn" size="large" @click="refund(item)" :disabled="item.total_money == 0"
                  >退款</Button
                >
                <Button
                  v-if="item.is_yzt == 1"
                  class="action-btn primary"
                  type="primary"
                  size="large"
                  :to="{ path: '/trade/give/create', query: { uid: userInfo.uid } }"
                  >充值</Button
                >
              </div>
            </div>
            <div :key="'line-' + key" v-if="key < wallets.length - 1" class="recharge-balence-line"></div>
          </template>

          <!-- <div class="recharge-balence-line"></div> -->
          <!-- 云储值 -->
          <!-- <div class="recharge-balance-item">
            <div class="recharge-balance-header">
              <div class="balance-type">
                <div class="line"></div>
                云储值
              </div>
              <div class="balance-detail cursor" @click="checkChargeDetails('cloud')">
                变动明细 <Icon type="ios-arrow-forward" />
              </div>
            </div>
            <div class="recharge-balance-num">
              <span class="recharge-icon">¥</span>
              <span class="recharge-balance" v-text-format.money="rechargeInfo.total_money"></span>
            </div>
            <div class="recharge-balance-actions">
              <Button class="action-btn" size="large" @click="refund('cloud')">退款</Button>
              <Button class="action-btn primary" type="primary" size="large" @click="recharge">充值</Button>
            </div>
          </div> -->
        </div>
      </div>
    </div>
    <div class="tabs-wrapper">
      <!-- tab -->
      <!--      <div class="tabs flex">-->
      <!--        <p-->
      <!--          :class="{ 'active-tab': currentTabId == index }"-->
      <!--          v-for="(item, index) in tabList"-->
      <!--          :key="item.title + index"-->
      <!--          @click="tabIndexChange(index)"-->
      <!--        >-->
      <!--          {{ item.title }}-->
      <!--        </p>-->
      <!--      </div>-->
      <Tabs v-model="currentTabId" @on-click="tabIndexChange">
        <TabPane :label="item.title" v-for="item in computeTabList" :key="item.id" :name="item.id"></TabPane>
      </Tabs>
      <div class="detail-content" v-if="currentTabId == '1'">
        <div class="detail-top flex">
          <div class="detail-top-l">
            <div class="content-title">
              <h2>消费分析</h2>
            </div>
            <div class="content-data">
              <div class="content-1">
                <div class="data-item">
                  <p class="p-title">消费金额(元)</p>
                  <p class="p-content">￥{{ userStatistics.total_consume_price || 0 }}</p>
                </div>
                <div class="data-item">
                  <p class="p-title">消费次数(次)</p>
                  <p class="p-content">{{ userStatistics.total_consume_num || 0 }}</p>
                </div>
                <div class="data-item">
                  <p class="p-title">客单价(元)</p>
                  <p class="p-content">￥{{ userStatistics.order_per_price || 0 }}</p>
                </div>
              </div>
              <div class="content-2">
                <div class="data-item" v-if="!is_rst">
                  <p class="p-title">储值次数(次)</p>
                  <p class="p-content">{{ (userStatistics.recharge && userStatistics.recharge.num) || 0 }}</p>
                </div>
                <div class="data-item">
                  <p class="p-title">累计实充金额(元)</p>
                  <p class="p-content" v-text-format.number="getToTalRealFee"></p>
                  <p
                    v-if="
                      Number(userStatistics.recharge?.real_fee) > 0 && Number(userStatistics.recharge_yzt?.real_fee) > 0
                    "
                  >
                    (￥{{ userStatistics.recharge?.real_fee || 0 }} + ￥{{ userStatistics.recharge_yzt?.real_fee || 0 }}
                    )
                  </p>
                </div>
                <div class="data-item">
                  <p class="p-title">最近一次消费时间</p>
                  <p class="p-content">{{ userStatistics.last_consume_time | data_format('YYYY-MM-DD') }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="detail-top-r">
            <div class="chart-box">
              <div class="title"><h2>消费结构占比</h2></div>
              <div class="flex">
                <div id="pie-chart" ref="pie-chart"></div>
                <div class="table-pie">
                  <Table
                    :columns="consumeCols"
                    style="margin: 70px 30px 0 0"
                    :data="consumeData"
                    v-show="consumeData.length"
                  >
                    <template slot-scope="{ row }" slot="ratio"> {{ row.ratio }}%</template>
                  </Table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="detail-center flex">
          <div class="detail-center-l flex-1">
            <p class="center-title card-title">HIS诊疗</p>
            <p class="center-subTitle">症状统计</p>
            <div>
              <div v-show="symptomOptions.xData.length">
                <Vertical-Bar id="vertical" :dataSource="symptomOptions" height="200" key="vertical"></Vertical-Bar>
              </div>
              <p class="flex flex-item-center empty-style" v-show="!symptomOptions.xData.length">暂无数据</p>
            </div>
            <p class="center-subTitle card-title" style="margin-top: 30px">常用治疗榜单</p>
            <Table :columns="treatCols" :data="treatList" height="183">
              <template slot-scope="{ row, index }" slot="No">
                <div class="top-three">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0" />
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1" />
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2" />
                </div>
              </template>
              <template slot-scope="{ row }" slot="num"> {{ row.num }}件</template>
            </Table>
          </div>
          <div class="detail-center-r flex-1">
            <p class="center-title card-title">商城购物</p>
            <p class="center-subTitle">常购商品榜单(TOP3)</p>
            <Table :columns="shopCols" :data="shopList" height="183">
              <template slot-scope="{ row, index }" slot="No">
                <div class="top-three">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0" />
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1" />
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2" />
                </div>
              </template>
              <template slot-scope="{ row }" slot="buy_num"> {{ row.buy_num }}件</template>
            </Table>
            <p class="center-subTitle card-title" style="margin-top: 47px">预约服务榜单(TOP3)</p>
            <Table :columns="appointmentCols" :data="appointmentList" height="183">
              <template slot-scope="{ Col, index }" slot="No">
                <div class="top-three">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0" />
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1" />
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2" />
                </div>
              </template>
              <template slot-scope="{ row }" slot="reserve_num"> {{ row.reserve_num }}次</template>
            </Table>
          </div>
        </div>
      </div>
      <component
        v-else
        :is="currentComponent"
        v-bind="{ mobile: userInfo.mobile, isTemporaryMobile: isTemporaryMobile, userInfo: userInfo }"
      ></component>
    </div>

    <div style="height: 35px"></div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>

    <ChangeDetails
      :charge-visible.sync="chargeVisible"
      :recharge-info="rechargeInfo"
      :isModalReq="isModalReq"
      :type="type"
    ></ChangeDetails>
    <authEditUserModal
      :edit-modal-visible.sync="editAuthModalVisible"
      @updateUserInfo="updateUserInfo"
      :getUserInfo="getUserInfo"
      :user-info="userInfo"
      :sexy-types="sexyTypes"
      :card-types="cardTypes"
      :source-types="sourceTypes"
      :label-types="labelTypes"
      :label-list="labelList"
      :phone="userInfo.mobile"
      :tag_list="tag_list"
      :authStatus="userInfo.auth_status"
    ></authEditUserModal>

    <EditUserModal
      :edit-modal-visible.sync="editModalVisible"
      @updateUserInfo="updateUserInfo"
      :getUserInfo="getUserInfo"
      :user-info="userInfo"
      :sexy-types="sexyTypes"
      :card-types="cardTypes"
      :source-types="sourceTypes"
      :label-types="labelTypes"
      :label-list="labelList"
      :tag_list="tag_list"
      @success="getMergeInfo"
    ></EditUserModal>

    <modify-user-mobile-modal
      v-model="modifyUserVisible"
      type="update"
      :uid="userInfo.uid"
      @updateSuccess="init"
      :phone="userInfo.mobile"
    ></modify-user-mobile-modal>
    <MessageModal v-model="msgSettingVisible" :uid="userInfo.uid" @settingComplete="settingComplete"></MessageModal>
    <user-merge-modal
      v-model="userMergeVisible"
      :merge_info="mergeInfo"
      :formData="update_formData"
      @success="getUserInfo"
    ></user-merge-modal>
    <!-- rst储值退款 -->
    <recharge-refund v-model="rechageInfoVisible" :uid="$route.query.uid" @success="init"></recharge-refund>

    <!-- 普通诊所储值退款 -->
    <RefundModal :refund-visible.sync="refundVisible" @checkDetail="checkDetail"></RefundModal>
  </div>
</template>

<script>
import charts from '@/mixins/charts';
import S from '@/libs/util';
// import detailTable from './components/detail-table';
import orderTab from './components/order-tab';
import cardDetailTab from './components/card-detail-tab';
import visitOrderTab from './components/visit-order-tab';
import VerticalBar from './components/vertical-bar';
import authEditUserModal from './components/authEditUserModal';
import EditUserModal from './components/EditUserModal';
import StoredRecord from './components/StoredRecord';
import ChangeDetails from './components/ChangeDetails';
import RefundModal from './components/RefundModal';
import ChronicDisease from '@/components/chronic/chronic-disease';
import exchangeDetailTab from './components/exchange-detail-tab';
import couponDetailTable from './components/coupon-detail-table.vue';
import modifyUserMobileModal from './components/modifyUserMobileModal.vue';
import userFollow from './components/userFollow.vue';
import FamilyGroup from './components/family-group/index.vue';
import VipInfo from './components/VipInfo';
import { cloneDeep } from 'lodash-es';
import userMergeModal from './components/userMergeModal.vue';
import rechargeRefund from '@/components/rechargeRefund/index.vue';
import { isRstClinic } from '../../libs/runtime';
import { $operator } from '@/libs/operation';

export default {
  name: 'detail',
  mixins: [charts],
  components: {
    modifyUserMobileModal,
    // detailTable,
    orderTab,
    cardDetailTab,
    VerticalBar,
    visitOrderTab,
    authEditUserModal,
    EditUserModal,
    StoredRecord,
    ChangeDetails,
    RefundModal,
    ChronicDisease,
    exchangeDetailTab,
    couponDetailTable,
    FamilyGroup,
    VipInfo,
    userMergeModal,
    userFollow,
    rechargeRefund,
    MessageModal: () => import('./components/MessageModal/index.vue'),
  },
  data() {
    return {
      tabList: [], // tab切换list
      defaultTabList: [
        {
          id: '1',
          title: '用户分析',
          component: '',
        },
        {
          id: '2',
          title: '慢病管理',
          component: 'ChronicDisease',
        },
        {
          id: '3',
          title: '消费订单明细',
          component: 'orderTab',
        },
        {
          id: '4',
          title: '卡券明细',
          component: 'cardDetailTab',
        },
        {
          id: '5',
          title: '通兑券明细',
          component: 'exchangeDetailTab',
        },
        {
          id: '6',
          title: '优惠券',
          component: 'couponDetailTable',
        },
        {
          id: '7',
          title: '随访记录',
          component: 'visitOrderTab',
        },
        {
          id: '8',
          title: '储值记录',
          component: 'StoredRecord',
        },
        {
          id: '9',
          title: '家庭组',
          component: 'FamilyGroup',
        },
        {
          id: '11',
          title: '用户跟随',
          component: 'userFollow',
        },
      ],
      vipTab: { id: '10', title: '会员信息', component: 'VipInfo' },
      chargeVisible: false, //变动明细弹窗
      refundVisible: false, //退款弹窗
      userInfo: {},
      userStatistics: {},
      patient: {},
      consumeCols: [
        { title: '类别', key: 'name', width: 80 },
        { title: '次数(次)', key: 'num' },
        { title: '金额(元)', key: 'price' },
        { title: '占比', slot: 'ratio' },
      ],
      consumeData: [],
      shopList: [], //预约服务
      appointmentList: [], // 常购商品

      currentTabId: '1', // 默认选中的tab
      treatCols: [
        {
          title: '排名',
          slot: 'No',
          align: 'center',
          width: 80,
        },
        {
          title: '名称',
          key: 'name',
          align: 'left',
        },
        {
          title: '数量',
          slot: 'num',
          align: 'right',
        },
      ],
      appointmentCols: [
        {
          title: '排名',
          slot: 'No',
          align: 'center',
          width: 80,
        },
        {
          title: '名称',
          key: 'gs_name',
          align: 'left',
        },
        {
          title: '数量',
          slot: 'reserve_num',
          align: 'right',
        },
      ], //预约服务榜单columns
      shopCols: [
        {
          title: '排名',
          slot: 'No',
          align: 'center',
          width: 80,
        },
        {
          title: '名称',
          key: 'goods_name',
          align: 'left',
        },
        {
          title: '数量',
          slot: 'buy_num',
          align: 'right',
        },
      ], // 常购商品columns
      treatList: [], // 常用治疗榜单数据
      // appointmentList: [], //预约服务榜单数据
      // shopList: [], // 常购商品数据,
      symptomOptions: { xData: [], yData: [] }, // 症状统计柱状图
      cardTypes: [],
      editModalVisible: false,
      editAuthModalVisible: false,
      sexyTypes: [],
      sourceTypes: [],
      showConfirm: false,
      localUser: {},
      rechargeInfo: {
        give_money: 0,
        total_money: 0,
        real_money: 0,
      },
      isModalReq: false, //是否从弹窗请求
      labelTypes: [], // 慢病枚举
      labelList: [], // 慢病tag
      modifyUserVisible: false,
      msgSettingVisible: false, //消息设置弹窗
      userMergeVisible: false, // 账号信息合并
      detailLoading: false,
      isOpenedEdit: false,
      mergeInfo: {},
      update_formData: {},
      tag_list: [],

      // 储值退款
      rechageInfoVisible: false,
      rechargeCurrentRow: {},

      // 榕树堂储值余额
      wallets: [],

      // 用于区分储值明细 USER_RECHARGE_YZT云值通 USER_RECHARGE原
      type: '',
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  computed: {
    is_rst() {
      return isRstClinic();
    },
    isTemporaryMobile() {
      return this.userInfo.user_type !== '1';
    },
    currentComponent() {
      return this.tabList.find(item => item.id === this.currentTabId).component;
    },
    computeTabList() {
      const list = this.tabList;
      return list;
    },
    getToTalRealFee() {
      return $operator.add(this.userStatistics.recharge?.real_fee, this.userStatistics.recharge_yzt?.real_fee);
    },
  },
  watch: {
    detailLoading: {
      immediate: true,
      handler(val) {
        // 只打开一次，获取数据后再打开
        if (!val && !this.isOpenedEdit && this.userInfo?.auth_status && this.$route.query.openEdit === 'openEdit') {
          this.isOpenedEdit = true;
          this.editUserInfo(this.userInfo.auth_status === '2' ? 'editAuthModalVisible' : 'editModalVisible');
        }
      },
    },
  },
  methods: {
    getUserTagGroupList() {
      this.$api
        .getUserTagGroupList({
          page: 1,
          pageSize: 99,
          status: '1',
        })
        .then(res => {
          this.tag_list =
            res?.list
              ?.filter(item => item?.tag_info?.length > 0 && item.status === '1')
              .map(item => ({
                ...item,
                tag_info: item.tag_info?.filter(itm => itm.status === '1'),
              })) || [];
        });
    },
    getMergeInfo(info, formData) {
      this.userMergeVisible = true;
      this.mergeInfo = info;
      this.update_formData = formData;
    },
    settingComplete(v) {
      this.userInfo.follow_sys = v;
    },
    init() {
      const { uid } = this.$route.query;
      if (uid) {
        this.getUserTypes();
        this.getUserTagGroupList();
      }
    },
    // 修改用户手机号
    modifyMobile() {
      this.modifyUserVisible = true;
    },
    checkDetail() {
      this.isModalReq = true;
      this.chargeVisible = true;
    },
    refund(item) {
      if (this.is_rst) {
        if (item.is_yzt == '1') {
          this.rechageInfoVisible = true;
        } else {
          this.refundVisible = true;
        }
      } else {
        this.refundVisible = true;
      }
    },
    //查看充值记录
    checkChargeDetails(item) {
      this.isModalReq = false;
      this.chargeVisible = true;
      if (this.is_rst) {
        this.type = item.type;
        this.rechargeInfo = item;
      }
    },
    updateUserInfo() {
      console.log(32132);
    },
    editUserInfo(key = '') {
      this.userInfo = {};
      this.userInfo = this.$lodash.cloneDeep(this.localUser);
      this[key] = true;
    },
    messageSetting() {
      if (this.isTemporaryMobile) return;
      this.msgSettingVisible = true;
    },
    //获取证件类型
    getUserTypes() {
      this.$api.getUserType().then(
        res => {
          console.log(res);
          this.cardTypes = S.descToArrHandle(res.card_type_desc);
          this.sourceTypes = S.descToArrHandle(res.source_desc);
          this.sexyTypes = S.descToArrHandle(res.sex_desc);
          this.labelTypes = res.label_desc;
          console.log('=>(detail.vue:408) this.labelTypes', this.labelTypes);
          this.getUserInfo();
        }
        // resolve => this.$Message.error(resolve.errmsg)
      );
    },
    tabIndexChange(id) {
      this.currentTabId = id;
      this.$router.replace({ query: { ...this.$route.query, routeTabId: id } });
      this.getUserInfo();
    },
    onSetRole(uid, role) {
      this.$http.post('clinic/user.user.setrole', { uid: uid, role: role }).then(
        () => {
          this.$Message.success(role === 'MEMBER' ? '撤销成功' : '设置成功');
          this.getUserInfo();
        },
        () => {}
      );
    },
    back() {
      // this.$router.push( {
      //   path: '/user/list',
      // } )
      this.$router.back();
    },
    // 判断路由上的tab是否在枚举的tab里面,不在,则初始化0
    isHasTab(tabIndex) {
      const arr = this.tabList.map(item => item.id);
      console.log('%c=>(detail.vue:529) arr', 'font-size: 18px;color: #FF7043 ;', arr);
      let flag = false;
      if (arr.indexOf(tabIndex) > -1) {
        flag = true;
      } else {
        flag = false;
      }
      return flag;
    },
    // 处理症状排名顺序
    handleSymptom(options) {
      console.log('options', options);
      if (!options.length) return { xData: [], yData: [] };
      let echartOptions = { xData: [], yData: [] };
      options.forEach(item => {
        echartOptions.xData.push(item.name);
        echartOptions.yData.push(item.num);
      });
      console.log('echartOptions', echartOptions);
      return echartOptions;
    },
    getUserInfo() {
      const uid = this.$route.query.uid;
      this.detailLoading = true;
      this.$api
        .getUserInfo({ uid })
        .then(
          res => {
            console.log('🚀 ~ getUserInfo ~ res=>', res.user);
            const isReadyHasVipTab = this.tabList.some(item => item.id === '10');

            if (res.user.vip_info.length) {
              if (!isReadyHasVipTab) {
                this.tabList = [];
                let tabs = cloneDeep(this.defaultTabList);
                tabs.unshift(this.vipTab);
                this.tabList = tabs;
                !this.$route.query.routeTabId && (this.currentTabId = '10');
              }
            } else {
              this.tabList = this.defaultTabList;
            }

            this.userInfo = {};
            this.localUser = {};
            this.rechargeInfo = res.user?.recharge;

            if (this.is_rst) {
              this.wallets = res.user.wallets;
            }

            if (res.user.birthday) {
              res.user.birthdayDate = res.user.birthday;
              res.user.birthday = S.formatDuration(res.user.birthday);
            }
            res.user.vip_st_time = res.user?.vip_info?.vip_st_time || '';
            res.user.vip_et_time = res.user?.vip_info?.vip_et_time || '';
            res.user.is_vip = res.user.vip_info?.length > 0 ? '1' : '0';
            this.localUser = res.user;
            this.userInfo = this.$lodash.cloneDeep(res.user);
            console.log(
              '%c [ this.userInfo ]-579',
              'font-size:13px; background:#697057; color:#adb49b;',
              this.userInfo
            );
            this.patient = res.patient;
            // 常用治疗榜单数据
            this.treatList = res.treakRank;
            this.userStatistics = res.userStatistics;
            // 常购商品榜单数据
            this.shopList = res.shopOrderAttrRanking;
            // 预约服务榜单数据
            this.appointmentList = res.reserveOrderRanking;
            // 症状排名
            this.symptomOptions = this.handleSymptom(res.aiSymptomsRank);
            // 食养标签
            this.labelList = this.userInfo.label_list;
            let pieData = {
              legendData: ['商城购物', 'HIS诊疗'],
              seriesData: {
                name: '消费占比',
                radius: ['0', '65%'],
                data: [
                  { name: '商城购物', value: res.userStatistics.shop_turnover, itemStyle: { color: '#FAC858' } },
                  { name: 'HIS诊疗', value: res.userStatistics.his_turnover, itemStyle: { color: '#8080FF' } },
                ],
              },
            };

            if (this.currentTabId == 0 || this.currentTabId == 1) {
              this.initPieChart('pie-chart', pieData, false);
            }

            this.consumeData = [
              {
                name: '商城购物',
                num: res.userStatistics.shop_paid_num,
                price: res.userStatistics.shop_turnover,
                ratio: res.userStatistics.shop_turnover_ratio,
              },
              {
                name: 'HIS诊疗',
                num: res.userStatistics.his_paid_num,
                price: res.userStatistics.his_turnover,
                ratio: res.userStatistics.his_turnover_ratio,
              },
            ];
            this.$forceUpdate();
            const routeTabId = this.$route.query.routeTabId;
            if (this.isHasTab(routeTabId)) {
              this.currentTabId = routeTabId;
            }
            // this.currentTabId = this.$route.query.routeTabId;
          },
          () => {}
        )
        .finally(() => {
          this.detailLoading = false;
        });
    },
    handleReachTop() {
      console.log('asda');
    },
  },
};
</script>

<style scoped lang="less">
.user-wrapper {
  font-size: 14px;

  .user-info {
    border-radius: 2px;
    margin-bottom: 16px;
    display: flex;

    .user-detail {
      background: #fff;
      padding: 16px;
      border-radius: 4px;
      flex: 2;

      .user-avatar {
        .fa-user-circle {
          font-size: 60px;
          color: #979cb0;
        }

        .user-name {
          font-weight: 600;
          font-size: 20px;
          color: #303133;
          line-height: 28px;
          margin-left: 16px;
          margin-right: 12px;
        }

        .user-async {
          height: 16px;
          font-weight: 400;
          font-size: 11px;
          color: #25b830;
          line-height: 16px;
          background: #e9f8ea;
          border-radius: 2px;
          padding: 2px 4px;
        }

        img {
          display: block;
          width: 64px;
          height: 64px;
          border-radius: 50%;
        }
      }

      .user-btn {
        .msg-icon-disabled {
          padding: 4px;
          background: #f5f6f8;
          border-radius: 2px;
        }
      }
    }

    .user-recharge {
      margin-left: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      display: flex;
      flex-direction: column;

      .recharge-header {
        display: flex;
        align-items: center;

        .title {
          font-weight: 600;
          font-size: 16px;
          color: #303133;
          line-height: 22px;
          flex: 1;
        }

        .detail-link {
          display: inline-flex;
          align-items: center;
          color: #909399;
        }
      }

      .recharge-balance {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 30px;
        color: #303133;
        line-height: 42px;
      }

      .recharge-actions {
        display: flex;
      }
    }

    .user-recharge-new {
      display: flex;
      flex-direction: column;
      margin-left: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 4px;
      flex: 1;

      line-height: normal;
      .recharge-title {
        font-size: 16px;
        font-weight: 600;
        color: #222;
        margin-bottom: 16px;
      }

      .recharge-balance-group {
        display: flex;
        flex: 1;
        gap: 0 20px;
        .recharge-balence-line {
          width: 1px;
          background: #e6e6e6;
          height: 100%;
          align-self: center;
        }

        .recharge-balance-item {
          flex: 1;

          // display: flex;
          // flex-direction: column;
          .recharge-balance-header {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .balance-type {
              display: flex;
              align-items: center;
              font-weight: 500;
              font-size: 14px;
              color: #333333;
              .line {
                width: 3px;
                height: 14px;
                margin-right: 4px;
                background: linear-gradient(90deg, #155bd4 0%, #4988fd 100%);
              }
            }

            .balance-detail {
              font-size: 13px;
              color: #999999;
            }
          }

          .recharge-balance-num {
            text-align: center;
            margin: 24px 0 40px 0;
            .recharge-icon {
              font-size: 24px;
              font-weight: 600;
            }
            .recharge-balance {
              font-size: 34px;
              font-weight: 600;
            }
          }

          .recharge-balance-actions {
            display: flex;
            gap: 4px;
            .action-btn {
              flex: 1;

              &.primary {
                flex: 2;
              }
            }
          }
        }
      }
    }
  }

  .detail-content {
    margin-top: 20px;

    .detail-top {
      .detail-top-l {
        flex: 3;

        .content-title {
          // border-bottom: 1px solid #F2F3F5;
          margin-bottom: 20px;

          h2 {
            font-weight: 600;
            font-size: 16px;
            color: #303133;
            line-height: 22px;
          }
        }

        .content-data {
          //display: flex;
          //flex-wrap: wrap;
          min-width: 400px;
          margin: 0 0px 20px;

          .content-1 {
            display: flex;
            box-sizing: border-box;

            .data-item:nth-of-type(1) {
              background: #d09d9d;
            }

            .data-item:nth-of-type(2) {
              background: #bdd09d;
            }

            .data-item:nth-of-type(3) {
              background: #af9dd0;
              margin-right: 0;
            }
          }

          .content-2 {
            display: flex;
            box-sizing: border-box;

            .data-item:nth-of-type(1) {
              background: #add5d6;
            }

            .data-item:nth-of-type(2) {
              background: #e9b690;
            }

            .data-item:nth-of-type(3) {
              background: #d0b39d;
              margin-right: 0;
            }
          }

          .data-item {
            flex: 1;
            border-radius: 8px;
            padding: 12px 0 24px 12px;
            color: #ffffff;
            margin-right: 10px;
            margin-bottom: 10px;
            box-sizing: border-box;

            .p-title {
              margin-bottom: 14px;
              font-size: 12px;
              font-weight: 500;
              color: #ffffff;
              line-height: 17px;
            }

            .p-content {
              font-size: 27px;
              font-family: DINCond-Bold, DINCond;
              font-weight: bold;
              color: #ffffff;
              line-height: 31px;
            }
          }
        }
      }

      .detail-top-r {
        flex: 2;
      }
    }

    .detail-center {
      border-top: 1px solid rgba(204, 204, 204, 0.3);

      //.center-title {
      //  font-size: 14px;
      //  font-weight: 600;
      //  color: #333333;
      //  line-height: 20px;
      //}

      //.center-subTitle {
      //  font-size: 12px;
      //  font-weight: 600;
      //  color: #333333;
      //  line-height: 17px;
      //}

      .detail-center-l {
        border-right: 1px solid rgba(204, 204, 204, 0.3);
        padding: 20px;
      }

      .detail-center-r {
        padding: 20px;
      }
    }
  }
}

.bold {
  font-weight: 900;
}

.table-box {
  height: 312px;
  background: #fafbff;
  border-radius: 8px;
  margin: 20px 30px 0;
}

.chart-box {
  width: 100%;
  height: 350px;
  padding: 0 20px;
  width: 100%;
  // box-shadow: 0px 0px 8px rgba(153, 153, 153, 0.2);
  border-radius: 4px;
}

.title {
  width: 100%;
  // border-bottom: 1px solid #F2F3F5;
  h2 {
    font-size: 14px;
    color: #333;
  }
}

#pie-chart {
  width: 200px;
  height: 200px;
  margin-top: 30px;
  margin-left: 20px;
}

.table-pie {
  min-width: 250px;
  flex: 1;
}

.runking-box {
  // width: 100%;
  height: 285px;
  margin-top: 20px;

  .runking-left {
    margin-right: 20px;
  }

  .runking {
    padding: 0 20px;
    box-shadow: 0px 0px 8px rgba(153, 153, 153, 0.2);
    border-radius: 4px;

    .runking-info {
      font-size: 14px;
      margin-top: 30px;
      margin-left: 10px;
    }

    .runking-name {
      line-height: 20px;
      margin-right: 10px;
    }
  }
}

.tabs-wrapper {
  ::v-deep .ivu-tabs-nav-container {
    font-size: 14px;
  }
}

.detail-content {
  ::v-deep .ivu-table {
    thead {
      th {
        background: #ffffff;
        border-bottom: none;
        color: #999999;
      }
    }

    td {
      border-bottom: none;
    }

    &::before {
      height: 0;
    }
  }
}
</style>

<style lang="less" scoped>
.edit-user-box {
  padding: 5px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 10px;
  color: #000000;
  background: #f5f6fa;
  border-radius: 4px;
  border: 1px solid #dbdee8;
  line-height: 1;

  &:hover {
    background: rgb(225, 225, 225, 0.1);
  }
}

// tab切换
.tabs {
  border-bottom: 1px solid #dcdee0;

  > p {
    position: relative;
    bottom: -1px;
    padding: 10px 28px;
    background: #f7f8fa;
    border: 1px solid #dcdee0;
    border-right: none;
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    color: #646566;
    line-height: 20px;
    cursor: pointer;
  }

  & > p:nth-child(1) {
    border-top-left-radius: 4px;
  }

  & > p:nth-last-child(1) {
    border-top-right-radius: 4px;
    border-right: 1px solid #dcdee0;
  }

  .active-tab {
    background: #fff;
    color: #333;
    border-bottom: none;
  }
}

// top 排名图片
.top-three {
  > img {
    width: auto;
    height: 20px;
  }
}

.empty-style {
  height: 200px;
  color: #ccc;
  font-size: 12px;
}

/deep/ .ivu-table thead th {
  border-bottom: 0.5px solid #ccc;
}

/deep/ .ivu-table td {
  border-bottom: 0.5px solid #ccc;
}

.trend-tag-box {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  height: auto;

  .trend-tag {
    padding: 6px;
    border-radius: 2px;
    opacity: 0.8;
    border: 1px solid #bcc3d7;
    margin-right: 10px;
    cursor: pointer;
    margin-bottom: 8px;
  }

  .tag-active {
    background: #155bd4;
    opacity: 1;
    color: #ffffff;
    border-color: #155bd4;
  }
}
</style>
<style lang="less" scoped>
.stored-box {
  box-sizing: border-box;
  width: 372px;
  padding: 16px 14px 16px 24px;
  background: #f8f8fb;
  border-radius: 6px;
  border: 1px solid #e4e6ed;
  color: #767c96;
  min-height: 144px;
  height: fit-content;
  margin-top: -50px;
  margin-right: 20px;
  z-index: 2;

  .stored-h {
    display: flex;
    justify-content: space-between;

    .stored-title {
    }

    .change-detail {
    }
  }

  .stored-c {
    font-size: 46px;
    font-family: DINCond-Black, DINCond;
    font-weight: 900;
    color: #757c98;
    line-height: 55px;
    text-indent: -8px;
    margin-top: 14px;
    word-break: break-all;
  }

  .stored-b {
    display: flex;
    justify-content: flex-end;
  }
}

.tabs-wrapper {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
}

.card-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  line-height: 22px;
}
</style>

<style lang="less">
.app-inner:has(.user-wrapper) {
  background: transparent !important;
  padding: 6px !important;
}

.app-container:has(.user-wrapper) {
  background: #f5f6f8;
}
</style>
