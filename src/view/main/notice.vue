<template>
  <div>
    <StandardPage
      :key="'StandardPage' + Math.random().toString().substring(2, 15)"
      :total="total"
      :page-size.sync="queryFormData.page_size"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="message-header">
          <div class="message-title">预约消息（{{ unreadNoticeCount }}）</div>
          <div
            v-if="unreadNoticeCount"
            class="clearUnread"
            @click="clearAllUnread"
            style="margin-left: auto; margin-top: 4px"
          >
            全部标记已读
          </div>
        </div>
      </template>
      <div class="message-content" v-loading="tableLoading">
        <div
          :class="{
            'message-content-item': true,
            isRead: +item.status === 3,
          }"
          v-for="item in list"
          :key="item.id"
        >
          <img
            @click="clearUnread(item)"
            v-if="+item.status !== 3"
            src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0321/180012_24645.png"
          />
          <img
            @click="clearUnread(item)"
            v-if="+item.status === 3"
            src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0321/185749_50873.png"
          />
          <div class="content">
            <div class="header">
              <el-badge :hidden="+item.status !== 2" is-dot class="content-item-header-dot">
                <div class="notify-message-tittle" @click="clearUnread(item)">{{ item.title || '-' }}</div>
              </el-badge>
              <div class="notify-message-time">{{ formatTime(item) }}</div>
            </div>
            <div class="notice-text">
              <div class="text" @click="clearUnread(item)">{{ formatNoticeText(item) }}</div>
              <div class="clearUnread" @click="toTargetPage(item)" style="margin-left: auto">查看详情</div>
            </div>
          </div>
        </div>
        <div v-if="list.length === 0 && !tableLoading" class="empty-table">
          <img src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0321/184505_71624.png" alt="" />
        </div>
      </div>
    </StandardPage>
    <custom-dialog :visible.sync="openDialog" :content="dialogText" :loading="savaLoading" :on-ok="onOk" />
  </div>
</template>

<script>
import StandardPage from '@/components/StandardTable/other.vue';
import { cloneDeep  } from 'lodash-es';
import S from '@/libs/util';
import CustomDialog from '@/components/custom-dialog/index.vue';
import moment from 'moment/moment';

const init_query_form_data = {
  page: 1,
  page_size: 20,
  status: '',
};
export default {
  name: 'notice',
  components: { CustomDialog, StandardPage },
  data() {
    return {
      queryFormData: { ...cloneDeep(init_query_form_data) },
      STATUS_OPTIONS: [
        { key: '', desc: '全部' },
        { key: '2', desc: '未读消息', color: 'error' },
        { key: '3', desc: '已读消息', color: 'default' },
      ],
      NOTICE_TYPES: {
        CLINIC_RESERVE: {
          name: '预约',
          path: '/reserve/listing/detail',
        },
      },
      tableLoading: false,
      list: [],
      total: 0,
      unreadNoticeCount: 0,
      status_count: {},
      tableCols: [
        {
          title: '序号',
          type: 'index',
          width: 50,
        },
        {
          title: '消息',
          slot: 'notice',
          align: 'center',
          tooltip: true,
          minWidth: 240,
        },
        {
          title: '时间',
          slot: 'time',
          align: 'center',
          tooltip: true,
          width: 140,
        },
        { title: '操作', slot: 'action', width: 150, align: 'center' },
      ],
      openDialog: false,
      dialogText: '',
      savaLoading: false,
      homeNotice: null,
    };
  },
  created() {},
  mounted() {
    console.log('mounted')
    this.queryFormData = S.merge(init_query_form_data, this.$route.query);
    this.getNoticeList();
    this.getUnreadCount();
    // 右上角标签的消息通道
    if (this.homeNotice) {
      typeof this.homeNotice.close === 'function' && this.homeNotice.close();
      typeof this.homeNotice.removeEventListener === 'function' &&
        this.homeNotice.removeEventListener('message', this.handleMessage);
      this.homeNotice = null;
    }
    this.homeNotice = new BroadcastChannel('homeNotice');
    this.homeNotice.addEventListener('message', this.handleMessage);
  },
  beforeDestroy() {
    this.homeNotice.close();
    this.homeNotice.removeEventListener('message', this.handleMessage);
    this.homeNotice = null;
  },
  methods: {
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.getNoticeList();
    },
    clearAllUnread() {
      this.openDialog = true;
      this.dialogText = '是否把全部消息标记为已读';
    },
    getUnreadCount() {
      this.$api.getUnreadCount({}).then(res => {
        this.unreadNoticeCount = +res?.quantity || 0;
      });
    },
    // 有回调就是去目标页
    clearUnread(item, callback) {
      console.log('点击已读：', item);
      if (+item?.status === 3) {
        callback && callback();
        return;
      }
      const ids = item?.id ? item.id : undefined;
      console.log('点击已读请求参数：', ids);
      this.$api.setNoticeRead({ ids }).then(() => {
        if (!callback) {
          this.$Message.success('标记成功');
        }
        callback && callback();
      });
    },
    onOk() {
      this.clearUnread({}, () => this.getNoticeList());
    },
    toTargetPage(item) {
      console.log(item, 'item')
      const path = this.NOTICE_TYPES?.[item.event_type]?.path;
      if (item?.content?.target === '_blank') {
        this.clearUnread(item, () => this.openNewPage(path, item.content.params));
      }
    },
    openNewPage(path, query) {
      this.notifyDropdown = false;
      const href = this.$router.resolve({
        path,
        query,
      }).href;
      window.open(href, '_blank');
    },
    formatTime(item) {
      if (!item.create_time) return '-';
      if (isNaN(+item.create_time * 1000)) return '-';
      return moment(item.create_time * 1000).format('YYYY-MM-DD HH:mm');
    },
    formatNoticeText(item) {
      const message = item?.content?.message;
      if (!message) return '-';
      if (!Array.isArray(message)) return '-';
      return message
        .map(texts => {
          if (!Array.isArray(texts)) return '-';
          if (texts?.[0]) return texts.join('：');
          return texts.join('');
        })
        .join('｜');
    },
    handleMessage(e) {
      console.log(e.data, 'notice页面收到通知e.data');
      if (e?.data.type === 'updateMessage') {
        console.log('预约通知：获取列表和数量');
        this.getNoticeList();
        this.getUnreadCount();
        return;
      }
      if (e?.data.type === 'updateReadMsg') {
        console.log('已读通知：修改状态为已读');
        const ids = e.data?.data?.notify_id?.split(',') || [];
        this.list.forEach((item, i) => {
          if (ids.includes(item.id)) {
            this.$set(this.list[i], 'status', '3');
          }
        });
        console.log('已读通知：重新获取数量');
        this.getUnreadCount();
      }
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.queryFormData.page_size = pageSize;
      this.getNoticeList();
    },
    getNoticeList() {
      this.tableLoading = true;
      this.$api.getNoticeList(this.queryFormData).then(data => {
        this.total = data.total;
        this.list = data.list;
        this.status_count = data?.status_count || {};
      })
        .finally(() => {
          this.tableLoading = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.clearUnread {
  color: #115bd4;
  cursor: pointer;
}
.clearUnread:hover {
  color: #447cdd;
}
.message-header {
  width: 100%;
  height: 40px;
  display: flex;
  border-bottom: 1px solid #ebedf0;
  .message-title {
    font-weight: 600;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
  }
}
.message-content {
  width: 100%;
  min-height: 400px;
  .message-content-item {
    width: 100%;
    display: flex;
    margin-top: 16px;
    flex-shrink: 0;
    > img {
      width: 32px;
      height: 32px;
      margin-right: 16px;
      object-fit: cover;
      cursor: pointer;
    }
    .content {
      flex: 1;
      height: 78px;
      border-bottom: 1px solid #ebedf0;
      .header {
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .notify-message-tittle {
          font-weight: 600;
          font-size: 15px;
          color: #303133;
          cursor: pointer;
        }
        .notify-message-time {
          font-size: 12px;
          color: #909399;
        }
      }
      .notice-text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 6px;
        cursor: pointer;
        .text {
          flex: 1;
          padding-right: 10px;
          font-size: 13px;
          color: #606266;
          line-height: 18px;
          white-space: nowrap; /* 防止文本换行 */
          overflow: hidden; /* 隐藏溢出的内容 */
          text-overflow: ellipsis; /* 显示省略号来代表被修剪的文本 */
        }
      }
    }
  }

  .message-content-item.isRead {
    .notify-message-tittle {
      color: #909399 !important;
    }
    .notify-message-time {
      color: #a8abb2 !important;
    }
    .notice-text .text {
      color: #a8abb2;
    }
  }
  .empty-table {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    > img {
      width: 260px;
      height: 260px;
      object-fit: cover;
    }
  }
}
</style>
