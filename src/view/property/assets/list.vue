<template>
  <div class="company-wrapper">
    <standard-table
      :columns="tableCols"
      :data="list"
      :loading="tableLoading"
      :current.sync="queryFormData.page"
      :page-size.sync="queryFormData.pageSize"
      :total="total"
      @on-change="onPageChange"
    >
      <template slot="header">
        <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <FormItem>
              <DatePicker
                type="month"
                clearable
                placeholder="时间"
                v-model="queryFormData.month"
                @on-change="onMonthChange"
              />
            </FormItem>

            <FormItem>
              <Input v-model="queryFormData.name_mobile" placeholder="员工姓名/手机号" clearable />
            </FormItem>

            <FormItem style="text-align: left">
              <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </FormItem>
          </Row>
        </Form>
        <!-- 统计卡片区域 -->
        <div class="stats-cards-wrapper">
          <div class="stats-card">
            <div class="card-header">
              <span class="card-title">待结算金额(元)</span>
              <Tooltip max-width="400" theme="dark" placement="top" class="custom-tooltip ml6">
                <div slot="content">
                  <!-- <p>待结算金额=</p> -->
                  <div>1. 预确认待结算员工的业绩提成；</div>
                  <div>2. 员工历史时间内总业绩提成，原订单相关服务尚未结束；</div>
                  <div>3. 服务完结后到结算时间自动流转到该员工的结算金额中并确认业绩提成</div>
                </div>
                <p class="flex flex-item-center cursor">
                  <Icon type="ios-help-circle" color="rgb(159, 166, 193)" size="16" />
                </p>
              </Tooltip>
            </div>
            <div class="card-amount">￥{{ formatAmount(totalStats.pend_settle_money) }}</div>
          </div>

          <div class="stats-card">
            <div class="card-header">
              <span class="card-title">结算金额(元)</span>
              <Tooltip max-width="300" theme="dark" placement="top" class="custom-tooltip ml6">
                <div slot="content">
                  <!-- <p>结算金额=</p> -->
                  <div>1. 已确认员工的业绩提成；</div>
                  <div>2. 按照订单实际收款金额计算</div>
                </div>
                <p class="flex flex-item-center cursor">
                  <Icon type="ios-help-circle" color="rgb(159, 166, 193)" size="16" />
                </p>
              </Tooltip>
            </div>
            <div class="card-amount">￥{{ formatAmount(totalStats.settle_money) }}</div>
          </div>

          <div class="stats-card">
            <div class="card-header">
              <span class="card-title">门店结算业绩</span>
              <Tooltip max-width="300" theme="dark" placement="top" class="custom-tooltip ml6">
                <div slot="content">
                  <!-- <p>门店结算业绩=</p> -->
                  <div>1. 订单已结算的总金额；</div>
                  <div>2. 不包含已收款但未完成的订单</div>
                </div>
                <p class="flex flex-item-center cursor">
                  <Icon type="ios-help-circle" color="rgb(159, 166, 193)" size="16" />
                </p>
              </Tooltip>
            </div>
            <div class="card-amount">￥{{ formatAmount(totalStats.settle_order_money) }}</div>
          </div>
        </div>
      </template>
      <template v-slot:staff_info="{ row }">
        <div class="flex flex-item-align">
          <img
            :src="row.physio_avatar || 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png'"
            class="avatar"
          />

          <div style="text-align: left">
            <div>姓名：{{ row.physio_name }}</div>
            <div>手机号：{{ row.physio_mobile || '-' }}</div>
            <div>身份证号：{{ row.physio_idcard }}</div>
          </div>
        </div>
      </template>
      <template v-slot:mdp_role_name="{ row }">
        {{ row.mdp_role_name || '-' }}
      </template>
      <template v-slot:mdp_level_name="{ row }">
        {{ row.mdp_level_name || '-' }}
      </template>

      <template v-slot:role_name="{ row }">
        <div v-if="row.role_name.length !== 0">
          <Tag v-for="item in row.role_name || []" :key="item.roleid" type="border">{{ item.name }}</Tag>
        </div>
        <div v-else>-</div>
      </template>

      <template v-slot:settle_payment_money="{ row }">
        <a @click="seeMoneyModal(row, 1)">￥{{ row.settle_payment_money || '0' }}</a>
      </template>
      <template v-slot:pend_settle_payment_money="{ row }">
        <a @click="seeMoneyModal(row, 0)">￥{{ row.pend_settle_payment_money || '0' }}</a>
      </template>

      <!-- <template v-slot:commission_payment_money="{ row }">
        <a @click="seeMoneyModal(row)">￥{{ row.commission_payment_money }}</a>
      </template> -->
      <!-- <template slot-scope="{ row }" slot="source_info">
        <div>诊所：{{ row.clinic_name || '-' }}</div>
      </template> -->

      <template slot-scope="{ row }" slot="mdp">
        {{ row.mdp || '-' }}
      </template>
    </standard-table>
    <staff-wallet-modal
      v-model="staffVisible"
      :row="currentRow"
      :currentModalType="currentModalType"
    ></staff-wallet-modal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'libs/util';
import staffWalletModal from './components/staffWalletModal.vue';
import StandardTable from '@/components/StandardTable/index.vue';
import renderHeader from '@/mixins/renderHeader';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  mobile: '',
  wallet_code: '',
  bind_status: '',
  settle_times: '',
  month: '',
};

const pend_pay_tip = `1、已确认员工的业绩提成；
2、按照订单实际收款金额计算`;
const pend_waiting_tip = `1、预确认待结算员工的业绩提成；
2、员工历史时间内总业绩提成，原订单相关服务尚未结束；
3、服务完结后到结算时间自动流转到该员工的结算金额中并确认业绩提成`;
export default {
  name: 'list',
  mixins: [search, renderHeader],
  components: { StandardTable, staffWalletModal },
  data() {
    return {
      apiName: 'getMonthlyAssetsList',
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      queryFormData: {
        ...init_query_form_data,
      },
      tableCols: [
        { title: '员工信息', slot: 'staff_info', align: 'center', width: 280 },
        { title: 'MDP身份', key: 'mdp_role_name', slot: 'mdp_role_name', align: 'center' },
        { title: '职级', key: 'mdp_level_name', slot: 'mdp_level_name', align: 'center' },
        { title: '门店角色', key: 'role_name', slot: 'role_name', align: 'center' },
        { title: '月份', key: 'month', align: 'center' },
        // { title: '按订单金额记账（元）', slot: 'commission_origin_money', align: 'center' },
        // { title: '按实收金额记账（元）', slot: 'commission_payment_money', align: 'center' },
        // { title: '来源信息', slot: 'source_info', align: 'center', width: 200 },
        {
          title: '结算金额(元)',
          renderHeader: (h, params) => this._renderHeader(h, params, pend_pay_tip),
          slot: 'settle_payment_money',
          align: 'center',
        },
        {
          title: '待结算金额(元)',
          renderHeader: (h, params) => this._renderHeader(h, params, pend_waiting_tip),
          slot: 'pend_settle_payment_money',
          align: 'center',
        },
      ],
      statusDesc: [],
      staffVisible: false,
      bindOptionList: [
        { id: '1', desc: '绑定' },
        { id: '2', desc: '未绑定' },
      ],
      currentRow: {},
      currentModalType: '',
      bindVisible: false,
      settle_times: '',
      totalStats: {
        pend_settle_money: 0,
        settle_money: 0,
        settle_order_money: 0,
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);

    // 默认当前月
    const now = new Date();
    const month = now.getFullYear() + '-' + (now.getMonth() + 1 < 10 ? '0' : '') + (now.getMonth() + 1);
    this.queryFormData.month = month;

    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    onMonthChange(val) {
      this.queryFormData.month = val;
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    handlerListData(data) {
      this.totalStats = data.statistics;
    },
    changeComStatus({ status, id }) {
      let params = {
        status: status === 'ON' ? 'OFF' : 'ON',
        id,
      };
      this.$api.changeStatus(params).then(
        () => {
          this.$Message.success(`${params.status === 'ON' ? '启用' : '禁用'}成功`);
          this.submitQueryForm(true);
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    /**
     * 查看金额详情
     * */
    seeMoneyModal(row, settle_status = '') {
      // this.currentRow = row;
      // this.currentModalType = modalType;
      // this.staffVisible = true;
      const employeeData = encodeURIComponent(JSON.stringify(row));
      this.$router.push({
        path: '/property/assets/accounting',
        query: {
          employeeData,
          settle_status,
        },
      });
    },
    showBindModal(row) {
      this.bindVisible = true;
      this.currentRow = row;
    },
    /**
     * 格式化金额：千分位 + 保留2位小数
     */
    formatAmount(amount) {
      if (!amount && amount !== 0) {
        return '0.00';
      }

      // 确保是数字类型
      const num = parseFloat(amount);
      if (isNaN(num)) {
        return '0.00';
      }

      // 保留2位小数并添加千分位分隔符
      return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.avatar {
  width: 29px;
  height: 29px;
  min-width: 29px;
  min-height: 29px;
  border-radius: 50%;
  margin-right: 10px;
}

::v-deep .ivu-form-item {
  margin-bottom: 16px;

  label {
    vertical-align: middle;
  }
}

.table-wrapper {
  .table-fun {
    //text-align: right;
    padding: 10px 0;
  }
}

.stats-cards-wrapper {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;

  .stats-card {
    flex: 1;
    background: #fff;
    border: 1px solid #e8eaec;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .card-title {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }

    .card-amount {
      font-size: 24px;
      font-weight: 600;
      color: #2d8cf0;
    }
  }
}
</style>
