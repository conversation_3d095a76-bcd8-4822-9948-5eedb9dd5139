/**结算金额 */
const pend_pay_tip = `1、已确认员工的业绩提成；
2、按照订单实际收款金额计算`;

/**当月总业绩 */
const monthly_income_money_tip = `现金收入（诊所当日进账的所有钱总和），即： 订单交易额- 订单交易额里储值支付的部分 + 储值金额。`;

/**当月营收 */
const settle_order_money_tip = `1、订单里的实物商品，订单已完成后，T+7算营收
2、订单里的虚拟商品，核销后，T+1算营收、过期后立即确认收入。
3、即今日营收为 T-7日的实物商品收入和T-1天的虚拟商品收入。
4、仅统计聚合支付的订单`;

/**待结算金额 */
const pend_waiting_tip = `1、预确认待结算员工的业绩提成；
2、员工历史时间内总业绩提成，原订单相关服务尚未结束；
3、服务完结后到结算时间自动流转到该员工的结算金额中并确认业绩提成`;

/**预估记账业绩 */
const jz_estimated_tip = `指在未统计人员的评价系数和职级系数的情况下的预估记账业绩
服务消耗业绩：实收金额 × 产品比例
服务销售业绩：实收金额 × 产品比例×销售比例
实物销售业绩：实收金额 × 产品比例×销售比例
药品销售业绩：实收金额 × 产品比例
问诊费和挂号费业绩：实收金额 × 50%`;

/**实际记账业绩*/
const jz_actual_tip = `指在次月统计处人员的评价系数和职级系数的情况下的计算出记账业绩
服务消耗业绩：实收金额 × 产品比例 ×【（用户评价系数+职级系数）÷2】
服务销售业绩：实收金额 × 产品比例 × 销售比例 ×【（用户评价系数+职级系数）÷2】
实物销售业绩：实收金额  × 产品比例 × 销售比例 × 【（用户评价系数+职级系数）÷2】
药品销售业绩：实收金额  × 产品比例 × 【（用户评价系数+职级系数）÷2】
问诊费和挂号费业绩：实收金额  × 50% × 【（用户评价系数+职级系数）÷2】`;

export {
  pend_pay_tip,
  settle_order_money_tip,
  monthly_income_money_tip,
  pend_waiting_tip,
  jz_estimated_tip,
  jz_actual_tip,
};
