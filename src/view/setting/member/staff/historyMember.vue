<template>
  <Modal
    :value="visible"
    width="80%"
    title="历史员工数据"
    lock-scroll
    :mask-closable="false"
    :before-close="closeModal"
    @on-cancel="closeModal"
    class-name="vertical-center-modal"
  >
    <div class="global-list-box">
      <div class="global-list-search" ref="searchRefs">
        <!-- 搜索条件区域 -->
        <div class="global-list-search-condition">
          <Form id="searchForm" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem label="">
              <Input type="text" v-model="queryFormData.keywords" placeholder="用户名/手机号/账号" clearable />
            </FormItem>
            <FormItem label="">
              <Select v-model="queryFormData.role_id" placeholder="请选择后台角色" clearable>
                <Option value="">全部</Option>
                <Option v-for="(item, index) in roles" :label="item.name" :value="item.id" :key="index">
                  <div class="flex flex-item-between">
                    <span>{{ item.name }}</span>
                    <span class="text-role">{{ item.source === 'SYSTEM' ? '系统内建' : '自定义' }}</span>
                  </div>
                </Option>
              </Select>
            </FormItem>
            <FormItem label="">
              <Select v-model="queryFormData.status" placeholder="请选择状态" clearable>
                <Option value="">全部</Option>
                <Option v-for="(status, statusKey) in statusDesc" :value="statusKey" :key="statusKey"
                  >{{ status.desc }}
                </Option>
              </Select>
            </FormItem>
          </Form>
        </div>
        <!-- 搜索条件功能区域 -->
        <div class="global-list-search-operate">
          <Button class="search-btn" type="primary" @click="onSearch">筛选</Button>
          <span class="list-reset-btn" @click="onResetSearch">
            <svg-icon class="reset-icon" iconClass="btn-clear" />
            <span>清除条件</span>
          </span>
        </div>
      </div>
      <Table
        ref="standardTable"
        class="global-table-style"
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
        :height="$store.state.app.clientHeight - 400"
        :row-class-name="rowClassName"
      >
        <template slot-scope="{ row }" slot="role">
          <div class="flex flex-wrap pt8" v-if="row.role_name.length">
            <div
              class="sys-tag"
              :class="{ 'custom-tag': item.source === 'CUSTOM' }"
              v-for="(item, index) in row.role_name"
              :key="index"
            >
              <Tooltip theme="light" trigger="hover" :content="item.source_text">
                <div>{{ item.name }}</div>
              </Tooltip>
            </div>
          </div>
          <div v-else>-</div>
        </template>
        <template slot-scope="{ row }" slot="weAppRole">
          {{ row.weapp_role_name || '-' }}
        </template>

        <template slot-scope="{ row, index }" slot="sign">
          <div v-if="row.audit_status === '1'">
            <span v-show="!row.sign">未设置</span>
            <img v-show="row.sign" :src="row.sign" style="width: 76px; height: 30px" />
          </div>
          <div v-else>-</div>
        </template>
        <template slot-scope="{ row }" slot="auditStatus">
          <!-- 已认证 -->
          <div class="flex flex-item-align" v-if="row.audit_status === '1'">
            <img
              v-if="row.status === 'OFF'"
              style="width: 14px; height: 14px"
              src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0701/105048_91063.png"
            />
            <img
              v-else
              style="width: 14px; height: 14px"
              src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0626/160353_47936.png"
            />
            <span>{{ row.audit_status_text }}</span>
          </div>
          <!-- 审核中 -->
          <div class="flex flex-item-align" v-else-if="row.audit_status === '2'">
            <img
              v-if="row.status === 'OFF'"
              style="width: 14px; height: 14px"
              src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0701/105857_86928.png"
            />
            <img
              v-else
              style="width: 14px; height: 14px"
              src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0626/161037_99513.png"
            />
            <span>{{ row.audit_status_text }}</span>
          </div>

          <!-- 去认证 -->
          <div v-else>
            -
          </div>
        </template>
        <template slot-scope="{ row }" slot="enable_commission">
          <span>{{ row.enable_commission == '1' ? '参与' : '不参与' }}</span>
        </template>
        <template slot-scope="{ row }" slot="status">
          <span v-if="row.status == 'OFF'" class="text-danger">{{ statusDesc[row.status].desc }}</span>
          <span v-else>{{ statusDesc[row.status].desc }}</span>
        </template>
      </Table>
      <div class="global-list-page" style="margin-left: auto; margin-top: 12px">
        <KPage
          :total="total"
          :page-size.sync="queryFormData.pageSize"
          :current.sync="queryFormData.page"
          :page-size-opts="[10, 20, 50, 80, 100, 200]"
          @on-change="onPageChange"
        />
      </div>
    </div>
    <div class="footer" slot="footer">
      <Button @click="closeModal">关闭</Button>
    </div>
  </Modal>
</template>

<script>
import io from '@/libs/io';
import { cloneDeep  } from 'lodash-es';

const init_page_form = {
  keywords: '',
  status: '',
  is_history: '1',
  role_id: '',
  pageSize: 20,
  page: 1,
};
export default {
  name: 'staffHistoryMember',
  props: {
    visible: Boolean,
  },
  data() {
    return {
      roles: [],
      statusDesc: [],
      tableCols: [
        { title: '姓名', key: 'name', minWidth: 40 },
        { title: '手机号', key: 'temp_mobile', minWidth: 40 },
        { title: '账号', key: 'temp_account', minWidth: 40 },
        { title: '后台角色', slot: 'role', minWidth: 80 },
        { title: '资质认证状态', slot: 'auditStatus', minWidth: 40 },
        { title: '小程序权限', slot: 'weAppRole', minWidth: 40 },
        { title: '电子签名', slot: 'sign', minWidth: 40 },
        { title: '服务分佣', slot: 'enable_commission', minWidth: 40 },
        { title: '状态', slot: 'status', width: 60 },
      ],
      tableLoading: false,
      list: [],
      total: 0,
      queryFormData: cloneDeep(init_page_form),
    };
  },
  watch: {
    visible: {
      handler(flag) {
        if (flag) {
          this.queryFormData = cloneDeep(init_page_form);
          this.getLists();
        }
      },
    },
  },
  methods: {
    closeModal() {
      this.$emit('update:visible', false);
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getLists();
    },
    onResetSearch() {
      this.queryFormData = cloneDeep(init_page_form);
      this.getLists();
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.getLists();
    },
    getLists: function () {
      console.log(this.queryFormData, 'this.queryFormData')
      this.tableLoading = true;
      io.get('clinic/member.list', { data: this.queryFormData })
        .then(data => {
          this.list = data?.members || [];
          this.total = +data?.total || 0;
          this.statusDesc = data?.statusDesc || [];
          this.roles = data?.all_roles || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 给表格行设置样式
    rowClassName(row) {
      return row.status === 'OFF' ? 'del-cell' : '';
    },
  },
};
</script>

<style scoped lang="less">
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}
</style>
