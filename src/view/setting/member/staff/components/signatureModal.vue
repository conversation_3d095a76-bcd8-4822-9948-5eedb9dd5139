<template>
  <Modal
    ref="eSignatureModal"
    :value="value"
    :mask-closable="false"
    width="800px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div style="margin: 20px 0">根据各地卫健相关政策，诊所医生开方可使用电子签名签署</div>
      <img class="echo-img" v-if="echo_autograph" :src="echo_autograph" :draggable="false" />
      <div style="border: 1px dashed #ccc">
        <drawing-board class="esignature" ref="esign" @drawStart="drawStart" />
      </div>
    </div>
    <div slot="footer">
      <Button @click="handleReset" :disabled="!is_draw_start && echo_autograph == ''">重 签</Button>
      <Button type="primary" @click="handleGenerate" :disabled="echo_autograph != ''" :loading="submitLoading"
        >确 认</Button
      >
    </div>
  </Modal>
</template>

<script>
import drawingBoard from '@/components/drawing-board/index.vue';
import { cloneDeep } from 'lodash-es';

export default {
  name: 'eSignatureModal',
  mixins: [],
  components: { drawingBoard },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    echoImg: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      submitLoading: false,
      // 电子签名
      lineWidth: 6,
      lineColor: '#000000',
      bgColor: 'transparent',
      resultImg: '',
      isCrop: false,
      echo_autograph: '',
      is_draw_start: false,
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    drawStart() {
      this.is_draw_start = true;
    },
    changeVisible(val) {
      if (val) {
        if (this.echoImg) {
          this.echo_autograph = cloneDeep(this.echoImg);
        }
      } else {
        this.clearData();
      }
    },
    clearData() {
      this.is_draw_start = false;
      this.echo_autograph = '';
      this.handleReset();
    },
    cancel() {
      this.$emit('input', false);
    },
    submitForm() {},
    // 电子签名 - 重签
    handleReset() {
      if (this.echo_autograph) {
        this.echo_autograph = '';
      }
      this.is_draw_start = false;
      this.$refs.esign.reset();
    },
    // 电子签名 - 生成图片
    handleGenerate() {
      this.$refs.esign
        .generate()
        .then(res => {
          let file = this.base64ToFile(res, '电子签名.png');
          this.$emit('getESignature', file);
          this.cancel();
        })
        .catch(err => {
          this.$emit('getESignature', '');
          this.cancel();
        });
    },

    base64ToFile(base64, filename) {
      const arr = base64.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
.content {
  position: relative;
  .echo-img {
    position: absolute;
    left: 0px;
    bottom: 0px;
  }
}
</style>
