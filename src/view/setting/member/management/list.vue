<template>
  <div class="global-list-box">
    <div class="flex flex-item-between" style="border-bottom: 1px solid #efefef">
      <!--搜索区域-->
      <div class="global-list-search" ref="searchRefs" :style="{ height: expandStatus === 'expand' ? 'auto' : '52px' }">
        <!--搜索条件区域-->
        <div class="global-list-search-condition">
          <Form id="searchForm" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem label="">
              <Select
                v-model="queryFormData.physio_id"
                :placeholder="`请选择${is_rst ? getPhysioName + '/医生' : getPhysioName + '/医生'}`"
                clearable
              >
                <Option v-for="(item, index) in physical_list" :value="item.id" :key="index" :label="item.name">
                  <div class="flex flex-item-between">
                    <div class="mr10">{{ item.name }}</div>
                    <div style="color: #999">
                      {{ item.type === 'DOCTOR' ? '医生' : item.type === 'PHYSIO' && is_rst ? '理疗师' : '理疗师' }}
                    </div>
                  </div>
                </Option>
              </Select>
            </FormItem>
          </Form>
        </div>
        <!--搜索条件功能区域-->
        <div class="global-list-search-operate">
          <Button class="search-btn" type="primary" @click="onSearch">筛选</Button>
          <span class="list-reset-btn" @click="onResetSearch"
            ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
          >
          <div class="expand-or-collapse" v-if="isShowExpandDom" @click="expandCollapseEvent">
            <span>{{ expandStatus === 'expand' ? '收起筛选' : '更多筛选' }}</span>
            <img v-if="expandStatus === 'collapse'" src="@/assets/image/list/expand.png" />
            <img v-else src="@/assets/image/list/collapse.png" />
          </div>
        </div>
      </div>
      <!--切换区域-->
      <div>
        <RadioGroup v-model="manageMode" type="button" button-style="solid" @on-change="changeMode">
          <Radio label="list">列表</Radio>
          <Radio label="calendar">日历</Radio>
        </RadioGroup>
      </div>
    </div>

    <!-- 列表区域 -->
    <div class="global-list-table">
      <Spin
        class="flex flex-item-center"
        v-if="tableLoading"
        :style="{ height: $store.state.app.clientHeight - _getOtherHeight + 'px' }"
      ></Spin>
      <div v-else class="calendar">
        <!--  日历表头 -->
        <div class="change-date-header">
          <div class="btn-group" :style="{ marginTop: manageMode === 'list' ? '20px' : '' }">
            <Form v-show="manageMode === 'list'" @submit.native.prevent>
              <FormItem style="margin-bottom: 0; margin-right: 10px">
                <DatePicker
                  type="date"
                  :value="startDate"
                  placeholder="开始日期"
                  style="width: 120px"
                  :clearable="true"
                  :options="startDateOptions"
                  @on-change="handleStartDateChange"
                  @on-clear="handleStartDateClear"
                ></DatePicker>
                <span style="margin: 0 5px">至</span>
                <DatePicker
                  type="date"
                  :value="endDate"
                  placeholder="结束日期"
                  style="width: 120px"
                  :clearable="true"
                  :options="endDateOptions"
                  @on-change="handleEndDateChange"
                  @on-clear="handleEndDateClear"
                ></DatePicker>
              </FormItem>
            </Form>

            <div v-if="!isRepresent" class="flex">
              <Button type="primary" @click="batchManagement">批量排班</Button>
              <Button type="default" class="ml10" @click="clearManagement">清除排班</Button>
              <Button type="default" class="ml10" @click="importManagement">导入排班</Button>
            </div>
          </div>
          <div class="change-date-box" v-show="manageMode === 'calendar'">
            <Icon type="ios-arrow-back" class="arrow arrow-left" @click="updateMonth(-1)" />
            <div>{{ format_currentDate }}</div>
            <Icon type="ios-arrow-forward" class="arrow arrow-right" @click="updateMonth(1)" />
          </div>
        </div>

        <!-- 日历主体 -->
        <table border="1px" cellspacing="0" class="calendar-table" v-show="manageMode === 'calendar'">
          <thead class="calendar-thead">
            <tr class="calendar-tr">
              <th class="calendar-th calendar-left-check" v-if="!isRepresent">
                <div class="cell-content">
                  <Checkbox v-model="isChecked"></Checkbox>
                </div>
              </th>
              <th
                class="calendar-th"
                v-for="(header_item, header_index) in calendar_title"
                :key="'header' + header_index"
              >
                <div class="flex cell-content">
                  <Checkbox
                    v-if="!isRepresent"
                    v-model="header_item.isChecked"
                    @on-change="colChecked($event, header_index)"
                  ></Checkbox>
                  <div>{{ header_item.title }}</div>
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="calendar-tbody">
            <tr class="calendar-tr" v-for="(item, index) in calendar_list" :key="'item' + index">
              <td class="calendar-td calendar-left-check cell-content" v-if="!isRepresent">
                <div class="cell-content">
                  <Checkbox v-model="item.isChecked" @on-change="rowChecked($event, index)"></Checkbox>
                </div>
              </td>
              <td
                class="calendar-td calendar-td-cell"
                v-for="(chunk_item, chunk_index) in item.list"
                :key="'chunk_item' + chunk_index"
              >
                <Poptip
                  :ref="`physioRef${index}${chunk_index}`"
                  width="400"
                  trigger="hover"
                  placement="top-start"
                  title=""
                  :disabled="
                    !chunk_item.current ||
                    !chunk_item.info?.length ||
                    compareTimes(chunk_item.full_date, today_date) ||
                    !isHasChunkItemInfo(chunk_item.info)
                  "
                  content="content"
                  @on-popper-show="physioShow"
                  @on-popper-hide="physioHide"
                >
                  <div slot="title" class="poptip-title">{{ format_currentDate }}{{ chunk_item.date }}日</div>
                  <div slot="content" class="poptip-content">
                    <div
                      class="schedule-box"
                      v-for="(schedule_item, schedule_index) in chunk_item.info"
                      :key="'schedule' + schedule_index"
                      v-show="schedule_item?.physios?.length"
                    >
                      <div class="schedule-header">
                        <div class="schedule-header-vertical-line"></div>
                        <div class="schedule-desc">{{ schedule_item.range_name }}</div>
                        <Tooltip :content="schedule_item.range_time_text" max-width="400" :disabled="!physioVisible">
                          <div class="schedule-time ecs">{{ schedule_item.range_time_text }}</div>
                        </Tooltip>
                      </div>

                      <div class="schedule-name-box">
                        <div
                          class="schedule-name-item"
                          :class="{ 'rst-schedule-item': is_rst && name_item.type === 'PHYSIO' }"
                          v-for="(name_item, name_index) in schedule_item.physios"
                          :key="'name' + name_index"
                        >
                          <div
                            class="name"
                            :class="{ cursor: name_item?.name.length > 7 }"
                            @click="
                              is_rst && name_item.type === 'PHYSIO'
                                ? updatePhysio(
                                    {
                                      range_id: schedule_item.range_id,
                                      physio_id: name_item.id,
                                      date: chunk_item.full_date,
                                      name: name_item.name,
                                    },
                                    index,
                                    chunk_index
                                  )
                                : ''
                            "
                          >
                            <Tooltip
                              :content="name_item?.name"
                              max-width="400"
                              :disabled="name_item?.name.length < 8 || !physioVisible"
                            >
                              <div class="schedule-time ecs">{{ name_item?.name }}</div>
                            </Tooltip>
                          </div>
                          <div class="role">{{ name_item.type === 'DOCTOR' ? '医生' : getPhysioName }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="flex day-cell cell-content" @click="addDaySchedule(chunk_item)">
                    <div
                      class="day-text"
                      :class="{
                        'day-text-disabled': !chunk_item.current || compareTimes(chunk_item.full_date, today_date),
                      }"
                    >
                      {{ chunk_item.date }}
                    </div>
                    <template v-if="chunk_item.current && !compareTimes(chunk_item.full_date, today_date)">
                      <div class="cell-content-header" @click.stop>
                        <Checkbox
                          v-if="!isRepresent"
                          v-model="chunk_item.isChecked"
                          @on-change="dayCheckedChange($event, index, chunk_index, chunk_item)"
                        ></Checkbox>
                        <!--    处理样式异常      -->
                        <div v-else></div>
                        <div v-if="getStaffInfo(chunk_item.staff_info).length">
                          {{ getStaffInfo(chunk_item.staff_info).length }}人
                        </div>
                      </div>
                      <div class="cell-name-list ecs" v-if="isHasStaff(chunk_item.staff_info)">
                        {{ getStaffInfo(chunk_item.staff_info)?.join('、') }}
                      </div>
                      <div class="cell-tip-text" v-if="!isHasStaff(chunk_item.staff_info)">
                        点击{{ !isRepresent ? '添加' : '查看' }}人员排班
                      </div>
                    </template>
                  </div>
                </Poptip>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 列表主体 -->
        <Table
          class="global-table-style"
          :columns="tableCols"
          :data="listData"
          :height="$store.state.app.clientHeight - _getOtherHeight - 70"
          :loading="listLoading"
          v-show="manageMode === 'list' && listTimeRange.length > 0"
          style="margin-top: 40px"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template slot-scope="{ row, index }" slot="name"> {{ row.name }} {{ row.role_name }} </template>

          <template v-for="(item, date_index) in 31" slot-scope="{ row, index }" :slot="`range_name${date_index}`">
            <div
              :key="`range_name${date_index}`"
              :style="{ color: row.list[date_index]?.range_id === '0' ? '#ccc' : '' }"
            >
              {{ row.list[date_index]?.range_name }}
            </div>
          </template>

          <template slot-scope="{ row, index }" slot="action">
            <a @click="editSchedule(row)">编辑排班</a>
          </template>
        </Table>

        <div
          v-show="listTimeRange.length === 0"
          class="empty"
          :style="{ height: $store.state.app.clientHeight - _getOtherHeight - 70 + 'px' }"
        >
          请选择排班日期
        </div>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="global-list-page" ref="pageRefs"></div>

    <!-- 添加排班员工 -->
    <add-schedule
      v-model="addScheduleVisible"
      :checked_date="checked_date"
      :current_date="current_date"
      :current_ranges="current_ranges"
      :ranges="ranges"
      :isRepresent="isRepresent"
      :title="!isRepresent ? '添加排班员工' : '排班信息'"
      @close="scheduleClose"
      @success="addScheduleSuccess"
    ></add-schedule>

    <!-- 添加排班员工 -->
    <update-physio-modal
      v-model="updatePhysioVisible"
      :row="updateRow"
      :isRepresent="isRepresent"
    ></update-physio-modal>
    <!-- 导入排班   -->
    <import-schedule
      :visible.sync="importVisible"
      :excel-key-map="excelKeyMap"
      :import-api-name="importApiName"
      :startDate="actualStartDate"
      :endDate="endDate"
      :is_rst="is_rst"
      @refresh="handleImportSuccess"
    ></import-schedule>
    <!-- 编辑排班 -->
    <list-edit-schedule
      :visible.sync="editScheduleVisible"
      :staff="currentEditStaff"
      :pageStartDate="startDate"
      :startDate="actualStartDate"
      :endDate="endDate"
      @refresh="handleEditSuccess"
    ></list-edit-schedule>

    <!-- 批量排班 -->
    <list-batch-schedule
      :visible.sync="batchScheduleVisible"
      :selected-staff="selected_items"
      :startDate="actualStartDate"
      :endDate="endDate"
      @on-success="handleBatchSuccess"
    ></list-batch-schedule>
  </div>
</template>

<script>
import { getPhysioName, isRstClinic } from '@/libs/runtime';
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import { cloneDeep } from 'lodash-es';
import listCalc from '@/mixins/listCalc';
import calendar from './mixins/calendar';
import addSchedule from './components/addSchedule.vue';
import updatePhysioModal from './components/updatePhysioModal.vue';
import importSchedule from './components/importSchedule.vue';
import listEditSchedule from './components/listEditSchedule.vue';
import listBatchSchedule from './components/listBatchSchedule.vue';
let init_query_form_data = {
  physio_id: '',
  r: '',
};

export default {
  name: 'list',
  components: { addSchedule, updatePhysioModal, importSchedule, listEditSchedule, listBatchSchedule },
  mixins: [listCalc, calendar],

  data() {
    return {
      reserve_height: 50,
      today_date: this.$moment().format('YYYY-MM-DD'),
      currentDate: new Date(), // 当前日期
      calendar_title: [
        { title: '周日', isChecked: false },
        { title: '周一', isChecked: false },
        { title: '周二', isChecked: false },
        { title: '周三', isChecked: false },
        { title: '周四', isChecked: false },
        { title: '周五', isChecked: false },
        { title: '周六', isChecked: false },
      ],
      isChecked: false, // 控制所有的勾选
      new_currentDate: '',
      calendar_list: [], // 计算出来的日历数据
      infoVisible: false,
      addScheduleVisible: false, // 添加排班弹窗

      statusDesc: [],
      queryFormData: { ...init_query_form_data },

      list: [], // 当月所有的预约信息
      current_date: '', // 当前点击的日期
      tableLoading: false,
      ranges: [], // 所有班次
      current_ranges: [], // 单天班次
      physical_list: [],
      updatePhysioVisible: false,
      updateRow: {},
      physioVisible: false,
      isRepresent: false, // 是否由合伙人代理
      manageMode: 'calendar',
      importVisible: false,
      excelKeyMap: {
        序号: { key: 'number', required: false },
        姓名: { key: 'name', required: false },
        角色: { key: 'role', required: false },
      },
      importApiName: 'importSchedule',
      selected_items: {},
      editScheduleVisible: false,
      currentEditStaff: null,
      batchScheduleVisible: false,
      listTimeRange: [],
      startDate: null,
      endDate: null,
      startDateOptions: {
        disabledDate: date => {
          if (!this.endDate) return date && date.valueOf() < Date.now() - 86400000;
          const end = this.$moment(this.endDate);
          const current = this.$moment(date);
          const days = end.diff(current, 'days');
          return date.valueOf() < Date.now() - 86400000 || days < 0;
        },
      },
      endDateOptions: {
        disabledDate: date => {
          if (!this.startDate) return date && date.valueOf() < Date.now() - 86400000;
          const start = this.$moment(this.startDate);
          const current = this.$moment(date);
          const days = current.diff(start, 'days');
          // return days < 0 || days > 30;
          return date.valueOf() < Date.now() - 86400000 || days > 30;
        },
      },
      tableCols: [],
      listData: [],
      listLoading: false,
      dateList: [], // 后端提供的日期数据
    };
  },
  computed: {
    // 全部排班下是否存在最少一个班次有理疗师的
    isHasChunkItemInfo() {
      return list => {
        return list?.some(item => {
          return item.physios.length > 0;
        });
      };
    },
    getPhysioName() {
      return getPhysioName();
    },
    is_rst() {
      return isRstClinic();
    },
    isHasStaff() {
      return (info = []) => {
        let flag = false;
        info?.some(item => {
          if (item.physios?.length) {
            flag = true;
            return true;
          }
        });
        return flag;
      };
    },
    getStaffInfo() {
      return list => {
        let staff_name_list = [];
        list?.forEach(item => {
          item?.physios?.forEach(c_item => {
            if (c_item?.name) {
              staff_name_list.push(c_item?.name);
            }
          });
        });
        return staff_name_list;
      };
    },
    checked_date() {
      let list = [];
      this.calendar_list.forEach(item => {
        item?.list?.forEach(c_item => {
          if (c_item.isChecked) {
            list.push(c_item.full_date);
          }
        });
      });
      return list;
    },
    actualStartDate() {
      return new Date(this.startDate) < new Date() ? this.$moment().format('YYYY-MM-DD') : this.startDate;
    },
  },

  watch: {},

  created() {
    if (this.is_rst) {
      this.getR2ScheduleBatchConfig();
    } else {
      this.getDefaultTimeRange();
    }
    this.init();
  },

  methods: {
    getR2ScheduleBatchConfig() {
      this.$api.getR2ScheduleBatchConfig().then(res => {
        this.isRepresent = res?.is_reserve_union_range === '1';
        this.getDefaultTimeRange();
      });
    },
    physioShow() {
      this.physioVisible = true;
    },
    physioHide(val) {
      this.physioVisible = false;
    },
    updatePhysio(item, index, chunk_index) {
      this.updateRow = item;
      this.$refs[`physioRef${index}${chunk_index}`][0].visible = false;
      this.updatePhysioVisible = true;
    },
    init(reload = true) {
      this.getPhysicalTMemberlist();
      if (reload) {
        this.getCurrentDate();
      }
      this.calc_calendar_list();
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      this.submitQueryForm(true);
    },
    addScheduleSuccess() {
      this.init(false);
      this.unCheckedAll();
    },
    // 批量排班
    batchManagement() {
      // 日历模式
      if (this.manageMode === 'calendar') {
        const compareTime = this.$moment(this.new_currentDate, 'YYYY-MM');
        const currentTime = this.$moment().startOf('month');
        if (compareTime.isBefore(currentTime, 'month')) {
          this.$Message.error('不可以编辑过去的时间');
          return;
        }
        if (!this.checked_date.length) {
          this.$Message.error('请先选择日期');
          return;
        }
        if (this.checked_date.length > 1) {
          this.current_ranges = [];
        } else if (this.checked_date.length === 1) {
          this.current_ranges = this.list[this.checked_date[0]];
        }
        this.addScheduleVisible = true;
      } else if (this.manageMode === 'list') {
        if (S.isEmptyObject(this.selected_items)) {
          this.$Message.error('请选择至少一名员工');
          return;
        }
        this.batchScheduleVisible = true;
      }
    },
    // 清除排班 todo
    clearManagement() {
      // 日历模式
      if (this.manageMode === 'calendar') {
        if (!this.checked_date.length) {
          this.$Message.error('请先选择日期');
          return;
        }
        this.$Modal.confirm({
          title: '确认批量清除排班？',
          content: '',
          loading: true,
          okText: '确认清除',
          onOk: () => {
            this.getR2ScheduleBatchclean();
          },
        });
      } else if (this.manageMode === 'list') {
        if (S.isEmptyObject(this.selected_items)) {
          this.$Message.error('请选择至少一名员工');
          return;
        }
        this.$Modal.confirm({
          title: '确认批量清除排班？',
          content: '',
          loading: true,
          okText: '确认清除',
          onOk: () => {
            this.getR2ScheduleBatchclean();
          },
        });
      }
    },

    importManagement() {
      this.importVisible = true;
    },
    // 清除排班api
    getR2ScheduleBatchclean() {
      let params = {
        date_list: this.manageMode === 'list' ? this.dateList.map(item => item.ymd) : this.checked_date,
        physio_ids: this.manageMode === 'list' ? Object.keys(this.selected_items) : [],
      };
      this.$api
        .getR2ScheduleBatchclean(params)
        .then(res => {
          this.$Message.success('清除成功');
          this.init();
          this.unCheckedAll();
          this.getScheduleListByMember();
        })
        .finally(() => this.$Modal.remove());
    },
    scheduleClose() {
      this.current_date = '';
    },
    onSearch: function () {
      if (this.manageMode === 'calendar') {
        this.submitQueryForm();
      } else if (this.manageMode === 'list') {
        this.getScheduleListByMember();
      }
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      if (this.manageMode === 'calendar') {
        this.submitQueryForm();
      } else if (this.manageMode === 'list') {
        this.getScheduleListByMember();
      }
    },

    addDaySchedule(item) {
      // 非当前月的日期
      if (!item.current) {
        if (this.dateComparison(item.full_date)) {
          this.updateMonth(-1);
        } else {
          this.updateMonth(1);
        }
      } else {
        if (this.dateIsExpire(item.full_date)) {
          this.$Message.error('不可以编辑过去的时间');
          return;
        }
        this.current_date = item.full_date;
        if (this.list[item.full_date]) {
          this.current_ranges = this.list[item.full_date];
        } else {
          this.current_ranges = this.ranges;
        }
        this.addScheduleVisible = true;
      }
    },

    // 获取排班列表
    getsList() {
      this.tableLoading = true;
      let params = {
        st: this.$moment(this.new_currentDate, 'YYYY-MM').startOf('month').format('YYYY-MM-DD'),
        et: this.$moment(this.new_currentDate, 'YYYY-MM').endOf('month').format('YYYY-MM-DD'),
      };
      this.$api
        .getR2ScheduleList(params)
        .then(res => {
          this.list = res.list;
          this.ranges = res.ranges;
          this.handlerManagementData(res.list || {});
          // 筛选逻辑
          this.filterPhysicalShow();
        })
        .finally(() => (this.tableLoading = false));
    },
    filterPhysicalShow() {
      if (this.queryFormData.physio_id) {
        this.calendar_list.forEach((item, index) => {
          item.list.forEach((c_item, c_index) => {
            c_item?.staff_info?.forEach((s_item, s_index) => {
              let list = [];
              s_item?.physios.forEach((p_item, p_index) => {
                if (p_item.id === this.queryFormData.physio_id) {
                  list.push(p_item);
                }
              });
              this.$set(this.calendar_list[index].list[c_index].staff_info[s_index], 'physios', list);
            });
          });
        });
      }
    },
    handlerManagementData(obj) {
      this.calendar_list = [];
      this.calc_calendar_list();
      this.calendar_list.forEach((item, index) => {
        item.list.forEach((c_item, c_index) => {
          for (const key in obj) {
            if (c_item.full_date === key) {
              this.$set(this.calendar_list[index]?.list[c_index], 'info', obj[key]);
              this.$set(this.calendar_list[index]?.list[c_index], 'staff_info', cloneDeep(obj[key]));
            }
          }
        });
      });
    },

    // 获取员工（理疗师+医生）
    getPhysicalTMemberlist() {
      let params = {
        page: 1,
        pageSize: 1000,
        status: 'ON',
      };
      this.$api.getPhysicalTMemberlist(params).then(res => {
        let physicalList = [];
        let doctorList = [];
        physicalList = res.physio_list || [];
        doctorList = res.doctor_list || [];
        this.physical_list = [...physicalList, ...doctorList];
      });
    },

    getPhysicalTList() {
      let params = {
        page: 1,
        page_size: 2000,
        status: 'ON',
      };
      this.$api.getPhysicalTList(params).then(res => {
        this.physical_list = res.list.filter(item => item.status === 'ON');
      });
    },

    submitQueryForm(replace) {
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      let searchObj = {};
      for (const searchKey in this.queryFormData) {
        if (this.queryFormData[searchKey] || this.queryFormData[searchKey] === 0) {
          searchObj[searchKey] = this.queryFormData[searchKey];
        }
      }
      if (replace) {
        this.$router.replace({ query: searchObj });
      } else {
        this.$router.push({ query: searchObj });
      }
      this.getsList();
    },

    getDefaultTimeRange() {
      const today = this.$moment();
      const startOfWeek = today.startOf('week').format('YYYY-MM-DD');
      const endOfWeek = today.endOf('week').format('YYYY-MM-DD');
      this.startDate = startOfWeek;
      this.endDate = endOfWeek;
      this.listTimeRange = [startOfWeek, endOfWeek];
      this.updateTableCols();
      setTimeout(() => {
        this._getSearchBoxHeight();
      }, 10);
    },
    updateTableCols() {
      if (!this.listTimeRange || !this.listTimeRange.length) return;

      const startDate = this.$moment(this.listTimeRange[0]);
      const endDate = this.$moment(this.listTimeRange[1]);

      // 生成日期列
      const dateCols = this.generateDateCols(startDate, endDate);

      // 生成所有可能的列配置
      const allCols = [
        { type: 'selection', width: 60, align: 'center', show: !this.isRepresent },
        { title: '员工信息', slot: 'name', align: 'center', width: '120', show: true },
        ...dateCols.map(col => ({ ...col, show: true })),
        { title: '操作', slot: 'action', align: 'center', width: '100', fixed: 'right', show: !this.isRepresent },
      ];

      // 过滤显示的列
      this.tableCols = allCols.filter(col => col.show).map(({ show, ...col }) => col);
      this.getScheduleListByMember();
    },

    resetTableCols() {
      const allCols = [
        { type: 'selection', width: 60, align: 'center', show: !this.isRepresent },
        { title: '员工信息', slot: 'name', align: 'center', width: '120', show: true },
        { title: '操作', slot: 'action', align: 'center', width: '100', fixed: 'right', show: !this.isRepresent },
      ];

      this.tableCols = allCols.filter(col => col.show).map(({ show, ...col }) => col);
    },

    generateDateCols(startDate, endDate) {
      const dateCols = [];
      let currentDate = startDate.clone();
      let dateIndex = 0;

      while (currentDate.isSameOrBefore(endDate)) {
        const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][currentDate.day()];
        let flag = new Date(currentDate) < new Date() - 86400000;
        dateCols.push({
          title: `${currentDate.format('MM-DD')} ${weekDay}`,
          slot: `range_name${dateIndex}`,
          align: 'center',
          minWidth: 120,
          className: flag ? 'disabled-column' : '',
        });
        currentDate.add(1, 'day');
        dateIndex++;
      }

      return dateCols;
    },

    changeMode(val) {
      if (val === 'calendar') {
        this.submitQueryForm();
      } else if (val === 'list') {
        this.getDefaultTimeRange();
      }
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.physio_id, row);
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        this.$set(this.selected_items, item.physio_id, item);
      });
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.physio_id);
    },

    onSelectAllCancel: function (selection) {
      for (let k in this.listData) {
        this.$delete(this.selected_items, this.listData[k].physio_id);
      }
    },
    handleStartDateChange(date) {
      if (!date) {
        this.endDate = null;
        this.listTimeRange = [];
        this.resetTableCols();
        return;
      } else {
        this.startDate = this.$moment(date).format('YYYY-MM-DD');
      }
      this.updateListTimeRange();
    },
    handleEndDateChange(date) {
      if (!date) {
        this.startDate = null;
        this.listTimeRange = [];
        this.selected_items = {};
        this.resetTableCols();
        return;
      } else {
        this.endDate = this.$moment(date).format('YYYY-MM-DD');
      }
      this.updateListTimeRange();
    },
    handleStartDateClear() {
      this.startDate = null;
      this.endDate = null;
      this.listTimeRange = [];
      this.selected_items = {};
      this.resetTableCols();
    },
    handleEndDateClear() {
      this.startDate = null;
      this.endDate = null;
      this.listTimeRange = [];
      this.selected_items = {};
      this.resetTableCols();
    },
    updateListTimeRange() {
      if (this.startDate && this.endDate) {
        this.listTimeRange = [this.startDate, this.endDate];
        this.updateTableCols();
      }
    },
    editSchedule(row) {
      if (!this.listTimeRange.length) {
        this.$Message.error('请先选择排班日期范围');
        return;
      }
      this.currentEditStaff = row;
      this.editScheduleVisible = true;
    },
    handleEditSuccess() {
      this.getScheduleListByMember();
    },
    handleBatchSuccess() {
      this.getScheduleListByMember();
    },
    handleImportSuccess() {
      this.submitQueryForm();
      this.getScheduleListByMember();
    },
    getScheduleListByMember() {
      if (!this.startDate || !this.endDate) {
        this.$Message.error('请先选择排班日期');
        return;
      }
      this.listLoading = true;
      let params = {
        st: this.startDate,
        et: this.endDate,
        physio_id: this.queryFormData.physio_id,
      };
      this.$api
        .getScheduleListByMember(params)
        .then(res => {
          this.listData = res.list;
          this.dateList = res.date_list.filter(item => new Date(item.ymd) > new Date() - 86400000);
          this.selected_items = {};
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => (this.listLoading = false));
    },
  },
};
</script>

<style lang="less" scoped>
.calendar {
  margin-top: 20px;

  .change-date-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .btn-group {
      display: flex;
      position: absolute;
      left: 0px;
    }

    .change-date-box {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 25px;

      .arrow {
        cursor: pointer;

        &:hover {
          color: #8557fa;
          transform: scale(1.5);
        }
      }

      .arrow-left {
        margin-right: 20px;
      }

      .arrow-right {
        margin-left: 20px;
      }
    }
  }

  .calendar-table {
    width: 100%;
    margin-top: 20px;

    .calendar-thead {
      width: 100%;

      .calendar-th {
        background: #f7f8fa;
        display: flex;
        align-items: center;
        min-height: 60px !important;
        font-weight: normal;
      }
    }

    .calendar-tr {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .calendar-td {
      &:nth-child(8n) {
        //border-right: 1px solid #eee;
      }
    }

    // 日期单元格
    .day-cell {
      width: 100%;
      position: relative;
      cursor: pointer;

      &:hover {
        background: rgba(117, 70, 243, 0.1);
      }

      .day-text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #8557fa;
        font-size: 40px;
        opacity: 0.2;
      }

      .day-text-disabled {
        color: #ccc;
      }

      .cell-name-list {
        width: 100%;
        position: absolute;
        height: 24px;
        bottom: -1px;
        left: 1px;
        box-sizing: border-box;
        background: #8557fa;
        color: #fff;
        padding: 0 8px;
        line-height: 24px;
        z-index: 2;
        white-space: normal !important;
      }

      .cell-tip-text {
        position: absolute;
        left: 8px;
        bottom: 7px;
        color: #8557fa;
      }
    }

    // 单个单元格
    .calendar-td,
    .calendar-th {
      height: 100%;
      flex: 1;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: 12px;
      //border-top: 1px solid #eee;
      //border-left: 1px solid #eee;
      background-color: #f9f9f9;
      min-height: 85px;
      display: flex;

      .cell-content {
        padding: 10px;
        width: 100%;
        height: 100%;
        text-align: left;

        .cell-content-header {
          height: 15px;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #8557fa;
        }
      }
    }

    .calendar-left-check {
      flex: unset;
      width: 5% !important;
    }
  }
}

::v-deep .ivu-poptip {
  width: 100%;

  .ivu-poptip-rel {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="less" scoped>
.poptip-title {
  font-size: 14px;
}

.poptip-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;

  .schedule-box {
    .schedule-header {
      display: flex;
      align-items: center;

      .schedule-header-vertical-line {
        width: 4px;
        height: 20px;
        border-radius: 10px;
        margin-right: 6px;
        background: #8557fa;
      }

      .schedule-desc {
        color: #333;
        font-size: 14px;
      }

      .schedule-time {
        cursor: pointer;
        color: #999999;
        margin-left: 4px;
        margin-top: 6px;
      }
    }

    .schedule-name-box {
      margin-top: 10px;
      margin-bottom: 10px;
      padding-left: 10px;
      display: flex;
      flex-wrap: wrap;

      .rst-schedule-item {
        &:hover {
          .name {
            color: #155bd4 !important;
            cursor: pointer;
          }
        }
      }

      .schedule-name-item {
        font-size: 14px;
        width: 49%;
        display: flex;
        margin-bottom: 2px;

        &:nth-child(2n) {
          margin-left: 2%;
        }

        .name {
          color: #333;
          font-size: 14px;
          //white-space: pre-wrap;
          word-break: break-all;
        }

        .role {
          color: #999999;
          margin-left: 6px;
          white-space: pre-wrap;
          word-break: break-all;
          width: 32%;
          font-weight: 300;
        }
      }
    }
  }
}

.update-technician-content {
  padding: 10px 20px;

  .title {
  }

  .desc {
  }
}

table {
  border-color: #fff;
}
.ml10 {
  margin-left: 10px;
}
.global-list-box .global-list-search {
  border-bottom: 0;
}
</style>
<style>
.disabled-column {
  color: #ccc;
}
</style>
