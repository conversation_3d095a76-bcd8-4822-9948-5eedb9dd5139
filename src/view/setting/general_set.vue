<template>
  <div class="clinic-basic">
    <div class="block-header"><span>门店信息</span></div>
    <Form :label-width="130" :rules="storeInfoRules" :model="formData" ref="clinicForm">
      <Row>
        <Col :span="16">
          <FormItem label="门店名称:" prop="name">
            <Input v-model="formData.name" :maxlength="30" show-word-limit />
          </FormItem>
        </Col>
        <Col :span="16">
          <FormItem label="门店简称:" prop="simple_name">
            <Input v-model="formData.simple_name" :maxlength="30" show-word-limit />
            <div class="note">同时应用于小程序名称展示</div>
          </FormItem>
        </Col>
        <Col :span="16">
          <FormItem label="小程序门店状态：" prop="is_hide_mp">
            <RadioGroup v-model="formData.is_hide_mp">
              <Radio label="0">显示</Radio>
              <Radio label="1">隐藏</Radio>
            </RadioGroup>
            <div class="note">是否在榕树堂小程序中显示门店</div>
          </FormItem>
        </Col>
        <Col :span="16">
          <FormItem label="门店地址:" prop="county_code">
            <div class="flex flex-nowarp">
              <div class="address flex-1">
                <el-cascader
                  v-model="selectedAddress"
                  :options="options"
                  clearable
                  placeholder="请选择联系地址"
                  size="small"
                  popper-class="address-com"
                  style="width: 100%"
                  @change="regionChange"
                >
                </el-cascader>
              </div>
              <div class="ml10" style="width: 280px">
                <Input v-model.trim="formData.address_other" placeholder="详细地址" />
              </div>
            </div>
            <div class="note">地址将用于用户到店提货的地址指引，及诊所采购时默认的收货地址</div>
          </FormItem>
        </Col>
        <Col :span="16">
          <FormItem label="客服电话:" prop="clinic_phone_no">
            <Input v-model="formData.clinic_phone_no" />
          </FormItem>
        </Col>
        <Col :span="16">
          <FormItem label="门面图:" prop="facade_images">
            <div class="flex">
              <MaterialPicture v-model="formData.facade_images" :limit="5"></MaterialPicture>

              <Tooltip theme="light" placement="right" :offset="20" max-width="500" transfer-class-name="custom">
                <template slot="content">
                  <img src="@/assets/image/set/shop-template.png" style="width: 300px" alt="" />
                </template>
                <div class="see-mould cursor">
                  <div class="flex flex-item-align">
                    <span>查看示例图</span>
                    <Icon type="md-help-circle" size="18" class="ml6" />
                  </div>
                </div>
              </Tooltip>
            </div>

            <p class="tip">
              建议尺寸：750px*480px，支持 jpg/png 格式，最多5张，单个图片不超过 2 MB。若不上传将展示默认图。
            </p>
          </FormItem>
        </Col>
        <Col :span="16">
          <FormItem label="营业时间:" prop="work_time_desc">
            <Poptip
              title=""
              transfer
              style="width: 100%"
              ref="datePoptip"
              placement="bottom"
              @on-popper-hide="showPoptipHandler"
              popper-class="time-picker-poptip"
            >
              <div slot="content">
                <div class="rsj-checkbox-box flex flex-item-align">
                  <div class="rsj-checkbox-group">
                    <label
                      :class="['rsj-checkbox-wrap', { 'rsj-checkbox-isChecked': date.isChecked }]"
                      v-for="(date, index) in weekDays"
                      :key="index + 'date'"
                    >
                      <span class="rsj-checkbox">
                        <span class="zent-checkbox-inner"></span>
                        <input @click.stop="checkDate(date, index)" />
                      </span>
                      <span>{{ date.desc }}</span>
                    </label>
                  </div>
                  <div class="rsj-checkbox-btn">
                    <Button type="primary" style="margin-left: 8px" @click="selectDateHandler">确定</Button>
                  </div>
                </div>
              </div>
              <Input style="width: 100%" v-model="formData.work_time_desc" readonly placeholder="请选择营业时间" />
            </Poptip>
            <div class="time-box flex">
              <Select
                v-model="formData.work_time.st"
                placeholder="请选择开始营业时间"
                style="flex: 1; margin-right: 12px"
              >
                <Option
                  v-for="(item, index) in getTimePieces('st')"
                  :key="item.time"
                  :label="item.time"
                  :value="item.time"
                ></Option>
              </Select>
              <Select
                v-model="formData.work_time.et"
                :disabled="!formData.work_time.st"
                style="flex: 1"
                placeholder="请选择停止营业时间"
              >
                <Option
                  v-for="(item, index) in getTimePieces('et')"
                  :key="item.time"
                  :label="item.time"
                  :value="item.time"
                ></Option>
              </Select>
            </div>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <div class="fixed-bottom-wrapper">
      <Button type="primary" @click="onSave">保存</Button>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import { CodeToText, regionData, TextToCode } from '@/libs/chinaMap';
import { cloneDeep  } from 'lodash-es';

let init_form_data = {
  name: '',
  simple_name: '',
  clinic_phone_no: '',
  prov_name: '',
  prov_code: '',
  city_name: '',
  city_code: '',
  county_name: '',
  county_code: '',
  address_other: '',
  facade_images: [],
  work_time: {
    st: '',
    et: '',
  },
  work_time_desc: '',
  workdays: [],

  // 小程序门店状态
  is_hide_mp: '',
};

export default {
  name: 'general_set',
  components: {},
  computed: {
    getTimePieces() {
      return type => {
        let timePieces = [];
        if (type === 'st') {
          timePieces = this.halfTimePieces.slice(0, this.halfTimePieces.length - 1);
        } else {
          const index = this.halfTimePieces.findIndex(item => item.time === this.formData.work_time.st);
          timePieces = this.halfTimePieces.slice(index + 1, this.halfTimePieces.length);
        }
        return timePieces;
      };
    },
  },
  data() {
    const validatePhoneCheck = (rule, value, callback) => {
      console.log('-> %c rule, value, callback  === %o', 'font-size: 15px;color: green;', value);
      if (!value) {
        callback(new Error('请输入客服电话'));
      }
      var tel = /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/;
      const regPhone = /^1[3456789]\d{9}$/;
      if (value.length == 11) {
        //手机号码
        console.log(regPhone.test(value));
        if (regPhone.test(value)) {
          callback();
        } else {
          callback(new Error('请输入正确的手机号码'));
        }
      } else if (value.indexOf('-') != -1) {
        //电话号码
        console.log('-> %c tel.test( value )  === %o', 'font-size: 15px;color: green;', tel.test(value));
        if (tel.test(value)) {
          callback();
        } else {
          callback(new Error('请输入正确的座机号码'));
        }
      }
      callback(new Error('请输入正确的客服电话'));
    };
    return {
      selectedAddress: [],
      options: regionData,
      formData: cloneDeep(init_form_data),
      storeInfoRules: {
        clinic_phone_no: [
          { required: true, message: '请输入客服电话', trigger: 'change', validator: validatePhoneCheck },
        ],
        name: [
          { required: true, message: '请输入门店名称', trigger: 'change' },
          { max: 30, message: '门店名称不能超过30个字符', trigger: 'change' },
        ],
        county_code: [{ required: true, message: '请选择门店地址', trigger: 'change' }],
        // 非必填
        // facade_images: [
        //   { required: true, type: 'array', min: 1, message: '请上传诊所门面图', trigger: 'change,blur' },
        // ],
        work_time_desc: [{ required: true, message: '请选择营业时间', trigger: 'change' }],
      },
      weekDays: [],
      halfTimePieces: [
        { time: '00:00', value: 0 },
        { time: '00:30', value: 0.5 },
        { time: '01:00', value: 1 },
        { time: '01:30', value: 1.5 },
        { time: '02:00', value: 2 },
        { time: '02:30', value: 2.5 },
        { time: '03:00', value: 3 },
        { time: '03:30', value: 3.5 },
        { time: '04:00', value: 4 },
        { time: '04:30', value: 4.5 },
        { time: '05:00', value: 5 },
        { time: '05:30', value: 5.5 },
        { time: '06:00', value: 6 },
        { time: '06:30', value: 6.5 },
        { time: '07:00', value: 7 },
        { time: '07:30', value: 7.5 },
        { time: '08:00', value: 8 },
        { time: '08:30', value: 8.5 },
        { time: '09:00', value: 9 },
        { time: '09:30', value: 9.5 },
        { time: '10:00', value: 10 },
        { time: '10:30', value: 10.5 },
        { time: '11:00', value: 11 },
        { time: '11:30', value: 11.5 },
        { time: '12:00', value: 12 },
        { time: '12:30', value: 12.5 },
        { time: '13:00', value: 13 },
        { time: '13:30', value: 13.5 },
        { time: '14:00', value: 14 },
        { time: '14:30', value: 14.5 },
        { time: '15:00', value: 15 },
        { time: '15:30', value: 15.5 },
        { time: '16:00', value: 16 },
        { time: '16:30', value: 16.5 },
        { time: '17:00', value: 17 },
        { time: '17:30', value: 17.5 },
        { time: '18:00', value: 18 },
        { time: '18:30', value: 18.5 },
        { time: '19:00', value: 19 },
        { time: '19:30', value: 19.5 },
        { time: '20:00', value: 20 },
        { time: '20:30', value: 20.5 },
        { time: '21:00', value: 21 },
        { time: '21:30', value: 21.5 },
        { time: '22:00', value: 22 },
        { time: '22:30', value: 22.5 },
        { time: '23:00', value: 23 },
        { time: '23:30', value: 23.5 },
        { time: '次日 00:00', value: 24 },
      ],
    };
  },

  created() {
    this.getOptions();
  },

  methods: {
    getOptions() {
      this.$api.getClinicOptions().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.weekDays = S.descToArrHandle(res.weekDesc).map((item, index) => {
          return {
            ...item,
            isChecked: false,
          };
        });
        this.get();
      });
    },
    showPoptipHandler() {
      this.$nextTick(() => {
        this.weekDays.map(item => {
          if (this.formData.workdays.indexOf(item.id) !== -1) {
            item.isChecked = true;
          } else {
            item.isChecked = false;
          }
        });
      });
    },
    selectDateHandler() {
      console.log(this.weekDays);
      const selectedDate = this.weekDays.filter(item => item.isChecked).map(item => item.id);
      console.log('-> %c selectedDate  === %o', 'font-size: 15px;color: green;', selectedDate);
      this.formData.workdays = selectedDate;
      this.getBusinessDatetext();
      console.log(this.$refs.datePoptip);
      this.$refs.datePoptip.handleClose();
    },
    checkDate(date, index) {
      console.log(this.weekDays);
      this.weekDays[index].isChecked = !date.isChecked;
    },
    getBusinessDatetext() {
      const days = this.formData.workdays.sort();
      console.log('-> %c days  === %o', 'font-size: 15px;color: green;', days);
      if (!days.length) {
        this.formData.work_time_desc = '';
        return;
      }
      const dayMap = {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日',
      };
      let resultArr = [],
        i = 0;
      resultArr[i] = [days[0]];
      days.reduce((pre, cur) => {
        cur - pre === 1 ? resultArr[i].push(cur) : (resultArr[++i] = [cur]);
        return cur;
      });
      this.formData.work_time_desc = resultArr
        .map(item => {
          if (item.length === 1) {
            return dayMap[item[0]];
          } else {
            return `${dayMap[item[0]]}至${dayMap[item[item.length - 1]]}`;
          }
        })
        .join('、');
    },
    get: function () {
      io.get('clinic/clinic.get')
        .then(data => {
          console.log('-> %c data  === %o', 'font-size: 15px;color: green;', data);
          this.formData.name = data.name;
          this.formData.simple_name = data.simple_name;
          this.formData.clinic_phone_no = data.clinic_phone_no;
          this.formData.workdays = data.workdays;
          this.formData.address_other = data.address_other;
          this.formData.prov_name = data.prov_name;
          this.formData.prov_code = data.prov_code;
          this.formData.is_hide_mp = data.is_hide_mp;

          this.formData.county_name = data.county_name;
          this.formData.county_code = data.county_code;
          this.formData.city_name = data.city_name;
          this.formData.city_code = data.city_code;
          this.formData.work_time = data.work_time;
          this.formData.facade_images = data.facade_images;
          // console.log("-> %c data.county_name  === %o", "font-size: 15px;color: green;", TextToCode[data.prov_name][data.city_name][data.county_name].code)
          if (data.county_code || data.county_name) {
            this.selectedAddress = [data.prov_code, data.city_code, data.county_code];
          } else {
            this.selectedAddress = [data.prov_code, data.city_code];
          }
          this.weekDays.map(item => {
            if (this.formData.workdays.indexOf(item.id) !== -1) {
              item.isChecked = true;
            }
          });
          if (!data.work_time_desc) {
            this.selectDateHandler();
          } else {
            this.formData.work_time_desc = data.work_time_desc;
          }
          console.log('-> %c this.weekDays  === %o', 'font-size: 15px;color: green;', this.weekDays);
        })
        .catch(error => {
          console.log('-> %c error  === %o', 'font-size: 15px;color: green;', error);
          {
          }
        });
    },

    onSave: function () {
      this.$refs.clinicForm.validate(valid => {
        if (valid) {
          io.post('clinic/clinic.update', this.formData)
            .then(() => {
              this.$Message.success('保存成功');
            })
            .catch(error => {
              {
              }
            });
        } else {
          this.$Message.error('请填写完整信息');
        }
      });
    },
    regionChange(address) {
      console.log(address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.prov_code = prov.code;
        this.formData.prov_name = prov.name;
        this.formData.city_code = city.code;
        this.formData.city_name = city.name;
        this.formData.county_code = county.code;
        this.formData.county_name = county.name;
      } else {
        this.formData.prov_code = '';
        this.formData.prov_name = '';
        this.formData.city_code = '';
        this.formData.city_name = '';
        this.formData.county_code = '';
        this.formData.county_name = '';
        this.formData.address_other = '';
      }

      // 手动触发校验
      // this.$refs.formData.validateField('selectedAddress')
    },
  },
};
</script>

<style lang="less" scoped>
.time-box {
  margin-top: 20px;
}

.see-mould {
  display: flex;
  align-items: flex-end;
  font-size: 12px;
  font-weight: 400;
  color: #155bd5;
  margin-top: 44px;
}

.cursor {
  cursor: pointer;
}

.tip {
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  line-height: 17px;
  margin: 0px;
}

.ml6 {
  margin-left: 6px;
}

::v-deep .ivu-poptip-rel {
  width: 100%;
}

::v-deep .ivu-tooltip-inner {
  max-height: 600px !important;
}
</style>
<style lang="less">
@theme-color: #1157e5;
.time-picker-poptip {
  .ivu-poptip-body {
    padding: 13px 12px;
  }

  .ivu-poptip-body-content {
    overflow: hidden;
  }

  .rsj-checkbox-group {
    width: 660px;

    .rsj-checkbox-wrap {
      position: relative;
      text-align: center;
      line-height: 30px;
      border: 1px solid #bbb;
      width: 80px;
      height: 30px;
      border-radius: 2px;
      cursor: pointer;
      font-weight: 400;
      margin-right: 10px;
      padding: 0;
      vertical-align: middle;
      display: inline-block;
      font-size: 14px;
      box-sizing: border-box;

      .rsj-checkbox {
        opacity: 0;
        position: absolute;
        bottom: 0;
        right: 0;
        background-size: 100% 100%;
        background-image: url('~@/assets/image/base/rsj-checked.png');
        display: inline-block;
        width: 16px;
        height: 16px;
        white-space: nowrap;
        outline: none;
        vertical-align: middle;
        line-height: 1;
        margin: 0;
        padding: 0;

        .zent-checkbox-inner {
        }

        input {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          margin: 0;
          padding: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          opacity: 0;
          cursor: pointer;
        }
      }
    }

    .rsj-checkbox-isChecked {
      border: 1px solid @theme-color;
      color: @theme-color;

      .rsj-checkbox {
        opacity: 1;
      }
    }
  }
}

.custom {
  .ivu-tooltip-inner {
    max-height: max-content;
  }
}

.clinic-basic{
  padding-bottom: 40px;
}
</style>
