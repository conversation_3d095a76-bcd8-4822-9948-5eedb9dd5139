<template>
  <div style="padding-bottom: 20px">
    <Form
      ref="doctorForm"
      :model="formData"
      :rules="ruleValidate"
      :label-width="120"
      :disabled="$route.query.openType === 'detail' || formDataDisable"
    >
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="姓名" prop="name">
            <Input
              v-model="formData.name"
              :disabled="is_quick || $route.query.openType === 'edit'"
              placeholder="请输入姓名"
              maxlength="15"
              show-word-limit
            ></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.name" :disabled="is_quick" class="error-reason">
            上次驳回原因：{{ reject_data.name }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="手机号" prop="mobile">
            <Input
              v-model="formData.mobile"
              :disabled="is_quick || $route.query.openType === 'edit'"
              placeholder="请输入手机号"
            ></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.mobile" class="error-reason">上次驳回原因：{{ reject_data.mobile }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="榕树家诊所" prop="hosp_name_custom">
            <Input v-model="formData.hosp_name_custom" disabled></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.hosp_name_custom" class="error-reason">
            上次驳回原因：{{ reject_data.hosp_name_custom }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="职业" prop="profession">
            <Select v-model="formData.profession" placeholder="请选择职业" @on-change="changeProfession">
              <Option v-for="(item, key) in professionDesc" :value="item.id" :key="key">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.profession" class="error-reason">上次驳回原因：{{ reject_data.profession }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="职称" prop="professional_title">
            <Select v-model="formData.professional_title" placeholder="请选择职称">
              <Option v-for="(item, key) in professionTitleDesc" :value="item.id" :key="key">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.professional_title" class="error-reason">
            上次驳回原因：{{ reject_data.professional_title }}
          </div>
        </Col>
      </Row>

      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="科室" prop="dept_name">
            <Input v-model="formData.dept_name" placeholder="请输入科室"></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.dept_name" class="error-reason">上次驳回原因：{{ reject_data.dept_name }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="邮箱" prop="email">
            <Input v-model="formData.email" placeholder="请输入邮箱"></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.email" class="error-reason">上次驳回原因：{{ reject_data.email }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="我的擅长" prop="my_specialty">
            <Input
              v-model="formData.my_specialty"
              type="textarea"
              show-word-limit
              :maxlength="500"
              :rows="4"
              :autosize="{ minRows: 4, maxRows: 10 }"
              placeholder="请输入我的擅长"
            ></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.my_specialty" class="error-reason">上次驳回原因：{{ reject_data.my_specialty }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="个人简介" prop="description">
            <Input
              v-model="formData.description"
              type="textarea"
              show-word-limit
              :maxlength="500"
              :rows="4"
              :autosize="{ minRows: 4, maxRows: 10 }"
              placeholder="请输入个人简介"
            ></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.description" class="error-reason">上次驳回原因：{{ reject_data.description }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="头像" prop="avatar">
            <MaterialPicture
              v-if="needShowPicture('avatar')"
              v-model="formData.avatar"
              :limit="1"
              :disabled="$route.query.openType === 'detail' || formDataDisable"
            ></MaterialPicture>
            <div v-else>-</div>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.avatar" class="error-reason">上次驳回原因：{{ reject_data.avatar }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="证件类型" prop="idcard_type_kw">
            <Select v-model="formData.idcard_type_kw" placeholder="请选择证件类型" :disabled="is_quick && isRst">
              <Option v-for="(item, key) in idCardDesc" :value="item.id" :key="key">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.idcard_type_kw" class="error-reason">
            上次驳回原因：{{ reject_data.idcard_type_kw }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="证件号" prop="id_card">
            <Input v-model="formData.id_card" placeholder="请输入证件号" :disabled="is_quick && isRst"></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.id_card" class="error-reason">上次驳回原因：{{ reject_data.id_card }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="身份证正面照片" prop="id_card_front">
            <MaterialPicture
              v-if="needShowPicture('id_card_front')"
              v-model="formData.id_card_front"
              :limit="1"
              :disabled="$route.query.openType === 'detail' || formDataDisable"
            ></MaterialPicture>
            <div v-else>-</div>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.id_card_front" class="error-reason">上次驳回原因：{{ reject_data.id_card_front }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="身份证反面照片" prop="id_card_back">
            <MaterialPicture
              v-if="needShowPicture('id_card_back')"
              v-model="formData.id_card_back"
              :limit="1"
              :disabled="$route.query.openType === 'detail' || formDataDisable"
            ></MaterialPicture>
            <div v-else>-</div>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.id_card_back" class="error-reason">上次驳回原因：{{ reject_data.id_card_back }}</div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="执业证编码" prop="practice_certificate.code">
            <Input v-model="formData.practice_certificate.code" placeholder="请输入执业证编码"></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.practice_certificate?.code" class="error-reason">
            上次驳回原因：{{ reject_data.practice_certificate?.code }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="发证日期" prop="practice_certificate.date">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              :value="formData.practice_certificate.date"
              placeholder="请选择发证日期"
              :options="disableOptions"
              @on-change="val => (formData.practice_certificate.date = val)"
            />
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.practice_certificate?.date" class="error-reason">
            上次驳回原因：{{ reject_data.practice_certificate?.date }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="执业机构名称" prop="practice_certificate.name">
            <Input v-model="formData.practice_certificate.name" placeholder="请输入执业机构名称"></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.practice_certificate?.name" class="error-reason">
            上次驳回原因：{{ reject_data.practice_certificate?.name }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="执业证照片" prop="practice_certificate.images">
            <MaterialPicture
              v-if="
                (!$route.query.id && formDataDisable && formData.practice_certificate.images?.length > 0) ||
                (!$route.query.id && !formDataDisable) ||
                ($route.query.id && $route.query.openType === 'reject') ||
                ($route.query.id && $route.query.openType === 'edit') ||
                ($route.query.id && formData.practice_certificate.images?.length > 0)
              "
              v-model="formData.practice_certificate.images"
              :limit="9"
              :disabled="$route.query.openType === 'detail' || formDataDisable"
            ></MaterialPicture>
            <div v-else>-</div>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.practice_certificate?.images" class="error-reason">
            上次驳回原因：{{ reject_data.practice_certificate?.images }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="职称证照片" v-if="!isNoRequirePracticeTitle" prop="practice_title_certificate">
            <MaterialPicture
              v-if="needShowPicture('practice_title_certificate')"
              v-model="formData.practice_title_certificate"
              :limit="9"
              :disabled="$route.query.openType === 'detail' || formDataDisable"
            ></MaterialPicture>
            <div v-else>-</div>
          </FormItem>
          <FormItem label="职称证照片" v-show="isNoRequirePracticeTitle">
            <MaterialPicture
              v-if="needShowPicture('practice_title_certificate')"
              v-model="formData.practice_title_certificate"
              :limit="9"
              :disabled="$route.query.openType === 'detail' || formDataDisable"
            ></MaterialPicture>
            <div v-else>-</div>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.practice_title_certificate" class="error-reason">
            上次驳回原因：{{ reject_data.practice_title_certificate }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="资格证编码" prop="doctor_certificate.code">
            <Input v-model="formData.doctor_certificate.code" placeholder="请输入资格证编码"></Input>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.doctor_certificate?.code" class="error-reason">
            上次驳回原因：{{ reject_data.doctor_certificate?.code }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="发证日期" prop="doctor_certificate.date">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              :value="formData.doctor_certificate.date"
              placeholder="请选择发证日期"
              :options="disableOptions"
              @on-change="val => (formData.doctor_certificate.date = val)"
            />
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.doctor_certificate?.date" class="error-reason">
            上次驳回原因：{{ reject_data.doctor_certificate?.date }}
          </div>
        </Col>
      </Row>
      <Row :gutter="20">
        <Col :span="12">
          <FormItem label="资格证照片" prop="doctor_certificate.images">
            <MaterialPicture
              v-if="
                (!$route.query.id && formDataDisable && formData.doctor_certificate.images?.length > 0) ||
                (!$route.query.id && !formDataDisable) ||
                ($route.query.id && $route.query.openType === 'reject') ||
                ($route.query.id && $route.query.openType === 'edit') ||
                ($route.query.id && formData.doctor_certificate.images?.length > 0)
              "
              v-model="formData.doctor_certificate.images"
              :limit="9"
              :disabled="$route.query.openType === 'detail' || formDataDisable"
            ></MaterialPicture>
            <div v-else>-</div>
          </FormItem>
        </Col>
        <Col :span="12">
          <div v-if="reject_data.doctor_certificate?.images" class="error-reason">
            上次驳回原因：{{ reject_data.doctor_certificate?.images }}
          </div>
        </Col>
      </Row>
    </Form>
    <!--    <FormListComp-->
    <!--      ref="FormListComp"-->
    <!--      :formList="formListMap"-->
    <!--      :formData="formData"-->
    <!--      :ruleValidate="ruleValidate"-->
    <!--    ></FormListComp>-->

    <ConfirmModal
      content="确认提交？"
      contentText="平台将会在1-3个工作日完成审核，确认提交？"
      :confirmVisible.sync="confirmVisible"
      @ok="confirmFn"
      :loading="confirmLoading"
    />

    <ConfirmModal
      content="是否回填？"
      :contentText="backIdcardText"
      :confirmVisible.sync="backIdcardVisible"
      @ok="backIdcardConfirmFn"
    />

    <div class="fixed-bottom-wrapper">
      <Button class="btnStyle" @click="cancel">返回</Button>
      <Button
        class="btnStyle"
        type="primary"
        style="margin-left: 16px"
        v-if="!$route.query.id || $route.query.openType === 'edit'"
        @click="onSaveFun"
      >
        提交
      </Button>
      <Button
        class="btnStyle"
        type="primary"
        style="margin-left: 16px"
        v-if="$route.query.id && openType === 'reject'"
        @click="onSaveFun"
        >重新提交
      </Button>
    </div>
  </div>
</template>

<script>
// import FormListComp from './components/formList.vue';
import ConfirmModal from '@/components/confirmModal/confirmModal';
import S from '@/libs/util'; // Some commonly used tools
import * as runtime from '@/libs/runtime'; // Runtime information
import { cloneDeep, get  } from 'lodash-es';
// import { validateIDCard } from '@/libs/validator';
import Picture from '@/components/upload/picture';

const init_form_data = {
  name: '',
  mobile: '',
  // source_desc: '',
  hosp_name_custom: runtime.getUser().clinic_name,
  profession: '',
  dept_name: '',
  professional_title: '',
  email: '',
  my_specialty: '',
  description: '',
  avatar: '',
  idcard_type_kw: '',
  id_card: '',
  id_card_front: '',
  id_card_back: '',
  practice_certificate: {
    code: '',
    date: '',
    name: '',
    images: [],
  },
  doctor_certificate: {
    code: '',
    date: '',
    images: [],
  },
  practice_title_certificate: [],
  e_signature: '',
};
export default {
  name: 'physician-detail',
  components: { ConfirmModal, Picture },
  // mixins: getMixins([create, edit]),
  data() {
    return {
      formData: {
        ...init_form_data,
      },
      idCardFormData: { ...init_form_data },
      openType: '',
      confirmVisible: false,
      backIdcardText: '',
      backIdcardVisible: false,
      //职称证为非必填
      includeVal: ['4', '5', '6'],
      ruleValidate: {
        name: [
          {
            required: true,
            trigger: 'change,blur',
            validator: (rule, value, callback) => {
              // console.log(value);
              if (!value) {
                callback(new Error('请输入姓名'));
              }
              if (value.length > 15) {
                callback(new Error('姓名不能超过15个字'));
              } else {
                callback();
              }
            },
          },
        ],
        mobile: [
          {
            required: true,
            trigger: 'change,blur',
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入手机号'));
              } else {
                let phoneReg = /^1[3-9]\d{9}$/;
                if (!phoneReg.test(value)) {
                  callback(new Error('手机号格式不正确'));
                } else {
                  callback();
                }
              }
            },
          },
        ],
        hosp_name_custom: [{ required: true, message: '请输入榕树家诊所', trigger: 'blur' }],
        profession: [{ required: true, message: '请输入职业', trigger: 'change,blur' }],
        professional_title: [{ required: true, message: '请输入职称', trigger: 'change,blur' }],
        dept_name: [{ required: false, message: '请输入科室', trigger: 'blur' }],
        email: [{ required: false, message: '请输入邮箱', trigger: 'blur' }],
        my_specialty: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              console.log('value: ', value);
              if (value.trim().length < 15) {
                callback(new Error('我的擅长不能少于15个字'));
              } else {
                callback();
              }
            },
          },
        ],
        description: [
          {
            required: false,
            trigger: 'change',
            // validator: (rule, value, callback) => {
            //   if (value.length < 15) {
            //     if (!value) {
            //       callback();
            //     }
            //     callback(new Error('个人简介不能少于15个字'));
            //   } else {
            //     callback();
            //   }
            // },
          },
        ],
        avatar: [{ required: true, message: '请上传头像', trigger: 'change' }],
        idcard_type_kw: [{ required: true, message: '请选择证件类型', trigger: 'change,blur' }],
        id_card: [
          {
            required: true,
            trigger: ['blur', 'change'],
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入证件号码'));
              }
              const idCardReg =
                /^(1[1-5]|2[1-3]|3[1-7]|4[1-6]|5[0-4]|6[1-5])\d{4}(19\d{2}|20(0[0-9]|1[0-8]))((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
              const CNResidencePermitReg =
                /^8[123]0000(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;
              if (!idCardReg.test(value) && !CNResidencePermitReg.test(value)) {
                callback(new Error('证件格式不正确'));
              } else {
                console.log('根据idcard 获取身份信息');
                this.getSettingByIdCard(value, callback);
              }
            },
          },
        ],
        id_card_front: [{ required: true, message: '请选择身份证正面照片', trigger: 'change' }],
        id_card_back: [{ required: true, message: '请选择身份证反面照片', trigger: 'change' }],
        'practice_certificate.code': [
          {
            required: true,
            trigger: 'change,blur',
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入执业证编码'));
              } else {
                let reg = /^[a-zA-Z0-9]+$/;
                if (!reg.test(value)) {
                  callback(new Error('执业证编码必须为数字字母组合'));
                } else {
                  callback();
                }
              }
            },
          },
        ],
        'practice_certificate.date': [{ required: true, message: '请选择发证日期', trigger: 'change,blur' }],
        'practice_certificate.name': [{ required: true, message: '请输入执业机构名称', trigger: 'change,blur' }],
        'practice_certificate.images': [
          {
            required: true,
            message: '请选择执业证照片',
            trigger: 'change',
            validator: (rule, value, callback) => {
              console.log('=>(detail.vue:382) value', value);
              if (value.length < 1) {
                callback(new Error('请上传执业证照片'));
              } else {
                callback();
              }
            },
          },
        ],
        practice_title_certificate: [
          {
            required: true,
            message: '请选择职称证照片',
            trigger: 'change',
            type: 'array',
          },
        ],
        'doctor_certificate.code': [
          {
            required: false,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              let reg = /^[a-zA-Z0-9]+$/;
              if (!reg.test(value)) {
                if (!value) {
                  callback();
                }
                callback(new Error('资格证编码必须为数字字母组合'));
              } else {
                callback();
              }
            },
          },
        ],
        'doctor_certificate.date': [{ required: false, message: '请选择发证日期', trigger: 'blur' }],
        'doctor_certificate.images': [
          {
            required: false,
            message: '请选择资格证照片',
            trigger: 'change',
            type: 'array',
          },
        ],
      },
      defaultRuleValidate: {},

      // 重构新增
      professionDesc: [],
      professionTitleDesc: [],
      idCardDesc: [],
      disableOptions: {
        disabledDate(date) {
          return date && date.valueOf() >= Date.now();
        },
      },
      formDataDisable: false,
      stopGetIdInfo: false, // 提交时不再执行回填逻辑
      reject_data: {},
      confirmLoading: false,
    };
  },
  created() {},
  computed: {
    is_quick() {
      return this.$route.query.is_quick == '1';
    },
    isRst() {
      return runtime.isRstClinic();
    },
    // 判断当表单禁用状态时，是否展示图片控件
    needShowPicture() {
      return key => {
        return (
          (!this.$route.query.id && this.formDataDisable && this.formData[key]) ||
          (!this.$route.query.id && !this.formDataDisable) ||
          (this.$route.query.id && this.$route.query.openType === 'reject') ||
          (this.$route.query.id && this.$route.query.openType === 'edit') ||
          (this.$route.query.id && this.formData[key])
        );
      };
    },

    // 医学生/医士/住院医师，职称证非必填
    isNoRequirePracticeTitle() {
      return this.formData.profession === 'CAREER_ASSISTANT' || this.formData.professional_title === '4';
    },
  },
  beforeMount() {},
  mounted() {
    this.openType = this.$route.query.openType;
    this.getOptions().then(res => {
      if (this.$route.query.id) {
        this.getDetail();
      } else {
        this.formData = cloneDeep(init_form_data);
        let query = this.$route.query;
        if (query.is_quick == '1') {
          this.formData.name = query.name;
          this.formData.mobile = query.mobile;
          this.formData.avatar = query.avatar;
          this.formData.description = query.desc;
          this.formData.idcard_type_kw = query.id_card_type;
          this.formData.id_card = query.idcard;
        }
      }
    });
  },
  methods: {
    getSettingByIdCard(id_card, callback) {
      this.$api
        .getSeetingInfoByIdCardVerify({ id_card: id_card })
        .then(res => {
          callback && callback();
          this.checkIdCard();
        })
        .catch(err => {
          callback && callback(new Error('证件校验不合法'));
        });
    },
    onSaveFun() {
      try {
        this.stopGetIdInfo = true;
        console.log(this.formData);
        this.$refs.doctorForm.validate(valid => {
          console.log('valid: ', valid);
          if (valid) {
            this.confirmVisible = true;
          } else {
            this.$Message.error('请完善信息');
          }
          setTimeout(() => {
            this.stopGetIdInfo = false;
          }, 0);
        });
      } catch (e) {
        console.log('e: ', e);
        this.stopGetIdInfo = false;
      }
    },
    submit() {
      let params = this.formData;
      this.saveSubmitSync(params);
    },
    saveSubmitSync(params) {
      this.confirmLoading = true;
      this.$api
        .savePhysicianDetail(params)
        .then(res => {
          this.$Message.success('保存成功');
          this.confirmVisible = false;
          this.cancel();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    getDetail() {
      this.$api.getPhysicianDetail({ id: this.$route.query.id }).then(res => {
        // 表单赋值
        this.formData = {
          ...res,
          hosp_name_custom: runtime.getUser().clinic_name,
        };

        this.changeProfession(this.formData.profession);

        // 设置默认验证（平台驳回原因）
        if (this.$route.query.openType === 'reject') {
          this.reject_data = res.reject_data;
          // let formListMapKeysArr = Array.from(this.formListMap.keys());
          // for (const iterator of formListMapKeysArr) {
          //   if (get(reject_data, iterator)) {
          //     this.setFormInitValidate(iterator, [
          //       {
          //         required: true,
          //         message: get(reject_data, iterator),
          //         trigger: 'blur',
          //         validator: (rule, value, callback) => {
          //           let reg = /^[a-zA-Z0-9]+$/;
          //           if (!reg.test(value)) {
          //             if (!value) {
          //               callback();
          //             }
          //             callback(new Error('资格证编码必须为数字字母组合'));
          //           } else {
          //             callback();
          //           }
          //         },
          //       },
          //     ]);
          //   }
          // }
        }
      });
    },
    // 设置单项验证
    setFormInitValidate(key, validate) {
      // 过滤掉已经设置的验证或没有设置验证的项
      if (this.ruleValidate[key]) {
        this.$set(this.defaultRuleValidate, key, cloneDeep(this.ruleValidate[key]));
        console.log('=>(detail.vue:791) this.defaultRuleValidate', this.defaultRuleValidate);
        this.setRuleValidate(key, validate);
        this.$refs.formValidate.validateField(key);
        this.setRuleValidate(key, this.defaultRuleValidate[key] || []);
        this.$delete(this.defaultRuleValidate, key);
      }
    },
    getOptions() {
      return new Promise((resolve, reject) => {
        this.$api.getPhysicianOptions().then(res => {
          this.professionDesc = S.descToArrHandle(res.professionDesc);
          this.idCardDesc = S.descToArrHandle(res.idCardDesc);
          resolve();
        });
      });
    },
    // 设置单项验证
    setRuleValidate(key, valueObj) {
      this.$set(this.ruleValidate, key, cloneDeep(valueObj));
    },
    confirmFn() {
      this.submit();
    },
    // 点击回填
    backIdcardConfirmFn() {
      console.log(this.idCardFormData);
      delete this.idCardFormData.id;
      this.formData = {
        ...this.idCardFormData,
        id_card_relation: 1,
        hosp_name_custom: runtime.getUser().clinic_name,
        practice_certificate_images: this.idCardFormData.practice_certificate.images,
      };
      this.changeProfession(this.formData.profession); // 回显职称下拉
      this.formDataDisable = true; // 禁用表单
      this.backIdcardVisible = false;
      // todo 图片回填异常展示
      setTimeout(() => {
        this.$refs.formValidate.validateField('avatar');
        this.$refs.formValidate.validateField('id_card_front');
        this.$refs.formValidate.validateField('id_card_back');
        this.$refs.formValidate.validateField('practice_certificate.images');
        this.$refs.formValidate.validateField('practice_title_certificate');
      }, 0);

      this.$Message.success('回填成功');
    },
    cancel() {
      this.$router.go(-1);
    },

    changeProfession(val) {
      console.log('=>(detail.vue:797) val', val);
      this.professionTitleDesc = S.descToArrHandle(this.professionDesc.find(item => item.id === val).data);
    },

    checkIdCard() {
      document.body.click();
      if (!this.stopGetIdInfo) {
        this.$api.getInfoByIdCard({ id_card: this.formData.id_card }).then(res => {
          console.log('=>(detail.vue:825) res', res);
          if (res.id) {
            this.backIdcardText = `该证件（${res.id_card}）已经完成过医生认证，是否快速获取信息进行填充？`;
            this.backIdcardVisible = true;
            this.idCardFormData = cloneDeep(res);
            console.log('=>(detail.vue:854) this.idCardFormData', this.idCardFormData);
          }
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
::v-deep .ivu-date-picker {
  width: 100%;
}

.error-reason {
  //padding-left: 120px;
  color: #ed4014;
  font-size: 12px;
}
</style>
