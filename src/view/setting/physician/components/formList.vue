<template>
  <div>
    <Form ref="formValidate" :model="formData" :rules="ruleValidate" :label-width="120">
      <Row v-for="(item, index) in Array.from(formList.values())" :key="index">
        <Col :span="16">
          <FormItem :ref="item.prop" :label="item.label" :prop="item.prop" v-if="item.type === 'text'">
            <Input
              :value="getFormItem(formData, item.prop)"
              :placeholder="item.placeholder"
              :disabled="!!item.disabled"
              :maxlength="item.len || 100"
              :show-word-limit="item.len ? true : false"
              @input="e => inputFunc(e, item)"
              @on-blur="e => inputBlur(e, item)"
            ></Input>
          </FormItem>
          <FormItem :ref="item.prop" :label="item.label" :prop="item.prop" v-if="item.type === 'select'">
            <Select
              :value="getFormItem(formData, item.prop)"
              @input="e => inputFunc(e, item)"
              :placeholder="item.placeholder"
              :disabled="!!item.disabled"
            >
              <!-- <Option value="">全部</Option> -->
              <Option
                v-for="(selectItem, key) in item.selectList || []"
                :value="selectItem[item.selectId] || selectItem.id"
                :key="key"
                >{{ selectItem[item.selectShowName] || selectItem.name || '暂无设置' }}
              </Option>
            </Select>
          </FormItem>
          <FormItem :ref="item.prop" :label="item.label" :prop="item.prop" v-if="item.type === 'textarea'">
            <Input
              :value="getFormItem(formData, item.prop)"
              @input="e => inputFunc(e, item)"
              type="textarea"
              show-word-limit
              :maxlength="500"
              :rows="4"
              :autosize="{ minRows: 4, maxRows: 10 }"
              :placeholder="item.placeholder"
              :disabled="!!item.disabled"
            ></Input>
          </FormItem>
          <FormItem
            :ref="item.prop"
            :label="item.label"
            :prop="item.prop"
            v-if="item.type === 'img'"
            style="margin-top: 20px"
          >
            <MaterialPicture
              v-if="getImgLength(item) !== 0 || !item.disabled"
              :value="getFormItem(formData, item.prop)"
              @input="e => inputFunc(e, item)"
              :limit="getLimit(item)"
              :disabled="!!item.disabled"
            ></MaterialPicture>
            <div v-else>-</div>
            <!-- {{ getImgLength(item) }} -->
            <!-- {{ item.disabled }} -->
          </FormItem>
          <FormItem :ref="item.prop" :label="item.label" :prop="item.prop" v-if="item.type === 'date'">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              :value="getFormItem(formData, item.prop)"
              @input="e => inputFunc(e, item)"
              @on-change="e => inputFunc(e, item)"
              :placeholder="item.placeholder"
              :disabled="!!item.disabled"
              :options="item.options || {}"
            />
          </FormItem>
        </Col>
      </Row>
    </Form>
  </div>
</template>

<script>
import { get, set  } from 'lodash-es';
import moment from 'moment';

export default {
  name: 'formList',
  components: {},
  props: {
    formList: {
      type: [Array, Map],
      default: () => [],
    },
    formData: {
      type: Object,
      default: () => {},
    },
    ruleValidate: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {
    // console.log(Array.from(this.formList.values()).flat());
    console.log(this.formList);
  },
  computed: {
    getLimit() {
      // getFormItem(formData, item.prop) instanceof Array
      return function (item) {
        return (!!item.disabled && this.getImgLength(item)) || item.limit;
      };
    },
    getImgLength() {
      // getFormItem(formData, item.prop) instanceof Array
      return function (item) {
        let isArray = this.getFormItem(this.formData, item.prop) instanceof Array;
        return isArray ? this.getFormItem(this.formData, item.prop).length : 1;
      };
    },
  },
  methods: {
    getFormRef() {
      return this.$refs.formValidate;
    },
    inputFunc(e, item) {
      if (item.type === 'date' && !!e) {
        e = moment(e).format('YYYY-MM-DD');
      }
      set(this.formData, item.prop, e);
      if (item.onChange) {
        let itemValue = null;
        if (item.selectList?.length) {
          itemValue = item.selectList.find(valueItem => valueItem[item.selectId] === e);
        }

        item.onChange(e, item, itemValue);
      }
    },
    inputBlur(e, item) {
      if (item.onBlur) {
        item.onBlur(e, item);
      }
    },

    getFormItem: get,
  },
};
</script>

<style lang="less" scoped>
.rejectData {
  ::v-deep .ivu-form-item-label {
    color: red;
  }

  .reject {
    // border: 1px solid red;
    padding-left: 10px;
    color: red;
    word-wrap: break-word;
  }
}
</style>
