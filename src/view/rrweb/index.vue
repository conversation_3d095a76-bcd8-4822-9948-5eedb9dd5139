<template>
  <div>
    <div class="test-input">
      <Input type="textarea" v-model="text" />
    </div>
    <div class="btn-box">
      <ButtonGroup>
        <Button type="primary" @click="startRecord">开始录制</Button>
        <Button type="primary" @click="stopRecord">停止录制</Button>
        <Button type="primary" @click="playRecord">开始播放</Button>
      </ButtonGroup>
    </div>
    <div id="show" style="width: 1024px; height: 656px; margin: 0 auto; border: 1px solid #ccc; margin-top: 10px"></div>
  </div>
</template>

<script>
import * as rrweb from 'rrweb';

console.log('-> %c rrweb  ===    %o', 'font-size: 15px;color: #fa8c16 ;', rrweb);
import rrwebPlayer from 'rrweb-player';
import 'rrweb-player/dist/style.css';
import cloneDeep from 'lodash/cloneDeep';
import pako from 'pako';
import { Base64 } from 'js-base64';
import { stopRecord, recordAndSave, unzip } from '@/libs/rrweb';
export default {
  name: 'RRWeb',
  data() {
    return {
      stopFn: null,
      text: '',
      eventsMatrix: []
    };
  },
  mounted() {
    // this.startRecord();
    const data = [1, 2, 3, 4, 5, 6, 7, 8, 9];
    let dataJson = JSON.stringify(data);
    console.log(Base64.encode(dataJson));
    stopRecord();
  },
  beforeDestroy() {
    if (this.stopFn) {
      this.stopFn();
    }
  },
  methods: {
    startRecord() {
      this.stopFn = rrweb.record({
        emit: (event, isCheckout) => {
          console.log('=>(index.vue:53) event', event);
          if (isCheckout) {
            console.log('=>(index.vue:54) isCheckout', isCheckout);
            let cloneEvent = cloneDeep(this.eventsMatrix);
            // this.eventsMatrix = [];
            // 上报
          }
          this.eventsMatrix.push(event);
        },
        recordCanvas: true,
        checkoutEveryNms: 10 * 1000, // 每10s重新制作快照
        checkoutEveryNth: 200, // 每 200 个 event 重新制作快照
        // packFn: rrweb.pack,
        sampling: {
          // do not record mouse movement
          // mousemove: false,
          // do not record mouse interaction
          // mouseInteraction: false,
          // set the interval of scrolling event
          scroll: 150, // do not emit twice in 150ms
          // set the interval of media interaction event
          // media: 800,
          // set the timing of record input
          input: 'last' // When input mulitple characters, only record the final input
        }
      });
    },
    stopRecord() {
      this.stopFn();
    },
    playRecord() {
      let el = document.getElementById('show');
      el.innerHTML = '';
      console.log('-> %c this.eventsMatrix  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.eventsMatrix);
      let events = this.eventsMatrix;
      //取最后一个快照回放
      let replayer = new rrwebPlayer({
        target: el, // 可以自定义 DOM 元素
        props: {
          events
          // unpackFn: rrweb.unpack //解压数据
        }
      });
      // return replayer;
    }
  }
};
</script>

<style scoped lang="less"></style>
