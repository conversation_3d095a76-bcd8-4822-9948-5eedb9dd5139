<template>
  <Modal :value="visible" footer-hide :title="title" lock-scroll :width="width" @on-visible-change="changeVisible">
    <div class="content-wrapper">
      <div class="value-explanation" v-for="(item, index) in explanations" :key="index + 'v'">
        <div class="title">{{ item.title }}</div>
        <div class="value" v-for="(v, idx) in item.content" :key="idx + 'c'">
          <span class="label">{{ v.label }}</span>
          <span v-html="v.value"></span>
        </div>
      </div>
      <div class="extra-box">
        <div class="title">另外，</div>
        <div class="extra-content" v-for="item in extraContent" :key="item">
          <span class="dot"></span>
          <span>{{ item }}</span>
        </div>
      </div>

      <div class="cases-box">
        <div class="case-tip-title">如下通过几个案例，来具体阐述价值是如何计算出来的。</div>
        <div class="case-item" v-for="(c, i) in caseList" :key="i + 'case'">
          <div class="case-title">{{ c.title }}</div>
          <div class="case-content">
            <span class="letter">例如：</span>
            <div class="case-text" v-html="c.content"></div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ValueExplanation',
  data() {
    return {
      visible: true,
      title: '',
      width: '680px',
      close: null,
      explanations: [
        {
          title: '通兑券的价值：',
          content: [
            {
              label: '总价值：',
              value: '是指通兑券在订单中的价格占比，乘以实际支付金额，所得的实际价值。',
            },
            {
              label: '剩余价值：',
              value: `是指通兑券总价值减去通兑券中 <span style="color: #EE3838">已兑换</span>的服务的价值。`,
            },
          ],
        },
        {
          title: '卡券（服务）的价值：',
          content: [
            {
              label: '总价值：',
              value: '是指服务在订单中的价格占比，乘以实际支付金额，所得的实际价值。',
            },
            {
              label: '剩余价值：',
              value: `是指服务总价值减去<span style="color: #EE3838">已核销</span>的服务的价值。 `,
            },
          ],
        },
      ],
      extraContent: [
        '当购买的通兑券的状态为已过期或已停用时，此通兑券的剩余价值将变为0。',
        '当购买的卡券（服务）的状态为已过期或已停用时，此卡券（服务）的剩余价值将变为0。',
        '发放途径是储值赠送的通兑券，它的总价值和剩余价值都为0。',
        '发放途径是关联赠送或储值赠送或会员赠送的卡券(服务)，它的总价值和剩余价值都为0。',
      ],
      caseList: [
        {
          title: '案例一：（订单里仅一个商品，且订单无优惠）',
          content: `一个用户使用微信支付了一笔订单，订单中仅含一个通兑券甲1017元，没有优惠金额。
            订单实际支付了：1017元。
            那么，<span class="result">通兑券甲的总价值为1017元， 剩余价值为1017元。</span>
          `,
        },
        {
          title: '案例二：（订单里仅一个商品，且订单有优惠）',
          content: `一个用户使用微信支付了一笔订单，订单中仅含一个通兑券甲1017元，优惠金额17元。
            订单实际支付：1017-17=1000元。
            那么，<span class="result">通兑券甲的总价值为1000元， 剩余价值为1000元。</span>
          `,
        },
        {
          title: '案例三：（订单里含有多个商品，且订单有优惠）',
          content: `一个用户使用微信支付了一笔订单，订单里含通兑券甲 1017元，通兑券乙 2000元 ，优惠金额17元。
            订单实际支付：1017+2000-17=3000元
            通兑券甲在订单中的占比是： 1017/（1017+2000）=0.34
            那么，<span class="result">通兑券甲的总价值为：0.34X3000=1011.27元，剩余价值为1011.27元。</span>
          `,
        },
        {
          title: '案例四：（通过储值余额支付订单）',
          content: `一个用户的储值余额为5000元，实充余额为3000元。（储值余额=实充余额+赠送余额）
            他使用储值余额支付了一笔订单，订单中仅含一个通兑券甲1017元，优惠金额17元。
            订单实际支付：1017-17=1000元。
            实充余额在储值余额中的占比=3000/5000=0.6
            那么，<span class="result">通兑券甲的总价值为1000X0.6=600元，剩余价值为600元。</span>
          `,
        },
        {
          title: '案例五：（购买了虚拟商品，核销了服务）',
          content: `一个用户使用微信支付了一笔订单，订单里含虚拟商品甲 1017元，虚拟商品乙 2000元 ，优惠金额17元。
            假设虚拟商品甲 含有 服务A，服务B，服务C。
            服务A 可用3次，每次200元
            服务B 可用1次，每次400元
            订单实际支付了：1017+2000-17=3000元。
            虚拟商品甲在订单中的占比是： 1017/（1017+2000）=0.34
            那么，<span class="result">虚拟商品甲的总价值为：0.34X3000=1011.27元</span>
            因为此时没有核销任何服务，，<span class="result">虚拟商品甲的剩余价值也为1011.27元</span>
            3次服务A 在虚拟商品中的占比为200X3/（200X3+400X1）=0.6
            那么，<span class="result">3次服务A的总价值为：0.6X1011.27=606.76元，剩余价值为606.76元</span>

            然后仅核销了1次服务A，1次服务A 在虚拟商品中的占比为200X1/（200X3+400X1）=0.2
            那么，<span class="result">3次服务A的总价值 为606.76元，剩余价值为606.76-606.76X0.2=485.41元</span>
          `,
        },
        {
          title: '案例六：（购买了通兑券，兑换了服务，后续也核销了服务）',
          content: `一个用户使用微信支付了一笔订单，订单里含通兑券甲 1017元，通兑券乙 2000元 ，优惠金额17元。
            假设通兑券甲 含有 服务A，服务B，服务C，一共可以兑换4次。
            服务A 可兑换4次，每次200元
            服务B 可兑换2次，每次400元
            服务C 可兑换3次，每次300元
            订单实际支付了：1017+2000-17=3000元。
            虚拟商品甲在订单中的占比是： 1017/（1017+2000）=0.34
            那么，<span class="result">通兑券甲的总价值为：0.34X3000=1011.27元</span>
            因为此时没有兑换任何服务，<span class="result">虚拟商品甲的剩余价值也为1011.27元</span>

            此时仅兑换了1次服务A ，其他服务没有兑换，所有服务也没有核销。
            含有服务A的，服务组合的总计金额最大的组合方式：1次服务A，2次服务B，1次服务C。
            所以服务组合总计金额为：1x200+2x400+1X300=1300元
            1次服务A 在服务组合中的占比为200/1300=0.15
            那么，<span class="result">1次服务A 的价值为0.15X1011.27=151.69元。</span>
            <span class="result">通兑券甲的总价值为1011.27元，通兑券甲的剩余价值为1011.27-151.69=859.58元。</span>

            然后核销了一次服务A，那么，<span class="result">一次服务A的总价值为151.69元，剩余价值为151.69-151.69=0元</span>
          `,
        },
      ],
    };
  },
  methods: {
    show(data) {
      console.log(data);
      this.visible = true;
      this.title = data.title;
      this.width = data.width;
      this.close = data.close;
    },
    hide() {
      this.visible = false;
      this.close();
      this.close = null;
    },
    changeVisible(val) {
      !val && this.hide();
    },
  },
};
</script>

<style lang="less" scoped>
.content-wrapper {
  max-height: 520px;
  overflow-y: auto;
  font-size: 14px;
  .value-explanation {
    margin-bottom: 24px;

    .title {
      font-weight: 600;
      font-size: 15px;
      color: #303133;
      line-height: 24px;
      margin-bottom: 12px;
    }

    .label {
      width: 70px;
      font-weight: 500;
      text-align: right;
      display: inline-block;
    }

    .value {
      font-size: 14px;
      color: #303133;
      line-height: 22px;
      margin-left: 16px;
      margin-bottom: 8px;
    }

    &:last-child {
      padding-bottom: 12px;
      border-bottom: 1px solid #ebedf0;
    }
  }

  .extra-box {
    font-size: 14px;
    color: #303133;
    line-height: 22px;

    .title {
      margin-bottom: 12px;
    }

    .extra-content {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      margin-left: 16px;

      .dot {
        width: 6px;
        height: 6px;
        background: #3088ff;
        border-radius: 50%;
        margin-right: 8px;
      }
    }
  }

  .cases-box {
    color: #303133;
    line-height: 22px;

    .case-tip-title {
      margin-top: 30px;
      margin-bottom: 13px;
    }

    .case-title {
      font-weight: 600;
      margin-bottom: 13px;
    }

    .case-item {
      background: #f9fafb;
      border-radius: 4px;
      padding: 16px;
      margin-bottom: 25px;
      &:last-child {
        margin-bottom: 0px;
      }

      .case-content {
        white-space: pre-line;
        display: inline-flex;

        .case-text {
          color: #303133;
          line-height: 26px;
          flex: 1;
        }

        ::v-deep .result {
          color: #3088ff;
        }
      }
    }
  }
}
</style>
