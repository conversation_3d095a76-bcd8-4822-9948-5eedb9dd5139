<template>
  <div>
    <div class="card-item">
      <div class="card-title">
        <div class="card-title-border"></div>
        <div class="card-title-text">用户信息</div>
      </div>
      <div class="card-content">
        <div class="card-content-item" style="width: 100%">
          <div class="content-item-label">用户：</div>
          <div class="content-item-text" style="overflow: unset">
            <div class="search-box" ref="searchBoxRef">
              <div class="current-user" v-if="user?.uid">
                <div class="current-user-left">
                  <div
                    class="avatar-box"
                    :class="{ 'vip-avatar-box': user?.vip_info?.length > 0 }"
                    :style="{ borderColor: getVipBorderColor(user.vip_info) }"
                  >
                    <img v-if="user?.vip_info?.length" class="vip-icon" :src="getVipIcon(user.vip_info)" />
                    <img
                      class="avatar"
                      :src="
                        user.avatar
                          | imageStyle(
                            'B.w300',
                            'https://img-sn-i01s-cdn.rsjxx.com/image/2024/1218/095042_17701.png-B.w300'
                          )
                      "
                    />
                  </div>

                  <div class="user-info">
                    <div class="user-info-top">
                      <div class="user-info-name">{{ user.real_name || user.name }}</div>
                      <div class="user-info-sex">
                        <span v-if="user.age && user.sex_text">（</span>
                        <span>{{ user.sex_text || user.sex_desc }}</span>
                        <span v-if="user.age && user.sex_text"> | </span>
                        <span>{{ user.age ? `${user.age}岁` : '' }}</span>
                        <span v-if="user.age && user.sex_text">）</span>
                      </div>
                    </div>
                    <div class="user-info-mobile-box">
                      <div class="info-mobile">{{ user.mobile }}</div>
                      <div class="info-stage-mobile" v-if="user.show_staging_mobile == '1'">
                        <span class="stage-tag">暂存</span>
                        <span class="stage-mobile">{{ user.staging_mobile }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="delete-user-icon-box" v-if="!detail?.user?.uid">
                  <Tooltip content="移除" placement="top">
                    <img
                      class="delete-user-icon"
                      @click="deleteUserInfo"
                      src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/102709_78163.png"
                    />
                  </Tooltip>
                </div>
              </div>
              <div class="create-user-wrap" v-else>
                <el-autocomplete
                  class="custom-user-autocomplete"
                  ref="custom"
                  v-model="nickname"
                  style="width: 400px"
                  :debounce="600"
                  :popper-append-to-body="false"
                  :fetch-suggestions="querySearchAsync"
                  :trigger-on-focus="true"
                  @blur="blur"
                  placeholder="输入用户姓名、手机号搜索"
                  @select="handleSelect"
                >
                  <template slot-scope="{ item }">
                    <div class="autocomplete" v-if="!item.empty" style="white-space: pre-wrap">
                      <div
                        class="avatar-box"
                        :class="{ 'vip-avatar-box': item.vip_info.length > 0 }"
                        :style="{ borderColor: getVipBorderColor(item.vip_info) }"
                      >
                        <img v-if="item.vip_info.length > 0" class="vip-icon" :src="getVipIcon(item.vip_info)" />
                        <img
                          class="avatar-icon"
                          :src="
                            item.avatar
                              | imageStyle(
                                'B.w300',
                                'https://img-sn-i01s-cdn.rsjxx.com/image/2024/1218/095042_17701.png-B.w300'
                              )
                          "
                        />
                      </div>
                      <div class="info-content">
                        <span class="name">{{ item.patient_name }}</span>
                        <span class="info">
                          <span>{{ item.sex_text }}</span>
                          <span v-if="item.age && item.sex_text"> | </span>
                          <span>{{ item.age ? `${item.age}岁` : '' }}</span>
                        </span>
                        <span class="mobile">{{ item.mobile }}</span>
                        <span class="stage-mobile-box" v-if="item.show_staging_mobile === '1'">
                          <span class="stage-icon">暂存</span>
                          <span class="stage-mobile">{{ item.staging_mobile }}</span>
                        </span>
                      </div>
                    </div>
                    <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>
                      <p class="flex flex-c">
                        <span>{{ nickname }}</span>
                        <span class="tip">尚无该用户</span>
                      </p>
                      <a>创建用户</a>
                    </div>
                  </template>
                </el-autocomplete>
                <div class="create-user" @click.stop="creatConsumer">
                  <svg-icon name="plus" :size="12" />
                  创建用户
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div :loading="gerReserveLoading">
      <div v-for="(card, index) in cards" :key="index">
        <div class="line"></div>
        <div class="card-item pb-12">
          <div class="card-title">
            <div class="card-title-border"></div>
            <div class="card-title-text">服务信息{{ cards.length > 1 ? index + 1 : '' }}</div>
          </div>
          <div class="card-content">
            <div
              class="card-content-item"
              v-if="reservesList(card).length > 0 && user.uid"
              style="width: 100%; align-items: flex-start"
            >
              <div class="content-item-label" style="margin-top: 4px">关联预约单：</div>
              <div class="content-item-text">
                <RadioGroup v-model="card.reserve_id" @on-change="val => handleSelectReserve(val, index)" vertical>
                  <Radio
                    :label="item.id"
                    v-for="item in reservesList(card)"
                    :key="item.id"
                    :disabled="isDisabledReserve(item, card)"
                  >
                    <span>预约到店时间：</span>
                    <span style="color: #333333">{{ item.reserve_at || '-' }}</span>
                    <span style="margin-left: 12px">预约单号：</span>
                    <a v-if="item.code" @click.stop.prevent="toReserveDetail(item)">{{ item.code }}</a>
                    <span v-else>-</span>
                  </Radio>
                  <Radio label="empty">不关联</Radio>
                </RadioGroup>
              </div>
            </div>
            <div class="card-content-item">
              <div class="content-item-label">服务名称：</div>
              <div class="content-item-text">
                {{ card.service_name || '-' }}
              </div>
            </div>
            <div class="card-content-item">
              <div class="content-item-label">服务类型：</div>
              <div class="content-item-text">
                {{ card.inner_type_desc || '-' }}
              </div>
            </div>
            <div class="card-content-item">
              <div class="content-item-label">所属商品/问诊：</div>
              <div class="content-item-text">
                {{ card.goods_name || '-' }}
              </div>
            </div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-title">
            <div class="card-title-border"></div>
            <div class="card-title-text">理疗师信息{{ cards.length > 1 ? index + 1 : '' }}</div>
          </div>
          <div class="card-content">
            <div class="card-content-item">
              <div class="content-item-label">服务人：</div>
              <div class="content-item-text">
                <Select
                  @on-change="val => selectedTechnician(val, index)"
                  v-model="card.physio_id"
                  style="width: 240px"
                  placeholder="请选择服务人"
                  clearable
                  :transfer="false"
                >
                  <Option v-for="(item, index) in card.technicianList" :value="item.id" :key="index">
                    {{ item.name_info }}
                  </Option>
                </Select>
              </div>
            </div>
            <!--            <div class="card-content-item">-->
            <!--              <div class="content-item-label">等级/比例：</div>-->
            <!--              <div class="content-item-text">-->
            <!--                <div class="readonly-box">-->
            <!--                  <template v-if="is_980_goods === '1'"> 固定分成 </template>-->
            <!--                  <template v-else>-->
            <!--                    <span v-if="card.level">{{ card.level_text || '-' }}</span>-->
            <!--                    <span v-if="card.level">/</span>-->
            <!--                    <span>{{ +card.ratio ? card.ratio + '%' : '-' }}</span>-->
            <!--                  </template>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--            </div>-->
            <div class="card-content-item">
              <div class="content-item-label">业绩提成：</div>
              <div class="content-item-text">
                <div v-if="card.physio_id" class="readonly-box">
                  <template v-if="is_980_goods === '1'"> 10元 </template>
                  <span v-else>{{ getMoney(card) }}元</span>
                </div>
                <div v-else class="readonly-box">-</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建用户 -->
    <create-user-modal
      :showCreateInfo="optionChange"
      :source-list="sourceList"
      :visible.sync="consumerVisibleDia"
      :level-list="levelList"
      :name="creatName"
    ></create-user-modal>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import S from '@/libs/util';
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';
import { $operator } from '@/libs/operation';
import { openNewPage } from '@/utils/helper';

export default {
  name: 'outVerification',
  components: { CreateUserModal },
  props: {
    detail: {
      type: Object,
      default: () => ({}),
    },
    card_no: {
      type: [Number, String],
      default: '',
    },
  },
  data() {
    return {
      is_out_sales_card: '',
      is_980_goods: '',
      current_user_info: {},
      user: {
        name: '',
        mobile: '',
        uid: '',
        balance: '',
      },
      cards: [],
      nickname: '',
      consumerVisibleDia: false,
      gerReserveLoading: false,
      sourceList: [],
      reserves: [],
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],
      creatName: '',
    };
  },
  computed: {
    reservesList() {
      return card => this.reserves?.filter(item => item.goods_service_id === card.goods_service_id) || [];
    },
    is980ServiceCard() {
      return this.detailInfo.is_980_service_card === '1';
    },
    getVipIcon() {
      return vipInfo => {
        // 980会员与榕粉会员合并使用相同icon
        let isVip = vipInfo?.findIndex(item => item.user_type === '1' || item.user_type === '4') > -1;
        if (isVip) {
          return require(`@/assets/image/order/vip_980.png`);
        }
        let is9800Vip = vipInfo?.findIndex(item => item.user_type === '3') > -1;
        if (is9800Vip) {
          return require(`@/assets/image/order/vip_9800.png`);
        }

        return '';
      };
    },
    getVipBorderColor() {
      return vipInfo => {
        return vipInfo?.findIndex(item => item.user_type === '3') > -1 ? '#FAD88F' : '#B0C3DD';
      };
    },
    getMoney() {
      return card => {
        const technician = card.technicianList?.find(item => item.id === card.physio_id);
        const real_price = +technician?.divide_rules?.real_price || 0;
        const fixed_factor = +technician?.fixed_factor || 1;
        const pay_amount = +card?.pay_amount || 0;
        const isLessThan = pay_amount < real_price;
        const server = isLessThan ? technician?.divide_rules?.lt?.server : technician?.divide_rules?.gte?.server;
        const divide_type = server?.divide_type || 'fixed';
        const divide_value = +server?.divide_value || 0;
        let price = 0;
        if (divide_type === 'fixed') {
          price = $operator.multiply(divide_value, fixed_factor);
        }
        if (divide_type === 'ratio') {
          price = $operator.multiply(divide_value, pay_amount);
          price = $operator.multiply(price / 100, fixed_factor);
        }
        return price;
      };
    },
    isDisabledReserve() {
      return (reserve, card) => {
        const selectedReserve = this.cards?.filter(item => !!item.reserve_id)?.map(item => item.reserve_id) || [];
        return selectedReserve.includes(reserve.id) && reserve.id !== card.reserve_id;
      };
    },
  },
  watch: {
    detail: {
      handler(data) {
        data = cloneDeep(data);
        this.is_out_sales_card = data?.is_out_sales_card || '';
        this.is_980_goods = data?.is_980_goods || '';
        this.pay_amount = data?.pay_amount || '0.00';
        this.pay_at = data?.pay_at || '';
        this.out_goods_name = data?.out_goods_name || '';
        this.cards = data.cards || [];
        this.extra_info = data.extra_info;
        this.user = data.user || this.user;
        for (let i = 0; i < (data?.cards?.length || 0); i++) {
          const item = data?.cards[i];
          this.getOrderRevenueMembers(item.service_id, i);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {
    getOrderRevenueMembers(id, index) {
      this.$api
        .getOrderRevenueMembers({
          type: '1',
          service_id: id,
        })
        .then(res => {
          this.$set(this.cards[index], 'technicianList', res);
        });
    },
    selectedTechnician(val, index) {
      const technician = this.cards[index]?.technicianList?.find(item => item?.id === val) || {};
      this.$set(this.cards[index], 'physio_id', technician.id || '');
      this.$set(this.cards[index], 'ratio', technician.ratio || '');
      this.$set(this.cards[index], 'level', technician.level || '');
      this.$set(this.cards[index], 'level_text', technician.level_text || '');
      this.$set(this.cards[index], 'name', technician.name || '');
      this.$set(this.cards[index], 'user_type', technician.type || '');
    },
    handleSelectReserve(val, index) {
      this.$set(this.cards[index], 'reserve_id', val);
      const reserve = this.reserves?.find(item => item?.id === val) || {};
      if (val === 'empty') {
        this.selectedTechnician('', index);
        return;
      }
      reserve.physio_id && this.selectedTechnician(reserve.physio_id, index);
    },
    deleteUserInfo() {
      this.current_user_info = {};
      this.user.name = '';
      this.user.mobile = '';
      this.nickname = '';
      this.user.uid = '';
      this.user.balance = '';
      this.reserves = [];
      this.cards = this.cards?.map(item => ({
        ...item,
        reserve_id: '',
        physio_id: '',
        ratio: '',
        level: '',
        level_text: '',
        name: '',
        user_type: '',
      }));
    },
    /**
     * @description:远程搜索用户信息
     * */
    querySearchAsync(keyword, cb) {
      this.creatName = cloneDeep(keyword);
      if (this.searchTimes && !keyword) {
        cb(this.userList);
      } else {
        let copyName = this.name || 'none';
        if (keyword !== copyName) {
          this.getUserList({ search: keyword }, cb);
          return;
        } else {
          cb(this.userList);
        }
        this.getUserList({ search: keyword }, cb);
      }
    },
    handleSelect(item) {
      this.current_user_info = item;
      this.user.name = item.patient_name;
      this.user.mobile = item.mobile;
      this.nickname = item.patient_name;
      this.user.uid = item.uid;
      this.user.balance = item.recharge_money;
      this.user.age = item.age;
      this.user.avatar = item.avatar;
      this.user.sex_text = item.sex_text;
      this.user.vip_info = item.vip_info || {};
      this.reserves = [];
      this.gerReserveLoading = true;
      this.$api
        .serviceOutCardReserveList({
          card_no: this.card_no,
          user_id: this.user.uid,
        })
        .then(res => {
          this.reserves = res.reserves || [];
          this.cards = this.cards?.map(item => ({
            ...item,
            reserve_id: '',
            physio_id: '',
            ratio: '',
            level: '',
            level_text: '',
            name: '',
            user_type: '',
          }));
        })
        .finally(() => {
          this.gerReserveLoading = false;
        });
    },
    toReserveDetail(reserve) {
      openNewPage('/reserve/listing/list', {
        code: reserve.code,
      });
    },
    // 当搜索的人不存在时,失焦清除绑定数据,不允许自建
    blur() {
      setTimeout(() => {
        if (!this.user.name) {
          if (!this.consumerVisibleDia) {
            this.nickname = '';
            this.$refs.custom?.getData();
          }
        } else {
          this.nickname = this.user.name;
        }
      }, 200);
    },
    getUserList({ search = '', uid = '' }, cb) {
      return new Promise(resolve => {
        let params = {
          page: 1,
          pageSize: 20,
          search,
          uid: search ? '' : uid,
          is_not_bind_outshop: '1',
        };
        this.searchTimes++;
        if (search) {
          this.searchTimes = 0;
        }
        this.$api.getUserList(params).then(res => {
          resolve(res);
          // 获取用户数据
          this.handleUserList(res.users, cb);
        });
      });
    },
    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      typeof cb === 'function' && cb(data);
    },

    // api-获取options
    getArrivalOptions() {
      this.$api.getArrivalOptions().then(res => {
        // 用户来源
        this.sourceList = S.descToArrHandle(res.userFromDesc);
      });
    },
    // 点击创建用户，显示弹窗
    creatConsumer() {
      this.consumerVisibleDia = true;
      this.$refs.custom.close();
    },

    // 创建用户返回的数据
    optionChange(item) {
      this.user.balance = '';
      this.user.name = item.patient_name;
      this.user.mobile = item.mobile;
      this.nickname = item.patient_name;
      this.user.uid = item.uid;
      this.current_user_info = {
        balance: '',
        nickname: this.nickname,
        ...item,
      };
    },
  },
};
</script>

<style scoped lang="less">
.card-item {
  width: 100%;
  padding: 24px 0;
  .card-title {
    width: 100%;
    display: flex;
    align-items: center;
    .card-title-border {
      width: 3px;
      height: 16px;
      background: #155bd4;
      margin-right: 12px;
    }
    .card-title-text {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
    }
  }
  .card-content {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .card-content-item {
      width: 33.33%;
      margin-top: 20px;
      display: flex;
      align-items: center;
      position: relative;
      .content-item-label {
        width: 120px;
        text-align: right;
        font-size: 13px;
        color: #333333;
        line-height: 20px;
      }
      .content-item-text {
        flex: 1;
        font-size: 13px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .readonly-box {
          margin: 0;
        }
      }
      .content-item-tip {
        padding-left: 120px;
        position: absolute;
        color: red;
        bottom: -32px;
      }
    }
  }
}

.search-box {
  min-height: 44px;
  min-width: fit-content;
  width: 100%;
  display: flex;
  //justify-content: center;
  align-items: center;

  .current-user {
    width: fit-content;
    height: 44px;
    background: #f9fafb;
    border-radius: 4px;
    border: 1px solid #ebedf0;
    padding: 6px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .current-user-left {
      display: flex;
      align-items: center;

      .avatar-box {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        position: relative;
        box-sizing: content-box;
        //border: 1px solid #EBEDF0;
        //background: #EBEDF0;
        .vip-icon {
          width: 24px;
          min-width: 24px;
          height: 12px;
          position: absolute;
          bottom: -5px;
          right: 5px;
        }

        .avatar {
          width: 32px;
          min-width: 32px;
          height: 32px;
          border-radius: 50%;
        }
      }

      .vip-avatar-box {
        border: 1px solid #e7c1a6;
      }

      .user-info {
        display: flex;
        align-items: center;
        margin-left: 16px;

        .user-info-top {
          display: flex;
          align-items: center;

          .user-info-name {
            //font-weight: 600;
            font-size: 14px;
            color: #303133;
            //line-height: 24px;
          }

          .user-info-sex {
            margin-left: 12px;
            font-weight: 400;
            font-size: 14px;
            color: #909399;
            //line-height: 18px;
          }
        }

        .user-info-mobile-box {
          display: flex;
          align-items: center;
          margin-left: 12px;

          .info-mobile {
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 20px;
          }

          .info-stage-mobile {
            margin-left: 20px;
            font-weight: 400;
            font-size: 13px;
            color: #909399;
            line-height: 20px;
            display: flex;
            align-items: center;

            .stage-tag {
              background: #fff3df;
              border-radius: 2px;
              padding: 1px 4px;
              font-weight: 400;
              font-size: 12px;
              color: #ffa300;
              line-height: 18px;
              min-width: fit-content;
            }

            .stage-mobile {
              margin-left: 6px;
              font-weight: 400;
              font-size: 13px;
              color: #909399;
              line-height: 20px;
            }
          }
        }
      }
    }

    .delete-user-icon {
      display: block;
      width: 12px;
      height: 12px;
      margin-left: 10px;
      cursor: pointer;
      margin-top: 4px;

      &:hover {
        transform: scale(1.3);
      }
    }

    .delete-user-icon-box {
      //margin-top: -23px;
      //margin-left: -3px;
      //background: url('https://img-sn-i01s-cdn.rsjxx.com/image/2025/0108/163541_57316.png') no-repeat;
      //background-size: 10px 10px;
      //width: 16px;
      //height: 16px;
      //cursor: pointer;

      //&:hover {
      //  .delete-user-icon {
      //    display: block;
      //  }
      //}
    }
  }

  .autocomplete {
    display: flex;
    align-items: center;
    padding: 8px 0px;

    .avatar-box {
      box-sizing: content-box;
      width: 38px;
      min-width: 38px;
      height: 38px;
      //background: #D8D8D8;
      border-radius: 50%;
      position: relative;

      .vip-icon {
        width: 30px;
        min-width: 30px;
        height: 12px;
        position: absolute;
        bottom: -6px;
        right: 4px;
      }

      .avatar-icon {
        width: 38px;
        min-width: 38px;
        height: 38px;
        border-radius: 50%;
        margin-left: 0px;
        margin-top: 0px;
      }
    }

    .vip-avatar-box {
      border: 1px solid #e7c1a6;
    }

    .info-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .name {
        margin-left: 16px;
        //font-weight: 600;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
      }

      .info {
        margin-left: 12px;
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 18px;
      }

      .mobile {
        margin-left: 12px;
        font-weight: 400;
        font-size: 13px;
        color: #999999;
        line-height: 18px;
      }

      .stage-mobile-box {
        margin-left: 12px;

        .stage-icon {
          padding: 1px 4px;
          background: #fff3df;
          border-radius: 2px;
          font-weight: 400;
          font-size: 12px;
          color: #ffa300;
          line-height: 18px;
          transform: scale(0.8);
        }

        .stage-mobile {
          margin-left: 6px;
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 18px;
        }
      }
    }
  }
}

.line {
  border-top: 1px dashed #ccc;
}
.pb-12 {
  padding-bottom: 12px;
}

.create-user-wrap {
  width: 100%;
  display: flex;
  align-items: center;
  .create-user {
    font-weight: 400;
    font-size: 13px;
    color: #155bd4;
    line-height: 16px;
    margin-left: 24px;
    cursor: pointer;
  }
  .create-user:hover {
    color: #447cdd;
  }
}
</style>
