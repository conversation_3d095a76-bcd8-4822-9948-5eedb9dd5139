<template>
  <div class="list-wrapper">
    <standard-table
      :columns="columns"
      :data="list"
      :loading="tableLoading"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <Col>
              <FormItem label="" :label-width="0">
                <Input v-model="queryFormData.keyword" clearable placeholder="请输入用户姓名/注册名/手机号" />
              </FormItem>

              <FormItem label="">
                <Input v-model="queryFormData.card_no" clearable placeholder="请输入卡券编号" />
              </FormItem>

              <FormItem label="">
                <DatePicker
                  type="daterange"
                  placeholder="核销时间"
                  :value="timeRange"
                  clearable
                  @on-change="times => handleTimeChange(times)"
                ></DatePicker>
              </FormItem>

              <FormItem label="" :label-width="0">
                <Input v-model="queryFormData.out_trade_no" clearable placeholder="请输入订单编号" />
              </FormItem>

              <FormItem label="">
                <Input
                  v-model="queryFormData.artificer_name"
                  clearable
                  :placeholder="is_rst ? '请输入服务人' : '请输入服务理疗师'"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col>
              <FormItem v-if="isDirect">
                <Select v-model="queryFormData.refund_status" placeholder="售后情况" clearable>
                  <Option value="">全部</Option>
                  <Option v-for="(item, key) in refundStatusDesc" :value="item.id" :key="key">{{ item.desc }}</Option>
                </Select>
              </FormItem>

              <FormItem class="">
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <Button type="default" class="mr10" @click="exportExcel" :loading="exportLoading">导出</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </template>

      <template slot-scope="{ row }" slot="perfor_money">
        <span v-if="row.perfor_money">￥{{ row.perfor_money || 0 }}</span>
        <span v-else>-</span>
      </template>

      <!-- 用户 -->
      <template slot-scope="{ row }" slot="user">
        <p class="flex">
          <span class="slot-label">姓名：</span>
          <KLink :to="{ path: '/user/detail', query: { uid: row.user.uid } }" target="_blank">
            {{ (row.user && row.user.real_name) || '-' }}
          </KLink>
        </p>
        <p class="flex">
          <span class="slot-label">注册名：</span>
          <span>{{ (row.user && row.user.nickname) || '-' }}</span>
        </p>
        <p class="flex"><span class="slot-label">手机号：</span>{{ row.user && row.user.mobile }}</p>
      </template>
      <!-- 用户 -->
      <template slot-scope="{ row }" slot="rst_user">
        <KLink :to="{ path: '/user/detail', query: { uid: row.user.uid } }" target="_blank">
          {{ (row.user && row.user.real_name) || '-' }}
        </KLink>
        <div>{{ row.user && row.user.mobile }}</div>
      </template>

      <!-- 服务理疗师 -->
      <template slot-scope="{ row }" slot="artificer">
        <div v-if="is_rst">
          <div v-if="row.divide_info && Number(row.divide_info?.physio_id)">
            <p>
              {{ row.divide_info?.physio_name
              }}<span v-if="row.divide_info?.mdp_permission_name"
                >({{ row.divide_info?.mdp_permission_name }} | {{ row.divide_info?.mdp_level_text }})</span
              >
            </p>
          </div>
          <div v-else>-</div>
        </div>
        <div v-else>
          <div v-if="row.artificer && row.artificer.name">
            <p>理疗师:{{ row.artificer && row.artificer.name }}</p>
            <p>佣金: ￥{{ (row.artificer && row.artificer.amount) || 0 }}</p>
          </div>
          <div v-else>-</div>
        </div>
      </template>
      <template slot-scope="{ row }" slot="out_trade_no">
        <div v-if="!row.out_trade_no">-</div>
        <k-link
          v-else
          :to="{
            path: '/service/card/list',
            query: {
              out_trade_no: row.out_trade_no,
            },
          }"
          target="_blank"
        >
          {{ row.out_trade_no }}
        </k-link>
      </template>
      <template slot-scope="{ row }" slot="trade_no_info">
        <div>卡券号: {{ row.card_no || '-' }}</div>
        <div>
          所属卡券:
          <k-link
            :to="{
              path: '/service/card/list',
              query: {
                out_trade_no: row.out_trade_no,
              },
            }"
            target="_blank"
            >{{ row.out_trade_no }}
          </k-link>
        </div>
        <div>
          预约单号:
          <k-link
            v-if="row.reserve_code"
            :to="{
              path: '/reserve/listing/detail',
              query: {
                id: row.reserve_id,
              },
            }"
            target="_blank"
            >{{ row.reserve_code }}
          </k-link>
          <span v-else>-</span>
        </div>
      </template>
      <!-- 核销人 -->
      <template slot-scope="{ row }" slot="operator">
        <div v-if="is_rst">
          <div>核销人：{{ row.operator?.name || '-' }}</div>
          <div>{{ row.used_time }}</div>
        </div>
        <div v-else>
          <div v-if="row.operator && row.operator.name">{{ row.operator.name }}</div>
          <div v-else>-</div>
        </div>
      </template>

      <!-- 核销时间 -->
      <template slot-scope="{ row }" slot="used_time">
        <span>{{ row.used_time }}</span>
      </template>
      <!-- 核销时间 -->
      <template slot-scope="{ row }" slot="appraise_info">
        <div v-if="Number(row.appraise_info?.id) > 0">
          <div>总体：{{ Number(row.appraise_info?.overall_grade).toFixed(1) }}</div>
          <div>理疗师：{{ Number(row.appraise_info?.technology_grade).toFixed(1) }}</div>
          <div>环境：{{ Number(row.appraise_info?.comfort_grade).toFixed(1) }}</div>
          <div>服务：{{ Number(row.appraise_info?.service_grade).toFixed(1) }}</div>
        </div>
        <div v-else>未评价</div>
      </template>
      <!-- 疗效照片 -->
      <template slot-scope="{ row }" slot="therapy_images">
        <div
          class="flex flex-item-align"
          v-if="row.therapy_before_images?.length > 0 || row.therapy_after_images?.length > 0"
        >
          <div class="media-left media-middle" v-viewer="[row.therapy_before_images[0]]">
            <img :src="row.therapy_before_images[0]" style="width: 35px; object-fit: cover" class="img-rounded" />
          </div>
          <div class="media-left media-middle" v-viewer="[row.therapy_after_images[0]]">
            <img :src="row.therapy_after_images[0]" style="width: 35px; object-fit: cover" class="img-rounded" />
          </div>
          <div v-if="row.therapy_before_images?.length > 1 || row.therapy_after_images?.length > 1">···</div>
        </div>
        <div v-else>-</div>
      </template>

      <template slot-scope="{ row }" slot="action">
        <a v-if="!is_rst" @click="modifyRecord(row)">修改核销记录</a>
      </template>
      <template slot-scope="{ row }" slot="rst_action">
        <!--        -->
        <a v-if="row.therapy_before_images?.length === 0" class="mr10" @click="getUploadImageQrCode(row)">上传</a>
        <a @click="goDetail(row)">详情</a>
      </template>
    </standard-table>

    <!-- 修改核销记录的弹窗 -->
    <Modal v-model="recordVisible" :mask-closable="false" title="修改核销记录">
      <div class="record-content">
        <Form :model="recordParams" ref="recordParams" :label-width="70">
          <Form-item label="服务理疗师">
            <Select v-model="recordParams.artificer_id" placeholder="请选择服务理疗师" clearable>
              <Option v-for="(item, key) in artificerList" :value="item.id" :key="key">{{ item.name }}</Option>
            </Select>
          </Form-item>

          <Form-item label="理疗师分佣" prop="amount">
            <Input-number
              v-model="recordParams.amount"
              placeholder="请输入本次服务理疗师可获得的佣金"
              :min="0"
              :precision="2"
              :active-change="false"
            ></Input-number>
          </Form-item>
        </Form>
      </div>
      <div slot="footer">
        <Button type="default" @click="recordCancel">取消</Button>
        <Button type="primary" :loading="recordConfirmLoading" @click="recordConfirm">确定</Button>
      </div>
    </Modal>
    <rstUploadModal
      :visible.sync="uploadVisible"
      :qr-src="qrSrc"
      :reserve-id="reserve_id"
      @refresh="getsList"
    ></rstUploadModal>
  </div>
</template>

<script>
import S from '@/libs/util';
import renderHeader from '@/mixins/renderHeader';
import renderClickHeader from '../mixins/renderClickHeader';

import search from '@/mixins/search'; // Some commonly used tools
import { isDirectClinic, isRstClinic } from '@/libs/runtime';
import rstUploadModal from './components/rstUploadModal.vue';
import StandardTable from '@/components/StandardTable/index.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '', // 用户姓名/注册名/手机号
  card_no: '', // 卡券编号
  out_trade_no: '', // 订单编号
  st: '', // 开始时间
  et: '', // 结束时间
  artificer_name: '', // 服务理疗师
  refund_status: '', // 售后状态
  r: '',
};
export default {
  name: 'list',
  components: { StandardTable, rstUploadModal },
  mixins: [search, renderHeader, renderClickHeader],
  props: {},
  data() {
    return {
      queryFormData: { ...init_query_form_data }, // 列表查询条件
      apiName: 'getWriteofflist',
      st_options: {},
      et_options: {},
      total: 0,
      tableLoading: false, // 表格loading
      exportLoading: false, // 导出excel按钮的loading

      detailColumns: [
        { title: '券号', key: 'card_no' },
        { title: '服务项目', key: 'card_name' },
        {
          title: '价值',
          slot: 'perfor_money',
          renderHeader: (h, params) => this._renderClickHeader(h, params),
          // isRstClinic() ? h('div', params.column.title) : this._renderClickHeader(h, params),
        },
        { title: '用户', slot: 'user', minWidth: 100 },
        { title: '所属订单', slot: 'out_trade_no' },
        { title: '服务理疗师', slot: 'artificer' },
        { title: '核销人', slot: 'operator' },
        { title: '核销时间', slot: 'used_time' },
        { title: '操作', slot: 'action' },
      ], // 明细columns

      directDetailColumns: [
        { title: '券号', key: 'card_no' },
        { title: '服务项目', key: 'card_name' },
        {
          title: '价值',
          slot: 'perfor_money',
          renderHeader: (h, params) => this._renderClickHeader(h, params),
          // isRstClinic() ? h('div', params.column.title) : this._renderClickHeader(h, params),
        },
        { title: '用户', slot: 'user', minWidth: 100 },
        { title: '所属订单', slot: 'out_trade_no' },
        { title: '服务理疗师', slot: 'artificer' },
        { title: '核销人', slot: 'operator' },
        { title: '核销时间', slot: 'used_time' },
        { title: '售后情况', key: 'refund_status_text' },
        { title: '操作', slot: 'action' },
      ], // 明细columns
      rstColumns: [
        { title: '所属用户', slot: 'rst_user', width: 120 },
        { title: '服务项目', key: 'card_name', width: 140 },
        {
          title: '价值',
          slot: 'perfor_money',
          width: 120,
          renderHeader: (h, params) => this._renderClickHeader(h, params),
          // isRstClinic() ? h('div', params.column.title) : this._renderClickHeader(h, params),
        },
        { title: '单号信息', slot: 'trade_no_info', width: 260 },
        { title: '服务人信息', slot: 'artificer', minWidth: 100 },
        { title: '核销信息', slot: 'operator', width: 140 },
        { title: '售后情况', key: 'refund_status_text', width: 100 },
        {
          title: '评价情况',
          slot: 'appraise_info',
          width: 150,
          renderHeader: (h, params) => this._renderHeader(h, params, '用户在榕树堂小程序对订单的评价情况'),
        },
        {
          title: '疗效照片',
          slot: 'therapy_images',
          width: 150,
          renderHeader: (h, params) => this._renderHeader(h, params, '支持上传用户理疗前后的照片用于疗效对比'),
        },
        { title: '操作', slot: 'rst_action', width: 100, fixed: 'right' },
      ], // 明细columns
      detailList: [], // 理疗师table列表信息

      /* 弹窗修改核销记录 */
      recordVisible: false, // 修改核销记录的弹窗标识
      typeList: [], // 服务类型数据
      artificerList: [], // 理疗师下拉列表数据
      enableArtificerList: [], // 用于修改卡券核销记录的理疗师列表-drop
      recordConfirmLoading: false, // 修改核销记录的确定按钮的loading
      recordParams: {
        artificer_id: '', // 理疗师
        amount: null, // 理疗师分佣
        id: '', // 要修改的理疗师的id
      }, // 修改核销记录弹窗数据
      refundStatusDesc: [],
      uploadVisible: false,
      qrSrc: '',
      reserve_id: '',
    };
  },
  computed: {
    isDirect() {
      return isDirectClinic();
    },
    is_rst() {
      return isRstClinic();
    },
    columns() {
      if (this.is_rst) {
        return this.rstColumns;
      }
      if (this.isDirect) {
        return this.directDetailColumns;
      }
      return this.detailColumns;
    },
  },
  watch: {},
  created() {
    // 获取理疗师list
    this.getWriteArtificerlist();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {
    this.getServiceCardOptions();
  },
  methods: {
    /**
     * @description:修改核销记录功能
     */
    // 修改核销记录
    modifyRecord(row) {
      this.recordVisible = true;
      this.recordParams = {
        ...this.recordParams,
        ...{
          id: row.id,
          amount: Number((row.artificer && row.artificer.amount) || 0),
          artificer_id: row.artificer_id || '',
        },
      };
    },
    // 修改核销记录确定事件
    recordConfirm() {
      const { artificer_id, amount } = this.recordParams;
      // 理疗师可以为空
      // if (!Boolean(artificer_id)) {
      //   this.$Message.error('尚未指定服务理疗师')
      //   return
      // }
      this.recordConfirmLoading = true;
      let params = {
        ...this.recordParams,
        artificer_id: artificer_id == undefined ? '' : artificer_id,
        amount: artificer_id ? (amount == null ? 0 : amount) : 0,
      };

      this.$api
        .updateChangecheckin(params)
        .then(() => {
          this.submitQueryForm();
          this.recordVisible = false;
          this.$Message.success('核销记录修改成功');
        })
        .finally(() => {
          this.recordConfirmLoading = false;
        });
    },
    // 修改核销记录取消事件
    recordCancel() {
      this.recordVisible = false;
    },
    // api-获取理疗师列表数据
    getWriteArtificerlist() {
      this.$api.getWriteArtificerlist().then(res => {
        this.artificerList = res.artificer_list;
      });
    },

    // 导出excel
    exportExcel() {
      this.exportLoading = true;
      let params = this.queryFormData;
      this.$delete(params, 'page');
      this.$delete(params, 'pageSize');
      this.$api
        .exportwriteoff(params)
        .then(res => {
          this.download(res.url);
        })
        .finally(() => (this.exportLoading = false));
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    // 重置搜索条件
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    // api-获取options
    getServiceCardOptions() {
      this.$api.getServiceCardOptions().then(res => {
        this.refundStatusDesc = S.descToArrHandle(res.refundStatusDesc);
      });
    },
    getUploadImageQrCode(row) {
      let params = {
        id: row.id,
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api.getUploadImageQrCode(params).then(res => {
        console.log('=>(detail.vue:242) res', res);
        this.reserve_id = row.id;
        this.qrSrc = res.url;
        this.uploadVisible = true;
      });
    },
    goDetail(row) {
      this.$router.push({
        path: '/service/writeDetail/detail',
        query: { id: row.id },
      });
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.mb10 {
  margin-bottom: 10px;
}

.mt-25 {
  margin-top: -25px;
}

/deep/ .ivu-input-number {
  width: 100%;
}

p {
  margin: 0;
}

.ml10 {
  margin-left: 10px;
}

.slot-label {
  width: 60px;
  min-width: 60px;
  text-align: right;
  color: #aaa;
}
</style>

<style lang="less">
.custom_white_space {
  .ivu-tooltip-inner {
    white-space: pre-wrap;
  }
}
</style>
