<template>
  <Modal
    :value="visible"
    width="450px"
    title="上传照片"
    @on-cancel="cancel"
    @on-visible-change="visibleChange"
    footer-hide
    class-name="vertical-center-modal"
    :mask-closable="false"
  >
    <div class="container">
      <div v-show="!isUploadSuccess">
        <div style="text-align: center">请扫码上传照片</div>
        <div class="qrCode" id="qrCode" ref="QrCode">
          <img :src="qrCodeSrc" alt="" style="width: 170px; height: 170px" />
        </div>
      </div>
      <div v-show="isUploadSuccess" class="success-mask">
        <Icon type="md-checkmark-circle" color="#579f38" size="30" />
        上传成功
      </div>
    </div>
  </Modal>
</template>

<script>
import S from 'libs/util';
// import createQrCode from 'mixins/createQrCode';
import createQrCode from '@/mixins/creatQrCode';
import html2canvas from 'html2canvas';
// import { saveAs } from 'file-saver'
// import Canvas2Image from '@/utils/canavs2img'
const initFormData = {
  code: '', //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'modal',
  mixins: [createQrCode],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    qrSrc: {
      type: String,
      default: '',
    },
    reserveId: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      formData: { ...initFormData },
      qrCodeSrc: '',
      downloadUrl: '',
      downLoading: false,
      uploadNotice: null,
      isUploadSuccess: false,
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    cancel() {
      this.$emit('update:visible', false);
      this.$emit('cancel');
    },
    visibleChange(val) {
      if (val) {
        this.qrCodeSrc = this._creatQrCode(this.qrSrc, 'QrCode', { QR_width: 300, QR_height: 300 }, true);
        console.log('=>(rstUploadModal.vue:85) 弹框打开', this.uploadNotice);
        this.uploadNotice = new BroadcastChannel('homeNotice');
        console.log('=>(rstUploadModal.vue:86) this.uploadNotice', this.uploadNotice);
        this.uploadNotice.addEventListener('message', this.handleUploadNotice);
      } else {
        if (this.uploadNotice) {
          typeof this.uploadNotice.close === 'function' && this.uploadNotice.close();
          typeof this.uploadNotice.removeEventListener === 'function' &&
            this.uploadNotice.removeEventListener('message', this.handleUploadNotice);
          this.uploadNotice = null;
          console.log('=>(rstUploadModal.vue:94) 关闭销毁', this.uploadNotice);
        }

        if (this.isUploadSuccess) {
          this.isUploadSuccess = false;
          this.$emit('refresh');
        }
      }
    },

    handleUploadNotice(e) {
      console.log(e.data, 'notice页面收到通知e.data');
      let reserve_id = e.data.data.reserve_id;
      console.log('=>(rstUploadModal.vue:99) reserve_id', reserve_id);
      console.log('=>(rstUploadModal.vue:100) this.reserveId', this.reserveId);
      if (this.reserveId === reserve_id) {
        this.isUploadSuccess = true;
      }
    },
    // 通过a标签下载
    download(url) {
      console.log('-> %c url  === %o', 'font-size: 15px;color: green;', url);
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      downloadLink.setAttribute('download', '活动二维码' + '.png');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 15px;
  height: 300px;

  .qrCode {
    //width: 170px;
    //height: 170px;
    background: #ffffff;
    padding: 12px;

    img {
      margin: 15px;
    }
  }

  .text {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.75);
    text-align: center;
  }

  .activity_name {
    width: 170px;
    margin-top: 5px;
    text-align: center;
  }
}
::v-deep .qrCode {
  position: relative;
  img {
    width: 170px;
    height: 170px;
  }
}
.success-mask {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  //background-color: rgb(255, 255, 255);
  //color: greenyellow;
  font-size: 20px;
}
</style>

<style lang="less">
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}
</style>
