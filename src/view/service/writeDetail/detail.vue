<template>
  <div>
    <Form label-position="right" :label-width="65" label-colon>
      <div class="block-header"><span>核销信息</span></div>

      <div class="basic-form-item">
        <Row :gutter="40">
          <Col span="8">
            <FormItem label="卡券号">{{ formData.card_no || '-' }}</FormItem>
          </Col>
          <Col span="8">
            <FormItem label="服务项目">{{ formData.card_name || '-' }}</FormItem>
          </Col>
          <Col span="8">
            <FormItem label="服务人"
              >{{ formData.divide_info?.physio_name
              }}<span v-if="formData.divide_info?.mdp_permission_name"
                >({{ formData.divide_info?.mdp_permission_name }} | {{ formData.divide_info?.mdp_level_text }})</span
              >
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="业绩提成">
              <span v-if="formData.divide_info?.money">￥{{ formData.divide_info?.money || 0 }}</span>
              <span v-else>-</span>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="注册名">{{ formData.user?.nickname || '-' }}</FormItem>
          </Col>
          <Col span="8">
            <FormItem label="姓名">
              <KLink :to="{ path: '/user/detail', query: { uid: formData.user?.uid } }" target="_blank">
                {{ (formData.user && formData.user.real_name) || '-' }}
              </KLink>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="手机号">{{ formData.user?.mobile }}</FormItem>
          </Col>
          <Col span="8">
            <FormItem label="核销人">{{ formData.operator?.name || '-' }}</FormItem>
          </Col>
          <Col span="8">
            <FormItem label="核销时间">{{ formData.used_time || '-' }}</FormItem>
          </Col>
          <Col span="8">
            <FormItem label="所属卡券"
              ><k-link
                :to="{
                  path: '/service/card/list',
                  query: {
                    out_trade_no: formData.out_trade_no,
                  },
                }"
                target="_blank"
                >{{ formData.out_trade_no }}
              </k-link></FormItem
            >
          </Col>
          <Col span="8">
            <FormItem label="预约单号"
              ><k-link
                v-if="formData.reserve_code"
                :to="{
                  path: '/reserve/listing/detail',
                  query: {
                    id: formData.reserve_id,
                  },
                }"
                target="_blank"
                >{{ formData.reserve_code }}
              </k-link></FormItem
            >
          </Col>
        </Row>
      </div>

      <div class="block-header"><span>疗效对比</span></div>
      <Row>
        <Col span="24">
          <FormItem label="理疗前">
            <div class="flex flex-item-align" v-if="formData.therapy_before_images?.length > 0">
              <div class="media-left media-middle" v-viewer="formData.therapy_before_images">
                <img
                  v-for="item in formData.therapy_before_images"
                  :src="item"
                  style="width: 100px; height: 100px; object-fit: contain; margin-right: 10px"
                  class="img-rounded"
                />
              </div>
            </div>
            <div v-else>-</div>
          </FormItem>
        </Col>
        <Col span="24">
          <FormItem label="理疗后">
            <div class="flex flex-item-align" v-if="formData.therapy_after_images?.length > 0">
              <div class="media-left media-middle" v-viewer="formData.therapy_after_images">
                <img
                  v-for="item in formData.therapy_after_images"
                  :src="item"
                  style="width: 100px; height: 100px; object-fit: contain; margin-right: 10px"
                  class="img-rounded"
                />
              </div>
            </div>
            <div v-else>-</div>
          </FormItem>
        </Col>
      </Row>

      <div class="block-header"><span>用户评价</span></div>
      <Row>
        <Col span="12">
          <FormItem label="评分情况">
            <div v-if="Number(formData?.appraise_info?.id) > 0">
              <div class="flex">
                <div class="appraise-label">总体</div>
                <Rate disabled v-model="formData.appraise_info.overall_grade" />
              </div>
              <div class="flex">
                <div class="appraise-label">理疗师</div>
                <Rate disabled v-model="formData.appraise_info.technology_grade" />
              </div>
              <div class="flex">
                <div class="appraise-label">环境</div>
                <Rate disabled v-model="formData.appraise_info.comfort_grade" />
              </div>
              <div class="flex">
                <div class="appraise-label">服务</div>
                <Rate disabled v-model="formData.appraise_info.service_grade" />
              </div>
            </div>
            <div v-else>-</div>
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem label="评论">{{ formData?.appraise_info?.comment || '-' }}</FormItem>
        </Col>
      </Row>
    </Form>
    <div class="fixed-bottom-wrapper">
      <Button v-if="formData.therapy_before_images?.length > 0" class="mr10" @click="reUploadImg">重传</Button>
      <back-button class="mr10"></back-button>
      <Button v-if="formData.therapy_before_images?.length === 0" type="primary" @click="uploadImg">上传</Button>
    </div>
    <rstUploadModal
      :visible.sync="uploadVisible"
      :qr-src="qrSrc"
      :reserve-id="reserve_id"
      @refresh="getServiceCardDetail"
    ></rstUploadModal>
  </div>
</template>

<script>
import rstUploadModal from './components/rstUploadModal.vue';
export default {
  name: 'detail',

  components: { rstUploadModal },
  props: {},
  data() {
    return {
      formData: {
        appraise_info: {},
      },
      reserve_id: '',
      uploadVisible: false,
      qrSrc: '',
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getUploadImageQrCode();
    if (this.$route.query.id) {
      this.getServiceCardDetail();
    }
  },
  methods: {
    getServiceCardDetail() {
      let params = {
        id: this.$route.query.id,
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api.getServiceCardDetail(params).then(res => {
        console.log('=>(detail.vue:242) res', res);
        this.formData = res;
        this.formData.appraise_info.technology_grade = Number(res.appraise_info.technology_grade);
        this.formData.appraise_info.service_grade = Number(res.appraise_info.service_grade);
        this.formData.appraise_info.overall_grade = Number(res.appraise_info.overall_grade);
        this.formData.appraise_info.overall_grade = Number(res.appraise_info.overall_grade);
      });
    },
    reUploadImg() {
      this.$Modal.confirm({
        title: '提示',
        content: '请确认是否重新上传理疗前后照片？',
        onOk: () => {
          this.uploadVisible = true;
        },
        onCancel: () => {},
      });
    },
    uploadImg() {
      this.uploadVisible = true;
    },
    getUploadImageQrCode() {
      let params = {
        id: this.$route.query.id,
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api.getUploadImageQrCode(params).then(res => {
        console.log('=>(detail.vue:242) res', res);
        this.reserve_id = this.$route.query.id;
        this.qrSrc = res.url;
      });
    },
  },
};
</script>
<style scoped lang="less">
.appraise-label {
  width: 40px;
}
</style>
