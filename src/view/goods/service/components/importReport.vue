<template>
  <div>
    <Modal
      ref="editActivityModal"
      :value="visible"
      :title="'导入分佣金额'"
      :mask-closable="false"
      width="500px"
      @on-cancel="cancel"
      @on-visible-change="changeVisible"
    >
      <div class="import-schedule">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <div class="step-title">下载表单模版</div>
            <div class="step-desc">
              <p>· 按照<a>模版示例.xlsx</a>在模版内录入数据</p>
              <p>· 只写入第一张工作表(sheet1)</p>
              <p>· 请勿修改表格标题，防止导入失败</p>
              <p>· 下载的表单模板为诊所自定义的服务</p>
            </div>
            <div style="text-align: center">
              <Button type="primary" ghost @click="downloadTemplate">下载表单模版</Button>
            </div>
          </div>
        </div>
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <div class="step-title">上传文件</div>
            <div class="step-desc">
              <p>· 请上传*.xls，*.xlsx格式文件</p>
              <p>· 当前版本最多只允许上传500条数据切文件大小不超过10M</p>
            </div>
            <div style="text-align: center">
              <RExcelUpload
                v-if="visible"
                :disabled="isUploadOnly && (errorList.length > 0 || successList.length > 0)"
                ref="excelUpload"
                btnType="button"
                btnText="上传文件"
                @excel-upload="excelUpload"
              />
            </div>

            <!-- 错误报告 -->
            <div
              class="error-report flex flex-item-center"
              style="color: #aaaaaa"
              v-show="errorList.length || successList.length"
            >
              <p><span></span>本次导入结果：成功{{ succ_num }}条数据，失败{{ fail_num }}条数据</p>
              <!--          ，<span v-if="errorList.length">请修改正确后再导入</span>-->
              <p class="download-error cursor hover" v-show="Number(fail_num) > 0" @click="seeReport">查看错误报告</p>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <Button @click="cancel">{{ importDisabled ? '关闭' : '取消' }}</Button>
        <Button
          type="primary"
          @click="importConfirm"
          :loading="excelUploadLoading"
          :disabled="importDisabled || !validExcelList.length"
          >导入</Button
        >
      </div>
    </Modal>
    <!-- 查看错误报告 -->
    <Modal v-model="reportVisible" :mask-closable="false" width="750" :hide-footer="true" title="错误报告">
      <div class="report-content">
        <Table :columns="reportColumn" :data="errorList" height="400"></Table>
      </div>
      <div slot="footer">
        <Button @click="reportVisible = false">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'libs/util';
import RExcelUpload from '@/components/k-excel-upload/RExcelUploadV2.vue';
import { getPhysioName } from '@/libs/runtime';

export default {
  name: 'importSchedule',
  components: { RExcelUpload },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    excelKeyMap: {
      type: Object,
      default: () => {},
      required: true,
    },
    // 是否上传唯一
    isUploadOnly: {
      type: Boolean,
      default: false,
    },
    // 前置条件(必须使用promise，提供loading关闭条件)。默认无前置条件
    precondition: {
      type: Function,
      default: () =>
        new Promise(resolve => {
          resolve(true);
        }),
    },
    importApiName: {
      type: String,
      default: 'importGoodsServiceTemplate',
      required: true,
    },
    chunkNum: {
      type: Number,
      default: 50,
    },
    restParams: {
      type: Object,
      default: () => {},
    },
    startDate: {
      type: String,
      default: '',
    },
    endDate: {
      type: String,
      default: '',
    },
    is_rst: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      submitLoading: false,
      fileList: [],
      errorList: [],
      successList: [],
      succ_num: 0,
      fail_num: 0,
      reportVisible: false,
      reportColumn: [
        { title: '序号', type: 'index', align: 'center' },
        {
          title: '服务ID',
          key: 'goods_service_id',
          align: 'center',
        },
        { title: '固定分账金额', key: 'fixed_divide_value', align: 'center' },
        { title: '失败原因', key: 'fail_msg', align: 'center' },
      ],
      importDisabled: false,
      excelUploadLoading: false,
      validExcelList: [],
    };
  },
  computed: {
    getPhysioName() {
      return getPhysioName();
    },
  },

  methods: {
    changeVisible(val) {
      if (!val) {
        this.fileList = [];
      }
    },
    cancel() {
      this.initImportNums();
      this.$emit('update:visible', false);
    },
    downloadTemplate() {
      this.$api.getgoodsServiceImportTemplate({}).then(res => {
        console.log('=>(importSchedule.vue:140) res', res);
        this.download(res.url);
      });
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
    excelUpload(excelList) {
      console.log('-> %c excelList  === %o', 'font-size: 15px;color: green;', excelList);
      this.validExcelList = [];
      if (!excelList.length) {
        this.$Message.error('请上传正确的表格');
        return;
      }

      // 从exelKeyMap获取对应字段key
      const excelKeys = Object.keys(this.excelKeyMap);
      let requiredKeys = [];
      let resultList = [];

      console.log(excelKeys, 'excelKeys')
      excelList.forEach(item => {
        let temObj = {};
        let dateList = [];
        // 检查是否是"年/月/日"格式
        const chineseFormat = /^\d{4}年\d{1,2}月\d{1,2}日$/;

        // 检查是否是"月/日/年"格式
        const slashFormat = /^\d{4}\/\d{1,2}\/\d{1,2}$/;

        for (const field in item) {
          console.log(excelKeys.includes(field), field, 'field')
          // 检查是否是excelKeyMap中的标准字段
          if (excelKeys.includes(field)) {
            const { key, type = 'string', required = false } = this.excelKeyMap[field];

            if (required && !requiredKeys.includes(key)) {
              requiredKeys.push(key);
            }

            if (item[field] || item[field] === 0) {
              temObj[key] = item[field];
              if (type === 'date') {
                temObj[key] = this.handleExcelDate(item[field]);
              }
            } else {
              temObj[key] = '';
            }
          }
          // 处理由xlsx方法导致时间的表头被转换成了别的格式
          else {
            if (chineseFormat.test(field)) {
              // 已经是正确格式，直接返回
              dateList.push({
                [field]: item[field],
              });
            } else if (slashFormat.test(field)) {
              // 将"日/月/年"转换为"年/月/日"格式
              const [year, month, day] = field.split('/');
              let fieldFormat = `${year}年${parseInt(month, 10)}月${parseInt(day, 10)}日`;
              // 已经是正确格式，直接返回
              dateList.push({
                [fieldFormat]: item[fieldFormat],
              });
            } else if (/^\d{1,2}\/\d{1,2}\/\d{2}$/.test(field)) {
              const [month, day, shortYear] = field.split('/');
              const fullYear = this.convertShortYear(shortYear);
              if (this.isValidDate(fullYear, month, day)) {
                let fieldFormat = `${fullYear}年${month.padStart(2, '0')}月${day.padStart(2, '0')}日`;
                // 已经是正确格式，直接返回
                dateList.push({
                  [fieldFormat]: item[field],
                });
              }
            } else {
              console.log('=>(importSchedule.vue:234) 存在异常表头');
              // return;
            }
          }
        }
        resultList.push(temObj);
      });

      console.log('-> resultList', resultList);
      let falseList = [];
      const validExcelList = resultList.filter(item => {
        return requiredKeys.every(key => {
          // 检索必填字段未填的错误数据
          if (this.isRequiredCheck && !(item[key] || item[key] === 0)) {
            falseList.push(item);
          }
          return item[key] || item[key] === 0;
        }); // 兼容为0项
      });

      if (this.isRequiredCheck && falseList.length > 0) {
        let prodNameList = [];
        falseList.forEach(item => {
          prodNameList.push(item[this.errorKey]);
        });
        let errorStr = '';
        if (prodNameList.length > 4) {
          errorStr = prodNameList.slice(0, 4).join(',') + '等......';
        } else {
          errorStr = prodNameList.join(', ');
        }
        this.$Message.error({
          content: `产品信息有误，请检查表格内容。${falseList.length}个错误产品：${errorStr}`,
          duration: 3,
        });
        return;
      }

      this.validExcelList = [...validExcelList];
      if (!this.validExcelList.length) {
        this.$Message.error('请上传有效的表格数据');
        return;
      }

      this.$emit('validateList', this.validExcelList);
      this.$nextTick(() => {
        this.initImportNums();
      });
      // this.initValidateExcel()
    },

    // 两位年份转四位（2000-2099范围）
    convertShortYear(shortYear) {
      const year = parseInt(shortYear);
      return year >= 50 ? 1900 + year : 2000 + year;
    },

    // 验证日期有效性
    isValidDate(y, m, d) {
      const date = new Date(y, m - 1, d);
      return date.getFullYear() == y && date.getMonth() + 1 == m && date.getDate() == d;
    },
    initImportNums() {
      if (this.successList.length || this.errorList.length) {
        this.$emit('refresh');
      }
      this.importDisabled = false;
      this.chunkIndex = 0;
      this.chunkItemsLength = 0;
      this.succ_num = 0;
      this.fail_num = 0;
      this.successList = [];
      this.errorList = [];
    },

    batchImportExcelList(chunkList) {
      this.excelUploadLoading = true;
      if (this.chunkIndex < +this.chunkItemsLength) {
        this.$api[this.importApiName]({ gs_list: chunkList, ...this.restParams })
          .then(res => {
            // if(this.isImportDrugs){
            //   console.log(this.isImportDrugs);
            //   const products = res.products
            //   const import_product = res.import_product.map(item=>{
            //     return {
            //       ...item,
            //       ...products[item.product_code]
            //     }
            //   })
            //   this.successList = [...this.successList, ...import_product]
            // }else {
            this.succ_num += Number(res.succ_num);
            this.fail_num += Number(res.fail_num);
            this.errorList = [...this.errorList, ...res.fail_data];
            this.successList = [...this.successList, ...res.succ_data];
            // }
          })
          .catch(e => {})
          .finally(() => {
            this.chunkIndex++;
            if (this.chunkIndex === this.chunkItemsLength) {
              this.excelUploadLoading = false;
              this.chunkIndex = 0;
              this.chunkItemsLength = 0;
              this.$Message.success('导入数据完成');
              this.$emit('emitSuccessList', this.successList);
              // 单独抛出额外事件做其他业务逻辑
              this.$emit('emitFinish', this.successList.length > 0);
              console.log('-> %c this.successList  === %o', 'font-size: 15px;color: green;', this.successList);
              this.validExcelList = [];
              this.importDisabled = true;
            } else {
              this.initBatchImportExcel();
            }
          });
      }
    },
    importConfirm() {
      // 有导入的前置条件
      this.excelUploadLoading = true;
      this.precondition().then(res => {
        if (res) {
          this.initBatchImportExcel();
        } else {
          this.excelUploadLoading = false;
        }
      });
    },
    initBatchImportExcel() {
      let chunkItems = this.$lodash.chunk(this.validExcelList, this.chunkNum);
      console.log('-> %c chunkItems  === %o', 'font-size: 15px;color: green;', chunkItems);
      this.chunkItemsLength = chunkItems.length;
      console.log('-> this.chunkItemsLength', this.chunkItemsLength);
      this.batchImportExcelList(chunkItems[this.chunkIndex]);
    },
    seeReport() {
      this.reportVisible = true;
    },
  },
};
</script>

<style scoped lang="less">
.import-schedule {
  padding: 0 20px;

  .step {
    display: flex;
    margin-bottom: 24px;

    .step-number {
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background: #2d8cf0;
      color: white;
      border-radius: 50%;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .step-content {
      flex: 1;

      .step-title {
        font-weight: bold;
        margin-bottom: 8px;
      }

      .step-desc {
        background: #f8f8f9;
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 4px;

        p {
          margin: 0;
          line-height: 1.8;
          color: #666;
        }
      }
    }
  }
}
::v-deep .custom-upload {
  justify-content: center;
}
.error-report {
  margin-top: 20px;

  .download-error {
    margin-left: 15px;
    color: red;
  }
}

.cursor {
  cursor: pointer;
}
</style>
