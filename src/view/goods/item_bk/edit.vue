<template>
  <div class="goods-item-wrapper" v-if="pageShow">
    <div class="block-header"><span>商品类型</span></div>

    <div
      v-if="!formData.id || (formData.id && formData.goods_type == 10)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 10 }"
      @click="formData.goods_type = 10"
    >
      <span>
        <span class="ks-goods-type_name">实物商品</span>
        <span class="ks-goods-type_desc">(物流发货)</span>
      </span>
    </div>
    <div
      v-if="!formData.id || (formData.id && formData.goods_type == 15)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 15 }"
      @click="formData.goods_type = 15"
    >
      <span>
        <span class="ks-goods-type_name">虚拟商品</span>
        <span class="ks-goods-type_desc">(无需物流)</span>
      </span>
    </div>
    <div
      v-if="!formData.id || (formData.id && formData.goods_type == 25)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 25 }"
      @click="formData.goods_type = 25"
    >
      <span>
        <span class="ks-goods-type_name">通兑券</span>
        <span class="ks-goods-type_desc">(仅兑换虚拟商品)</span>
      </span>
    </div>

    <div class="block-header"><span>基本信息</span></div>

    <KWidget label="商品名：" required>
      <Input
        v-model="formData.name"
        :disabled="!isCLI"
        maxlength="100"
        show-word-limit
        placeholder="请输入商品名称，100字以内"
      />
    </KWidget>
    <KWidget label="商品图：" required>
      <MaterialPicture v-model="formData.slide_imgs" :limit="isCLI ? 9 : 0" :disabled="!isCLI" />
      <div class="note">建议尺寸：800*800像素，图片大小不超过3.0M，最多9张，你可以拖拽图片调整顺序</div>
    </KWidget>
    <KWidget label="商品详情：" required>
      <MaterialPicture v-model="formData.detail_imgs" :limit="isCLI ? 30 : 0" :disabled="!isCLI" />
      <div class="note">图片大小不超过3.0M，最多30张，你可以拖拽图片调整顺序</div>
    </KWidget>
    <template v-if="formData.goods_type == 10">
      <div class="block-header"><span>价格库存</span></div>
      <KGoodsSpecs
        v-model="specs_attrs"
        :storedList="storedList"
        :sale_type="formData.sale_type"
        :saleTypeDesc="saleTypeDesc"
        :is_recharge_buy="formData.is_recharge_buy"
        :sw_scope_tmp="formData.sw_scope_tmp"
        :isCLI="isCLI"
        :is_pms_self="is_pms_self"
        :error-reason="errorReason"
      />
    </template>
    <template v-if="formData.goods_type == 15">
      <KWidget label="服务权益：" text required>
        <a class="space6" :style="{ color: isCLI ? '#155BD4' : '#999' }" @click="addService">添加服务</a>
        <span class="note" style="display: inline-block">设置此商品支持的服务可用次数</span>
        <table v-if="formData.services.length > 0" class="table" style="width: 500px">
          <thead>
            <tr>
              <th>已选服务</th>
              <th>价格</th>
              <th>可用次数</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in formData.services" :key="key">
              <td>{{ item.name }}</td>
              <td>{{ item.price | number_format }}</td>
              <td>
                <InputNumber
                  v-model="item.times"
                  controls-outside
                  :min="1"
                  :max="50"
                  style="width: 100px"
                  :disabled="!isCLI"
                />
              </td>
              <td>
                <a @click="onDelService(key)" :style="{ color: isCLI ? '#155BD4' : '#999' }">删除</a>
              </td>
            </tr>
          </tbody>
        </table>
      </KWidget>
      <div class="block-header"><span>价格库存</span></div>

      <KWidget label="价格：" required>
        <InputNumber v-model="formData.price" :precision="2" :active-change="false" :min="0" />
        元
      </KWidget>
      <KWidget label="储值价：" v-show="formData.is_recharge_buy == 'yes'">
        <InputNumber
          v-model="formData.stored_price"
          ref="stored"
          placeholder="请输入储值价"
          :precision="2"
          :min="0"
          @on-change="storedPriceChange"
          :active-change="false"
        />
        元
      </KWidget>
      <KWidget label="库存：" required>
        <InputNumber v-model="formData.stock" :precision="0" :active-change="false" :min="0" />
      </KWidget>

      <div class="block-header"><span>服务设置</span></div>

      <KWidget label="商品有效期：" required>
        <RadioGroup v-model="formData.service_info.expiration.type">
          <Radio label="1"><span></span></Radio>
          <div style="display: inline-block; margin-left: -10px">
            购买后
            <InputNumber
              v-model="formData.service_info.expiration.days"
              :min="1"
              placeholder="自定义"
              :disabled="formData.service_info.expiration.type != 1"
            />
            天内有效
          </div>
          <div class="block_10"></div>
          <Radio label="2"><span></span></Radio>
          <div style="display: inline-block; margin-left: -10px">
            截止到
            <DatePicker
              type="date"
              v-model="formData.service_info.expiration.date"
              placeholder="请选择日期"
              :options="expiration_datepicker_options"
              :disabled="formData.service_info.expiration.type != 2"
            />
            日有效
          </div>
        </RadioGroup>
      </KWidget>
      <KWidget label="是否可约：" text>
        <RadioGroup v-model="formData.service_info.is_appointment">
          <Radio label="1">需要预约</Radio>
          <Radio label="2">免预约</Radio>
        </RadioGroup>
      </KWidget>
      <KWidget label="是否可退：" text>
        <RadioGroup v-model="formData.service_info.is_can_refund">
          <Radio label="1">可退</Radio>
          <Radio label="2">不可退</Radio>
        </RadioGroup>
        <span class="note" style="display: inline-block">未使用是否可以退款</span>
      </KWidget>
      <KWidget label="使用时间：" required>
        <Input v-model="formData.service_info.working_time" />
        <span class="note">如：工作日8:00-19:00点间可用</span>
      </KWidget>
      <KWidget label="不可使用日期：">
        <Input v-model="formData.service_info.not_working_date" />
        <span class="note">如：周六周日、法定节假日不可用</span>
      </KWidget>
      <KWidget label="使用规则：">
        <Input
          v-model="formData.service_info.note_rule"
          type="textarea"
          :rows="5"
          placeholder="请输入使用规则"
          maxlength="100"
          show-word-limit
        />
      </KWidget>
      <KWidget label="上架范围：" required text>
        <CheckboxGroup v-model="formData.xn_scope_tmp">
          <Checkbox label="1">零售服务</Checkbox>
          <Checkbox label="2">问诊治疗</Checkbox>
        </CheckboxGroup>
      </KWidget>

      <KWidget label="储值购买: " required text>
        <Radio-group v-model="formData.is_recharge_buy">
          <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">{{ item.desc }}</Radio>
        </Radio-group>
      </KWidget>
    </template>

    <template v-if="formData.goods_type == 25">
      <KWidget label="兑换次数：" required>
        <InputNumber
          v-model="formData.exchange_num"
          :max="50"
          :disabled="!isCLI"
          show-word-limit
          placeholder="请输入兑换次数"
        />
        <span class="note" style="display: inline-block">兑换次数最多不能超过50次</span>
      </KWidget>
      <KWidget label="服务范围：" text required>
        <a class="space6" :style="{ color: isCLI ? '#155BD4' : '#999' }" @click="addService">添加服务</a>
        <span class="note" style="display: inline-block">设置此商品支持兑换的服务范围</span>
        <table v-if="formData.services.length > 0" class="table" style="width: 500px">
          <thead>
            <tr>
              <th>兑换服务范围</th>
              <th>来源</th>
              <th>价格</th>
              <th>最多可兑次数</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in formData.services" :key="key">
              <td>{{ item.name }}</td>
              <td>{{ item.source_platform_text }}</td>
              <td>{{ item.price | number_format }}</td>
              <td>
                <InputNumber
                  v-model="item.times"
                  controls-outside
                  :min="1"
                  :max="50"
                  style="width: 100px"
                  :disabled="!isCLI"
                />
              </td>
              <td>
                <a @click="onDelService(key)" :style="{ color: isCLI ? '#155BD4' : '#999' }">删除</a>
              </td>
            </tr>
          </tbody>
        </table>
      </KWidget>
      <KWidget label="备注：">
        <Input v-model="formData.remark" :disabled="!isCLI" maxlength="20" show-word-limit placeholder="请输入备注" />
      </KWidget>
      <div class="block-header"><span>价格库存</span></div>

      <KWidget label="价格：" required>
        <InputNumber v-model="formData.price" :precision="2" :active-change="false" :min="0" />
        元
      </KWidget>
      <KWidget label="储值价：" v-show="formData.is_recharge_buy == 'yes'">
        <InputNumber
          v-model="formData.stored_price"
          ref="stored"
          placeholder="请输入储值价"
          :precision="2"
          :min="0"
          @on-change="storedPriceChange"
          :active-change="false"
        />
        元
      </KWidget>
      <KWidget label="库存：" required>
        <InputNumber v-model="formData.stock" :precision="0" :active-change="false" :min="0" />
      </KWidget>

      <div class="block-header"><span>服务设置</span></div>
      <KWidget label="商品有效期：" required>
        <RadioGroup v-model="formData.service_info.expiration.type">
          <Radio label="1"><span></span></Radio>
          <div style="display: inline-block; margin-left: -10px">
            购买后
            <InputNumber
              v-model="formData.service_info.expiration.days"
              :min="1"
              placeholder="自定义"
              :disabled="formData.service_info.expiration.type != 1"
            />
            天内有效
          </div>
          <div class="block_10"></div>
          <Radio label="2"><span></span></Radio>
          <div style="display: inline-block; margin-left: -10px">
            截止到
            <DatePicker
              type="date"
              v-model="formData.service_info.expiration.date"
              placeholder="请选择日期"
              :options="expiration_datepicker_options"
              :disabled="formData.service_info.expiration.type != 2"
            />
            日有效
          </div>
        </RadioGroup>
      </KWidget>

      <KWidget label="储值购买: " required text>
        <Radio-group v-model="formData.is_recharge_buy">
          <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">{{ item.desc }}</Radio>
        </Radio-group>
      </KWidget>
    </template>
    <div class="block_40"></div>
    <div class="block_40"></div>
    <div class="block_40"></div>

    <div class="fixed-bottom-wrapper">
      <Button @click="onSave" :loading="this.stateManager.saveBtnLoading">保存</Button>
      <Dvd />
      <Button type="primary" @click="onPublish" :loading="stateManager.publishBtnLoading">确认发布</Button>
    </div>

    <KGoodsService
      v-model="stateManager.serviceModal"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
      :optionsList="optionsList"
    />

    <confirm-modal
      :confirmVisible.sync="confirmAddressVisible"
      :cancelText="cancelText"
      :confirmText="confirmText"
      :content="'确认诊所地址'"
      :contentText="''"
      @ok="confirmAddress"
      @cancel="cancelAddress"
    >
      <div slot="contentTxt" v-if="hover_type === 'ERR_ADDR'" style="padding-left: 52px">
        诊所尚未设置门店地址，无法选择用户到店自提，是否前往设置？
      </div>
      <div slot="contentTxt" v-else style="padding-left: 52px">
        <div>请确认门店的提货地址</div>
        <div style="color: #0000ff">{{ cli_addr_detail }}</div>
      </div>
    </confirm-modal>
  </div>
  <div v-else>
    <Spin fix></Spin>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
/* eslint-disable */
import KGoodsSpecs from '@/components/k-goods-specs';
import KGoodsService from '@/components/k-goods-services';
import confirmModal from '@/components/confirmModal/confirmModal';
import { colsObj } from '../../daily/visit/common/columns';
import { cloneDeep } from 'lodash-es';

let init_form_data = {
  id: '',
  name: '',
  goods_type: 10,
  desc: '',
  slide_imgs: [],
  detail_imgs: [],
  specs_data: [],
  attrs: [],
  price: 0,
  is_recharge_buy: 'yes', // 虚拟服务的储值购买
  stored_price: null, // 虚拟服务的储值价
  stock: 0,
  services: [],
  xn_scope_tmp: ['1'],
  xn_scope: '',
  sw_scope_tmp: ['1'],
  shelf_scope: '',
  sale_type: 'CLI_SELF',
  exchange_num: null,
  remark: '',
  r: '',
};
// todo  不同类型商品提交的时候，需要的字段不一样，需要根据不同的类型，来判断
export default {
  name: 'edit',
  components: {
    KGoodsSpecs,
    KGoodsService,
    confirmModal,
  },

  data() {
    return {
      pageShow: false,
      formData: {
        service_info: {
          expiration: {
            type: '1', // 1 购买后${days}天内有效；2 ${date}日期前有效；
            date: '', //
            days: 365, //
          },
          is_appointment: '1', // 1免预约 2需要预约
          is_can_refund: '1', // 1不可退 2可退
          working_time: '', // 工作时间
          not_working_date: '', // 不可使用日期：
          note_rule: '', // 使用规则
        },
        ...cloneDeep(init_form_data),
      },
      getLoading: true,

      specs_attrs: [[], []],

      stateManager: {
        serviceModal: false,
        saveBtnLoading: false, // 保存按钮状态
        publishBtnLoading: false, // 发布按钮状态
      },

      storedList: [], // 储值购买枚举
      saleTypeDesc: [], // 销售方式枚举

      expiration_datepicker_options: {
        disabledDate(date) {
          return S.moment(date).unix() <= S.moment(new Date()).unix();
        },
      },
      isCLI: true, // 是否诊所自建
      is_pms_self: false,
      confirmAddressVisible: false,
      cancelText: '',
      confirmText: '',
      contentText: '',
      hover_type: '',
      cli_addr_detail: '',
      isPlat: false,
      errorReason: [],
      optionsList: {
        // 服务列表
        servTypeDesc: [],
        sourcePlatformDesc: [],
      },
    };
  },

  created() {
    this.getIndexOptions();
    if (this.$route.query.id) {
      // 编辑时才有的参数
      this.formData.id = this.$route.query.id;
    }

    if (this.formData.id) {
      this.get();
    } else {
      this.pageShow = true;
    }
  },
  mounted() {
    console.log(this.formData);
  },
  methods: {
    storedPriceChange(val) {
      if (Number(val) == 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },

    onSelectedService: function (items) {
      console.log('-> %c items  === %o ', 'font-size: 15px', items);
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          price: item.price,
          times: isExist === -1 ? 1 : this.formData.services[isExist].times,
          source_platform_text: item.source_platform_text,
        };
      });
      this.stateManager.serviceModal = false;
    },

    onDelService: function (index) {
      if (!this.isCLI) {
        return;
      }
      this.formData.services.splice(index, 1);
    },

    onPreview: function () {},

    getAttrs(val) {
      let attrs = this.$lodash.cloneDeep(val);
      console.log('-> %c attrs  === %o ', 'font-size: 15px', attrs);
      attrs.forEach((item, index) => {
        console.log('item', item);
        let _relate_his_prods = [];
        item.relate_his_prods &&
          item.relate_his_prods.length &&
          item.relate_his_prods.forEach(single_item => {
            console.log('single_item.buy_multiples', single_item.buy_multiples);
            _relate_his_prods.push({
              prod_id: single_item.prod_id,
              sales_unit: single_item._sales_unit,
              buy_multiples: single_item.buy_multiples,
            });
          });
        item.relate_his_prods = _relate_his_prods;
        item.stock = item.stock;
        item.is_relate_his_prod = _relate_his_prods.length > 0 ? 1 : 0; // 是否有关联货品的标识
      });
      return attrs;
    },

    getFormData: function () {
      let formData = { ...this.formData };
      formData.specs_data = this.specs_attrs[0];
      // formData.attrs = this.specs_attrs[1]
      formData.attrs = this.getAttrs(this.specs_attrs[1]);

      if (!this.validationData(formData)) {
        return false;
      }

      formData.slide_imgs = this.formData.slide_imgs;
      formData.detail_imgs = this.formData.detail_imgs;
      formData.specs_data = formData.specs_data;
      formData.attrs = formData.attrs;
      formData.services = formData.services;
      if (this.formData.service_info.expiration.date) {
        this.formData.service_info.expiration.date = S.moment(this.formData.service_info.expiration.date).format(
          'YYYY-MM-DD'
        );
      }
      formData.service_info = formData.service_info;
      formData.xn_scope = formData.xn_scope_tmp.length == 2 ? '9' : formData.xn_scope_tmp[0];
      formData.shelf_scope = formData.sw_scope_tmp.length == 2 ? '9' : formData.sw_scope_tmp[0];
      delete formData.xn_scope_tmp;
      delete formData.sw_scope_tmp;

      return formData;
    },
    onSave: function () {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }

      this.stateManager.saveBtnLoading = true;
      let params = {
        ...formData,
        stored_price: formData.stored_price == null ? '' : formData.stored_price,
      };
      io.post('clinic/goods.index.edit', params)
        .then(res => {
          if (!this.handleConfirm(res)) {
            return;
          }
          this.$Message.success({
            content: '保存成功',
          });
          this.$router.push({ path: '/goods/item/list' });
        })
        .catch(error => {})
        .finally(() => {
          this.stateManager.saveBtnLoading = false;
        });
    },

    onPublish: function () {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }
      this.stateManager.saveBtnLoading = true;
      let params = {
        ...formData,
        stored_price: formData.stored_price == null ? '' : formData.stored_price,
        sw_scope_tmp: formData.goods_type === '10' ? formData.sw_scope_tmp : '',
      };

      io.post('clinic/goods.index.publish', params)
        .then(res => {
          if (!this.handleConfirm(res)) {
            return;
          }
          this.$Message.success({
            content: '发布成功',
          });
          this.$router.push({ path: '/goods/item/list' });
        })
        .catch(error => {})
        .finally(() => {
          this.stateManager.publishBtnLoading = false;
          this.stateManager.saveBtnLoading = false;
        });
    },

    get: function () {
      let id = this.formData.id;
      io.get('clinic/goods.index.getinfo', { data: { id } })
        .then(data => {
          let goods = data.goods;
          this.is_pms_self = !!Number(data.is_pms_self);
          this.errorReason = data.error_pms_self;
          this.formData.id = goods.id;
          this.formData.name = goods.name;
          this.formData.desc = goods.desc;
          this.formData.is_recharge_buy = goods.is_recharge_buy;
          this.formData.price = Number(goods.price);
          this.formData.stored_price = goods.stored_price == '' ? null : Number(goods.stored_price);
          this.formData.stock = Number(goods.stock);
          this.formData.goods_type = goods.goods_type;
          this.formData.detail_imgs = goods.detail_imgs;
          this.formData.slide_imgs = goods.slide_imgs;
          this.formData.specs_data = goods.specs_data;
          this.formData.attrs = goods.attrs;
          this.formData.remark = goods.remark;
          this.formData.exchange_num = +goods.exchange_num;
          this.specs_attrs = [this.formData.specs_data, this.formData.attrs];
          if (goods.goods_type !== '25') {
            // 通兑券保留次数为空
            for (let key in goods.services ? goods.services : []) {
              goods.services[key].times = Number(goods.services[key].times);
            }
          }
          this.formData.services = goods.services;
          this.formData.service_info = !S.isEmptyObject(goods.service_info)
            ? goods.service_info
            : this.formData.service_info;
          this.formData.service_info.expiration.days = Number(this.formData.service_info.expiration.days);
          this.formData.service_info.expiration.date =
            this.formData.service_info.expiration.date === ''
              ? ''
              : S.moment(this.formData.service_info.expiration.date).format('YYYY-MM-DD');

          this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1', '2'] : [goods.xn_scope];

          this.formData.sw_scope_tmp = goods.shelf_scope == '9' ? ['1', '2'] : [goods.shelf_scope];

          this.formData.sw_scope_tmp = goods.shelf_scope == '9' ? ['1', '2'] : [goods.shelf_scope];

          this.isCLI = goods.source_platform === 'CLI';
          this.isPlat = goods.source_platform === 'PLAT';
          if (!this.isPlat) {
            this.handleSaleType();
          }
          if (goods.sale_type) {
            this.formData.sale_type = goods.sale_type;
          }
          console.log('=>(edit.vue:404) this.isCLI', this.isCLI);
          this.pageShow = true;
        })
        .catch(error => {});
    },

    // 储值购买枚举值
    getIndexOptions() {
      this.$api.getIndexOptions().then(res => {
        this.storedList = S.descToArrHandle(res.isRechargeBuyDesc);
        this.saleTypeDesc = S.descToArrHandle(res.saleTypeDesc);
        this.optionsList.servTypeDesc = S.descToArrHandle(res.servTypeDesc);
        this.optionsList.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc);
        if (!this.$route.query.id) {
          this.handleSaleType();
        }
      });
    },

    addService() {
      if (!this.isCLI) {
        return;
      }
      this.stateManager.serviceModal = true;
    },

    validationData: function (data) {
      console.log('-> %c data  === %o ', 'font-size: 15px', data);
      if (data.name.trim() == '') {
        this.$Message.error('请填写商品标题');
        return false;
      }

      if (data.slide_imgs.length <= 0) {
        this.$Message.error('至少上传一张商品图');
        return false;
      }

      if (data.detail_imgs.length <= 0) {
        this.$Message.error('至少上传一张商品详情图');
        return false;
      }

      if (data.goods_type == 10) {
        // 实物商品
        let specs_error_msg = '';

        data.specs_data.forEach(item => {
          if (item.name == '') {
            specs_error_msg = '规格名称不能为空';
            return false;
          }

          item.specs_atoms.forEach(atom => {
            if (atom[0] == '') {
              specs_error_msg = '规格值不能为空';
              return false;
            }
            if ((item.isAddImg && !atom[1]) || atom[1] == '') {
              specs_error_msg = '请选添加规格图片';
              return false;
            }
          });
        });
        if (specs_error_msg) {
          this.$Message.error(specs_error_msg);
          return false;
        }

        if (S.isEmptyObject(data.attrs)) {
          this.$Message.error('规格值不能为空');
          return false;
        }

        data.attrs.forEach(item => {
          let price = parseFloat(item.price);
          // let stored_price = parseFloat( Number(item.stored_price) )
          item.stored_price = item.stored_price == null ? '' : item.stored_price;
          if (!/^\d([\d\.]+)?/.test(price)) {
            specs_error_msg = '请输入有效的商品价格';
            return false;
          }

          if (price < 0.01) {
            specs_error_msg = '商品价格不允许小于0.01元';
            return false;
          }
          // 储值价非必填
          // if ( data.is_recharge_buy == 'yes' && stored_price < 0.01 ) {
          //   specs_error_msg = '储值价不允许小于0.01元'
          //   return false
          // }

          if (price > 100000) {
            specs_error_msg = '商品价格不允许大于100000.00元';
            return false;
          }

          let stock = parseInt(item.stock);
          if (!/^\d([\d]+)?/.test(stock)) {
            specs_error_msg = '请输入有效的商品库存';
            return false;
          }
        });

        if (specs_error_msg) {
          this.$Message.error(specs_error_msg);
          return false;
        }

        if (!this.formData.sw_scope_tmp.length) {
          this.$Message.error('请选择实物商品上架范围');
          return false;
        }
      }

      if (data.goods_type == 15) {
        // 虚拟商品
        if (data.services.length <= 0) {
          this.$Message.error('请添加服务权益');
          return false;
        }

        if (data.stock <= 0) {
          this.$Message.error('请填写库存');
          return false;
        }
        if (!data.service_info.working_time) {
          this.$Message.error('请输入使用时间');
          return false;
        }

        if (!/^\d([\d\.]+)?/.test(data.price)) {
          this.$Message.error('请输入有效的商品价格');
          return false;
        }
        let price = parseFloat(data.price);
        if (price < 0.01) {
          this.$Message.error('商品价格不允许小于0.01元');
          return false;
        }
        if (price > 100000) {
          this.$Message.error('商品价格不允许大于100000.00元');
          return false;
        }

        if (data.xn_scope_tmp.length == 0) {
          this.$Message.error('请选择虚拟商品上架范围');
          return false;
        }

        // 储值价非必填
        // if ( data.is_recharge_buy == 'yes' && data.stored_price < 0.01) {
        //   this.$Message.error( '储值价不允许小于0.01元' )
        //   return false
        // }
      }
      if (data.goods_type == 25) {
        // 虚拟商品
        if (data.services.length <= 0) {
          this.$Message.error('请添加服务权益');
          return false;
        }
        if (data.exchange_num <= 0) {
          this.$Message.error('请填写兑换次数');
          return false;
        }

        // 是否有空
        let isHasEmpty = false;
        // 是否有单个服务兑换次数大于总兑换次数
        let isSingleMore = false;

        const totalTimes = data.services.reduce((total, item) => {
          if (item.times == null) {
            isHasEmpty = true;
          }
          if (item.times > data.exchange_num) {
            isSingleMore = true;
            // this.$Message.error( `【${item.name}】兑换次数不能大于服务权益总次数` )
            item.times = data.exchange_num;
          }
          return total + item.times;
        }, 0);

        // 单个服务超出,拦截
        if (isSingleMore) {
          return false;
        }

        if (!isHasEmpty && totalTimes < data.exchange_num) {
          this.$Message.error('兑换次数不能大于服务权益总次数');
          return false;
        }

        if (data.stock <= 0) {
          this.$Message.error('请填写库存');
          return false;
        }

        if (!/^\d([\d\.]+)?/.test(data.price)) {
          this.$Message.error('请输入有效的商品价格');
          return false;
        }
        let price = parseFloat(data.price);
        if (price < 0.01) {
          this.$Message.error('商品价格不允许小于0.01元');
          return false;
        }
        if (price > 100000) {
          this.$Message.error('商品价格不允许大于100000.00元');
          return false;
        }

        if (data.xn_scope_tmp.length == 0) {
          this.$Message.error('请选择虚拟商品上架范围');
          return false;
        }

        // 储值价非必填
        // if ( data.is_recharge_buy == 'yes' && data.stored_price < 0.01) {
        //   this.$Message.error( '储值价不允许小于0.01元' )
        //   return false
        // }
      }
      return true;
    },

    confirmAddress() {
      if (this.hover_type === 'ERR_ADDR') {
        this.confirmAddressVisible = false;
        const a = document.createElement('a');
        a.setAttribute('href', '/setting/general_set');
        a.setAttribute('target', '_blank');
        a.setAttribute('style', 'display:none');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        // window.open('/setting/general_set')
        // this.$router.push({path:'/setting/general_set', query:{back: 'true'}})
      }
      if (this.hover_type === 'ERR_CONFIRM_ADDR') {
        this.confirmAddr();
      }
    },

    cancelAddress() {
      if (this.hover_type === 'ERR_CONFIRM_ADDR') {
        this.confirmAddressVisible = false;
        window.open('/setting/general_set');
        // this.$router.push({path:'/setting/general_set', query:{back: 'true'}})
      }
    },

    confirmAddr() {
      this.$api
        .confirmAddr()
        .then(() => {
          this.confirmAddressVisible = false;
        })
        .catch(err => {});
    },

    handleConfirm(res) {
      let flag = true;
      this.hover_type = res.hover_type;
      if (this.hover_type === 'ERR_ADDR') {
        this.confirmAddressVisible = true;
        this.cancelText = '取消';
        this.confirmText = '去设置';
        flag = false;
        return flag;
      }
      if (this.hover_type === 'ERR_CONFIRM_ADDR') {
        this.confirmAddressVisible = true;
        this.cli_addr_detail = res.addr_detail;
        this.cancelText = '地址有误, 去修改';
        this.confirmText = '确定';
        flag = false;
        return flag;
      }
      return flag;
    },

    handleSaleType() {
      this.saleTypeDesc = this.saleTypeDesc.filter(item => item.id !== 'PMS_SELF');
    },
  },
};
</script>

<style lang="less">
.goods-item-wrapper {
  .ivu-input-wrapper,
  .ivu-select {
    width: 80%;
    max-width: none;
  }

  .ivu-input {
    max-width: none;
  }

  .ks-goods-type {
    position: relative;
    display: inline-block;
    cursor: pointer;
    margin: 0 0 0 10px;
    text-align: center;
    width: 115px;
    border-radius: 2px;
    border: 1px solid #cacaca;
    padding: 8px 0;
  }

  .ks-goods-type.active {
    border-color: #155bd4;
  }

  .ks-goods-type.active:after {
    content: '';
    display: inline-block;
    position: absolute;
    width: 24px;
    height: 24px;
    bottom: -1px;
    right: -1px;
    background: url(https://img01.biranmall.com/image/2021/0224/155801_7284881.png) no-repeat;
    background-size: 24px auto;
  }

  .ks-goods-type_name {
    display: block;
    font-weight: bold;
  }

  .ks-goods-type_desc {
    display: block;
    color: #999;
  }

  .ks-input-number {
    width: 100px;

    .ivu-input-number-input {
      text-align: center;
    }
  }

  .ivu-date-picker-editor {
    width: 100% !important;
  }
}

.widget-form-content {
  .ivu-input-number {
    width: 100px;
  }
}
</style>
