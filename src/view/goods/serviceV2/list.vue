<template>
  <div class="list-wrapper">
    <standard-table
      class="goods-table"
      :no-data-text="''"
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex">
          <Form inline :label-width="0" class="search-form" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem label="">
              <Input v-model="queryFormData.keyword" type="text" placeholder="服务名称" />
            </FormItem>
            <FormItem label="">
              <Select v-model="queryFormData.serv_type" placeholder="服务类型" clearable>
                <Option value="">全部</Option>
                <Option v-for="type in typeList" :key="type.id" :value="type.id">{{ type.desc }}</Option>
              </Select>
            </FormItem>
            <FormItem label="">
              <Input type="text" v-model="queryFormData.out_goods_name" placeholder="外部渠道商品名称" />
            </FormItem>
            <FormItem>
              <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </FormItem>
          </Form>

          <a v-if="isClinicToOpc" style="margin-left: auto; line-height: 32px" @click="() => (historyVisible = true)">
            查看历史
          </a>
        </div>
      </template>

      <template #name="{ row }">
        <div>{{ row.name }}</div>
      </template>
      <template #can_use_scope_desc="{ row }">
        <div>{{ row.can_use_scope_desc || '-' }}</div>
      </template>

      <!--        <template #first_tier_price="{ row }">-->
      <!--          <div class="lv-box">-->
      <!--            <div class="label">单次价:</div>-->
      <!--            <div class="value">-->
      <!--              {{ row.first_tier_price ? `¥ ${Number(row.first_tier_price || 0).toFixed(2)}` : '-' }}-->
      <!--            </div>-->
      <!--          </div>-->
      <!--          <div class="lv-box">-->
      <!--            <div class="label">会员价:</div>-->
      <!--            <div class="value">-->
      <!--              {{ row.first_tier_vip_price ? `¥ ${Number(row.first_tier_vip_price || 0).toFixed(2)}` : '-' }}-->
      <!--            </div>-->
      <!--          </div>-->
      <!--        </template>-->
      <template #price="{ row }">
        <div class="lv-box">
          <div class="label">单次价:</div>
          <div class="value">
            {{ row.price ? `¥ ${Number(row.price || 0).toFixed(2)}` : '-' }}
          </div>
        </div>
        <div class="lv-box">
          <div class="label">会员价:</div>
          <div class="value">
            {{ row.vip_price ? `¥ ${Number(row.vip_price || 0).toFixed(2)}` : '-' }}
          </div>
        </div>
      </template>
      <!--        <template #not_first_tier_price="{ row }">-->
      <!--          <div class="lv-box">-->
      <!--            <div class="label">单次价:</div>-->
      <!--            <div class="value">-->
      <!--              {{ row.not_first_tier_price ? `¥ ${Number(row.not_first_tier_price || 0).toFixed(2)}` : '-' }}-->
      <!--            </div>-->
      <!--          </div>-->
      <!--          <div class="lv-box">-->
      <!--            <div class="label">会员价:</div>-->
      <!--            <div class="value">-->
      <!--              {{ row.not_first_tier_vip_price ? `¥ ${Number(row.not_first_tier_vip_price || 0).toFixed(2)}` : '-' }}-->
      <!--            </div>-->
      <!--          </div>-->
      <!--        </template>      -->
      <!-- <template #physio_count="{ row }">
        <a v-if="+row.physio_count > 0" @click="openServeModel(row)">{{ row.physio_count }}</a>
        <span v-else>-</span>
      </template> -->
      <template #sales="{ row }">
        <span>{{ row.sales || '-' }}</span>
      </template>
      <template slot-scope="{ row }" slot="out_sales_goods_names">
        <div class="text-ellipsis" v-overflow-tooltip v-for="(good, i) in row.out_sales_goods_names" :key="i">
          {{ good.out_sales_channel_name + (good.out_sales_channel_name ? '：' : '') }}{{ good.out_goods_name }}
        </div>
        <div v-if="row?.out_sales_goods_names?.length === 0">-</div>
      </template>
      <template slot="operate" slot-scope="{ row }">
        <KLink class="mr10" :to="{ path: '/goods/serviceV2/detail', query: { id: row.id, type: 'detail' } }">
          详情
        </KLink>
      </template>
    </standard-table>

    <serve-model :id="selectedRow.id || ''" :visible.sync="addServeVisible" />
    <serve2-history-serve :visible.sync="historyVisible" />
  </div>
</template>

<script>
import S from '@/libs/util';
import renderHeader from '@/mixins/renderHeader';
import search from '@/mixins/search';
import ServeModel from './components/serveModel.vue';
import StandardTable from '@/components/StandardTable/index.vue';
import { isClinicToOpc } from '@/libs/runtime';
import Serve2HistoryServe from '@/view/goods/serviceV2/components/historyServe.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  goods_type: '',
  dispatch_status: '1',
  r: '',
  source_platform: '',
  dispatch_type: 'RST',
  out_goods_name: '',
};
export default {
  name: 'List',
  components: {Serve2HistoryServe, StandardTable, ServeModel },
  mixins: [renderHeader, search],
  data() {
    return {
      isClinicToOpc: isClinicToOpc(),
      isOutSaleClinic: false,
      queryFormData: { ...init_query_form_data },
      apiName: 'getGoodsServiceList',
      tableLoading: false,
      selectedRow: {},
      addServeVisible: false,
      typeList: [],
      dispatchStatusDesc: [],
      list: [],
      total: 0,
      historyVisible: false,
    };
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getUserIsOutSaleClinic();
    // 获取服务类型
    this.getServiceType();
  },
  computed: {
    tableCols() {
      const list = [
        { title: '服务', slot: 'name' },
        { title: '服务类型', key: 'serv_type_text' },
        { title: '服务时长(分钟)', key: 'duration' },
        { title: '可核销人员', slot: 'can_use_scope_desc' },
        { title: '价格', slot: 'price' },
        { title: '总销量', slot: 'sales' },
        // {
        //   title: '可服务理疗师',
        //   slot: 'physio_count',
        //   renderHeader: (h, params) => this._renderHeader(h, params, '平台/合伙人已经分发到具体门店并且理疗师是启用状态'),
        // },
        {
          title: '渠道名称',
          slot: 'out_sales_goods_names',
          align: 'center',
          minWidth: 80,
          renderHeader: (h, params) =>
            this._renderHeader(
              h,
              params,
              '是指此商品在美团/抖音上有对应的商品，且两个商品已关联生效，可以在美团/抖音上售卖。'
            ),
        },
        { title: '操作', slot: 'operate', width: 80 },
      ];
      if (!this.isOutSaleClinic) {
        return list?.filter(item => item.slot !== 'out_sales_goods_names');
      }
      return list;
    },
  },
  methods: {
    getUserIsOutSaleClinic() {
      this.$api.getUserIsOutSaleClinic().then(res => {
        this.isOutSaleClinic = res?.is_allow_out_sales_channel === '1';
      });
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    openServeModel(row) {
      this.selectedRow = row;
      this.addServeVisible = true;
    },
    toEdit() {
      this.$router.push({
        path: '/reserve/manage/detail',
        query: {
          type: 'add',
        },
      });
    },
    // 获取服务类型数据
    getServiceType() {
      this.$api.getGoodsserviceOptions().then(res => {
        // .获取服务类型
        this.typeList = S.descToArrHandle(res.getServTypeDesc || {});
        this.dispatchStatusDesc = S.descToArrHandle(res.dispatchStatusDesc || {});
      });
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style scoped lang="less">
:deep(.search-form .ivu-form-item) {
  margin-bottom: 10px;
}
.lv-box {
  display: flex;
  align-items: center;

  .label {
    width: 45px;
    min-width: 45px;
    text-align: right;
  }

  .value {
    text-align: left;
    margin-left: 4px;
  }
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
