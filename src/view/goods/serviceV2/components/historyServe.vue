<template>
  <Modal
    :value="visible"
    width="80%"
    title="历史服务"
    lock-scroll
    :mask-closable="false"
    :before-close="closeModal"
    @on-cancel="closeModal"
    class-name="vertical-center-modal"
  >
    <div class="global-list-box">
      <div class="global-list-search" ref="searchRefs">
        <!-- 搜索条件区域 -->
        <div class="global-list-search-condition">
          <Form id="searchForm" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem>
              <Input type="text" v-model="queryFormData.keyword" placeholder="服务名称" />
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.serv_type" placeholder="类型">
                <Option v-for="desc in getServTypeDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
              </Select>
            </FormItem>

            <FormItem>
              <Select v-model="queryFormData.source_platform" placeholder="来源" clearable>
                <Option v-for="desc in sourcePlatformDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
              </Select>
            </FormItem>

            <FormItem>
              <Select v-model="queryFormData.fast_order_support" placeholder="快捷下单" clearable>
                <Option v-for="desc in getFastOrderDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
              </Select>
            </FormItem>
          </Form>
        </div>
        <!-- 搜索条件功能区域 -->
        <div class="global-list-search-operate">
          <Button class="search-btn" type="primary" @click="onSearch">筛选</Button>
          <span class="list-reset-btn" @click="onResetSearch">
            <svg-icon class="reset-icon" iconClass="btn-clear" />
            <span>清除条件</span>
          </span>
        </div>
      </div>

      <div class="flex flex-item-between">
        <div class="panel-nav">
          <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')">
            全部
          </a>
          <a
            :class="{ active: queryFormData.status == item.id }"
            class="nav"
            v-for="item in statusList"
            :key="item.id"
            @click.prevent.capture="onStatusChange(item.id)"
          >
            {{ item.desc }}
            <Tag :color="getTagColor(item.id)">
              {{ statusTotal[item.id] }}
            </Tag>
          </a>
        </div>
      </div>
      <Table
        ref="standardTable"
        class="global-table-style"
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
        :height="$store.state.app.clientHeight - 400"
        :row-class-name="rowClassName"
      >
        <template slot-scope="{ row }" slot="info">
          {{ row.name }}
        </template>
        <template slot-scope="{ row }" slot="price"> ¥ {{ row.price | number_format }} </template>
        <template slot-scope="{ row }" slot="source_platform_text">
          {{ row.source_platform_text || '-' }}
        </template>
        <template slot-scope="{ row, index }" slot="relate_goods">
          <poptip-table
            v-if="row.goods_count > 0"
            :key="index"
            :columns="columns"
            :data="row.goods_relation_info"
            width="500"
          >
            <a>{{ row.goods_count }}</a>
          </poptip-table>
          <div v-else>{{ row.goods_count || 0 }}</div>
        </template>
        <template slot-scope="{ row }" slot="fast_order_support_text">
          {{ row.fast_order_support_text }}
        </template>

        <template slot-scope="{ row }" slot="fixed_divide_value">
          {{ +row.fixed_divide_value > 0 ? row.fixed_divide_value : '-' }}
        </template>
        <template slot-scope="{ row }" slot="status">
          {{ row.status_text }}
        </template>
        <template slot-scope="{ row }" slot="operate">
          <a class="ml10" @click="toDetail(row)">详情</a>
        </template>
      </Table>
      <div class="global-list-page" style="margin-left: auto; margin-top: 12px">
        <KPage
          :total="total"
          :page-size.sync="queryFormData.pageSize"
          :current.sync="queryFormData.page"
          :page-size-opts="[10, 20, 50, 80, 100, 200]"
          @on-change="onPageChange"
        />
      </div>
    </div>
    <div class="footer" slot="footer">
      <Button @click="closeModal">关闭</Button>
    </div>
  </Modal>
</template>

<script>
import { cloneDeep  } from 'lodash-es';
import PoptipTable from '@/components/poptipTable/index.vue';
import S from '@/libs/util';
import RenderHeader from '@/mixins/renderHeader';

const init_page_form = {
  page: 1,
  pageSize: 20,
  keyword: '',
  status: '',
  source_platform: '',
  serv_type: '',
  fast_order_support: '',
  dispatch_type: 'NORMAL',
};
export default {
  name: 'serve2HistoryServe',
  components: { PoptipTable },
  mixins: [RenderHeader],
  props: {
    visible: Boolean,
  },
  data() {
    return {
      statusList: [],
      statusTotal: {},
      statusDesc: [],
      getFastOrderDesc: [],
      getServTypeDesc: [],
      sourcePlatformDesc: [],
      tableCols: [
        { title: 'ID', key: 'id', minWidth: 80 },
        { title: '服务', slot: 'info', minWidth: 150 },
        { title: '价格', slot: 'price', minWidth: 140 },
        { title: '类型', key: 'serv_type_text', minWidth: 100 },
        { title: '来源', slot: 'source_platform_text', minWidth: 100 },
        { title: '关联商品', slot: 'relate_goods', minWidth: 100 },
        { title: '固定分佣金额', slot: 'fixed_divide_value', minWidth: 100 },
        {
          title: '快捷下单',
          slot: 'fast_order_support_text',
          minWidth: 100,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '设置后快捷下单后，可以在手动建单时，直接下单购买服务'),
        },
        { title: '状态', slot: 'status', width: 80 },
        { title: '操作', slot: 'operate', width: 80 },
      ],

      columns: [
        { title: '商品名', key: 'name', width: 200, align: 'left' },
        { title: '类型', key: 'goods_type_text', slot: 'goods_type_text', textColor: '#909399', align: 'left' },
        {
          title: '来源',
          key: 'source_platform_text',
          slot: 'source_platform_text',
          textColor: '#909399',
          align: 'left',
        },
        { title: '单价', key: 'price', slot: 'price', align: 'right', isMoney: true },
      ],
      tableLoading: false,
      list: [],
      total: 0,
      queryFormData: cloneDeep(init_page_form),
    };
  },
  computed: {
    getTagColor() {
      return status => {
        switch (status) {
          case 'ON':
            return 'success';
          case 'DELETED':
            return 'error';
          default:
            return 'default';
        }
      };
    },
  },
  watch: {
    visible: {
      handler(flag) {
        if (flag) {
          this.queryFormData = cloneDeep(init_page_form);
          this.getServiceType();
          this.getGoodsserviceOptions();
          this.getLists();
        }
      },
    },
  },
  methods: {
    closeModal() {
      this.$emit('update:visible', false);
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getLists();
    },
    onResetSearch() {
      this.queryFormData = cloneDeep(init_page_form);
      this.getLists();
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.getLists();
    },
    // 状态切换
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.getLists();
    },
    getLists: function () {
      this.tableLoading = true;

      this.$api
        .getGoodsServiceList({ ...this.queryFormData })
        .then(data => {
          this.list = data?.list || [];
          this.total = +data?.total || 0;
          this.statusDesc = data.statusDesc;
          this.statusList = S.descToArrHandle(data.statusDesc);
          this.statusTotal = data.statusTotal;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 获取服务类型、
    getServiceType() {
      this.$api
        .getServiceType({
          dispatch_type: 'NORMAL',
        })
        .then(res => {
          this.getServTypeDesc = S.descToArrHandle(res || {});
        });
    },

    // 获取枚举
    getGoodsserviceOptions() {
      this.$api
        .getGoodsserviceOptions({
          dispatch_type: 'NORMAL',
        })
        .then(res => {
          this.getFastOrderDesc = S.descToArrHandle(res.getFastOrderDesc || {});
          this.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc || {});
        });
    },
    toDetail(row) {
      let query = {
        id: row.id,
        type: 'detail',
        dispatch_type: 'NORMAL',
      };
      const href = this.$router.resolve({
        path: '/goods/service/detail',
        query,
      }).href;
      window.open(href, '_blank');
    },
    // 给表格行设置样式
    rowClassName(row) {
      return row.status === 'OFF' ? 'del-cell' : '';
    },
  },
};
</script>

<style scoped lang="less">
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}
</style>
