<template>
  <div class="specs-wrapper">
    <KWidget label="商品规格：">
      <div class="specs-container">
        <div class="specs-group">
          <div v-for="(specs, specskey) in specsData" :key="specskey" class="specs-group_item">
            <div class="specs-group_item-name media">
              <div class="media-left"><b>规格名：</b></div>
              <div class="media-body">
                <Input
                  class="input-mini1"
                  :disabled="isDetailDisabled || isConnectedTaocan"
                  type="text"
                  v-model="specs.name"
                  :ref="'input-' + specskey"
                />
                <Dvd />
                <Dvd />
                <Dvd />
                <Dvd />
                <label v-if="specskey == 0" style="font-weight: 400">
                  <Checkbox :disabled="isDetailDisabled" v-model="specs.isAddImg">添加规格图片</Checkbox>
                </label>
                <div
                  class="specs-group_item-del btn-delete"
                  v-if="!isConnectedTaocan && !isDetailDisabled"
                  @click="onDelSpecs(specskey)"
                >
                  ×
                </div>
              </div>
            </div>
            <div class="specs-group_item-value media">
              <div class="media-left">规格值：</div>
              <div class="media-body">
                <div class="atom-list" v-viewer="{ url: 'data-source' }">
                  <div
                    v-for="(atom, atomkey) in specs.specs_atoms"
                    :key="atomkey"
                    class="atom"
                    :class="{ active: specs.isAddImg }"
                  >
                    <Input
                      class="input-mini2"
                      :disabled="isDetailDisabled || isConnectedTaocan"
                      v-model="atom[0]"
                      :ref="'input-' + specskey + '-' + atomkey"
                    />
                    <div v-if="specs.isAddImg" class="img-comp">
                      <div class="arrow"></div>
                      <div v-if="atom[1]" class="img-container">
                        <img :src="atom[1] | imageStyle" :data-source="atom[1]" />
                        <div class="img-del btn-delete" @click="onDelSpecsImg(atomkey)" v-if="!isDetailDisabled">×</div>
                      </div>
                      <div v-show="!atom[1]" class="img-container flex-c flex-item-align">
                        <MaterialPicture :limit="1" @input="onUploadSuccess(atomkey, arguments[0])"> </MaterialPicture>
                        <span class="img-note">添加规格图片</span>
                      </div>
                    </div>
                  </div>
                </div>
                <a class="atom-add" :disabled="isDetailDisabled || isConnectedTaocan" @click="onAddAtom(specskey)"
                  >添加规格值</a
                >
                <div v-if="specskey == 0 && specs.isAddImg" class="note">
                  仅支持为第一组规格设置规格图片，买家选择不同规格会看到对应规格图片，建议尺寸：800 x 800像素
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="add_specs_btn_show" class="specs-op">
          <Button :disabled="isDetailDisabled || isConnectedTaocan" @click="onAddSpecs">添加规格项目</Button>
        </div>
      </div>
    </KWidget>
    <KWidget label="规格明细：" :key="refreshStockTableKey" v-if="stockData.length > 0">
      <table class="table goods-specs-table ks-specs-table" style="border: 1px solid #ddd">
        <thead>
          <tr>
            <template v-for="(specs, specskey) in specsData">
              <th :key="specskey">
                {{ specs.name }}
              </th>
            </template>
            <th><span class="text-danger">*</span> 价格(元)</th>
            <!-- <th v-if="goods_type === '40'"><span class="text-danger">*</span> 一线城市定价</th> -->
            <!-- <th v-if="goods_type === '40'"><span class="text-danger">*</span> 非一线城市定价</th> -->
            <th width="8%" v-show="give == 'yes' && !isRst">
              <Tooltip
                maxWidth="200px"
                placement="top"
                content="商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价"
              >
                <svg-icon iconClass="help" class="helpIcon"></svg-icon>
              </Tooltip>
              储值价
            </th>
            <th>可售库存</th>
            <th>诊所货品</th>
            <th>货品id</th>
            <th>货品类型</th>
            <th>售卖单位</th>
            <th>剩余库存</th>
            <th>单位售价(元)</th>
            <th>起购倍数</th>
            <th></th>
          </tr>
        </thead>
        <tbody class="ks-specs-table-body">
          <tr v-for="(stock, stockkey) in stockData" :key="stockkey">
            <td v-for="(item, index) in specsData" :key="index">
              {{ stock.specs_atoms[item.name] }}
            </td>
            <td v-if="!isRst">
              <InputNumber
                class="input-mini"
                :min="0"
                :precision="2"
                :active-change="false"
                v-model="stock.price"
                placeholder="请填写价格"
                :disabled="disabled"
              ></InputNumber>
              <p class="recommendTip" v-if="stock.relate_his_prods.length">推荐价格：￥{{ recommendPrice(stock) }}</p>
            </td>
            <td v-else>
              <div>
                <InputNumber
                  class="input-mini"
                  :min="0"
                  :precision="2"
                  :max="99999999"
                  :active-change="false"
                  v-model="stock.price"
                  placeholder="销售价"
                  :disabled="disabled"
                ></InputNumber>
              </div>

              <div>
                <InputNumber
                  class="input-mini"
                  :min="0"
                  :precision="2"
                  :max="99999999"
                  :active-change="false"
                  v-model="stock.vip_price"
                  placeholder="会员价"
                  :disabled="disabled"
                  style="margin: 6px 0"
                ></InputNumber>
              </div>
              <p class="recommendTip" style="margin-top: -4px; font-size: 11px" v-if="stock.relate_his_prods.length">
                推荐价格：￥{{ recommendPrice(stock) }}
              </p>
            </td>

            <!-- 储值价 -->
            <td v-show="give == 'yes' && !isRst">
              <InputNumber
                :ref="`sStored${stockkey}`"
                class="input-mini"
                :active-change="false"
                :precision="2"
                :min="0"
                @on-change="storedPriceChange($event, stockkey)"
                v-model="stock.stored_price"
                :disabled="disabled"
                placeholder="请填写储值价"
              ></InputNumber>
            </td>

            <td>
              <InputNumber
                class="input-mini"
                :min="1"
                v-model="stock.stock"
                placeholder="请输入可售库存"
                :precision="0"
                :disabled="stock.relate_his_prods.length > 0 || disabled"
              ></InputNumber>
            </td>

            <!-- 诊所货品 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <p
                  class="single-item"
                  v-for="(checked_item, currentGoodsIndex) in stock.relate_his_prods"
                  :key="'goodsName' + currentGoodsIndex"
                  style="min-width: 60px"
                >
                  <span class="flex flex-item-align">
                    <span>{{ checked_item.generic_name }}</span>
                    <span
                      v-if="!isDetailDisabled"
                      class="cursor margin-left10 flex flex-item-align"
                      @click="removeCheckedGoods(stockkey, currentGoodsIndex)"
                      style="width: 40px"
                      >移除</span
                    >
                  </span>
                </p>
              </template>
            </td>

            <td>
              <template v-if="stock.relate_his_prods.length">
                <p
                  class="single-item"
                  v-for="(checked_item, currentGoodsIndex) in stock.relate_his_prods"
                  :key="'goodsName' + currentGoodsIndex"
                  style="min-width: 60px"
                >
                  <span class="flex flex-item-align">
                    <span>{{ checked_item.prod_id }}</span>
                  </span>
                </p>
              </template>
            </td>

            <!-- 货品类型 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <p
                  class="single-item"
                  v-for="(checked_item, currentGoodsIndex) in stock.relate_his_prods"
                  :key="'goodsName' + currentGoodsIndex"
                  style="min-width: 60px"
                >
                  <span class="flex flex-item-align">
                    <span>{{ checked_item.prod_type_text }}</span>
                  </span>
                </p>
              </template>
            </td>

            <!-- 售卖单位 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <div
                  class="single-item"
                  v-for="(checked_item, index) in stock.relate_his_prods"
                  :key="'select' + index"
                >
                  <Select
                    :disabled="isDetailDisabled"
                    v-model="checked_item._sales_unit"
                    @on-select="saleChange($event, stockkey, index, checked_item.sales_units)"
                  >
                    <Option
                      v-for="(select_item, select_index) in checked_item.sales_units"
                      :value="select_item.unit"
                      :key="select_index + 'select'"
                      >{{ select_item.unit }}
                    </Option>
                  </Select>
                </div>
              </template>
            </td>

            <!-- 剩余库存-->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <p class="single-item" v-for="(checked_item, index) in stock.relate_his_prods" :key="'price' + index">
                  <Input class="input-mini" v-model="checked_item._stock_num" disabled />
                </p>
              </template>
            </td>

            <!-- 单位售价 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <p class="single-item" v-for="(checked_item, index) in stock.relate_his_prods" :key="'price' + index">
                  <Input class="input-mini" v-model="checked_item._price" disabled />
                </p>
              </template>
            </td>

            <!-- 起购倍数 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <p
                  class="single-item"
                  v-for="(checked_item, index) in stock.relate_his_prods"
                  :key="'purchaseMultiple' + index"
                >
                  <InputNumber
                    :disabled="isDetailDisabled"
                    class="input-mini"
                    :min="1"
                    v-model="checked_item.buy_multiples"
                    placeholder="请输入起购倍数"
                    :precision="0"
                    @on-change="multiplesChange($event, stockkey, index)"
                  ></InputNumber>
                </p>
              </template>
            </td>
            <td :class="{ height100: stock.relate_his_prods.length == 1 }">
              <span v-if="isDetailDisabled" style="color: #999">关联诊所货品</span>
              <span v-else style="color: #155bd4" class="cursor" @click="connectStock(stockkey)">关联诊所货品</span>
            </td>
          </tr>
        </tbody>
        <tfoot v-if="!disabled">
          <tr>
            <td colspan="12">
              <label class="pull-left" style="line-height: 30px">批量设置：</label>
              <div v-show="batch_set_btn" class="pull-left" style="line-height: 30px">
                <a class="ks-batch-attr space6" @click="onBatchBtn('price')">价格</a>
                <a class="ks-batch-attr" @click="onBatchBtn('stored_price')" v-if="give == 'yes'">储值价</a>
                <a class="ks-batch-attr ml6" @click="onBatchBtn('stock')">库存</a>
              </div>
              <div v-show="!batch_set_btn" class="form-group pull-left">
                <InputNumber
                  element-id="batch-set-input"
                  class="input-mini"
                  :min="0"
                  v-model="batch_set_value"
                  :placeholder="batchPlaceholder"
                ></InputNumber>
                <!-- <Input ref="batch-set-input" type="number" v-model="batch_set_value" class="input-mini" /> -->
                <Dvd />
                <Button @click="onBatchSave">保存</Button>
                <Dvd />
                <Button @click="onBatchCancel">取消</Button>
              </div>
            </td>
          </tr>
        </tfoot>
      </table>
    </KWidget>

    <KWidget
      label="分账策略："
      :key="'fenzhang' + refreshStockTableKey"
      v-if="stockData.length > 0 && goods_type === '40'"
    >
      <table class="table goods-specs-table ks-specs-table" style="border: 1px solid #ddd">
        <thead>
          <tr>
            <template v-for="(specs, specskey) in specsData">
              <th :key="specskey">
                {{ specs.name }}
              </th>
            </template>
            <th><span class="text-danger">*</span>分账规则</th>
          </tr>
        </thead>

        <tbody class="ks-specs-table-body">
          <tr v-for="(stock, stockkey) in stockData" :key="stockkey">
            <td v-for="(item, index) in specsData" :key="index">
              {{ stock.specs_atoms[item.name] }}
            </td>
            <td>
              <div class="strategy-wrap" v-if="!isEmpty(stock.divide_rules)">
                <!-- <span style="white-space: nowrap">实付时≥</span>
                <custom-input-number
                  :key="'gtereal_price' + stockkey"
                  v-model="stock.divide_rules.real_price"
                  style="width: 210px; margin: 0 4px"
                  placeholder="请填写价格"
                  :disabled="disabled"
                >
                  <span slot="prepend">单次成本价</span>
                  <span slot="append">元</span>
                </custom-input-number> -->
                <!-- <span style="white-space: nowrap">销售提成按</span> -->
                <span style="white-space: nowrap">根据实收金额，销售提成按</span>
                <custom-input-number
                  :key="'gtedivide_value' + stockkey"
                  v-model="stock.divide_rules.gte.sales.divide_value"
                  type="number"
                  :step="0.01"
                  style="width: 230px; margin: 0 4px"
                  placeholder="请填写分账比例"
                  :disabled="disabled"
                >
                  <div slot="prepend" style="padding: 0 4px">分账比例</div>
                  <span slot="append">%</span>
                  <!-- <Select
                    v-model="stock.divide_rules.gte.sales.divide_type"
                    slot="prepend"
                    style="width: 85px; margin: 0 4px"
                    :disabled="disabled"
                  >
                    <Option value="ratio">分账比例</Option>
                    <Option value="fixed">固定分成</Option>
                  </Select>
                  <span slot="append">
                    {{ stock.divide_rules.gte.sales.divide_type === 'ratio' ? '%' : '元' }}
                  </span> -->
                </custom-input-number>
                <span style="white-space: nowrap">分账</span>
              </div>
              <!-- <div v-else style="white-space: nowrap">
                <span style="white-space: nowrap">实付时≥单次成本价</span>
                <span style="margin: 0 4px">{{ stock.divide_rules.real_price || 0 }}</span>
                <span style="white-space: nowrap">元，销售提成按</span>
                <span style="margin: 0 4px">{{ formatFenZhangText(stock, 'gte') }}</span>
                <span>分账</span>
              </div> -->
              <!-- <div class="strategy-wrap" v-if="!isEmpty(stock.divide_rules)">
                <span style="white-space: nowrap">实付时&lt;</span>
                <custom-input-number
                  :key="'ltreal_price' + stockkey"
                  v-model="stock.divide_rules.real_price"
                  type="number"
                  :step="0.01"
                  style="width: 210px; margin: 0 4px"
                  placeholder="请填写价格"
                  :disabled="disabled"
                >
                  <span slot="prepend">单次成本价</span>
                  <span slot="append">元</span>
                </custom-input-number>
                <span style="white-space: nowrap">销售提成按</span>
                <custom-input-number
                  :key="'ltdivide_value' + stockkey"
                  v-model="stock.divide_rules.lt.sales.divide_value"
                  type="number"
                  :step="0.01"
                  :active-change="false"
                  style="width: 230px; margin: 0 4px"
                  :disabled="disabled"
                  placeholder="请填写价格"
                >
                  <Select
                    v-model="stock.divide_rules.lt.sales.divide_type"
                    slot="prepend"
                    style="width: 85px; margin: 0 4px"
                    :disabled="disabled"
                  >
                    <Option value="ratio">分账比例</Option>
                    <Option value="fixed">固定分成</Option>
                  </Select>
                  <span slot="append">
                    {{ stock.divide_rules.lt.sales.divide_type === 'ratio' ? '%' : '元' }}
                  </span>
                </custom-input-number>
                <span style="white-space: nowrap">分账</span>
              </div>
              <div v-else style="white-space: nowrap">
                <span>实付时&lt;单次成本价</span>
                <span style="margin: 0 4px">{{ stock.divide_rules.real_price || 0 }}</span>
                <span>元，销售提成按</span>
                <span style="margin: 0 4px">{{ formatFenZhangText(stock, 'lt') }}</span>
                <span>分账</span>
              </div> -->
            </td>
          </tr>
        </tbody>
      </table>
    </KWidget>
    <KGoodsChoose
      :visible.sync="goodDiaVisible"
      @selectGoods="selectGoods"
      :checkedGoods="modalCheckedGoods"
    ></KGoodsChoose>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
/* eslint-disable */
import './index.less';
import Viewer from 'v-viewer';
import Vue from 'vue';
import KGoodsChoose from '@/components/k-goods-choose';
import { $operator } from '@/libs/operation';
import MaterialPicture from '@/components/MaterialCenter/MaterialPicture.vue';
import { cloneDeep  } from 'lodash-es';
import { isEmpty } from '@/utils/helper';
import CustomInputNumber from '@/components/CustomInputNumber';

import { isRstClinic } from '@/libs/runtime';

Vue.use(Viewer);
export default {
  name: 'k-goods-specs',
  components: {
    MaterialPicture,
    KGoodsChoose,
    CustomInputNumber,
  },
  props: {
    value: {
      type: Array,
      default() {
        return [];
      },
    },
    is_recharge_buy: {
      type: String,
      default: 'yes',
    },
    sell_ent_type: {
      type: String,
      default: 'CLI',
    },
    sw_scope_tmp: {
      type: Array,
      default: () => [],
    },
    storedList: {
      type: Array,
      default: () => [],
    },
    source_platform: {
      type: String,
      default: '',
    },
    is_in_tc: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    is_rst: {
      type: String,
      default: '2',
    },
    goods_type: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      specsData: [],
      stockData: [],

      add_specs_btn_show: true,
      refreshStockTableKey: 0,
      batch_set_btn: true,
      batch_set_key: '',
      batch_set_value: null,

      goodDiaVisible: false, // 关联货品弹窗
      checkedIndex: null,
      modalCheckedGoods: [], // 当前组关联的诊所货品,用于弹窗回显

      give: 'yes', // 储值购买,
      scope_tmp: [], // 上架范围
      sellType: ['CLI'],
      batchPlaceholder: '请输入批量价格',
    };
  },

  computed: {
    // 用与判断是否是榕树堂
    isRst() {
      return isRstClinic();
    },

    formatFenZhangText() {
      return (row, type) => {
        if (!row.divide_rules?.[type]) return '-';
        const { divide_type, divide_value } = row.divide_rules?.[type].sales || {};
        const text1 = `${divide_type === 'ratio' ? '分账比例' : '固定金额'}`;
        const text2 = `${divide_type === 'ratio' ? '%' : '元'}`;
        return ` ${text1 || '-'}   ${divide_value || '-'}${text2 || '-'} `;
      };
    },
    // 是否禁止修改
    isDetailDisabled() {
      // 详情禁止编辑，如果是诊所自定义的，可以编辑
      let isCLI = this.source_platform === 'CLI';
      return this.$route.query.id && !isCLI;
    },
    recommendPrice() {
      return list => {
        let price = 0;
        if (list.relate_his_prods.length) {
          list.relate_his_prods.forEach(item => {
            let totol_price = $operator.multiply(Number(item.buy_multiples), Number(item._price));
            price = $operator.add(price, totol_price);
          });
        }
        return price;
      };
    },
    // 是否被关联到了套餐商品
    isConnectedTaocan() {
      return this.is_in_tc === '1';
    },
  },

  created() {
    this.tableMergeCell();
    // 编辑回显
    if (this.$route.query.id) {
      console.log('=>(k-goods-specs.vue:403) this.stockData', this.stockData);
      this.stockData.forEach(item => {
        item.relate_his_prods.forEach(prods => {
          prods._sales_unit = this.setDefaultUnit(prods).unit || '';
          prods._stock_num = this.setDefaultUnit(prods).stock_num || '';
          prods._price = this.setDefaultUnit(prods).price || '';
        });
      });
    }
  },

  methods: {
    isEmpty,
    // 如果储值价设置为小于0，置为空
    storedPriceChange(val, index) {
      if (Number(val) == 0) {
        this.stockData[index].stored_price = null;
        let customR = `sStored${index}`;
        this.$refs[customR][0].currentValue = null;
      }
    },
    onBatchBtn: function (key) {
      if (key === 'price') {
        this.batchPlaceholder = '请批量设置价格';
      }
      if (key === 'stock') {
        this.batchPlaceholder = '请批量设置可售库存';
      }
      if (key === 'stored_price') {
        this.batchPlaceholder = '请批量设置储值价';
      }
      this.batch_set_btn = false;
      this.batch_set_key = key;
      this.$nextTick(() => {
        document.getElementById('batch-set-input').focus();
      });
    },
    onBatchSave: function () {
      for (let key in this.stockData) {
        // 价格批量
        if (this.batch_set_key === 'price') {
          this.stockData[key][this.batch_set_key] = Number(this.batch_set_value);
        } else if (this.batch_set_key === 'stock') {
          console.log('-> batch_set_key', key, this.batch_set_key);
          if (!this.stockData[key].relate_his_prods.length) {
            this.stockData[key][this.batch_set_key] = Number(this.batch_set_value);
          }
        } else {
          this.stockData[key][this.batch_set_key] = Number(this.batch_set_value);
        }
      }
      this.onBatchCancel();
    },
    onBatchCancel: function () {
      this.batch_set_btn = true;
      this.batch_set_key = '';
      this.batch_set_value = null;
    },

    // 添加规格值
    onAddAtom: function (index) {
      this.specsData[index].specs_atoms.push(['']);
      this.tableMergeCell();

      let n = 'input-' + index + '-' + (this.specsData[index].specs_atoms.length - 1);
      this.$nextTick(() => this.$refs[n][0].focus());
    },

    // 删除规格值
    onDelAtom: function (index, atom_index) {
      this.specsData[index].specs_atoms.splice(atom_index, 1);
      this.tableMergeCell();
    },

    // 添加规格
    onAddSpecs: function () {
      this.specsData.push({
        name: '',
        isAddImg: false,
        specs_atoms: [['']],
      });

      if (this.specsData.length >= 3) {
        this.add_specs_btn_show = false;
      } else {
        this.add_specs_btn_show = true;
      }

      this.tableMergeCell();

      let n = 'input-' + (this.specsData.length - 1);
      this.$nextTick(() => this.$refs[n][0].focus());
    },

    // 删除规格
    onDelSpecs: function (index) {
      this.specsData.splice(index, 1);

      if (this.specsData.length >= 3) {
        this.add_specs_btn_show = false;
      } else {
        this.add_specs_btn_show = true;
      }

      this.tableMergeCell();
    },

    // 删除规格图片
    onDelSpecsImg: function (atom_index) {
      this.$set(this.specsData[0].specs_atoms[atom_index], 1, '');
    },

    onUploadSuccess: function (atom_index, img) {
      this.$set(this.specsData[0].specs_atoms[atom_index], 1, img);
    },

    //渲染商品库存模块
    render_goods_stock: function (goods_specs_data, goods_stock_data) {
      if (S.isUndefined(goods_specs_data)) {
        return true;
      }

      let wait_combination_arrs = [];

      goods_specs_data.forEach((item, index) => {
        let atoms = [];
        item.specs_atoms.forEach(atom_item => {
          atoms.push(atom_item[0]);
        });
        if (!S.isEmptyObject(atoms)) {
          wait_combination_arrs.push(atoms);
        }
      });

      let specs_combination = [];
      let tmp_specs_combination = this.bigcombination(wait_combination_arrs);
      let default_goods_attr_fields = {
        id: 0,
        price: null,
        stored_price: null,
        stock: 0,
        sku: '',
        cost_price: 0,

        // 每一款规格下关联的诊所货品
        relate_his_prods: [],
      };

      // 如果是榕树堂实物商品，增加一线城市价格 和 非一线城市价格
      if (this.goods_type === '40') {
        default_goods_attr_fields.first_tier_price = null;
        default_goods_attr_fields.not_first_tier_price = null;
        default_goods_attr_fields.first_tier_vip_price = null;
        default_goods_attr_fields.not_first_tier_vip_price = null;
        default_goods_attr_fields.divide_rules = cloneDeep({
          real_price: null,
          gte: {
            serve: null,
            sales: {
              divide_type: 'ratio',
              divide_value: '',
            },
          },
          lt: {
            serve: null,
            sales: {
              divide_type: 'ratio',
              divide_value: '',
            },
          },
        });
      }
      let new_goods_stock_data = [];

      for (let i = 0; i < tmp_specs_combination.length; i++) {
        let tmp_comb = tmp_specs_combination[i];
        goods_specs_data.forEach((specs_item, k) => {
          if (S.isUndefined(specs_combination[i])) {
            specs_combination[i] = {};
          }
          specs_combination[i][specs_item.name] = tmp_comb[k];
        });

        let specs_atoms = specs_combination[i];

        new_goods_stock_data[i] = cloneDeep(default_goods_attr_fields);
        new_goods_stock_data[i] = S.merge(new_goods_stock_data[i], { specs_atoms: specs_atoms });

        let match_goods_stock_item = [];
        goods_stock_data.forEach(goods_stock_item => {
          let is_match = true;
          for (let specs_name in goods_stock_item.specs_atoms) {
            if (goods_stock_item.specs_atoms[specs_name] != specs_atoms[specs_name]) {
              is_match = false;
            }
          }
          for (let specs_name in specs_atoms) {
            if (goods_stock_item.specs_atoms[specs_name] != specs_atoms[specs_name]) {
              is_match = false;
            }
          }
          if (is_match) {
            match_goods_stock_item = cloneDeep(goods_stock_item);
          }
        });

        if (!S.isEmptyObject(match_goods_stock_item)) {
          new_goods_stock_data[i] = S.merge(cloneDeep(new_goods_stock_data[i]), match_goods_stock_item);
        }
      }

      this.stockData = cloneDeep(new_goods_stock_data);
    },

    tableMergeCell: function () {
      this.$nextTick(() => {
        this.refreshStockTableKey += 1;
        // 合并单元格
        setTimeout(() => {
          let tb = document.getElementsByClassName('ks-specs-table-body')[0];
          for (let col = this.specsData.length - 2; col >= 0; col--) {
            this.mergeCell(tb, 0, this.stockData.length - 1, col);
          }
          if (this.goods_type === '40') {
            let tb1 = document.getElementsByClassName('ks-specs-table-body')[1];
            for (let col = this.specsData.length - 2; col >= 0; col--) {
              this.mergeCell(tb1, 0, this.stockData.length - 1, col);
            }
          }
        }, 20);
      });
    },

    // 合并单元格
    // start_row 起始行 (从0开始)
    // end_row 结束行 (从0开始)
    // col 合并的列号，对第几列进行合并 (从0开始)
    mergeCell: function (tb, start_row, end_row, col) {
      for (let i = start_row; i < end_row; i++) {
        if (tb.rows[start_row].cells[col].innerHTML == tb.rows[i + 1].cells[col].innerHTML) {
          tb.rows[i + 1].removeChild(tb.rows[i + 1].cells[col]);
          tb.rows[start_row].cells[col].rowSpan = tb.rows[start_row].cells[col].rowSpan + 1;
        } else {
          this.mergeCell(tb, i + 1, end_row, col);
          break;
        }
      }
    },

    //多个一维数组所有可能的组合
    bigcombination: function (arrs) {
      if (arrs.length < 1) {
        return arrs;
      }

      let _comb = function (a, b) {
        let arr = Array();
        for (let i = 0; i < a.length; i++) {
          if (S.isUndefined(b)) {
            arr.push(a[i]);
          } else {
            for (let j = 0; j < b.length; j++) {
              arr.push(a[i] + '#@#' + b[j]);
            }
          }
        }
        return arr;
      };

      let combarrs = _comb(arrs[0], arrs[1]);
      let index = 2;
      while (true) {
        if (arrs[index]) {
          combarrs = _comb(combarrs, arrs[index]);
          index++;
        } else {
          break;
        }
      }

      for (let i = 0; i < combarrs.length; i++) {
        combarrs[i] = combarrs[i].split('#@#');
      }

      return combarrs;
    },

    // 关联的诊所货品数据
    selectGoods(selectGoodsList) {
      const { checkedIndex } = this;
      let relate_his_prods = [];
      selectGoodsList.forEach(item => {
        relate_his_prods.push({
          _sales_unit: this.setDefaultUnit(item).unit || '',
          _stock_num: this.setDefaultUnit(item).stock_num || '',
          _price: this.setDefaultUnit(item).price || '',
          buy_multiples: 1,
          prod_id: item.id,
          sales_units: item.sales_units,
          generic_name: item.generic_name,
          prod_type_text: item.prod_type_text,
        });
      });
      console.log('selectGoodsList', selectGoodsList);
      this.stockData[checkedIndex].relate_his_prods = [
        ...this.stockData[checkedIndex].relate_his_prods,
        ...relate_his_prods,
      ];

      // this.stockData[checkedIndex].checkedGoodList = [...this.stockData[checkedIndex].checkedGoodList, ...selectGoodsList]

      this.calcSaleStock(checkedIndex);
    },

    // 计算可售库存
    calcSaleStock(index) {
      if (!this.stockData.length) return;
      let saleStockNum = 0;

      let multiplesList = [];
      let _wait_stock_list = this.stockData[index].relate_his_prods;

      _wait_stock_list.forEach((stock_item, stock_index) => {
        multiplesList.push(Math.floor(stock_item._stock_num / stock_item.buy_multiples));
      });

      saleStockNum = multiplesList.sort((a, b) => a - b)[0] || 0;

      this.stockData[index].stock = saleStockNum;
    },

    // 关联诊所货品点击
    connectStock(index) {
      this.goodDiaVisible = true;
      this.checkedIndex = index;
      this.modalCheckedGoods = this.stockData[index].relate_his_prods;
    },

    // 如果货单单位有选中的，默认带出单位，库存，单位售价
    setDefaultUnit(item) {
      // if ( !item.length ) return {}
      let resultList = item.sales_units.filter(item => item.checked == '1');
      return resultList[0] || {};
    },

    // 移除单个商品
    removeCheckedGoods(stockIndex, currentGoodsIndex) {
      this.stockData[stockIndex].relate_his_prods.splice(currentGoodsIndex, 1);
      this.calcSaleStock(stockIndex);
    },

    // 单位切换
    saleChange(val, stockKey, index, sales_units) {
      let unit_index = '';
      sales_units.forEach((item, index) => {
        if (val.value === item.unit) {
          unit_index = index;
        }
      });
      let _item = this.stockData[stockKey].relate_his_prods[index];
      _item._stock_num = sales_units[unit_index].stock_num;
      _item._price = sales_units[unit_index].price;
      _item._sales_unit = val.value;

      _item.sales_units.sort();
      this.calcSaleStock(stockKey);
    },

    // 起购倍数变化
    multiplesChange(e, stockKey, index) {
      if (e === null || e === 0) {
        let _header = this.stockData[stockKey].relate_his_prods;
        // _header[index].buy_multiples = 1
      } else {
        this.calcSaleStock(stockKey);
      }
    },
  },

  watch: {
    is_recharge_buy: {
      immediate: true,
      handler(val) {
        this.give = val;
      },
    },

    sw_scope_tmp: {
      immediate: true,
      handler(val) {
        this.scope_tmp = val;
      },
    },

    sell_ent_type: {
      immediate: true,
      handler(val) {
        this.sellType = val;
      },
    },

    specsData: {
      deep: true,
      handler: function (val) {
        this.render_goods_stock(val, this.stockData);
        let value = [...this.value];
        value[0] = val;
        this.$emit('input', value);
      },
    },

    stockData: {
      deep: true,
      handler: function (val) {
        let value = [...this.value];
        value[1] = val;
        this.$emit('input', value);
      },
    },

    value: {
      immediate: true,
      handler: function (val) {
        if (!S.isUndefined(val[0])) {
          this.specsData = val[0];
          if (this.specsData.length >= 3) {
            this.add_specs_btn_show = false;
          }
        }
        if (!S.isUndefined(val[1])) {
          // 价格，可售库存，起购倍数均转化为number类型
          val[1].map(item => {
            item.price = Number(item.price) == 0 ? null : Number(item.price);
            item.stored_price = Number(item.stored_price) == 0 ? null : Number(item.stored_price);
            if (!item.relate_his_prods.length) {
              item.stock = Number(item.stock) == 0 ? null : Number(item.stock);
            } else {
              item.stock = Number(item.stock);
            }
            item.relate_his_prods.forEach(relate_item => {
              relate_item.buy_multiples = Number(relate_item.buy_multiples);
            });
          });
          this.stockData = val[1];
        }

        // isAddImg 转换成Bool类型
        for (let key in this.specsData) {
          if (!this.specsData[key]['isAddImg'] || this.specsData[key]['isAddImg'] == '0') {
            this.specsData[key]['isAddImg'] = false;
          } else {
            this.specsData[key]['isAddImg'] = true;
          }
        }
      },
    },
  },
};
</script>

<style lang="less">
.cursor {
  cursor: pointer;
  color: #155bd4;
}

.margin-left10 {
  margin-left: 10px;
}

.single-item {
  // height: 30px;
  margin-bottom: 20px;
  line-height: 30px;

  &:nth-last-child(1) {
    margin-bottom: 0;
  }
}

.recommendTip {
  color: #999;
  position: absolute;
  margin-top: 10px;
}

.height100 {
  height: 100px !important;
}

.helpIcon {
  padding-top: 2px;
  cursor: pointer;
  width: 16px !important;
  height: 16px !important;
}

.ml6 {
  margin-left: 6px;
}

.sw-wrapper {
  .ivu-checkbox {
    width: 18px;
  }
}
.strategy-wrap {
  display: flex;
  align-items: center;
  > span {
    margin: 10px;
  }
  > span:first-child {
    margin-left: 0;
  }
  .ivu-select-disabled .ivu-select-selection {
    color: #333333;
  }
}
</style>
