<template>
  <div>
    <div>
      <KWidget label="兑换次数：" required>
        <InputNumber
          :disabled="isDetailDisabled"
          v-model="formData.exchange_num"
          :max="100"
          show-word-limit
          :precision="0"
          :min="0"
          style="width: 150px"
          placeholder="请输入兑换次数"
        />
        <span class="note" style="display: inline-block">兑换次数最多不能超过100次</span>
      </KWidget>
      <KWidget label="服务范围：" text required>
        <span class="space6" v-if="isDetailDisabled">添加服务</span>
        <a v-else class="space6" style="color: #155bd4" @click="addService">添加服务</a>
        <span class="note" style="display: inline-block">设置此商品支持兑换的服务范围</span>
        <table v-if="formData.services && formData.services.length > 0" class="table" style="width: 600px">
          <thead>
            <tr>
              <th>兑换服务范围</th>
              <!--              <th>来源</th>-->
              <th>价格</th>

              <th>最多可兑次数</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in formData.services" :key="key">
              <td>{{ item.name }}</td>
              <!--              <td>{{ item.source_platform_text }}</td>-->
              <!-- <td v-if="!isRst">{{ item.price | number_format }}</td>
              <td v-if="isRst">{{ item.first_tier_price | number_format }}</td>
              <td v-if="isRst">{{ item.not_first_tier_price | number_format }}</td> -->
              <td v-if="!isRst">{{ item.price | number_format }}</td>
              <td v-if="isRst">
                <div>销售价：¥{{ item.price | number_format }}</div>
                <div>会员价：¥{{ item.vip_price | number_format }}</div>
              </td>
              <td>
                <InputNumber
                  :disabled="isDetailDisabled"
                  v-model="item.times"
                  controls-outside
                  :precision="0"
                  :min="1"
                  :max="50"
                  style="width: 100px"
                />
              </td>
              <td>
                <span v-if="isDetailDisabled">-</span>
                <a v-else @click="onDelService(key)" style="color: #155bd4">删除</a>
              </td>
            </tr>
          </tbody>
        </table>
      </KWidget>
      <KWidget label="备注：">
        <Input
          v-model="formData.remark"
          maxlength="20"
          show-word-limit
          placeholder="请输入备注"
          :disabled="isRstClinic"
        />
      </KWidget>
      <div class="block-header"><span>价格库存</span></div>

      <KWidget v-if="!isRst" label="价格：" required>
        <InputNumber v-model="formData.price" :precision="2" :active-change="false" :min="0" :disabled="isRstClinic" />
        元
      </KWidget>
      <KWidget v-else label="价格：" required>
        <div class="flex gap-10">
          <div>
            销售价：<InputNumber
              v-model="formData.price"
              :precision="2"
              :active-change="false"
              :min="0"
              :disabled="isRst"
            />
          </div>
          <div>
            会员价：<InputNumber
              v-model="formData.vip_price"
              :precision="2"
              :active-change="false"
              :min="0"
              :disabled="isRst"
            />
          </div>
        </div>
      </KWidget>

      <template v-if="!isRst">
        <KWidget label="储值价：" v-show="formData.is_recharge_buy == 'yes'">
          <InputNumber
            v-model="formData.stored_price"
            ref="stored"
            style="width: 120px"
            placeholder="请输入储值价"
            :precision="2"
            :min="0"
            @on-change="storedPriceChange"
            :active-change="false"
          />
          元
        </KWidget>
      </template>

      <KWidget label="库存：" required>
        <InputNumber v-model="formData.stock" :precision="0" :active-change="false" :min="0" />
      </KWidget>

      <div class="block-header"><span>服务设置</span></div>
      <KWidget label="商品有效期：" required>
        <RadioGroup v-model="formData.service_info.expiration.type">
          <Radio label="1" :disabled="echoData.goods_type == '45'"><span></span></Radio>
          <div style="display: inline-block; margin-left: -10px">
            购买后
            <InputNumber
              v-model="formData.service_info.expiration.days"
              :min="1"
              placeholder="自定义"
              :disabled="echoData.goods_type == '45'"
            />
            天内有效{{ echoData.goods_type }}
          </div>
          <div class="block_10"></div>
          <Radio label="2" :disabled="echoData.goods_type == '45'"><span></span></Radio>
          <div style="display: inline-block; margin-left: -10px">
            截止到
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              :value="formData.service_info.expiration.date"
              @on-change="formData.service_info.expiration.date = arguments[0]"
              placeholder="请选择日期"
              :options="expiration_datepicker_options"
              :disabled="formData.service_info.expiration.type != 2 || echoData.goods_type == '45'"
            />
            <!-- :disabled="formData.service_info.expiration.type != 2" -->
            日有效
          </div>
        </RadioGroup>
      </KWidget>

      <KWidget label="上架范围：" required text>
        <CheckboxGroup v-model="formData.xn_scope_tmp">
          <Checkbox :disabled="isRstClinic" label="1">零售服务</Checkbox>
          <Checkbox :disabled="isRstClinic" label="2">问诊治疗</Checkbox>

          <!--          &lt;!&ndash; 问诊治疗不显示 &ndash;&gt;-->
          <!--          <Tooltip placement="top" max-width="300" content="该类型的商品暂不允许添加到【问诊治疗】中">-->
          <!--            <Checkbox label="disabled" :disabled="true">问诊治疗</Checkbox>-->
          <!--          </Tooltip>-->
        </CheckboxGroup>
      </KWidget>

      <KWidget v-if="!isRst" label="储值购买: " required text>
        <Radio-group v-model="formData.is_recharge_buy">
          <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">{{ item.desc }}</Radio>
        </Radio-group>
      </KWidget>
    </div>

    <k-goods-services
      v-model="serviceModalVisible"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
      :optionsList="optionsList"
    />
  </div>
</template>

<script>
import S from '@/libs/util';
import { cloneDeep } from 'lodash-es';
import KGoodsServices from '@/components/k-goods-services';
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'ExchangeCoupon',
  mixins: [],
  components: {
    KGoodsServices,
  },
  props: {
    // 储值购买枚举
    storedList: {
      type: Array,
      default: () => [],
    },
    sellEntTypes: {
      type: Array,
      default: () => [],
    },
    echoData: {
      type: Object,
      default: () => ({}),
    },
    optionsList: {
      type: Object,
      default() {
        return {
          servTypeDesc: [],
          sourcePlatformDesc: [],
        };
      },
    },
  },
  data() {
    return {
      formData: {
        service_info: {
          expiration: {
            type: '1', // 1 购买后${days}天内有效；2 ${date}日期前有效；
            date: '', //
            days: 365, //
          },
        },
        exchange_num: null, // 兑换次数
        vip_price: 0,
        price: 0, // 价格
        stored_price: null, // 储值价
        stock: null,
        services: [], //  服务
        remark: '', // 备注
        xn_scope_tmp: ['1'], // 上架范围
        is_recharge_buy: 'yes', // 储值购买
      },

      serviceModalVisible: false,

      // 截止日期
      expiration_datepicker_options: {
        disabledDate(date) {
          return S.moment(date).unix() <= S.moment(new Date()).unix();
        },
      },
    };
  },
  watch: {
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val) || !['25', '45'].includes(val.goods_type)) return;
        this.handleEchoData(val);
      },
      immediate: true,
    },

    'echoData.goods_type': {
      handler(val) {
        this.formData.is_recharge_buy = val == '45' ? '' : 'yes';
      },
      immediate: true,
    },
  },
  computed: {
    // 是否禁止修改
    isDetailDisabled() {
      // 详情禁止编辑，如果是诊所自定义的，可以编辑
      let isCLI = this.echoData.source_platform === 'CLI';

      return this.$route.query.id && !isCLI;
    },

    isRst() {
      return this.echoData.goods_type == '45';
    },
    isRstClinic() {
      return isRstClinic();
    },
  },
  mounted() {},
  methods: {
    // 通兑券校验
    validateForm() {
      const data = this.formData;

      if (data.exchange_num <= 0) {
        this.$Message.error('请填写兑换次数');
        return false;
      }

      if (data.services.length <= 0) {
        this.$Message.error('请添加服务权益');
        return false;
      }

      // 是否有空
      let isHasEmpty = false;
      // 是否有单个服务兑换次数大于总兑换次数
      let isSingleMore = false;

      const totalTimes = data.services.reduce((total, item) => {
        if (item.times == null) {
          isHasEmpty = true;
        }
        if (item.times > data.exchange_num) {
          isSingleMore = true;
          this.$Message.error(`【${item.name}】兑换次数不能大于服务权益总次数`);
          // item.times = data.exchange_num;
        }
        return total + item.times;
      }, 0);

      // 单个服务超出,拦截
      if (isSingleMore) {
        return false;
      }

      if (!isHasEmpty && totalTimes < data.exchange_num) {
        this.$Message.error('兑换次数不能大于服务权益总次数');
        return false;
      }

      // eslint-disable-next-line no-useless-escape
      if (!/^\d([\d\.]+)?/.test(data.price)) {
        this.$Message.error('请输入有效的商品价格');
        return false;
      }
      let price = parseFloat(data.price);
      if (price < 0.01) {
        this.$Message.error('商品价格不允许小于0.01元');
        return false;
      }
      if (price > 100000) {
        this.$Message.error('商品价格不允许大于100000.00元');
        return false;
      }

      if (data.stock <= 0) {
        this.$Message.error('请填写库存');
        return false;
      }

      // 储值价非必填
      // if ( data.is_recharge_buy == 'yes' && data.stored_price < 0.01) {
      //   this.$Message.error( '储值价不允许小于0.01元' )
      //   return false
      // }

      if (data.xn_scope_tmp.length == 0) {
        this.$Message.error('请选择通兑券上架范围');
        return false;
      }
      if (data.service_info.expiration.type === '1') {
        if (!data.service_info.expiration.days) {
          this.$Message.error('请输入购买后有效期天数');
          return false;
        }
      } else {
        if (!data.service_info.expiration.date) {
          this.$Message.error('请选择购买后有效期');
          return false;
        }
      }

      if (!this.isRst) {
        if (!data.is_recharge_buy) {
          this.$Message.error('请选择储值购买');
          return false;
        }
      }

      return true;
    },

    // 获取通兑券的数据
    getFormData() {
      let formData = {
        ...this.formData,
      };
      formData.xn_scope = this.formData.xn_scope_tmp.length == 2 ? '9' : this.formData.xn_scope_tmp[0];
      delete formData.xn_scope_tmp;
      return formData;
    },

    handleEchoData(goods) {
      console.log('🚀 ~ handleEchoData ~ goods=>', goods);
      this.formData.exchange_num = Number(goods.exchange_num);

      for (let key in goods.services ? goods.services : []) {
        goods.services[key].times =
          Number(goods.services[key].times || 0) === 0 ? null : Number(goods.services[key].times || 0);
      }
      this.formData.services = goods.services;

      this.formData.remark = goods.remark;

      this.formData.price = Number(goods.price);
      if (this.isRst) {
        this.formData.vip_price = Number(goods.vip_price);
      }
      this.formData.stored_price = goods.stored_price == '' ? null : Number(goods.stored_price);
      this.formData.stock = Number(goods.stock || 0);

      this.formData.service_info = !S.isEmptyObject(goods.service_info)
        ? goods.service_info
        : this.formData.service_info;
      this.formData.service_info.expiration.days = Number(this.formData.service_info.expiration.days);

      // 关闭问诊治疗的勾选，对于已经选中的数据，拿掉
      this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1', '2'] : [goods.xn_scope];
      // this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1'] : [goods.xn_scope];
      this.formData.xn_scope = goods.xn_scope;
      this.formData.is_recharge_buy = goods.is_recharge_buy;
      this.originFormData = cloneDeep(this.formData);
      this.$delete(this.originFormData, 'xn_scope_tmp');
    },

    addService() {
      this.serviceModalVisible = true;
    },

    // 删除服务
    onDelService: function (index) {
      this.formData.services.splice(index, 1);
    },

    // 储值价
    storedPriceChange(val) {
      if (Number(val) == 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },

    // 选中
    onSelectedService: function (items) {
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          price: item.price,
          times: isExist === -1 ? 1 : this.formData.services[isExist].times,
        };
      });
      this.serviceModalVisible = false;
    },
  },
};
</script>

<style scoped lang="less"></style>
