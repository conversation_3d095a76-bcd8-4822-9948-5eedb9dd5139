<template>
  <div>
    <div>
      <KWidget label="设置套餐:" text required>
        <span class="space6" v-if="isDetailDisabled">添加产品</span>
        <a class="space6" v-else @click="mealModalVisible = true">添加产品</a>
        <span class="note" style="display: inline-block">支持添加实物、虚拟或通兑券商品组合售卖</span>
        <table v-if="formData.meal_lists.length > 0" class="table" style="max-width: 1000px">
          <thead>
            <tr>
              <th>ID</th>
              <th>商品名</th>
              <th>类型</th>
              <th>规格</th>
              <th style="width: 260px">价格</th>
              <th>库存</th>
              <th>数量</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in formData.meal_lists" :key="key">
              <td>{{ item.goodsId }}</td>
              <td class="goodsName">{{ item.name }}</td>
              <td>{{ item.goods_type_text }}</td>
              <td class="custom-select">
                <template v-if="item.goods_type == '10' || item.goods_type == '40'">
                  <Select
                    v-model="item.skuId"
                    :disabled="isDetailDisabled"
                    style="width: 130px"
                    @on-change="specsChange($event, key)"
                  >
                    <Option
                      v-for="(attrs_item, index) in item.attrs"
                      :value="attrs_item.id"
                      :label="attrs_item.spec"
                      :key="'specs' + index"
                      :disabled="item.skuId == attrs_item.id ? false : isDisabled(item.goodsId, attrs_item.id)"
                    >
                      {{ attrs_item.spec }} <span>￥{{ attrs_item.price }}</span>
                    </Option>
                  </Select>
                </template>
                <p v-else>-</p>
              </td>
              <td>
                <div v-if="!isRstClinic">
                  <template v-if="!item.chooseGoodsPrice && item.price_section">
                    <p v-if="!item.price_section.max" v-text-format.number="item.price_section.min"></p>
                    <p v-else>￥{{ item.price_section.min }} - ￥{{ item.price_section.max }}</p>
                  </template>
                  <p v-else v-text-format.number="item.chooseGoodsPrice"></p>
                </div>
                <div v-else>
                  <div>
                    <span>销售价：¥ {{ getPyGoodsPrice(item).price | number_format }}</span>
                  </div>
                  <div>
                    <span> 会员价：¥ {{ getPyGoodsPrice(item).vip_price | number_format }} </span>
                  </div>
                </div>

                <!-- <template v-if="item.goods_type === '40'">
                  <div>
                    <span>一&nbsp;&nbsp;&nbsp;线：{{ getPyGoodsPrice(item).first_tier_price | number_format }}</span>
                    <span style="margin-left: 8px">
                      会员价：{{ getPyGoodsPrice(item).first_tier_vip_price | number_format }}
                    </span>
                  </div>
                  <div>
                    <span>非一线：{{ getPyGoodsPrice(item).not_first_tier_price | number_format }}</span>
                    <span style="margin-left: 8px"
                      >会员价：{{ getPyGoodsPrice(item).not_first_tier_vip_price | number_format }}</span
                    >
                  </div>
                  <div>
                    <span>非一线：{{ getPyGoodsPrice(item).not_first_tier_price | number_format }}</span>
                    <span style="margin-left: 8px"
                      >会员价：{{ getPyGoodsPrice(item).not_first_tier_vip_price | number_format }}</span
                    >
                  </div>
                </template>
                <template v-else-if="!item.chooseGoodsPrice && item.price_section">
                  <p v-if="!item.price_section.max">￥{{ item.price_section.min }}</p>
                  <p v-else>
                    ￥{{ item.price_section.min | number_format }} - ￥{{ item.price_section.max | number_format }}
                  </p>
                </template>
                <p v-else>￥{{ item.chooseGoodsPrice | number_format }}</p> -->
                <!-- 未选规格时，显示区间 -->
                <!-- <template v-if="!item.chooseGoodsPrice && item.price_section">
                  <p v-if="!item.price_section.max" v-text-format.number="item.price_section.min"></p>
                  <p v-else>￥{{ item.price_section.min }} - ￥{{ item.price_section.max }}</p>
                </template>
                <p v-else v-text-format.number="item.chooseGoodsPrice"></p> -->
              </td>
              <td>{{ item.skuStock }}</td>
              <td>
                <InputNumber
                  v-model="item.num"
                  :disabled="isDetailDisabled"
                  controls-outside
                  :min="1"
                  :max="Number(item.skuStock)"
                  style="width: 100px"
                />
              </td>
              <td>
                <span v-if="isDetailDisabled">-</span>
                <template v-else>
                  <a v-if="!isLastPhysical(item)" @click="onDelMeal(key)">删除</a>
                  <Poptip
                    width="200"
                    :disabled="!isLastPhysical(item)"
                    v-else
                    confirm
                    title="套餐如果不关联实物商品，则关联赠送的服务卡券也将被清空"
                    @on-ok="onDelMeal(key)"
                  >
                    <a>删除</a>
                  </Poptip>
                </template>
              </td>
            </tr>
          </tbody>
        </table>
      </KWidget>

      <div class="block-header"><span>价格库存</span></div>

      <KWidget v-if="!isRstClinic" label="价格：" required>
        <InputNumber
          v-model="formData.price"
          :precision="2"
          :active-change="false"
          :min="0"
          :disabled="echoData.goods_type == 35"
        />
        元
        <span style="margin-left: 20px; color: #999" v-if="suggestPrice">建议售价: ￥{{ suggestPrice }}</span>
      </KWidget>
      <KWidget v-else label="价格：" required>
        <div class="flex gap-10">
          <div>
            销售价：<InputNumber
              v-model="formData.price"
              :precision="2"
              :active-change="false"
              :min="0"
              :disabled="isRstClinic"
            />
          </div>
          <div>
            会员价：<InputNumber
              v-model="formData.vip_price"
              :precision="2"
              :active-change="false"
              :min="0"
              :disabled="isRstClinic"
            />
          </div>
        </div>
        <span style="margin-left: 20px; color: #999" v-if="suggestPrice">建议售价: ￥{{ suggestPrice }}</span>
      </KWidget>

      <KWidget label="储值价：" v-show="formData.is_recharge_buy == 'yes' && echoData.goods_type != 35">
        <InputNumber
          v-model="formData.stored_price"
          ref="stored"
          style="width: 120px"
          placeholder="请输入储值价"
          :precision="2"
          :min="0"
          @on-change="storedPriceChange"
          :active-change="false"
        />
        元
      </KWidget>

      <KWidget label="库存：" required v-if="echoData.goods_type != 35">
        <InputNumber v-model="formData.stock" :precision="0" :active-change="false" :min="0" :disabled="isRstClinic" />
        <span style="marginleft: 20px; color: #999">若套餐中包含实物商品，将自动按实物商品库存量计算可售库存</span>
      </KWidget>

      <div style="margin-top: 20px" v-if="echoData.goods_type != 35">
        <div class="block-header">
          <span>关联赠送服务卡券</span>
          <Tooltip max-width="200" content="套餐商品中添加了实物商品，才可关联赠送卡券">
            <Icon type="md-help-circle" size="16" />
          </Tooltip>
        </div>
        <KWidget label="是否关联：" required class="flex-item-align">
          <Radio-group v-model="formData.relation_card">
            <Radio
              :disabled="!isCanEdit || !isTcHasPhysicalGoods"
              :label="item.id"
              v-for="(item, index) in relationList"
              :key="'stored' + index"
              >{{ item.desc }}
            </Radio>
          </Radio-group>
          <Button
            type="default"
            :disabled="!isCanEdit || !isTcHasPhysicalGoods"
            class="ml10"
            v-if="formData.relation_card == 1"
            @click="serviceModalVisible = true"
            >添加关联服务
          </Button>
        </KWidget>

        <KWidget label="发放方式：" v-if="formData.relation_card == 1" required class="flex-item-align">
          <Radio-group v-model="formData.grant_type">
            <Radio
              :disabled="!isCanEdit || !isTcHasPhysicalGoods || echoData.goods_type == 35"
              :label="item.id"
              v-for="(item, index) in grantList"
              :key="'stored' + index"
              >{{ item.desc }}
            </Radio>
          </Radio-group>
        </KWidget>

        <KWidget label="" v-if="formData.relation_card == 1">
          <table class="table" style="width: 500px">
            <thead>
              <tr>
                <th>服务</th>
                <th>来源</th>
                <th>类型</th>
                <th>发放数量</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, key) in formData.services" :key="key">
                <td>{{ item.name }}</td>
                <td>{{ item.source_platform_text }}</td>
                <td>{{ item.serv_type_text }}</td>
                <td>
                  <InputNumber
                    :disabled="!isCanEdit"
                    v-model="item.times"
                    controls-outside
                    :min="1"
                    :max="50"
                    style="width: 100px"
                    v-if="formData.grant_type == 1"
                  />
                  <div v-else>-</div>
                </td>
                <td>
                  <a :disabled="!isCanEdit" @click="onDelService(key)">删除</a>
                </td>
              </tr>
            </tbody>
          </table>
        </KWidget>
      </div>

      <div class="block-header"><span>服务设置</span></div>

      <KWidget label="上架范围：" required text>
        <CheckboxGroup v-model="formData.xn_scope_tmp">
          <Checkbox :disabled="echoData.goods_type == 35" label="1">零售服务</Checkbox>
          <!-- 问诊治疗不显示 -->
          <!--          <Tooltip placement="top" max-width="300" content="该类型的商品暂不允许添加到【问诊治疗】中">-->
          <!--            <Checkbox label="disabled" :disabled="true">问诊治疗</Checkbox>-->
          <!--          </Tooltip>-->
          <Checkbox :disabled="echoData.goods_type == 35" label="2">问诊治疗</Checkbox>
        </CheckboxGroup>
      </KWidget>

      <KWidget label="储值购买: " required text v-if="echoData.goods_type != 35">
        <Radio-group v-model="formData.is_recharge_buy">
          <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">
            {{ item.desc }}
          </Radio>
        </Radio-group>
      </KWidget>

      <template v-if="!echoData.goods_type == 35">
        <KWidget required text v-if="isContainPhysicalGoods">
          <div style="display: inline-block" slot="label">
            销售方式
            <Tooltip
              max-width="200"
              content="销售方式的选择将影响小程序用户的下单流程，暂不影响后台手动创建订单流程，总部一件代发的商品暂时不支持后台建单销售。"
            >
              <Icon type="md-help-circle" size="16" />
            </Tooltip>
            ：
          </div>
          <CheckboxGroup v-model="formData.sale_type">
            <Checkbox
              :label="item.id"
              v-for="(item, index) in sellEntTypes"
              :key="item.id"
              v-if="
                (echoData.goods_type != 35 && item.id !== 'PMS_SELF') ||
                (echoData.goods_type == 35 && item.id === 'USER_SELF')
              "
              :disabled="echoData.goods_type == 35"
            >
              <span>{{ item.desc }}</span>
            </Checkbox>
          </CheckboxGroup>
        </KWidget>
      </template>
    </div>

    <k-goods-meal v-model="mealModalVisible" @on-selected="onSelectedMeal" />
    <k-goods-services
      v-model="serviceModalVisible"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
      :optionsList="optionsList"
    />
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import S from '@/libs/util';
import KGoodsMeal from '@/components/k-goods-meal';
import KGoodsServices from '@/components/k-goods-services';
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'TaoCanGoods',
  mixins: [],
  components: {
    KGoodsMeal,
    KGoodsServices,
  },
  props: {
    // 储值购买枚举
    storedList: {
      type: Array,
      default: () => [],
    },
    sellEntTypes: {
      type: Array,
      default: () => [],
    },
    echoData: {
      type: Object,
      default: () => ({}),
    },
    optionsList: {
      type: Object,
      default() {
        return {
          servTypeDesc: [],
          sourcePlatformDesc: [],
        };
      },
    },
  },
  data() {
    return {
      formData: {
        meal_lists: [], // 套餐商品
        price: null, // 套餐商品价格
        stored_price: null, // 储值价
        stock: null, // 库存
        xn_scope_tmp: ['1'], // 上架范围
        is_recharge_buy: 'yes', // 储值购买
        sale_type: ['USER_SELF'], // 销售方式
        xn_scope: '',
        relation_card: '0',
        grant_type: '2',
        services: [],
      },

      relationList: [
        { id: '0', desc: '不关联' },
        { id: '1', desc: '关联' },
      ],
      grantList: [
        { id: '1', desc: '购买时发放' },
        { id: '2', desc: '订单交易后按需发放' },
      ],
      serviceModalVisible: false,

      tc_infos: [], // 整合的套餐商品数据

      suggestPrice: null, // 建议零售价
      chooseSkuIdLists: {}, // 已经选中的skuId
      mealModalVisible: false,
    };
  },
  watch: {
    // 套餐关联的商品里面不包含实物，初始化关联数据
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val) || (val.goods_type !== '20' && val.goods_type !== '35')) return;
        this.handleEchoData(val);
      },
      immediate: true,
    },
    'formData.meal_lists': {
      immediate: true,
      deep: true,
      handler(val) {
        if (val.length) {
          if (val.every(item => item.goods_type !== '10' || item.goods_type !== '40')) {
            this.clearRelationData();
          }
        }
        let suggestObj = this.computedMealStock(val);
        this.formData.stock = suggestObj.suggestStock;
        this.suggestPrice = suggestObj.suggestPrice;
      },
    },
  },
  computed: {
    isLastPhysical() {
      return item => {
        let current_physical_list = this.formData.meal_lists.filter(item => item.goods_type == 10);
        if (
          current_physical_list.length === 1 &&
          (item.goods_type == '10' || item.goods_type == '40') &&
          this.formData.relation_card == 1
        ) {
          return true;
        } else {
          return false;
        }
      };
    },
    isTcHasPhysicalGoods() {
      let list = this.formData.meal_lists || [];
      let isHas = list.some(item => item.goods_type == '10' || item.goods_type == '40');
      if (!isHas) {
        this.clearRelationData();
      }
      return isHas;
    },
    // 关联服务是否可以编辑
    isCanEdit() {
      return !this.echoData.id || (this.echoData.id && this.echoData.source_platform === 'CLI');
    },
    // 是否禁止修改
    isDetailDisabled() {
      // 详情禁止编辑，如果是诊所自定义的，可以编辑
      let isCLI = this.echoData.source_platform === 'CLI';
      return this.$route.query.id && !isCLI;
    },
    // 套餐中是否包含实物商品
    isContainPhysicalGoods() {
      for (let index in this.formData.meal_lists || []) {
        if (['10', '40'].includes(this.formData.meal_lists[index].goods_type)) {
          return true;
        }
      }
      return false;
    },

    getPyGoodsPrice() {
      return item => {
        return item?.attrs?.[item.skuId] || {};
      };
    },
    isRstClinic() {
      return isRstClinic();
    },
  },
  mounted() {},
  methods: {
    clearRelationData() {
      this.formData.relation_card = '0';
      this.formData.grant_type = '2';
      this.formData.services = [];
    },
    onSelectedService: function (items) {
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          serv_type: item.serv_type,
          serv_type_text: item.serv_type_text,
          source_platform_text: item.source_platform_text,
          times: isExist === -1 ? 1 : Number(this.formData.services[isExist].times || 0),
        };
      });
      this.serviceModalVisible = false;
    },
    onDelService(index) {
      this.formData.services.splice(index, 1);
    },
    // 储值价
    storedPriceChange(val) {
      if (Number(+val) == 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },

    // 套餐校验
    validateForm() {
      const data = this.formData;

      if (data.meal_lists.length <= 0) {
        this.$Message.error('请添加产品');
        return false;
      }

      if (!/^\d([\d\.]+)?/.test(data.price)) {
        this.$Message.error('请输入有效的商品价格');
        return false;
      }
      let price = parseFloat(data.price);
      if (price < 0.01) {
        this.$Message.error('商品价格不允许小于0.01元');
        return false;
      }
      if (price > 50000) {
        this.$Message.error('商品价格不允许大于50000.00元');
        return false;
      }

      let isHasEmpty = this.formData.meal_lists.some(item => item.goods_type == 10 && item.skuId == '');
      if (isHasEmpty) {
        this.$Message.error('商品规格不允许为空');
        return false;
      }

      let isHasEmptyNum = this.formData.meal_lists.some(item => Number(item.num || 0) == 0);
      if (isHasEmptyNum) {
        this.$Message.error('商品数量不允许为空');
        return false;
      }

      // 2024.7.29 允许库存为0
      // if (data.stock <= 0) {
      //   this.$Message.error('库存不能小于0');
      //   return false;
      // }

      if (data.xn_scope_tmp.length == 0) {
        this.$Message.error('请选择套餐上架范围');
        return false;
      }

      // 榕树堂套餐无储值购买-不校验
      if (this.echoData.goods_type !== '35') {
        if (!data.is_recharge_buy) {
          this.$Message.error('请选择储值购买');
          return false;
        }

        if (this.isContainPhysicalGoods && data.sale_type.length == 0) {
          this.$Message.error('请选择销售方式');
          return false;
        }
      }

      return true;
    },

    // 获取套餐的数据
    getFormData() {
      let formData = {
        ...this.formData,
      };
      let tc_infos = formData.meal_lists.map(item => {
        return {
          id: item.skuId,
          num: item.num,
        };
      });
      formData.tc_infos = JSON.stringify(tc_infos);
      delete formData.meal_lists;
      formData.xn_scope = this.formData.xn_scope_tmp.length == 2 ? '9' : this.formData.xn_scope_tmp[0];
      delete formData.xn_scope_tmp;
      return formData;
    },

    // 回显套餐商品数据
    handleEchoData(goods) {
      console.log('🚀 ~ handleEchoData ~ goods=>', goods);

      goods.tc_infos &&
        goods.tc_infos.forEach((tc_item, tc_index) => {
          this.formData.meal_lists.push({
            goodsId: tc_item.goods && tc_item.goods.id,
            skuId: tc_item.id,
            name: tc_item.name,
            goods_type: tc_item.goods_type,
            goods_type_text: tc_item.goods_type_text,
            attrs: tc_item.goods && tc_item.goods.attrs,
            price_section: tc_item.price_section,
            chooseGoodsPrice: tc_item.price, // 选中的商品的价格
            price: tc_item.price, // 选中的商品的价格
            vip_price: tc_item.vip_price, // 选中的商品的价格
            skuStock: tc_item.stock,
            num: Number(tc_item.num), // 商品数量
          });
        });
      this.filterHasChoosedSkuList();

      this.formData.price = Number(goods.price);
      this.formData.vip_price = Number(goods.vip_price);
      this.formData.stock = Number(goods.stock);
      this.formData.stored_price = goods.stored_price == '' ? null : Number(goods.stored_price);
      // 关闭问诊治疗的勾选，对于已经选中的数据，拿掉
      this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1', '2'] : [goods.xn_scope];
      // this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1'] : [goods.xn_scope];
      this.formData.is_recharge_buy = goods.is_recharge_buy;
      this.formData.xn_scope = goods.xn_scope;
      this.formData.sale_type = goods.sale_type || [];
      this.formData.relation_card = goods.relation_card;
      this.formData.grant_type = goods.grant_type;
      for (let key in goods.services ? goods.services : []) {
        goods.services[key].times = Number(goods.services[key].times);
      }
      this.formData.services = goods.services;
    },

    // 选中套餐商品
    onSelectedMeal(items) {
      console.log('-> items', items);
      // 需要区分添加的商品是单个sku还是多个sku,如果是单个sku，不可重复添加，如果是多个sku，sku不可重复添加
      let goodsIds = this.formData.meal_lists.map(item => item.goodsId);
      console.log('-> goodsIds', goodsIds);

      // 对于已经存在的套餐商品不允许添加
      items.forEach(item => {
        if (goodsIds.includes(item.id) && (item.goods_type == '15' || item.goods_type == '25')) {
          // 虚拟商品只能添加一次
          this.$Message.error(`${item.name}已存在,请勿重复添加`);
        } else {
          let hasSkuList = this.formData.meal_lists.filter(i_item => i_item.goodsId == item.id);
          let attrs_length = Object.keys(item.attrs).length;
          // 对于实体商品，如果增加的规格已经达到上限，不允许继续添加
          if (goodsIds.includes(item.id) && hasSkuList.length >= attrs_length) {
            this.$Message.error(`${item.name}规格最多为${attrs_length}种,请勿重复添加`);
          } else {
            this.formData.meal_lists.push({
              goodsId: item.id,
              skuId:
                item.goods_type == '15' || item.goods_type == '25' ? item.attrs[Object.keys(item.attrs)[0]].id : '',
              name: item.name,
              goods_type: item.goods_type,
              goods_type_text: item.goods_type_text,
              attrs: item.attrs,
              price_section: item.price_section,
              chooseGoodsPrice:
                item.goods_type == '15' || item.goods_type == '25'
                  ? item.attrs[Object.keys(item.attrs)[0]].price
                  : null, // 选中的商品的价格
              skuStock: item.stock,
              num: 1, // 商品数量
            });
          }
        }
      });
      this.mealModalVisible = false;
    },

    // 删除套餐的产品
    onDelMeal: function (index, item) {
      this.formData.meal_lists.splice(index, 1);
      this.filterHasChoosedSkuList();
    },

    // 计算套餐最大库存
    computedMealStock(list) {
      let suggestPrice = 0;
      let suggestStock = 0;
      list.forEach((item, index) => {
        if (index == 0) {
          suggestStock = Math.floor(Number(item.skuStock) / Number(item.num));
        } else {
          if (Math.floor(Number(item.skuStock) / Number(item.num)) < suggestStock) {
            suggestStock = Math.floor(Number(item.skuStock) / Number(item.num));
          }
        }
        suggestPrice = S.mathAdd(suggestPrice, S.mathMul(Number(item.num), Number(item.chooseGoodsPrice)));
      });
      return {
        suggestPrice,
        suggestStock,
      };
    },

    // 生成spu-sku选中的数据
    filterHasChoosedSkuList() {
      this.chooseSkuIdLists = {};
      this.formData.meal_lists.forEach(item => {
        if (this.chooseSkuIdLists[item.goodsId] && this.chooseSkuIdLists[item.goodsId].length) {
          if (!this.chooseSkuIdLists[item.goodsId].includes(item.skuId)) {
            this.chooseSkuIdLists[item.goodsId].push(item.skuId);
          }
        } else {
          this.chooseSkuIdLists[item.goodsId] = [item.skuId];
        }
      });
    },

    // 当前spu对应的sku选项是否可选
    isDisabled(spuId, skuId) {
      let flag = false;
      if (this.chooseSkuIdLists[spuId]) {
        flag = this.chooseSkuIdLists[spuId].includes(skuId) ? true : false;
      }
      return flag;
    },

    // 套餐商品选择规格
    specsChange(key, index) {
      this.formData.meal_lists[index].skuId = this.formData.meal_lists[index].attrs[key]?.id;
      this.formData.meal_lists[index].chooseGoodsPrice = this.formData.meal_lists[index].attrs[key]?.price;
      this.formData.meal_lists[index].skuStock = this.formData.meal_lists[index].attrs[key]?.stock;
      this.filterHasChoosedSkuList();
    },
  },
};
</script>

<style scoped lang="less">
p {
  margin: 0;
}
</style>
