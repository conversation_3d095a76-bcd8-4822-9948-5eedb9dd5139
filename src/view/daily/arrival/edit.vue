<template>
  <div class="edit-wrapper">
    <div class="form-content">
      <Form :label-width="120" ref="queryFormData" :label-colon="true" :model="queryFormData" :rules="ruleValidate">
        <FormItem label="到店日期" prop="arrival_time">
          <DatePicker
            type="date"
            :value="queryFormData.arrival_time"
            @on-change="changeArrivalTime"
            placeholder="请选择到店日期"
          ></DatePicker>
        </FormItem>

        <FormItem label="到店类型" prop="user_type">
          <Select
            v-model="queryFormData.user_type"
            placeholder="请选择到店类型"
            @change="selectChange($event, 'user_type')"
          >
            <Option v-for="(user_item, user_index) in userTypeDesc" :key="user_index + 'user'" :value="user_item.id">
              {{ user_item.desc }}
            </Option>
          </Select>
        </FormItem>

        <FormItem
          label="用户姓名"
          v-show="showOldCustomer"
          :prop="showOldCustomer ? 'oldCustomer' : ''"
          :key="showOldCustomer + 'show'"
        >
          <el-autocomplete
            ref="custom"
            v-model="queryFormData.oldCustomer"
            :debounce="600"
            :popper-append-to-body="false"
            :fetch-suggestions="querySearchAsync"
            :trigger-on-focus="true"
            @blur="blur"
            placeholder="请输入用户姓名/手机号;如第一次登记可直接输入手机号"
            @select="handleSelect"
          >
            <template slot-scope="{ item }">
              <div v-if="!item.empty" style="white-space: pre-wrap">
                <span>{{ item.patient_name }}</span>
                <span class="mr-8">{{ item.mobile }}</span>
                <span v-if="item.show_staging_mobile === '1'" style="font-size: 12px; color: #999"
                  >暂存 {{ item.staging_mobile }}</span
                >
              </div>
              <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>
                <p class="flex flex-c">
                  <span>{{ queryFormData.oldCustomer }}</span>
                  <span class="tip">尚无该用户</span>
                </p>
                <a>创建用户</a>
              </div>
            </template>
          </el-autocomplete>
        </FormItem>

        <FormItem label="用户手机" v-show="showOldCustomer">
          <Input type="text" :disabled="showOldCustomer" v-model="queryFormData.mobile" placeholder="" />
        </FormItem>

        <FormItem
          label="用户性别"
          :prop="!showOldCustomer ? 'sex' : ''"
          v-show="!showOldCustomer"
          :key="showOldCustomer"
        >
          <Select v-model="queryFormData.sex" :disabled="showOldCustomer" placeholder="请选择用户性别">
            <Option v-for="(sex_item, sex_index) in sexDesc" :key="sex_index + 'sex'" :value="sex_item.id">
              {{ sex_item.desc }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="用户性别" v-show="showOldCustomer">
          <Input type="text" :disabled="showOldCustomer" v-model="queryFormData.sex_text" placeholder="" />
        </FormItem>

        <FormItem label="用户级别" v-show="showOldCustomer">
          <Input type="text" :disabled="showOldCustomer" v-model="queryFormData.offline_level" placeholder="" />
        </FormItem>

        <FormItem label="用户类型" v-show="showOldCustomer">
          <Input type="text" :disabled="showOldCustomer" v-model="queryFormData.consume_type_text" placeholder="" />
        </FormItem>

        <FormItem label="用户来源" v-show="showOldCustomer">
          <Input type="text" :disabled="showOldCustomer" v-model="queryFormData.from_text" placeholder="" />
        </FormItem>

        <!-- new customer option -->
        <FormItem label="大致年龄段" v-show="!showOldCustomer" :prop="!showOldCustomer ? 'age_group' : ''">
          <Select
            v-model="queryFormData.age_group"
            placeholder="请选择大致年龄段"
            @change="selectChange($event, 'ask_type')"
          >
            <Option v-for="(age_item, age_index) in ageGroupDesc" :key="age_index + 'age'" :value="age_item.id">
              {{ age_item.desc }}
            </Option>
          </Select>
        </FormItem>

        <!-- new customer option -->
        <FormItem
          label="外貌特征"
          v-show="!showOldCustomer"
          :prop="!showOldCustomer ? 'exterior' : ''"
          :key="showOldCustomer + 'exterior'"
        >
          <Input
            v-model="queryFormData.exterior"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="请描述外貌特征"
          ></Input>
        </FormItem>

        <FormItem label="到店述求" prop="ask_type">
          <Select
            v-model="queryFormData.ask_type"
            placeholder="请选择到店述求"
            @change="selectChange($event, 'ask_type')"
          >
            <Option v-for="(ask_item, ask_index) in askTypeDesc" :key="ask_index + 'ask'" :value="ask_item.id">
              {{ ask_item.desc }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="消费类型" prop="consume_type" v-if="queryFormData.ask_type == 'HAS_CONSUME'">
          <Select
            v-model="queryFormData.consume_type"
            placeholder="请选择消费类型"
            @change="selectChange($event, 'consume_type')"
          >
            <Option
              v-for="(consume_item, consume_index) in consumeTypeDesc"
              :key="consume_index + 'consume'"
              :value="consume_item.id"
              >{{ consume_item.desc }}
            </Option>
          </Select>
        </FormItem>

        <!-- <FormItem label="到店述求" prop="ask_type">
          <Select v-model="queryFormData.ask_type" placeholder="">
            <Option v-for="(ask_item, ask_index) in askTypeDesc" :key="ask_index+'ask'" :value="ask_item.id">{{ ask_item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem label="消费类型" :prop="queryFormData.ask_type !== 'PASS_BY' ? 'consume_type' : ''" :key="queryFormData.ask_type">
          <Select v-model="queryFormData.consume_type" placeholder="">
            <Option v-for="(consume_item, consume_index) in consumeTypeDesc" :key="consume_index+'consume'" :value="consume_item.id">{{ consume_item.desc }}</Option>
          </Select>
        </FormItem> -->
        <FormItem label="跟进人" prop="follower_id">
          <Select v-model="queryFormData.follower_id" placeholder="请选择跟进人" filterable>
            <Option v-for="(item, index) in followerList" :key="item.id" :value="item.id" :label="item.label">
              <div class="flex flex-item-between">
                <span>{{ item.name }}</span>
                <span class="text-role">{{ item.role_name }}</span>
              </div>
            </Option>
          </Select>
        </FormItem>

        <FormItem label="备注">
          <Input
            v-model="queryFormData.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入备注内容"
          ></Input>
        </FormItem>
      </Form>
    </div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Dvd />
      <Dvd />
      <Button type="primary" @click="onSave" :loading="savaLoading">保存</Button>
    </div>

    <!-- 创建用户 -->
    <create-user-modal
      :showCreateInfo="optionChange"
      :source-list="sourceList"
      :visible.sync="consumerVisibleDia"
      :level-list="levelList"
      :name="creatName"
    ></create-user-modal>
  </div>
</template>

<script>
import S from '@/libs/util'; // Some commonly used tools
import moment from 'moment';
import { cloneDeep } from 'lodash-es';
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';

export default {
  name: 'edit',
  mixins: [],

  components: {
    CreateUserModal,
  },

  props: {},

  data() {
    return {
      name: '',
      creatName: '',
      consumerVisibleDia: false, // 创建用户的弹窗
      sourceList: [],
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],

      queryFormData: {
        id: '', // id有值为编辑
        uid: '',
        arrival_time: moment().locale('zh-cn').format('YYYY-MM-DD'), // 到店日期
        user_type: 'USER_TYPE_NEW', // 是否老客
        oldCustomer: '', // 关联老用户
        // consume_type_text: '', // 类型
        mobile: '', // 手机号
        offline_level: '', // 等级
        sex: '2', // 性别
        sex_text: '', // 性别
        from_text: '', // 来源

        age_group: 'THIRTY_FORTY', // 大致年龄段
        exterior: '', // 外貌描述
        ask_type: '', // 到店述求
        consume_type_text: '', // 用户类型
        consume_type: '', // 消费类型
        follower_id: '', // 跟进人
        remark: '', // 备注
      },
      userList: [], // 搜索得出的用户信息列表
      savaLoading: false, // 保存loading

      // options
      sexDesc: [],
      userTypeDesc: [], // 到店类型
      ageGroupDesc: [], // 大致年龄段
      askTypeDesc: [], // 到店述求
      consumeTypeDesc: [], // 消费类型

      ruleValidate: {
        arrival_time: [{ required: true, message: '该选择到店日期', trigger: 'change' }],
        user_type: [{ required: true, message: '请选择到店类型', trigger: 'change' }],
        sex: [{ required: true, message: '请选择用户性别', trigger: 'change' }],
        consume_type: [{ required: true, message: '请选择消费类型', trigger: 'change' }],
        age_group: [{ required: true, message: '请选择大致年龄段', trigger: 'change' }],
        // exterior: [
        //   { required: true, message: '请输入外貌特征', trigger: 'change' }
        // ],
        ask_type: [{ required: true, message: '请选择到店述求', trigger: 'change' }],
        follower_id: [{ required: true, message: '请选择跟进人', trigger: 'change' }],
        oldCustomer: [{ required: true, message: '请输入用户名/手机', trigger: 'change' }],
      },
      searchTimes: 0,
      followerList: [],
    };
  },

  computed: {
    showOldCustomer() {
      let { user_type } = this.queryFormData;
      return user_type == 'USER_TYPE_OLD';
    },
  },

  watch: {},

  created() {},

  mounted() {
    this.getArrivalOptions();

    // 如果有id,则进行回显
    let id = this.$route.query.id;
    this.getFollowerList();
    if (id) {
      this.getArrivalInfo(id);
    }
  },

  methods: {
    changeArrivalTime(time) {
      console.log('-> %c time  === %o ', 'font-size: 15px', time);
      this.queryFormData.arrival_time = time;
    },
    getFollowerList() {
      this.$api.getFollowerList().then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px', res);
        this.followerList = res.map(item => {
          return {
            ...item,
            label: item.name + '·' + item.role_name,
          };
        });
      });
    },
    back() {
      this.$router.back();
    },
    // 当搜索的人不存在时,失焦清除绑定数据,不允许自建
    blur() {
      setTimeout(() => {
        if (!this.name) {
          if (!this.consumerVisibleDia) {
            this.queryFormData.oldCustomer = '';
            this.$refs.custom.getData();
          }
        } else {
          this.queryFormData.oldCustomer = this.name;
        }
      }, 200);
    },

    selectChange(e, type) {
      this.queryFormData[type] = e;
    },

    /**
     * @description:远程搜索用户信息
     * */
    querySearchAsync(keyword, cb) {
      this.creatName = cloneDeep(keyword);
      if (this.searchTimes && !keyword) {
        cb(this.userList);
      } else {
        let copyName = this.name || 'none';
        if (keyword !== copyName) {
          this.getUserList(keyword, cb);
        } else {
          cb(this.userList);
        }
        this.getUserList(keyword, cb);
      }
    },

    // 点击创建用户，显示弹窗
    creatConsumer(val) {
      this.consumerVisibleDia = true;
      this.$refs.custom.close();
    },

    // 创建用户返回的数据
    optionChange(item) {
      console.log('item', item);
      let chooseObj = {
        patient_name: item.nickname,
        uid: item.uid,
        sex_text: this.handleSex(item.sex).desc || '',
        mobile: item.mobile,
        from_text: item.from_text,
        offline_level: item.offline_level,
      };
      // 当新用户创建成功，将新用户数据覆盖之前残留的旧用户数据
      this.queryFormData.oldCustomer = '';
      this.name = '';
      this.queryFormData.uid = '';
      this.queryFormData.sex_text = '';
      this.queryFormData.mobile = '';
      this.queryFormData.from_text = '';
      this.queryFormData.consume_type_text = '';
      this.queryFormData.offline_level = '';
      this.handleSelect(chooseObj);
    },
    // 选择老客
    handleSelect(val) {
      this.queryFormData.oldCustomer = val.patient_name;
      this.name = val.patient_name;
      this.queryFormData.uid = val.uid;
      this.queryFormData.sex_text = val.sex_text;
      this.queryFormData.mobile = val.mobile;
      this.queryFormData.from_text = val.from_text;
      this.queryFormData.consume_type_text = val.consume_type_text;
      this.queryFormData.offline_level = val.offline_level;
      this.$refs['queryFormData'].validateField('oldCustomer');
    },

    handleSex(type) {
      let resultList = this.sexDesc.filter(item => item.id == type);
      return resultList[0] || {};
    },

    // api-根据id获取当前用户的详情
    getArrivalInfo(id = '') {
      let params = {
        id,
      };
      this.$api.getArrivalInfo(params).then(res => {
        this.queryFormData = {
          ...this.queryFormData,
          ...res,
          ...{
            arrival_time: moment.unix(Number(res.arrival_time)).format('YYYY-MM-DD'),
            oldCustomer: res.user.patient_name || res.user.nickname,
          },
          ...res.user,
          cas_token: res.cas_token,
        };
        this.name = res.user.patient_name || res.user.nickname;
      });
    },

    // api-获取用户列表-用户手机号带出用户信息
    getUserList(search = '', cb) {
      let params = {
        page: 1,
        pageSize: 20,
        search,
      };
      this.searchTimes++;
      if (search) {
        this.searchTimes = 0;
      }
      this.$api.getUserList(params).then(res => {
        // 获取用户数据
        this.handleUserList(res.users, cb);
      });
    },
    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      cb(data);
    },

    // 保存
    onSave() {
      this.$refs['queryFormData'].validate(valid => {
        if (valid) {
          const {
            arrival_time,
            sex,
            age_group,
            exterior,
            ask_type,
            consume_type,
            uid,
            remark,
            cas_token,
            id = '',
            follower_id,
          } = this.queryFormData;
          const params = {
            arrival_time,
            sex,
            age_group,
            exterior,
            ask_type,
            consume_type: ask_type == 'HAS_CONSUME' ? consume_type : '',
            uid,
            remark,
            cas_token,
            id,
            follower_id,
          };
          this.editArrival(params);
        }
      });
    },

    // api-保存接口
    editArrival(params = {}) {
      this.savaLoading = true;
      this.$api
        .editArrival(params)
        .then(res => {
          this.$router.push('/daily/arrival/list');
        })
        .catch(error => {})
        .finally(() => {
          this.savaLoading = false;
        });
    },
    // api-获取options
    getArrivalOptions() {
      this.$api.getArrivalOptions().then(res => {
        // 用户来源
        this.sourceList = S.descToArrHandle(res.userFromDesc);
        // 用户性别
        this.sexDesc = S.descToArrHandle(res.sexDesc);
        // 到店类型
        this.userTypeDesc = S.descToArrHandle(res.userTypeDesc);
        // 大致年龄段
        this.ageGroupDesc = S.descToArrHandle(res.ageGroupDesc);
        // 到店述求
        this.askTypeDesc = S.descToArrHandle(res.askTypeDesc);
        // 消费类型
        this.consumeTypeDesc = S.descToArrHandle(res.consumeTypeDesc);
      });
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
.form-content {
  width: 50%;
  margin-left: 6%;
}

.mr-8 {
  margin: 0 8px;
}
</style>

<style lang="less" scoped>
// deep  components style
.ivu-date-picker,
.el-autocomplete {
  width: 100%;
}

::v-deep .el-input__inner {
  height: 32px;
  font-size: 12px;
  padding: 4px 7px;
  border: 1px solid #bcc3d7;
  border-radius: 2px;

  &:hover {
    border-color: #447cdd;
  }

  &:focus {
    border-color: #447cdd;
    outline: 0;
    // box-shadow: 0 0 0 2px rgb(21 91 212 / 20%);
    box-shadow: 0 0 0 2px rgba(68, 124, 221, 0.2);
  }
}

.text-role {
  color: #8c8c8c;
  font-size: 12px;
}
</style>
>
