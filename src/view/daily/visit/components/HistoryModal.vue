<template>
  <Modal
    v-model="visible"
    title="查看历史"
    width="80%"
    :mask-closable="false"
    :closable="true"
    class-name="history-modal"
    @on-visible-change="onVisibleChange"
  >
    <div class="history-modal-content">
      <div>
        <standard-table
          :loading="tableLoading"
          :columns="tableCols"
          :data="list"
          :total="total"
          :height="400"
          :page-size.sync="queryFormData.pageSize"
          :current.sync="queryFormData.page"
          @on-change="onPageChange"
        >
          <template #header>
            <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
              <FormItem label="">
                <Input v-model="queryFormData.keyword" placeholder="请输入用户姓名/手机号" />
              </FormItem>

              <FormItem label="">
                <DatePicker
                  type="daterange"
                  placeholder="请选择计划时间"
                  :value="timeRange"
                  clearable
                  @on-change="times => handleTimeChange(times)"
                ></DatePicker>
              </FormItem>

              <FormItem label="">
                <Select v-model="queryFormData.type" placeholder="全部">
                  <Option value="">全部</Option>
                  <Option v-for="(item, key) in typeDesc" :value="key" :key="key">{{ item.desc }}</Option>
                </Select>
              </FormItem>
              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Form>
            <div class="flex flex-item-between flex-item-align">
              <div class="panel-nav" v-if="Object.keys(statusDesc).length">
                <a
                  v-for="item in statusDesc"
                  :key="item.key"
                  class="nav"
                  :class="{
                    active:
                      queryFormData.status == item.key ||
                      (queryFormData.status == undefined && item.key == 'TODAY_WAIT_FOLLOW'),
                  }"
                  @click.prevent.capture="onStatusChange(item.key)"
                >
                  {{ item.desc }}
                  <!-- <Tag :color="item.color" v-if="item.color">{{ getThisValue(item.value) }}</Tag> -->
                </a>
              </div>
              <div></div>
            </div>
          </template>
          <!-- 用户信息 -->
          <template slot-scope="{ row }" slot="user">
            <div class="flex user flex-item-align">
              <img :src="row.user.avatar | imageStyle" alt="" />
              <div>
                <p class="flex width-perecnt-100">
                  <span class="user-label">姓名：</span><span class="user-text">{{ row.user.user_name }}</span>
                </p>
                <p class="flex">
                  <span class="user-label">性别：</span><span class="user-text">{{ row.user.sex_text || '-' }}</span>
                </p>
                <p class="flex">
                  <span class="user-label">年龄：</span><span class="user-text">{{ row.user.age_text || '-' }}</span>
                </p>
                <p class="flex">
                  <span class="user-label">手机号：</span><span class="user-text">{{ row.user.mobile || '-' }}</span>
                </p>
              </div>
            </div>
          </template>

          <!-- 随访信息 -->
          <template slot-scope="{ row }" slot="mr">
            <div v-if="row.type === 'MR_VISIT'">
              <p class="flex">
                <span class="user-label">就诊时间：</span
                ><span class="user-text">{{ row.mr.create_time | data_format('YYYY-MM-DD HH:mm') }}</span>
              </p>
              <p class="flex">
                <span class="user-label">医生：</span><span class="user-text">{{ row.mr.doctor_name }}</span>
              </p>
              <p class="flex">
                <span class="user-label">症状：</span
                ><span class="user-text cursor">
                  <Tooltip :content="row.mr.diag_result_text" placement="top" theme="light" max-width="280">
                    <span class="ecs ecs-2">{{ row.mr.diag_result_text }}</span>
                  </Tooltip>
                </span>
              </p>
            </div>
            <div v-else>
              <p class="flex">
                <span class="user-label">消费次数：</span><span class="user-text">{{ row.consume.num || 0 }}次</span>
              </p>
              <p class="flex">
                <span class="user-label">消费金额：</span>
                <span class="user-text" v-if="row.consume.money">￥{{ row.consume.money }}</span>
                <span class="user-text" v-else>-</span>
              </p>
            </div>
          </template>

          <template slot-scope="{ row }" slot="consume">
            <p class="flex">
              <span class="user-label">到店：</span
              ><span class="user-text">{{ row.consume.arrival_time | data_format('YYYY-MM-DD') }}</span>
            </p>
            <p class="flex">
              <span class="user-label">消费：</span
              ><span class="user-text">{{ row.consume.last_time | data_format('YYYY-MM-DD HH:mm') }}</span>
            </p>
          </template>

          <!-- 计划时间 -->
          <template slot-scope="{ row }" slot="plan_time">
            <p>{{ row.plan_time | data_format('YYYY-MM-DD') }}</p>
          </template>

          <!-- 状态 -->
          <template slot-scope="{ row }" slot="status_text">
            <Tooltip
              v-if="row.cancel.reason"
              :content="row.cancel.reason"
              placement="top"
              theme="light"
              max-width="280"
            >
              <p class="ecs ecs-3 cursor">
                {{ row.status_text }}
                <span v-if="row.status == 'CANCEL'">({{ row.cancel.reason || '-' }})</span>
              </p>
            </Tooltip>
            <div v-else>{{ row.status_text }}</div>
          </template>

          <!-- 最近操作信息 -->
          <template slot-scope="{ row }" slot="operator">
            <div v-if="queryFormData.status == 'FOLLOW_UP'">
              <p class="flex">
                <span class="user-label">随访人：</span><span class="user-text">{{ row.up_info.operator }}</span>
              </p>
              <p class="flex">
                <span class="user-label">随访时间：</span
                ><span class="user-text">{{ row.up_info.time | data_format('YYYY-MM-DD HH:mm') }}</span>
              </p>
            </div>
            <div v-else-if="queryFormData.status == 'CANCEL'">
              <p class="flex">
                <span class="user-label">取消人：</span><span class="user-text">{{ row.cancel.operator }}</span>
              </p>
              <p class="flex">
                <span class="user-label">取消时间：</span
                ><span class="user-text">{{ row.cancel.time | data_format('YYYY-MM-DD HH:mm') }}</span>
              </p>
            </div>
            <div v-else>-</div>
          </template>

          <template slot-scope="{ row }" slot="action">
            <p v-if="row.status === 'FOLLOW_UP'" @click="goVisit(row)"><a>详情</a></p>
            <a v-else>-</a>
          </template>
        </standard-table>
        <!-- 取消随访 -->
        <VisitCancel :visible.sync="cancelVisible" :id="willCancelId" @success="cancelSuccess"></VisitCancel>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">关闭</Button>
    </div>
  </Modal>
</template>
<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
/* eslint-disable */
import VisitCancel from '../../visit/components/visit-cancel';
import StandardTable from '@/components/StandardTable/index.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  // status: '', // 随访tab, 默认今日待随访
  status: 'FOLLOW_UP', // 随访tab, 默认今日待随访
  keyword: '', // 用户姓名/手机号
  st: '', // 计划开始时间
  et: '', // 计划结束时间
  type: '', // 随访类型
  r: '',
};

export default {
  name: 'list',
  components: {
    StandardTable,
    VisitCancel,
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getVisitList',
      tableCols: [],
      wait_tableCols: [
        { title: '编号', key: 'id', width: 70, align: 'center' },
        { title: '用户', slot: 'user', align: 'center', width: 230 },
        { title: '随访类型', key: 'type_text', align: 'center', width: 100 },
        { title: '随访信息', slot: 'mr', align: 'center', width: 240 },
        { title: '上次到店/消费时间', slot: 'consume', align: 'center', width: 220 },
        { title: '计划时间', slot: 'plan_time', align: 'center', width: 100 },
        { title: '状态', slot: 'status_text', align: 'center', minWidth: 170 },
        { title: '最近操作信息', slot: 'operator', align: 'center', width: 220 },
        { title: '操作', slot: 'action', align: 'center', width: 100 },
      ],
      unwait_tableCols: [
        { title: '编号', key: 'id', width: 70, align: 'center' },
        { title: '用户', slot: 'user', align: 'center', width: 230 },
        { title: '随访类型', key: 'type_text', align: 'center', width: 100 },
        { title: '随访信息', slot: 'mr', align: 'center', width: 240 },
        { title: '计划时间', slot: 'plan_time', align: 'center', width: 100 },
        { title: '状态', slot: 'status_text', align: 'center', minWidth: 170 },
        { title: '操作', slot: 'action', align: 'center', width: 100 },
      ],
      tableLoading: false,
      list: [],
      total: 0,

      typeDesc: [], // 随访类型

      // 取消弹窗
      cancelVisible: false, // 取消随访弹窗
      willCancelId: '', // 将要取消的随访id
      artificerList: [],
      statusDesc: [
        // { desc: '今日待随访', key: 'TODAY_WAIT_FOLLOW', color: 'error', value: 'TODAY_WAIT_FOLLOW' },
        // { desc: '其他待随访', key: 'OTHER_WAIT_FOLLOW', color: 'warning', value: 'OTHER_WAIT_FOLLOW' },
        { desc: '已随访', key: 'FOLLOW_UP' },
        { desc: '已取消', key: 'CANCEL' },
      ],
      statusNumber: {},
      timeRange: [],
    };
  },

  computed: {
    getThisValue() {
      return key => {
        return this.statusNumber[key];
      };
    },
    visible: {
      get() {
        return this.value;
      },
      set(v) {
        console.log('v: ', v);
        this.$emit('input', v);
      },
    },
  },

  created() {},
  watch: {
    'queryFormData.status': {
      immediate: true,
      handler(val) {
        if (val == 'FOLLOW_UP' || val == 'CANCEL') {
          this.tableCols = this.unwait_tableCols;
        } else {
          this.tableCols = this.wait_tableCols;
        }
      },
    },
    cancelVisible(val) {
      if (!val) {
        this.willCancelId = '';
      }
    },
  },
  methods: {
     handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },
    onVisibleChange(v) {
      if (!v) {
        this.closeModal();
      } else {
        this.getsList();
        this.getVisitOptions();
      }
    },
    closeModal() {
      this.visible = false;
    },
    goVisit({ uid, id, type }) {
      this.closeModal();
      this.$router.push({
        path: type === 'ACTIVITY_VISIT' ? '/daily/visit/activity-visit' : '/daily/visit/detail',
        query: { id, uid,is_old: 1 },
      });
    },
    //查看
    checkCustDetail(uid) {
      this.$router.push('/user/detail?uid=' + uid);
    },
    // 新增随访计划
    addVisitPlan() {
      this.$router.push({
        path: '/daily/visit/create',
      });
    },

    // 取消随访计划
    cancelVisit({ id }) {
      this.willCancelId = id;
      this.cancelVisible = true;
    },

    // 随访取消成功，重新拉取列表
    cancelSuccess(val) {
      if (val) {
        this.getsList();
      }
    },

    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.getsList();
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.getsList();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.getsList();
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          console.log(data, 'data');
          this.total = data.total;
          this.list = data.list;
          this.statusNumber = data.status;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getsList();
    },

    // 获取随访选项信息
    getVisitOptions() {
      this.$api.getVisitOptions().then(res => {
        this.typeDesc = res.typeDesc;
        // 过滤掉活动通知-open
        // this.$delete(this.typeDesc, 'ACTIVITY_VISIT')
      });
    },
  },
};
</script>

<style lang="less" scoped>
.user {
  img {
    width: 40px;
    height: 40px;
  }
}
</style>

<style lang="less" scoped>
.normal-p {
  margin: 0;
}
/deep/ .ivu-input-number {
  width: 100%;
}
.width-perecnt-100 {
  width: 100%;
  max-width: 100%;
}
.ml10 {
  margin-left: 10px;
}
.user-label {
  display: inline-block;
  width: 70px;
  min-width: 70px;
  text-align: right;
  color: #aaa;
}
.user-text {
  text-align: left;
}
.cursor {
  cursor: pointer;
}
p {
  margin: 0;
}
</style>
