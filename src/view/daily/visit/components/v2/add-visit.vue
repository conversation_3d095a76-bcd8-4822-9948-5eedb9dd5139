<template>
  <Modal
    ref="customModal"
    :value="modalVisible"
    width="650px"
    title="创建随访"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
    @on-cancel="handleCancel"
  >
    <div class="modal-content">
      <Form ref="followUpRef" :model="formData" :rules="rules">
        <div class="content-label">
          <div class="line"></div>
          <div class="label">基本信息</div>
        </div>
        <FormItem label="用户信息:" prop="pt_id" style="display: flex; padding-left: 8px">
          <div class="search-box" ref="searchBoxRef">
            <div class="current-user" v-if="current_user_info.id">
              <div class="current-user-left">
                <div class="avatar-box" :style="{ borderColor: '#B0C3DD' }">
                  <img
                    class="avatar"
                    :src="
                      current_user_info.avatar
                        | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
                    "
                  />
                </div>

                <div class="user-info">
                  <div class="user-info-top">
                    <div class="user-info-name">{{ current_user_info.name }}</div>
                    <div class="user-info-sex">
                      <span>{{ current_user_info.sex_desc }}</span>
                      <span v-if="current_user_info.sex_desc && current_user_info.age">｜</span>
                      <span>{{ current_user_info.age ? `${current_user_info.age}岁` : current_user_info.age }}</span>
                    </div>
                  </div>
                  <div class="user-info-mobile-box">
                    <div class="info-mobile">{{ current_user_info.mobile }}</div>
                  </div>
                </div>
              </div>

              <div class="delete-user-icon-box">
                <Tooltip content="移除" placement="top">
                  <img
                    class="delete-user-icon"
                    @click="deleteUserInfo()"
                    src="https://static.rsjxx.com/image/2025/0108/163541_57316.png"
                  />
                </Tooltip>
              </div>
            </div>
            <el-autocomplete
              v-else
              class="custom-user-autocomplete"
              ref="custom"
              v-model="nickname"
              :popper-append-to-body="false"
              :fetch-suggestions="querySearchAsync"
              :trigger-on-focus="true"
              @blur="blur"
              placeholder="输入用户姓名、手机号搜索"
              @select="handleSelect"
            >
              <template slot-scope="{ item }">
                <div class="autocomplete" v-if="item.id" style="white-space: pre-wrap">
                  <div class="avatar-box" :style="{ borderColor: '#B0C3DD' }">
                    <img
                      class="avatar-icon"
                      :src="
                        item.avatar
                          | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
                      "
                    />
                  </div>
                  <div class="info-content">
                    <span class="name">{{ item.name }}</span>
                    <span class="info">
                      <span>{{ item.sex_desc }}</span>
                      <span v-if="item.age && item.sex_desc"> | </span>
                      <span>{{ item.age ? `${item.age}岁` : '' }}</span>
                    </span>

                    <span class="mobile">{{ item.mobile }}</span>
                  </div>
                </div>
                <div class="flex flex-item-between flex-item-align" v-else>
                  <p class="flex flex-c">
                    <span>{{ nickname }}</span>
                    <span class="tip">尚无该患者</span>
                  </p>
                </div>
              </template>
            </el-autocomplete>
          </div>
        </FormItem>

        <div class="content-label">
          <div class="line"></div>
          <div class="label">随访信息</div>
        </div>

        <FormItem label="随访类型:" prop="type" style="display: flex; padding-left: 8px">
          <RadioGroup v-model="formData.type">
            <Radio label="VISIT" disabled>
              <Tooltip content="诊后随访为系统自动生成，暂不支持手动创建诊后随访" placement="top" max-width="200">
                <span>诊后随访</span>
              </Tooltip>
            </Radio>
            <Radio label="NORMAL">
              <span>常规随访</span>
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="随访目标:" prop="target" style="display: flex; padding-left: 8px">
          <Input
            v-model="formData.target"
            placeholder="请填写随访目标"
            :maxlength="10"
            style="width: 520px"
            show-word-limit
          />
        </FormItem>
      </Form>
    </div>
    <div slot="footer" class="modal-footer">
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" :loading="confirmLoading" @click="handleOK">立即创建</Button>
    </div>
  </Modal>
</template>

<script>
import { cloneDeep, debounce  } from 'lodash-es';
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';
import moment from 'moment';

export default {
  name: 'newAddVisit',
  components: { CreateUserModal },
  props: {
    visible: Boolean,
    options: {
      type: Object,
      default: () => ({
        type_desc: [],
      }),
    },
  },
  data() {
    return {
      confirmLoading: false,
      formData: {
        pt_id: '',
        date: '',
        target: '',
        type: 'NORMAL',
      },
      rules: {
        pt_id: [{ required: true, message: '请选择用户', trigger: 'change' }],
        date: [{ required: true, message: '请选择随访日期', trigger: 'change' }],
        type: [{ required: true, message: '请选择随访类型', trigger: 'change' }],
        target: [{ required: true, message: '请选择随访目标', trigger: 'blur' }],
      },
      nickname: '',
      current_user_info: {}, // 当前用户
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  mounted() {},
  methods: {
    changeVisible(visible) {
      console.log(visible, 'visible');
      if (!visible) {
        this.clearData();
      }
    },
    handleOK() {
      this.$refs.followUpRef.validate(valid => {
        if (valid) {
          this.confirmLoading = true;
          this.$api
            .createFollowUpV2({
              ...this.formData,
              date: moment().format('YYYY-MM-DD'),
            })
            .then(res => {
              this.handleCancel();
              // this.$emit('success');
              this.$Message.success('创建成功');
              this.$router.push({
                path: '/daily/visit/detail',
                query: {
                  id: res?.id,
                },
              });
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleCancel() {
      this.$emit('update:visible', false);
    },
    clearData() {
      this.nickname = '';
      this.current_user_info = {};
      this.name = '';
      this.mobile = '';
      this.nickname = '';
      this.formData.pt_id = '';
      this.formData.target = '';
    },
    deleteUserInfo() {
      this.$Modal.confirm({
        title: '更换用户将会清除随访信息，是否确认更换？',
        content: '<p></p>',
        onOk: () => {
          this.clearData();
        },
        onCancel: () => {},
      });
    },
    /**
     * @description:远程搜索用户信息
     * */
    querySearchAsync(keyword, cb) {
      this.creatName = cloneDeep(keyword);
      if (this.searchTimes && !keyword) {
        cb(this.userList);
      } else {
        let copyName = this.name || 'none';
        if (keyword !== copyName) {
          this.getUserList({ search: keyword }, cb);
        } else {
          cb(this.userList);
        }
        this.getUserList({ search: keyword }, cb);
      }
    },
    handleSelect(item) {
      this.current_user_info = item;
      this.name = item.patient_name;
      this.mobile = item.mobile;
      this.nickname = item.patient_name;
      this.formData.pt_id = item.id;
      this.$nextTick(() => {
        this.$refs.followUpRef.validateField('pt_id');
      });
    },
    // 当搜索的人不存在时,失焦清除绑定数据,不允许自建
    blur() {
      setTimeout(() => {
        if (!this.name) {
          if (!this.consumerVisibleDia) {
            this.nickname = '';
            this.$refs.custom?.getData();
          }
        } else {
          this.nickname = this.name;
        }
      }, 200);
    },
    // api-获取用户列表-用户手机号带出用户信息
    getUserList: debounce(function ({ search = '', id = '' }, cb) {
      console.log('search');
      return new Promise(resolve => {
        let params = {
          page: 1,
          pageSize: 20,
          keyword: search,
          id: search ? '' : id,
        };
        this.searchTimes++;
        if (search) {
          this.searchTimes = 0;
        }
        this.$api.getVisitSearchPatientList(params).then(res => {
          console.log(res, 'res');
          resolve(res);
          // 获取用户数据
          this.handleUserList(res.list, cb);
        });
      });
    }, 400),
    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      typeof cb === 'function' && cb(data);
    },
  },
};
</script>
<style scoped lang="less">
.search-box {
  margin-top: -10px;
  min-height: 64px;
  min-width: fit-content;
  width: 100%;
  display: flex;
  //justify-content: center;
  align-items: center;

  .current-user {
    width: 520px;
    height: 62px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #dcdcdc;
    padding: 10px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;

    .current-user-left {
      display: flex;
      align-items: center;

      .avatar-box {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        position: relative;
        box-sizing: content-box;
        //border: 1px solid #EBEDF0;
        //background: #EBEDF0;
        .vip-icon {
          width: 30px;
          min-width: 30px;
          height: 12px;
          position: absolute;
          bottom: -6px;
          right: 4px;
        }

        .avatar {
          width: 38px;
          min-width: 38px;
          height: 38px;
          border-radius: 50%;
        }
      }

      .vip-avatar-box {
        border: 1px solid #e7c1a6;
      }

      .user-info {
        margin-left: 16px;

        .user-info-top {
          display: flex;
          align-items: center;

          .user-info-name {
            //font-weight: 600;
            font-size: 14px;
            color: #303133;
            //line-height: 24px;
          }

          .user-info-sex {
            margin-left: 12px;
            font-weight: 400;
            font-size: 14px;
            color: #909399;
            //line-height: 18px;
          }
        }

        .user-info-mobile-box {
          display: flex;
          align-items: center;

          .info-mobile {
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 20px;
          }

          .info-stage-mobile {
            margin-left: 20px;
            font-weight: 400;
            font-size: 13px;
            color: #909399;
            line-height: 20px;
            display: flex;
            align-items: center;

            .stage-tag {
              background: #fff3df;
              border-radius: 2px;
              padding: 1px 4px;
              font-weight: 400;
              font-size: 12px;
              color: #ffa300;
              line-height: 18px;
              min-width: fit-content;
            }

            .stage-mobile {
              margin-left: 6px;
              font-weight: 400;
              font-size: 13px;
              color: #909399;
              line-height: 20px;
            }
          }
        }
      }
    }

    .delete-user-icon {
      display: block;
      width: 10px;
      height: 10px;
      cursor: pointer;

      &:hover {
        transform: scale(1.3);
      }
    }

    .delete-user-icon-box {
      //margin-top: -23px;
      //margin-left: -3px;
      //background: url('https://img-sn-i01s-cdn.rsjxx.com/image/2025/0108/163541_57316.png') no-repeat;
      //background-size: 10px 10px;
      //width: 16px;
      //height: 16px;
      //cursor: pointer;

      //&:hover {
      //  .delete-user-icon {
      //    display: block;
      //  }
      //}
    }
  }

  .autocomplete {
    display: flex;
    align-items: center;
    padding: 8px 0px;

    .avatar-box {
      box-sizing: content-box;
      width: 38px;
      min-width: 38px;
      height: 38px;
      //background: #D8D8D8;
      border-radius: 50%;
      position: relative;

      .vip-icon {
        width: 30px;
        min-width: 30px;
        height: 12px;
        position: absolute;
        bottom: -6px;
        right: 4px;
      }

      .avatar-icon {
        width: 38px;
        min-width: 38px;
        height: 38px;
        border-radius: 50%;
        margin-left: 0px;
        margin-top: 0px;
      }
    }

    .vip-avatar-box {
      border: 1px solid #e7c1a6;
    }

    .info-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .name {
        margin-left: 16px;
        //font-weight: 600;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
      }

      .info {
        margin-left: 12px;
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 18px;
      }

      .mobile {
        margin-left: 12px;
        font-weight: 400;
        font-size: 13px;
        color: #999999;
        line-height: 18px;
      }

      .stage-mobile-box {
        margin-left: 12px;

        .stage-icon {
          padding: 1px 4px;
          background: #fff3df;
          border-radius: 2px;
          font-weight: 400;
          font-size: 12px;
          color: #ffa300;
          line-height: 18px;
          transform: scale(0.8);
        }

        .stage-mobile {
          margin-left: 6px;
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 18px;
        }
      }
    }
  }
}

::v-deep .ivu-input {
  border: 1px solid #dcdcdc;
}

.label {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 400;
  font-size: 12px;
  color: #333333;
  line-height: 16px;

  .mark {
    color: #fa4f4f;
  }
}
.content-label {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .line {
    width: 3px;
    height: 14px;
    background: #155bd4;
    margin-right: 8px;
  }
  .label {
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
  }
}
</style>

<!--用户样式-->
<style lang="less" scoped>
// 搜索用户的样式优化
::v-deep .custom-user-autocomplete {
  width: 520px;
  height: 32px;
  border-radius: 4px;
  margin-top: -10px;

  .el-input__inner {
    height: 32px;
    padding: 0 12px;
    border-color: #dcdcdc;
    font-size: 12px;
    border-radius: 4px !important;
  }

  .is-disabled {
    .el-input__inner {
      background: #f3f3f3;
      color: #ccc;
    }
  }
}

::v-deep .el-input__inner:focus {
  //box-shadow: 0 0 0 1px rgba(68, 124, 221, 0.2)
  box-shadow: 0 0 0 1.5px rgba(21, 91, 212, 0.2);
}

.search-box {
  margin-top: -10px;
  min-height: 64px;
  min-width: fit-content;
  width: 100%;
  display: flex;
  //justify-content: center;
  align-items: center;

  .current-user {
    width: 520px;
    height: 62px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #dcdcdc;
    padding: 10px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .current-user-left {
      display: flex;
      align-items: center;

      .avatar-box {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        position: relative;
        box-sizing: content-box;
        //border: 1px solid #EBEDF0;
        //background: #EBEDF0;
        .vip-icon {
          width: 30px;
          min-width: 30px;
          height: 12px;
          position: absolute;
          bottom: -6px;
          right: 4px;
        }

        .avatar {
          width: 38px;
          min-width: 38px;
          height: 38px;
          border-radius: 50%;
        }
      }

      .vip-avatar-box {
        border: 1px solid #e7c1a6;
      }

      .user-info {
        margin-left: 16px;

        .user-info-top {
          display: flex;
          align-items: center;

          .user-info-name {
            //font-weight: 600;
            font-size: 14px;
            color: #303133;
            //line-height: 24px;
          }

          .user-info-sex {
            margin-left: 12px;
            font-weight: 400;
            font-size: 14px;
            color: #909399;
            //line-height: 18px;
          }
        }

        .user-info-mobile-box {
          display: flex;
          align-items: center;

          .info-mobile {
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 20px;
          }

          .info-stage-mobile {
            margin-left: 20px;
            font-weight: 400;
            font-size: 13px;
            color: #909399;
            line-height: 20px;
            display: flex;
            align-items: center;

            .stage-tag {
              background: #fff3df;
              border-radius: 2px;
              padding: 1px 4px;
              font-weight: 400;
              font-size: 12px;
              color: #ffa300;
              line-height: 18px;
              min-width: fit-content;
            }

            .stage-mobile {
              margin-left: 6px;
              font-weight: 400;
              font-size: 13px;
              color: #909399;
              line-height: 20px;
            }
          }
        }
      }
    }

    .delete-user-icon {
      display: block;
      width: 10px;
      height: 10px;
      cursor: pointer;

      &:hover {
        transform: scale(1.3);
      }
    }

    .delete-user-icon-box {
      //margin-top: -23px;
      //margin-left: -3px;
      //background: url('https://img-sn-i01s-cdn.rsjxx.com/image/2025/0108/163541_57316.png') no-repeat;
      //background-size: 10px 10px;
      //width: 16px;
      //height: 16px;
      //cursor: pointer;

      //&:hover {
      //  .delete-user-icon {
      //    display: block;
      //  }
      //}
    }
  }

  .autocomplete {
    display: flex;
    align-items: center;
    padding: 8px 0px;

    .avatar-box {
      box-sizing: content-box;
      width: 38px;
      min-width: 38px;
      height: 38px;
      //background: #D8D8D8;
      border-radius: 50%;
      position: relative;

      .vip-icon {
        width: 30px;
        min-width: 30px;
        height: 12px;
        position: absolute;
        bottom: -6px;
        right: 4px;
      }

      .avatar-icon {
        width: 38px;
        min-width: 38px;
        height: 38px;
        border-radius: 50%;
        margin-left: 0px;
        margin-top: 0px;
      }
    }

    .vip-avatar-box {
      border: 1px solid #e7c1a6;
    }

    .info-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .name {
        margin-left: 16px;
        //font-weight: 600;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
      }

      .info {
        margin-left: 12px;
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 18px;
      }

      .mobile {
        margin-left: 12px;
        font-weight: 400;
        font-size: 13px;
        color: #999999;
        line-height: 18px;
      }

      .stage-mobile-box {
        margin-left: 12px;

        .stage-icon {
          padding: 1px 4px;
          background: #fff3df;
          border-radius: 2px;
          font-weight: 400;
          font-size: 12px;
          color: #ffa300;
          line-height: 18px;
          transform: scale(0.8);
        }

        .stage-mobile {
          margin-left: 6px;
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 18px;
        }
      }
    }
  }
}

::v-deep .ivu-input {
  border: 1px solid #dcdcdc;
}

.label {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 400;
  font-size: 12px;
  color: #333333;
  line-height: 16px;

  .mark {
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-size: 12px;
    color: #ed4014;
  }
}

:deep(.ivu-input-disabled) {
  background: #f3f3f3;
}

:deep(.date-picker .ivu-input-disabled) {
  background: #f3f3f3;
}

::v-deep textarea.ivu-input {
  padding: 8px 12px;
}
</style>
