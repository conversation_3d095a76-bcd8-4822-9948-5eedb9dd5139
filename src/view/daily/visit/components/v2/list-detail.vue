<template>
  <div class="detail-wrapper">
    <div class="detail-header">{{ formatDate }}</div>
    <div class="detail-content" v-loading="loading">
      <div v-if="Object.keys(detail).length === 0" class="empty-box">
        <div class="empty">
          <img src="@/assets/image/follow-up/empty.png" alt="" />
          <span v-if="formData.view === 'OPERATION' && !formatDate.view_id"> 请先选择用户 </span>
          <span v-else>暂无随访记录</span>
        </div>
      </div>
      <div v-if="Object.keys(detail).length > 0" class="content">
        <div class="follow-item" v-for="key in detailKeys" :key="key">
          <div class="title">
            <div>
              {{
                (key === 'WAIT_FOLLOW' && '待随访') ||
                (key === 'TIMEOUT' && '未随访') ||
                (key === 'FOLLOW_UP' && '已随访') ||
                (key === 'CANCEL' && '已取消')
              }}
            </div>
            <div>({{ detail[key].length || 0 }})</div>
          </div>

          <div class="user-item" v-for="user in detail[key]" :key="user.id">
            <div class="user">
              <img class="user-avatar" :src="user.avatar | imageStyle('B.w300', user_avatar(user))" alt="" />
              <div class="user-info">
                <div class="info">
                  <div class="name">{{ user.pt_name || '-' }}</div>
                  <div class="gender">{{ user.sex_desc || '-' }}</div>
                  <div class="line"></div>
                  <div class="age">{{ user.pt_age || '-' }}岁</div>
                </div>
                <div class="mobile">{{ user.pt_mobile || '-' }}</div>
              </div>
            </div>
            <div class="follow-action">
              <div class="status">
                <div class="status-item">
                  <div class="label">上次到店</div>
                  <div class="colon">:</div>
                  <div class="text">{{ user.arrival_date || '-' }}</div>
                  <div class="status">
                    <img v-if="user.sub_type === 'NORMAL'" src="@/assets/image/follow-up/chang.png" alt="" />
                    <img v-if="user.sub_type === 'PRES'" src="@/assets/image/follow-up/chu.png" alt="" />
                    <img v-if="user.sub_type === 'PHYSICAL'" src="@/assets/image/follow-up/liliao.png" alt="" />
                  </div>
                </div>
                <div v-if="user.operator_name" class="status-item">
                  <div class="label">随访人</div>
                  <div class="colon">:</div>
                  <div class="text">{{ user.operator_name || '-' }}</div>
                </div>
              </div>
              <div class="action">
                <div class="btn" v-if="key !== 'WAIT_FOLLOW'" @click="toDetail(user)">
                  <span>详情</span>
                  <span>></span>
                </div>
                <div class="btn" v-else @click="toDetail(user)">
                  <span>随访</span>
                  <span>></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';

export default {
  name: 'followUpListDetail',
  props: {
    detail: {
      type: Object,
      default: () => ({}),
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    loading: Boolean,
    selectedDay: {
      type: String,
      default: moment().format('YYYY-MM-DD'),
    },
  },
  computed: {
    detailKeys() {
      const keys = Object.keys(this.detail);
      return ['WAIT_FOLLOW', 'FOLLOW_UP', 'TIMEOUT', 'CANCEL'].filter(key => keys.includes(key));
    },
    formatDate() {
      return moment(this.selectedDay).format('YYYY年M月D日');
    },
    user_avatar() {
      return user => {
        const woman = 'https://img-sn01.rsjxx.com/image/2025/0306/150751_28927.png';
        const man = 'https://img-sn01.rsjxx.com/image/2025/0306/150751_62458.png';
        return user.sex === '1' ? man : woman;
      };
    },
  },
  methods: {
    toDetail(user) {
      this.$router.push({
        path: '/daily/visit/detail',
        query: {
          id: user.id,
        },
      });
    },
  },
};
</script>

<style scoped lang="less">
.detail-wrapper {
  width: 100%;
  height: 100%;
  .detail-header {
    width: 100%;
    padding: 16px;
    border-bottom: 1px solid #ecedf0;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
  }
  .detail-content {
    height: e('calc(100% - 55px)');
    padding: 16px;
    overflow-x: hidden;
    overflow-y: auto;

    .empty-box {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: -100px;
      .empty {
        width: 100%;
        height: fit-content;
        display: flex;
        flex-direction: column;
        position: relative;
        > img {
          width: 182px;
          height: 140px;
          object-fit: cover;
        }
        > span {
          position: absolute;
          bottom: 32px;
          z-index: 2;
        }
      }
    }
    .content {
      width: 100%;
      height: fit-content;
      position: relative;
      .follow-item {
        width: 100%;
        height: fit-content;
        .title {
          display: flex;
          font-weight: 600;
          font-size: 15px;
          color: #333333;
          line-height: 22px;
          margin-top: 24px;
          > div:last-child {
            margin-left: 4px;
          }
        }
        .user-item {
          width: 100%;
          height: fit-content;
          margin-top: 12px;
          padding: 4px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #ecedf0;
          .user {
            width: 100%;
            height: 64px;
            background: #f9fafb;
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 12px;
            .user-avatar {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin-right: 12px;
              flex-shrink: 0;
            }
            .user-info {
              flex: 1;
              display: flex;
              flex-direction: column;
              .info {
                width: 100%;
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #999999;
                line-height: 18px;
                .name {
                  width: e('calc(100% - 58px)');
                  font-weight: 600;
                  font-size: 14px;
                  color: #333333;
                  line-height: 20px;
                  margin-right: 8px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                .line {
                  width: 1px;
                  height: 10px;
                  background: #999;
                  margin: 0 4px;
                }
              }
              .mobile {
                font-size: 12px;
                color: #333333;
                line-height: 18px;
              }
            }
          }
          .follow-action {
            width: 100%;
            padding: 12px 12px 8px 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .status {
              flex: 1;
              display: flex;
              flex-direction: column;
              .status-item {
                width: 100%;
                display: flex;
                .label {
                  width: 48px;
                  height: 18px;
                  font-size: 12px;
                  color: #999999;
                  line-height: 18px;
                  text-align: justify;
                  text-align-last: justify;
                }
                .colon {
                  padding-right: 6px;
                  height: 18px;
                  font-size: 12px;
                  color: #999999;
                  line-height: 18px;
                }
                .text {
                  font-size: 12px;
                  color: #303133;
                  line-height: 18px;
                }
                .status {
                  > img {
                    width: 16px;
                    height: 16px;
                    margin-left: 4px;
                  }
                }
              }
            }
            .action {
              width: 36px;
              margin-left: 12px;
              flex-shrink: 0;
              .btn {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 12px;
                color: #155bd4;
                line-height: 18px;
                cursor: pointer;
              }
            }
          }
        }
        .user-item:hover {
          background: rgba(21, 91, 212, 0.1);
        }
      }
      .follow-item:first-child .title {
        margin-top: 0;
      }
    }
  }
}
</style>
