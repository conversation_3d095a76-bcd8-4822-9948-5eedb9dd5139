<template>
  <!-- 随访详情页面主容器 -->
  <div class="follow-up-wrapper new-follow-up" v-loading="pageLoading">
    <!-- 页面头部：患者信息和就诊信息 -->
    <div class="follow-up-header">
      <!-- 患者基本信息区域 -->
      <div class="follow-up-header-user">
        <div class="user-avatar">
          <img :src="detail.avatar | imageStyle('B.w300', user_avatar(detail))" alt="" />
        </div>
        <div class="user-info">
          <div class="info-name">
            <div class="name">{{ detail.pt_name || '-' }}</div>
            <div class="sex">{{ detail.sex_text || '-' }}</div>
            <div class="line"></div>
            <div class="age">{{ detail.age || '-' }}岁</div>
          </div>
          <div class="info-phone">
            <span>{{ detail.pt_mobile || '-' }}</span>
            <span style="margin-left: 8px" v-if="detail.user_role">({{ detail.user_role }})</span>
          </div>
        </div>
      </div>
      <!-- 就诊信息区域 -->
      <div class="follow-up-header-case">
        <div class="follow-up-header-case-date">
          <div class="label">就诊日期：</div>
          <div class="date">{{ detail.visit_date || '-' }}</div>
        </div>
        <div class="follow-up-header-case-date">
          <div class="label">就诊医生：</div>
          <div class="date">{{ detail.doctor_name || '-' }}</div>
        </div>
        <div class="follow-up-header-case-date">
          <div class="label">到店时间：</div>
          <div class="date">{{ formatTime }}</div>
        </div>
        <div class="follow-up-header-case-icon" @click="recordVisible = true">
          <img src="@/assets/image/follow-up/case.png" alt="" />
          <a>查看病历</a>
        </div>
      </div>
    </div>
    <!-- 随访记录横向滚动区域 -->
    <div class="follow-up-record">
      <div class="record-tip">当前用户存在{{ wait_count || 0 }}条待随访记录</div>
      <div class="record-scroll">
        <img class="record-arrow" src="@/assets/image/follow-up/arrow-l.png" alt="" @click="handleArrowClick('left')" />
        <div class="record-scroll-content">
          <div class="record-items">
            <div
              :class="['record-item', selectedRow.index === record.index && 'active']"
              v-for="(record, index) in records"
              :key="index"
              @click="changeSideTab(record)"
            >
              <img
                class="type_img"
                v-if="record.status === 'FOLLOW_UP'"
                src="@/assets/image/follow-up/done.png"
                alt=""
              />
              <img
                class="type_img"
                v-if="record.status === 'TIMEOUT'"
                src="@/assets/image/follow-up/undone.png"
                alt=""
              />
              <img
                class="type_img"
                v-if="record.status === 'CANCEL'"
                src="@/assets/image/follow-up/canceled.png"
                alt=""
              />
              <img
                class="type_img"
                v-if="record.status === 'WAIT_FOLLOW'"
                src="@/assets/image/follow-up/pending.png"
                alt=""
              />
              <span>{{ record.visit_date || '-' }}</span>
              <img
                class="action_img"
                v-if="record.sub_type === 'NORMAL'"
                src="@/assets/image/follow-up/chang.png"
                alt=""
              />
              <img class="action_img" v-if="record.sub_type === 'PRES'" src="@/assets/image/follow-up/chu.png" alt="" />
              <img
                class="action_img"
                v-if="record.sub_type === 'PHYSICAL'"
                src="@/assets/image/follow-up/liliao.png"
                alt=""
              />
            </div>
          </div>
        </div>
        <img
          class="record-arrow"
          src="@/assets/image/follow-up/arrow-r.png"
          alt=""
          @click="handleArrowClick('right')"
        />
      </div>
    </div>
    <!-- 主要内容区域：左侧随访列表 + 右侧表单详情 -->
    <div class="follow-up-content">
      <!-- 左侧随访详情列表 -->
      <div class="side-tabs">
        <div class="side-tabs-header">
          <img v-if="selectedRow.status === 'FOLLOW_UP'" src="@/assets/image/follow-up/done.png" alt="" />
          <img v-if="selectedRow.status === 'TIMEOUT'" src="@/assets/image/follow-up/undone.png" alt="" />
          <img v-if="selectedRow.status === 'CANCEL'" src="@/assets/image/follow-up/canceled.png" alt="" />
          <img v-if="selectedRow.status === 'WAIT_FOLLOW'" src="@/assets/image/follow-up/pending.png" alt="" />
          <span>{{ selectedRow.visit_date || '-' }}</span>
        </div>
        <div class="side-tabs-scroll">
          <div class="side-tabs-content">
            <div
              :class="['side-tabs-item', record.id === selectedId && 'active']"
              v-for="(record, index) in selectedRow.list"
              :key="index"
            >
              <div class="side-tabs-item-header">
                <div class="left-title">
                  <div class="title">{{ record.defer_day_text || '-' }}</div>
                  <div class="tip" v-if="record.can_edit !== '1' && record.status === 'WAIT_FOLLOW'">*未到随访日期</div>
                </div>
                <div class="right-status" :style="{ color: status_color(record) }">{{ record.status_text || '-' }}</div>
              </div>
              <div class="side-tabs-item-info" @click="getDetail(record.id)">
                <div class="info-items">
                  <div class="label">随访日期</div>
                  <div class="text">{{ record.date || '-' }}</div>
                </div>
                <div class="info-items">
                  <div class="label">随访类型</div>
                  <div class="text">{{ record.type_text || '-' }}</div>
                </div>
                <div class="info-items">
                  <div class="label">随访目标</div>
                  <div class="text">{{ record.target || '-' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧随访表单内容 -->
      <div :key="detail.uuid" class="content" v-loading="detailLoading">
        <!-- 随访表单主体 -->
        <Form
          ref="FollowUpRef"
          :class="['follow-up-form', showDetailText && 'disabled-form']"
          :model="detail"
          :rules="rules"
          :label-width="140"
          label-position="right"
          label-colon
          :disabled="disabled"
        >
          <!-- 随访内容分组标题 -->
          <div class="label-wrap">
            <div class="line"></div>
            <div class="label">随访内容</div>
          </div>

          <!-- 随访基本信息 -->
          <FormItem label="随访类型" prop="type">
            <div v-if="showDetailText">{{ detail.type_text || '-' }}</div>
            <RadioGroup v-else v-model="detail.type">
              <Radio label="VISIT" disabled>
                <Tooltip content="诊后随访为系统自动生成，暂不支持手动创建诊后随访" max-width="200" placement="top">
                  <span>诊后随访</span>
                </Tooltip>
              </Radio>
              <Radio label="NORMAL" disabled>
                <span>常规随访</span>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="随访目标" prop="target">
            <div v-if="showDetailText">{{ detail.target || '-' }}</div>
            <Input
              v-else
              v-model="detail.target"
              disabled
              style="width: 80%; min-width: 250px; max-width: 600px"
              placeholder="请填写随访目标"
            />
          </FormItem>

          <FormItem label="随访方式" prop="method">
            <div v-if="showDetailText">{{ detail.method_text || '-' }}</div>
            <RadioGroup v-else v-model="detail.method" @on-change="changeMethod">
              <Radio :label="item.id" v-for="item in visit_method_desc" :key="item.id">
                {{ item.desc }}
              </Radio>
            </RadioGroup>
          </FormItem>

          <!-- 诊后随访专用字段 -->
          <FormItem v-if="detail.type === 'VISIT' && detail.method !== 'SMS'" label="全身舒适度" prop="comfort">
            <div v-if="showDetailText">{{ detail.comfort_text || '-' }}</div>
            <RadioGroup v-else v-model="detail.comfort">
              <Radio :label="item.id" border v-for="item in options.comfort_desc" :key="item.id">
                {{ item.desc }}
              </Radio>
            </RadioGroup>
          </FormItem>
          <div
            :key="detail.uuid"
            v-if="detail.type === 'VISIT' && detail.method !== 'SMS' && detail.diag_result.length > 0"
          >
            <FormItem label="初始症状" :required="!disabled" prop="diag_result" />

            <FormItem
              :label="result.name"
              v-for="(result, index) in detail.diag_result"
              :key="result.feedback_type + index.toString()"
              :prop="'diag_result.' + index + '.feedback_type'"
              class="hideRequiredMark"
              :rules="{ required: !disabled, message: '请选择初始症状', trigger: 'change' }"
            >
              <div v-if="showDetailText">{{ detail?.diag_result?.[index]?.feedback_type_text || '-' }}</div>
              <RadioGroup v-else v-model="detail.diag_result[index].feedback_type">
                <Radio :label="item.id" border v-for="item in options.effect_desc" :key="item.id">
                  {{ item.desc }}
                </Radio>
              </RadioGroup>
            </FormItem>
          </div>
          <!-- 常规随访专用字段 -->
          <FormItem v-if="detail.type === 'NORMAL'" label="用户积极度" prop="engagement">
            <div v-if="showDetailText">{{ detail.engagement_text || '-' }}</div>
            <RadioGroup v-else v-model="detail.engagement">
              <Radio :label="item.id" border v-for="item in options.engagement_desc" :key="item.id">
                {{ item.desc }}
              </Radio>
            </RadioGroup>
          </FormItem>

          <!-- 短信随访相关字段 -->
          <FormItem v-if="detail.method === 'SMS'" label="短信模板" prop="sms_template">
            <div v-if="showDetailText">{{ detail.sms_template_context || '-' }}</div>
            <Select v-else v-model="detail.sms_template" style="width: 80%; min-width: 250px; max-width: 600px">
              <Option :value="item.id" v-for="item in options.sms_template_desc" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="detail.sms_template && detail.method === 'SMS'" label="模板内容" prop="sms_template">
            <div class="template-text" style="width: 80%; min-width: 250px; max-width: 600px">
              {{ detail?.[detail.sms_template] }}
            </div>
          </FormItem>

          <!-- 随访结果分组标题 -->
          <div class="label-wrap">
            <div class="line"></div>
            <div class="label">随访结果</div>
          </div>

          <!-- 随访操作相关字段 -->
          <FormItem label="随访操作" prop="operation_type">
            <div v-if="showDetailText">{{ detail.operation_type_desc || '-' }}</div>
            <RadioGroup v-else v-model="detail.operation_type">
              <Radio :label="item.id" v-for="item in operationTypeOptions" :key="item.id">
                {{ formatOperationDesc(item) }}
              </Radio>
            </RadioGroup>
          </FormItem>

          <FormItem
            v-if="detail.type === 'VISIT' && detail.operation_type === 'EXIT' && +detail.next_id > 0"
            label="提前结束原因"
            prop="reason_type"
          >
            <div v-if="showDetailText">{{ detail.reason_type_text || '-' }}</div>
            <Select
              v-else
              v-model="detail.reason_type"
              placeholder="请选择提前结束原因"
              style="width: 80%; min-width: 250px; max-width: 600px"
            >
              <Option :value="item.id" v-for="item in options.reason_desc" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem v-if="detail.operation_type === 'PROCEED'" label="下次随访时间" prop="next_date">
            <div v-if="showDetailText">{{ detail.next_date || '-' }}</div>
            <DatePicker
              v-else
              type="date"
              v-model="detail.next_date"
              :options="disabledTime"
              style="width: 80%; min-width: 250px; max-width: 600px"
              placeholder="请选择下次随访时间"
              :disabled="detail.type === 'VISIT' && +detail.next_id > 0"
              @on-change="changeNextTime"
            ></DatePicker>

            <div
              v-if="detail.type === 'VISIT' && +detail.next_id > 0 && !disabled"
              style="color: #ee3838; font-size: 10px; margin-top: -6px"
            >
              *该时间由系统自动生成，暂不支持修改
            </div>
            <div
              v-if="(detail.type === 'NORMAL' || !detail.next_id) && !disabled"
              style="color: #ee3838; font-size: 10px; margin-top: -6px"
            >
              *填写下次随访时间当天会生成一条待随访记录
            </div>
          </FormItem>
          <!-- 预约和备注相关字段 -->
          <FormItem label="关联预约单" prop="reserve_id">
            <div
              v-if="!detail.reserve_id && !disabled"
              style="width: fit-content; color: #115bd4; cursor: pointer"
              @click.stop="handleOpenReserveModal"
            >
              <span>立即预约</span>
              <span style="margin-top: -1px">&gt;</span>
            </div>
            <div v-if="!detail.reserve_id && disabled">-</div>
            <div v-if="detail.reserve_id" style="color: #115bd4">
              <span style="color: #000">{{ reserveInfoText }}</span>
              <span
                style="margin-top: -1px; margin-left: 4px; cursor: pointer"
                @click.stop="openNewPageToReserveDetail(detail.reserve_id)"
              >
                详情>
              </span>
            </div>
          </FormItem>

          <FormItem
            label="随访备注"
            prop="remark"
            :rules="{ required: detail.reason_type === 'QT', message: '请输入提前结束原因', trigger: 'change' }"
          >
            <div v-if="showDetailText">{{ detail.remark || '-' }}</div>
            <Input
              v-else
              v-model="detail.remark"
              type="textarea"
              style="width: 80%; min-width: 250px; max-width: 600px"
              :autosize="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入备注信息"
            ></Input>
          </FormItem>
        </Form>
      </div>
    </div>

    <!-- 添加预约弹窗组件 -->
    <add-reserve-modal
      v-model="addReserveVisible"
      @success="addReserveSuccess"
      :user-info="current_user_info"
      :row="{}"
      :type="reserve_type"
      :options="options"
      :is-not-remove-user="true"
    ></add-reserve-modal>
    <KPatientRecordModal
      :dialogVisible.sync="recordVisible"
      :pt_id="detail.pt_id"
      :is_patient="!!detail.pt_id"
    ></KPatientRecordModal>
    <!-- 底部固定操作按钮区域 -->
    <div class="fixed-bottom-wrapper">
      <back-button>返回</back-button>
      <Button
        v-if="!disabled"
        class="btnStyle"
        type="primary"
        :loading="submitLoading"
        style="margin-left: 16px"
        @click="handleSubmit"
      >
        结束随访
      </Button>
    </div>
  </div>
</template>

<script>
/**
 * 随访详情页面组件
 * 用于展示和编辑患者随访记录的详细信息
 * 支持查看随访历史、编辑随访内容、关联预约等功能
 */

// 第三方库导入
import moment from 'moment';
import { merge  } from 'lodash-es';

// 项目工具库导入
import S from '@/libs/util';
import { isEmpty } from '@/utils/helper';

// 组件导入
import addReserveModal from '@/view/reserve/listing/components/addReserveModal.vue';

// 配置文件导入
import globalConfig from '@/config';

/**
 * 随访详情组件
 * 用于显示和编辑患者的随访记录详情
 * 支持诊后随访和常规随访两种类型
 */
export default {
  name: 'newVisitDetail',
  components: {
    addReserveModal, // 添加预约弹窗组件
    KPatientRecordModal: () => import('_c/k-patientRecord-modal/k-patientRecord-modal'),
  },

  /**
   * 组件属性定义
   * 当前组件暂无需要传入的props
   */
  props: {},

  /**
   * 组件数据定义
   * 包含随访详情、表单验证规则、选项数据等
   */
  data() {
    return {
      moment, // moment.js时间处理库

      // 日期选择器配置 - 禁用今天及之前的日期
      disabledTime: {
        disabledDate(date) {
          return moment(moment().format('YYYY-MM-DD')).isSameOrAfter(moment(date).format('YYYY-MM-DD'));
        },
      },
      // 随访详情数据对象
      recordVisible: false,
      detail: {
        // 基础信息
        status: '', // 随访状态
        id: '', // 随访记录ID
        type: '', // 随访类型 (VISIT: 诊后随访, NORMAL: 常规随访)
        target: '', // 随访目标
        reserve_data: {}, // 预约数据
        operation_type: '', // 随访操作类型 (PROCEED: 继续, EXIT: 结束)
        next_date: moment().locale('zh-cn').format('YYYY-MM-DD'), // 下次随访时间
        comfort: '', // 全身舒适度
        method: '', // 随访方式 (电话、微信、短信等)
        sms_template: '', // 短信模板ID
        engagement: '', // 用户积极度
        reason_type: '', // 提前结束原因类型
        remark: '', // 随访备注
        reserve_id: '', // 关联预约单ID
        diag_result: [], // 诊断结果数组
        user_info: {}, // 用户信息

        // 患者基本信息
        avatar: '', // 患者头像
        pt_name: '', // 患者姓名
        sex_text: '', // 性别文本
        age: '', // 年龄
        pt_mobile: '', // 患者手机号
        user_role: '', // 用户角色
        visit_date: '', // 就诊日期
        doctor_name: '', // 就诊医生
        arrival_time: '', // 到店时间戳
        pt_id: '', // 患者ID
        is_patient: '', // 是否是就诊人
        date: '', // 随访日期
        uuid: '', // 唯一标识符
        next_id: '', // 下次随访ID

        // 文本显示属性 (用于只读模式显示)
        type_text: '', // 随访类型文本
        method_text: '', // 随访方式文本
        comfort_text: '', // 全身舒适度文本
        engagement_text: '', // 用户积极度文本
        sms_template_context: '', // 短信模板内容
        operation_type_desc: '', // 随访操作描述
        reason_type_text: '', // 提前结束原因文本
        reserve_time_desc: '', // 预约时间描述
        physio_name: '', // 理疗师姓名
        reserve_type_desc: '', // 预约类型描述
      },
      // 表单验证规则
      rules: {
        type: [{ required: true, message: '请选择随访类型', trigger: 'change' }],
        target: [{ required: true, message: '请选择随访目标', trigger: 'blur' }],
        method: [{ required: true, message: '请选择随访方式', trigger: 'change' }],
        comfort: [{ required: true, message: '请选择全身舒适度', trigger: 'change' }],
        engagement: [{ required: true, message: '请选择用户积极度', trigger: 'change' }],
        sms_template: [{ required: true, message: '请选择短信模版', trigger: 'change' }],
        operation_type: [{ required: true, message: '请选择随访操作类型', trigger: 'change' }],
        next_date: [{ required: true, message: '请选择下次随访时间', trigger: 'change' }],
        reason: [{ required: true, message: '请选择提前结束原因', trigger: 'change' }],
      },

      // 随访记录相关数据
      records: [], // 随访记录列表
      wait_count: 0, // 待随访记录数量
      selectedRow: {}, // 当前选中的随访记录行
      selectedId: '', // 当前选中的随访详情ID
      // 下拉选项数据配置
      options: {
        type_desc: [], // 随访类型选项
        sub_type_desc: [], // 随访子类型选项
        visit_method_desc: [], // 诊后随访方式选项
        normal_method_desc: [], // 常规随访方式选项
        comfort_desc: [], // 全身舒适度选项
        visit_operation_desc: [], // 诊后随访操作选项
        normal_operation_desc: [], // 常规随访操作选项
        status_desc: [], // 状态选项
        effect_desc: [], // 效果选项
        engagement_desc: [], // 用户积极度选项
        result_desc: [], // 结果选项
        view_desc: [], // 视图选项
        sms_template_desc: [], // 短信模板选项
        reason_desc: [], // 提前结束原因选项
        out_sales_channel_desc: [], // 外部销售渠道选项
      },

      // 预约相关数据
      addReserveVisible: false, // 添加预约弹窗显示状态
      reserve_type: '1', // 预约类型 (1: 理疗, 2: 医生)
      current_user_info: {
        // 当前用户信息
        uid: '', // 用户ID
        real_name: '', // 真实姓名
        avatar: '', // 头像
        mobile: '', // 手机号
        sex_text: '', // 性别文本
      },

      // 加载状态控制
      submitLoading: false, // 提交按钮加载状态
      detailLoading: false, // 详情加载状态
      pageLoading: false, // 页面加载状态
    };
  },
  computed: {
    operationTypeOptions() {
      if (this.detail.type === 'VISIT') {
        const visit_operation_desc = this?.options?.visit_operation_desc || [];
        if (!this.detail.next_id) {
          return visit_operation_desc.sort((a, b) => +a.sort - +b.sort).reverse();
        }
        return visit_operation_desc.sort((a, b) => +a.sort - +b.sort);
      }
      if (this.detail.type === 'NORMAL') {
        const normal_operation_desc = this?.options?.normal_operation_desc || [];
        return normal_operation_desc?.sort((a, b) => +a.sort - +b.sort);
      }
      return [];
    },
    user_avatar() {
      return user => {
        const woman = 'https://img-sn01.rsjxx.com/image/2025/0306/150751_28927.png';
        const man = 'https://img-sn01.rsjxx.com/image/2025/0306/150751_62458.png';
        return user.sex === '1' ? man : woman;
      };
    },
    /**
     * 是否显示详情文本模式
     * 当随访状态为已完成或已取消时，显示只读文本而非表单控件
     */
    showDetailText() {
      return this.detail.status === 'FOLLOW_UP' || this.detail.status === 'CANCEL';
    },

    /**
     * 表单是否禁用
     * 只有状态为待随访且当前日期等于随访日期时才可编辑
     */
    disabled() {
      if (this.detail.status !== 'WAIT_FOLLOW') return true;
      if (moment().format('YYYY-MM-DD') !== this.detail.date) return true;
      return false;
    },

    /**
     * 格式化到店时间
     * 将时间戳转换为可读的日期时间格式
     */
    formatTime() {
      if (!(this.detail?.arrival_time * 1000)) return '-';
      return moment(this.detail.arrival_time * 1000).format('YYYY-MM-DD HH:mm');
    },
    /**
     * 格式化操作描述文本
     * 根据随访类型和下次随访ID动态生成操作描述
     */
    formatOperationDesc() {
      return item => {
        const { next_id, type } = this.detail || {};
        if (type === 'NORMAL') return item.desc;
        if (+next_id > 0 && item.id === 'PROCEED') return '下次继续跟进';
        if (+next_id > 0 && item.id === 'EXIT') return '不再跟进（结束）该随访计划';
        if (!next_id && item.id === 'PROCEED') return '额外增加一个随访任务';
        if (!next_id && item.id === 'EXIT') return '结束该随访计划';
        return item.desc;
      };
    },

    /**
     * 根据随访类型获取对应的随访方式选项
     * 常规随访过滤掉短信方式，诊后随访保留所有方式
     */
    visit_method_desc() {
      const visit_method_desc = this.options.visit_method_desc || [];
      const type = this.detail.type || '';
      const sub_type = this.detail.sub_type || '';
      if (type === 'NORMAL' || (type === 'VISIT' && sub_type !== 'PRES')) {
        return visit_method_desc.filter(item => item.id !== 'SMS');
      }
      return visit_method_desc;
    },

    /**
     * 格式化预约信息文本
     * 根据表单状态显示不同格式的预约信息
     */
    reserveInfoText() {
      if (this.disabled) {
        const { reserve_time_desc, physio_name, reserve_type_desc } = this.detail;
        return `${reserve_time_desc || ''}  (${physio_name} | ${reserve_type_desc || '医生'})`;
      }
      const reserve_data = this.detail.reserve_data || {};
      return `${reserve_data.reserve_date || ''} ${reserve_data.reserve_time || ''}  (${
        reserve_data.services?.[0]?.physical_name
      } | ${reserve_data.services?.[0]?.role_name || '医生'})`;
    },

    /**
     * 根据随访状态返回对应的颜色值
     * 用于状态标签的颜色显示
     */
    status_color() {
      return item => {
        if (item.status === 'CANCEL') return '#999999';
        if (item.status === 'TIMEOUT') return '#000000';
        if (item.status === 'FOLLOW_UP') return '#115bd4';
        return '#ffaa00';
      };
    },
  },
  /**
   * 组件创建完成生命周期钩子
   * 在组件实例创建完成后立即调用
   * 用于初始化获取随访记录数据
   */
  created() {
    this.getRecord();
  },

  /**
   * 组件挂载完成生命周期钩子
   * 在组件挂载到DOM后调用
   * 用于初始化滚动条检测和事件监听
   */
  mounted() {
    this.checkScrollbar();
    // 监听窗口大小变化，重新检测滚动条
    window.addEventListener('resize', this.checkScrollbar);
  },

  /**
   * 组件销毁前生命周期钩子
   * 清理事件监听器
   */
  beforeDestroy() {
    window.removeEventListener('resize', this.checkScrollbar);
  },

  methods: {
    /**
     * 查看病历
     * 在新窗口中打开HIS系统的患者病历页面
     */
    checkMr() {
      const hisDomain = globalConfig.HISDomain;
      const { pt_id } = this.detail;
      const url = `${hisDomain}/his/patient/patientinfo?id=${pt_id}`;
      window.open(url, '_blank');
    },

    /**
     * 打开添加预约弹窗
     * 显示预约类型选择对话框，用户选择后打开对应的预约弹窗
     */
    handleOpenReserveModal() {
      let selectedType = '1'; // 默认选中预约服务
      this.$Modal.remove();
      this.$Modal.confirm({
        title: '选择预约类型',
        render: h => {
          return h('div', { style: { display: 'flex', alignItems: 'center', marginTop: '16px' } }, [
            h('div', '预约类型：'),
            h(
              'RadioGroup',
              {
                props: {
                  value: selectedType,
                },
                on: {
                  'on-change': value => {
                    selectedType = value;
                  },
                },
              },
              [
                h(
                  'Radio',
                  {
                    props: {
                      label: '1',
                    },
                  },
                  '预约服务'
                ),
                h(
                  'Radio',
                  {
                    props: {
                      label: '2',
                    },
                  },
                  '预约医生'
                ),
              ]
            ),
          ]);
        },
        onOk: () => {
          if (!selectedType) {
            this.$Message.error('请选择预约类型');
            return false;
          }
          // 这里可以处理确定后的逻辑
          this.reserve_type = selectedType;
          this.addReserveVisible = true;
          this.current_user_info = this.detail.user_info || {};
        },
      });
    },
    /**
     * 预约成功回调
     * 更新随访详情中的预约相关数据
     * @param {Object} a - 预约成功回调参数1
     * @param {Object} b - 预约成功回调参数2
     * @param {Object} params - 预约数据参数
     */
    addReserveSuccess(a, b, params) {
      this.detail.reserve_data = {
        ...(params || {}),
        ...(params.reserve_data || {}),
      };
      this.detail.reserve_id = params.id;
    },

    /**
     * 提交随访表单
     * 验证表单数据并调用接口完成随访
     */
    handleSubmit() {
      this.$refs.FollowUpRef.validate(valid => {
        if (valid) {
          // TODO: 实现表单提交逻辑
          console.log('表单验证通过，可以提交');
          let operation_type_desc = '';

          const item = this.options.normal_operation_desc.find(item => item.id === this.detail.operation_type) || {};
          const { next_id, type } = this.detail;

          if (type === 'NORMAL') {
            operation_type_desc = item.desc;
          }
          if (+next_id > 0 && item.id === 'PROCEED') {
            operation_type_desc = '下次继续跟进';
          }
          if (+next_id > 0 && item.id === 'EXIT') {
            operation_type_desc = '不再跟进（结束）该随访计划';
          }
          if (!next_id && item.id === 'PROCEED') {
            operation_type_desc = '额外增加一个随访任务';
          }
          if (!next_id && item.id === 'EXIT') {
            operation_type_desc = '结束该随访计划';
          }
          const params = {
            id: this.detail.id,
            reserve_id: this.detail.reserve_id,
            operation_type: this.detail.operation_type,
            next_date: this.detail.next_date,
            comfort: this.detail.comfort,
            method: this.detail.method,
            sms_template: this.detail.sms_template,
            engagement: this.detail.engagement,
            diag_result: this.detail.diag_result,
            reason_type: this.detail.reason_type,
            remark: this.detail.remark,
            operation_type_desc,
          };
          this.submitLoading = true;
          this.$api
            .followUpFinished(params)
            .then(() => {
              this.$Message.success('随访结束成功');
              this.getRecord(this.selectedId);
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    },

    /**
     * 在新页面中打开预约详情
     * @param {string} id - 预约记录ID
     */
    openNewPageToReserveDetail(id) {
      const href = this.$router.resolve({
        path: '/reserve/listing/detail',
        query: {
          id,
        },
      }).href;
      window.open(href, '_blank');
    },

    /**
     * 获取随访记录列表
     * 根据记录ID获取患者的所有随访记录，并设置默认选中项
     * @param {string} recordId - 随访记录ID
     */
    getRecord(recordId) {
      const id = recordId || this.$route.query.id;
      if (!id) {
        return;
      }
      this.pageLoading = true;
      this.$api
        .getVisitRecordV2({
          id,
        })
        .then(res => {
          this.records = res?.list?.map((item, index) => ({ ...item, index })) || [];
          this.wait_count = res?.wait_count || 0;
          for (let i = 0; i < this.records.length; i++) {
            const record = this.records[i];
            if (record.checked === '1') {
              this.selectedRow = record;
              for (let j = 0; j < record?.list.length; j++) {
                if (record?.list[j]?.checked === '1') {
                  this.getDetail(record.list[j].id);
                  break;
                }
              }
              break;
            }
          }
          // 数据更新后重新检测滚动条
          this.checkScrollbar();
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    /**
     * 切换侧边栏选项卡
     * 选择不同的随访记录行，并自动选择对应的随访详情
     * @param {Object} row - 选中的随访记录行数据
     */
    changeSideTab(row) {
      this.selectedRow = row;
      this.selectedId =
        row.list.find(item => item.checked === '1')?.id ||
        row.list.find(item => moment().format('YYYY-MM-DD') === item.date)?.id ||
        row.list?.[0]?.id ||
        '';
      this.getDetail(this.selectedId);
    },

    /**
     * 获取随访详情
     * 根据随访ID获取详细信息并更新表单数据
     * @param {string} id - 随访详情ID
     */
    getDetail(id) {
      this.selectedId = '';
      if (!id) return;
      this.selectedId = id;
      // 调用接口获取详情
      this.detailLoading = true;
      this.$api
        .getVisitDetailV2({
          id,
        })
        .then(res => {
          const newDetail = merge(this.detail, res?.detail || {});
          newDetail.uuid = Math.random().toString(36).substr(2, 10);
          const detailKeys = Object.keys(newDetail);
          for (let i = 0; i < detailKeys.length; i++) {
            this.$set(newDetail, detailKeys[i], newDetail[detailKeys[i]]);
          }
          // this.$set(this, 'detail', newDetail);
          this.$set(
            this.detail,
            'diag_result',
            res?.detail?.diag_result?.map(item => {
              item.feedback_type = '';
              return item;
            }) || []
          );
          const options = res?.options || {};
          const keys = Object.keys(options);
          for (let i = 0; i < keys.length; i++) {
            const list = S.descToArrHandle(options?.[keys[i]] || {});
            this.$set(this.options, keys[i], list);
          }
          if (res?.detail?.type === 'VISIT') {
            if (!res?.detail?.next_id) {
              this.$set(this.detail, 'operation_type', res?.detail?.operation_type || 'EXIT');
            } else {
              this.$set(this.detail, 'operation_type', res?.detail?.operation_type || 'PROCEED');
            }
            if (res?.detail?.type === 'VISIT' && res?.detail?.sub_type !== 'PRES') {
              this.$set(this.detail, 'method', res?.detail?.method || 'MOBILE_VISIT');
            } else {
              this.$set(this.detail, 'method', res?.detail?.method || 'SMS');
            }
          }
          if (res?.detail?.type === 'NORMAL') {
            this.$set(this.detail, 'operation_type', res?.detail?.operation_type || 'EXIT');
            this.$set(this.detail, 'method', res?.detail?.method || 'MOBILE_VISIT');
          }
          this.$nextTick(() => {
            const children = this.$refs.FollowUpRef.$children || [];
            for (let j = 0; j < children.length; j++) {
              if (!children[j]?.prop) continue;
              if (children[j]?.prop?.indexOf('feedback_type') !== -1) continue;
              const value = this.detail?.[children[j].prop] || '';
              if (isEmpty(value)) {
                children[j].resetField();
              }
            }
          });
        })
        .finally(() => {
          this.detailLoading = false;
        });
    },
    /**
     * 更改下次随访时间
     * 日期选择器变化时的回调函数
     * @param {string} time - 选择的时间
     */
    changeNextTime(time) {
      this.detail.next_date = time;
    },
    changeMethod(method) {
      if (method === 'SMS') {
        this.detail.comfort = '';
        this.$set(
          this.detail,
          'diag_result',
          this?.detail?.diag_result?.map(item => {
            item.feedback_type = '';
            return item;
          }) || []
        );
      }
      if (method !== 'SMS') {
        this.detail.sms_template = '';
      }
      console.log(method, 'change');
    },

    /**
     * 检测滚动条是否存在
     * 如果不存在滚动条则隐藏箭头按钮
     */
    checkScrollbar() {
      this.$nextTick(() => {
        const scrollContent = this.$el.querySelector('.record-scroll-content');
        const arrows = this.$el.querySelectorAll('.record-arrow');

        if (scrollContent && arrows.length > 0) {
          const hasScrollbar = scrollContent.scrollWidth > scrollContent.clientWidth;
          scrollContent.style.padding = hasScrollbar ? '0 16px' : 0;
          arrows.forEach(arrow => {
            arrow.style.display = hasScrollbar ? 'block' : 'none';
          });
        }
      });
    },

    /**
     * 处理箭头点击事件
     * @param {string} direction - 滚动方向 'left' 或 'right'
     */
    handleArrowClick(direction) {
      const scrollContent = this.$el.querySelector('.record-scroll-content');
      const recordItem = this.$el.querySelector('.record-item');

      if (scrollContent && recordItem) {
        // 计算滚动距离：record-item宽度 + margin-right(12px)
        const itemWidth = recordItem.offsetWidth;
        const scrollDistance = itemWidth + 12;

        const currentScrollLeft = scrollContent.scrollLeft;
        const targetScrollLeft =
          direction === 'left' ? currentScrollLeft - scrollDistance : currentScrollLeft + scrollDistance;

        // 平滑滚动
        scrollContent.scrollTo({
          left: targetScrollLeft,
          behavior: 'smooth',
        });
      }
    },
  },
};
</script>
<style scoped lang="less">
.follow-up-wrapper {
  width: 100%;
  height: 100%;
  .follow-up-header {
    width: 100%;
    background-color: #fff;
    border-bottom: 10px solid #f2f2f2;
    display: flex;

    .follow-up-header-user {
      width: fit-content;
      height: 100%;
      display: flex;
      align-items: center;
      border-right: 1px solid #ecedf0;
      padding: 12px 0 12px 16px;
      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 12px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .user-info {
        width: 221px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .info-name {
          width: 100%;
          display: flex;
          align-items: center;
          > div {
            font-size: 12px;
            color: #999999;
            line-height: 18px;
          }
          .name {
            font-weight: 600;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            margin-right: 8px;
          }
          .line {
            width: 1px;
            height: 12px;
            background-color: #999999;
            margin: 0 4px;
          }
        }
        .info-phone {
          font-size: 12px;
          color: #333333;
          line-height: 18px;
        }
      }
    }
    .follow-up-header-case {
      flex: 1;
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 12px 16px 12px 0;
      .follow-up-header-case-date {
        width: fit-content;
        display: flex;
        align-items: center;
        margin: 0 auto;
        .label {
          font-size: 13px;
          color: #999999;
          line-height: 18px;
        }
        .date {
          font-size: 13px;
          color: #333333;
          line-height: 18px;
        }
      }
      .follow-up-header-case-icon {
        width: fit-content;
        display: flex;
        align-items: center;
        cursor: pointer;
        margin: 0 0 0 auto;
        > img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        > a {
          font-size: 12px;
          color: #155bd4;
          line-height: 18px;
        }
      }
    }
  }
  .follow-up-record {
    width: 100%;
    padding: 12px 16px;
    border-bottom: 10px solid #f2f2f2;
    .record-tip {
      font-size: 12px;
      color: #ffaa00;
      line-height: 16px;
      margin-bottom: 12px;
    }
    .record-scroll {
      width: 100%;
      display: flex;
      align-items: center;
    }
    .record-arrow {
      flex-shrink: 0;
      width: 42px;
      height: 32px;
      cursor: pointer;
      transition: opacity 0.3s ease;
      padding-right: 10px;
      background: #fff;
      &:hover {
        opacity: 0.7;
      }
    }
    .record-arrow:last-child {
      padding-right: 0;
      padding-left: 10px;
    }
    .record-scroll-content {
      flex: 1;
      padding: 0 16px;
      overflow-y: hidden;
      overflow-x: auto;
      .record-items {
        width: fit-content;
        display: flex;
        align-items: center;
        .record-item {
          display: flex;
          align-items: center;
          padding: 6px 12px;
          margin-right: 12px;
          background: #f5f6f8;
          border-radius: 4px;
          cursor: pointer;
          flex-shrink: 0;
          > .type_img {
            width: 12px;
            height: 12px;
          }
          > .action_img {
            width: 16px;
            height: 16px;
          }
          > span {
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            margin: 0 6px;
          }
          &.active {
            background: rgba(21, 91, 212, 0.04);
            border-radius: 4px;
            border: 1px solid #155bd4;
            > span {
              color: #155bd4;
            }
          }
        }
      }
    }
    .record-scroll-content::-webkit-scrollbar {
      display: none;
    }
  }
  .follow-up-content {
    width: 100%;
    height: e('calc(100vh - 275px)');
    display: flex;
    .side-tabs {
      width: 290px;
      height: 100%;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      .side-tabs-header {
        width: 100%;
        padding: 24px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        > img {
          width: 18px;
          height: 18px;
          margin-right: 12px;
        }
        > span {
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 22px;
        }
      }
      .side-tabs-scroll {
        width: 100%;
        flex: 1;
        padding: 0 16px 32px 16px;
        overflow-x: hidden;
        overflow-y: auto;
        .side-tabs-content {
          width: 100%;
          height: fit-content;
          padding-bottom: 16px;
          .side-tabs-item {
            width: 100%;
            margin-top: 24px;
            .side-tabs-item-header {
              width: 100%;
              display: flex;
              margin-bottom: 12px;
              justify-content: space-between;
              align-items: center;
              .left-title {
                flex: 1;
                .title {
                  font-weight: 600;
                  font-size: 14px;
                  color: #333333;
                  line-height: 20px;
                }
                .tip {
                  font-size: 12px;
                  color: #999999;
                  line-height: 16px;
                  margin-top: 2px;
                }
              }
              .right-status {
                width: fit-content;
                flex-shrink: 0;
                font-size: 12px;
                color: #ffaa00;
                line-height: 18px;
              }
            }
            .side-tabs-item-info {
              width: 100%;
              padding: 16px;
              background: #f9fafb;
              border: 1px solid #f9fafb;
              border-radius: 4px;
              cursor: pointer;
              .info-items {
                width: 100%;
                display: flex;
                margin-bottom: 12px;
                .label {
                  width: fit-content;
                  flex-shrink: 0;
                  font-size: 13px;
                  color: #999999;
                  line-height: 20px;
                  padding-right: 12px;
                }
                .text {
                  flex: 1;
                  font-size: 13px;
                  color: #333333;
                  line-height: 20px;
                  text-align: right;
                }
              }
              .info-items:last-child {
                margin-bottom: 0;
              }
            }
            &.active {
              .side-tabs-item-info {
                border-color: #155bd4;
                background: rgba(21, 91, 212, 0.04);
              }
            }
          }
          .side-tabs-item:first-child {
            margin-top: 0;
          }
        }
      }
    }
    .content {
      flex: 1;
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      padding: 16px 16px 32px 16px;
      border-left: 1px solid #f2f2f2;
      .label-wrap {
        width: fit-content;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        .line {
          width: 3px;
          height: 16px;
          background: linear-gradient(90deg, #155bd4 0%, #4988fd 100%);
          margin-right: 12px;
        }
        .label {
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 24px;
        }
      }
      .template-text {
        padding: 8px 12px;
        background: #f9fafb;
        border-radius: 4px;
        font-size: 13px;
        color: #333333;
        line-height: 20px;
        word-break: break-all;
        text-align: justify;
      }
    }
  }
}
.follow-up-form {
  :deep(.ivu-radio-border .ivu-radio) {
    display: none;
  }
  :deep(.ivu-radio-wrapper-checked.ivu-radio-border) {
    border-color: #155bd4 !important;
    color: #155bd4 !important;
  }
  :deep(.ivu-radio-wrapper-disabled.ivu-radio-border) {
    color: #aaaaaa !important;
  }
}
.hideRequiredMark {
  :deep(.ivu-form-item-label::before) {
    display: none;
  }
}
.disabled-form {
  :deep(.ivu-form-item-label::before) {
    display: none;
  }
}
.disabled-form {
  :deep(.ivu-form-item) {
    margin-bottom: 0;
  }
}
</style>
