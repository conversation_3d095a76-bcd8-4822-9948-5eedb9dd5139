<template>
  <div>
    <div v-if="showOldDetail" class="visit-detail-wrapper flex" style="height: calc(100vh - 120px)">
      <div class="customer-info" ref="customer-info">
        <div class="basic-info flex flex-item-between">
          <div class="user-info-base flex">
            <div class="info-left">
              <img v-if="patientInfo.avatar" :src="patientInfo.avatar | imageStyle" />
              <img v-else-if="patientInfo.sex == '1'" src="@/assets/image/base/consu_card_boy.png" />
              <img v-else-if="patientInfo.sex == '2'" src="@/assets/image/base/consu_card_girl.png" />
            </div>
            <div class="info-right">
              <div class="flex user-name">
                <p class="left-key" style="text-align: left; width: auto">{{ patientInfo.name }}</p>
                <p>
                  {{ patientInfo.sex_text || '-' }} / {{ patientInfo.age || '-' }}岁 /
                  {{ patientInfo.type_text || '-' }}
                  <span v-if="patientInfo.user_role !== 'SELF'"
                    >/ {{ patientInfo.user_name }}的{{ patientInfo.user_role_text }}</span
                  >
                </p>
              </div>
              <div class="flex">
                <p class="left-key">用户来源</p>
                <p class="left-value">{{ patientInfo.from_text || '-' }}</p>
              </div>
              <div class="flex center fs-12">
                <p class="left-key">联系电话</p>
                <p class="left-value">{{ patientInfo.mobile }}</p>
              </div>
              <div class="flex center fs-12">
                <p class="left-key">联系地址</p>
                <p class="left-value">{{ patientInfo.addr_detail || '-' }}</p>
              </div>
            </div>
          </div>
          <div class="file-btn flex" style="align-self: center">
            <div class="btn-item flex flex-c" @click="recordVisible = true">
              <svg-icon iconClass="medical-record" style="width: 20px; height: 20px; margin-top: 2px"></svg-icon>
              <span class="txt">病历</span>
            </div>
            <div
              class="btn-item flex flex-c"
              @click="$router.push(`/user/detail?uid=${uid}`)"
              v-show="patientInfo.is_patient === '0'"
            >
              <svg-icon iconClass="file" style="width: 20px; height: 20px; margin-top: 2px"></svg-icon>
              <span class="txt">档案</span>
            </div>
          </div>
        </div>
        <div class="info-content hidden-scroll" ref="info-content">
          <div class="panel-nav">
            <a
              class="nav"
              :class="{ active: curIndex === index }"
              v-for="(item, index) in navList"
              @click="changeNav(item, index)"
            >
              {{ item.title }} {{ item.num ? `(${item.num})` : '' }}
            </a>
          </div>
          <div class="panel-box">
            <div class="panel-item" id="nav-basic">
              <div class="nav-head sticky">
                <h4>基础信息</h4>
              </div>
              <div v-for="(item, index) in basicDetail" :key="index">
                <h4 class="basic-title" :style="{ marginTop: index !== 0 ? '0' : '12px' }">{{ item.title }}</h4>
                <div class="flex">
                  <div
                    class="panel-content-item"
                    style="width: 25%"
                    v-for="(info, infoKey) in item.contents"
                    :key="infoKey"
                  >
                    <span class="p-label">{{ info.type_text }}</span>
                    <span class="p-value">{{ info.type_val }}</span>
                  </div>
                  <!--                <div style="width: 25%" v-if="item.contents.length < 4"></div>-->
                </div>
              </div>
              <!--            <div class="panel-content-item">-->
              <!--              <span class="p-label">类型</span>-->
              <!--              <span class="p-value">{{ basicInfo.type_text }}</span>-->
              <!--            </div>-->
            </div>
            <div class="panel-item" id="nav-record">
              <div class="nav-head sticky">
                <h4>病历信息</h4>
              </div>
              <div class="panel-item" v-if="record_image_details.length">
                <h4 class="symptom-title">问诊参考(影像资料)</h4>
                <div class="">
                  <div
                    class="panel-content-item"
                    v-for="(images_item, images_index) in record_image_details"
                    :key="'images' + images_index"
                  >
                    <div class="p-label" v-if="images_item.type_val.length">{{ images_item.type_text }}</div>
                    <div class="img-wrapper p-value" v-if="images_item.type_val.length">
                      <el-image
                        v-for="(img_item, img_index) in images_item.type_val"
                        :key="'img_index' + img_index"
                        :src="img_item | imageStyle"
                        class="cursor custom-img"
                        :preview-src-list="images_item.type_val"
                      >
                      </el-image>
                      <!-- <img class="cursor" :src="img_item" alt="" v-for="( img_item, img_index ) in images_item.type_val" :key="'img_index'+img_index"> -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="panel-item" v-if="symptom_details.length">
                <h4 class="symptom-title">症状与主诉</h4>
                <div class="panel-content-item" v-for="item in symptom_details" :key="item.type_text">
                  <span class="p-label">{{ item.type_text }}</span>
                  <span class="p-value">{{ item.type_val }}</span>
                </div>
              </div>
            </div>
            <div class="panel-item" v-if="showBodyIndex">
              <h4 class="symptom-title">既往史与身体指标</h4>
              <!--            <div-->
              <!--              class="panel-content-item"-->
              <!--              v-for="(item, symptom_index) in medical_history"-->
              <!--              :key="'symptom' + symptom_index"-->
              <!--            >-->
              <!--              <span class="p-label">{{ item.type_text }}</span>-->
              <!--              <span class="p-value">{{ item.type_val }}</span>-->
              <!--            </div>-->
              <Row>
                <Col :span="6" v-if="body_index.height">
                  <div class="panel-content-item">
                    <span class="p-label">身高</span>
                    <span class="p-value">{{ body_index.height }}</span>
                  </div>
                </Col>

                <Col :span="6" v-if="body_index.weight">
                  <div class="panel-content-item">
                    <span class="p-label">体重</span>
                    <span class="p-value">{{ body_index.weight }}Kg</span>
                  </div>
                </Col>

                <Col :span="6" v-if="body_index.temperature">
                  <div class="panel-content-item">
                    <span class="p-label">体温</span>
                    <span class="p-value">{{ body_index.temperature }}℃</span>
                  </div>
                </Col>

                <Col :span="6" v-if="body_index.heart_rate">
                  <div class="panel-content-item">
                    <span class="p-label">心率</span>
                    <span class="p-value">{{ body_index.heart_rate }}bmp</span>
                  </div>
                </Col>

                <Col :span="6" v-if="body_index.systolic_pressure">
                  <div class="panel-content-item">
                    <span class="p-label">收缩压</span>
                    <span class="p-value">{{ body_index.systolic_pressure }}mmHg</span>
                  </div>
                </Col>
                <Col :span="6" v-if="body_index.diastolic_pressure">
                  <div class="panel-content-item">
                    <span class="p-label">舒张压</span>
                    <span class="p-value">{{ body_index.diastolic_pressure }}mmHg</span>
                  </div>
                </Col>
              </Row>
            </div>
            <!--体格检查与身体指标并入循环-->
            <!--          <div class="panel-item" v-if="showBodyIndex">-->
            <!--            <h4 class="symptom-title">既往史与身体指标</h4>-->
            <!--            <div class="panel-content-item" v-for="( item, symptom_index ) in medical_history"-->
            <!--                 :key="'symptom'+symptom_index">-->
            <!--              <span class="p-label">{{ item.type_text }}</span>-->
            <!--              <span class="p-value">{{ item.type_val }}</span>-->
            <!--            </div>-->
            <!--            -->
            <!--            <Row >-->
            <!--              <Col :span="6"  v-if="body_index.height">-->
            <!--                <div class="panel-content-item">-->
            <!--                  <span class="p-label">身高</span>-->
            <!--                  <span class="p-value">{{ body_index.height }}</span>-->
            <!--                </div>-->
            <!--              </Col>-->

            <!--              <Col :span="6"  v-if="body_index.weight">-->
            <!--                <div class="panel-content-item">-->
            <!--                  <span class="p-label">体重</span>-->
            <!--                  <span class="p-value">{{ body_index.weight }}Kg</span>-->
            <!--                </div>-->
            <!--              </Col>-->

            <!--              <Col :span="6"  v-if="body_index.temperature">-->
            <!--                <div class="panel-content-item">-->
            <!--                  <span class="p-label">体温</span>-->
            <!--                  <span class="p-value">{{ body_index.temperature }}℃</span>-->
            <!--                </div>-->
            <!--              </Col>-->

            <!--              <Col :span="6"  v-if="body_index.heart_rate">-->
            <!--                <div class="panel-content-item">-->
            <!--                  <span class="p-label">心率</span>-->
            <!--                  <span class="p-value">{{ body_index.heart_rate }}bmp</span>-->
            <!--                </div>-->
            <!--              </Col>-->

            <!--              <Col :span="6"  v-if="body_index.systolic_pressure">-->
            <!--                <div class="panel-content-item">-->
            <!--                  <span class="p-label">收缩压</span>-->
            <!--                  <span class="p-value">{{ body_index.systolic_pressure }}mmHg</span>-->
            <!--                </div>-->
            <!--              </Col>-->
            <!--              <Col :span="6" v-if="body_index.diastolic_pressure">-->
            <!--                <div class="panel-content-item">-->
            <!--                  <span class="p-label">舒张压</span>-->
            <!--                  <span class="p-value">{{ body_index.diastolic_pressure }}mmHg</span>-->
            <!--                </div>-->
            <!--              </Col>-->
            <!--            </Row>-->
            <!--          </div>-->

            <div class="panel-item" v-if="symptom_details.length">
              <div class="panel-content-item" v-for="item in medical_details" :key="item.type_text">
                <span class="p-label">{{ item.type_text }}</span>
                <span class="p-value">{{ item.type_val }}</span>
              </div>
            </div>
            <div class="panel-item prescription-item" id="nav-pres">
              <div class="nav-head sticky">
                <h4>处方</h4>
              </div>
              <div v-for="item in presList" :key="item.type">
                <div class="pres-content-item">
                  <div class="pres-head">
                    <h5 class="f-title">{{ item.type_text }}</h5>
                    <span class="number">单号:{{ item.pres_code }}</span>
                  </div>
                  <div class="herbal-item" v-if="item.type === 'HERBS' || item.type === 'herbs_info'">
                    <Row>
                      <Col :span="5" v-for="sub in item.attrs" :key="sub.id">
                        <span class="pres-item-text"
                          >{{ sub.name }} <span style="margin-left: 6px">{{ sub.quantity_text }}</span>
                        </span>
                      </Col>
                    </Row>
                    <div class="remark" v-if="item.remark || item.medical_advice">
                      <p v-if="item.medical_advice">医嘱和注意事项：{{ item.medical_advice }}</p>
                      <p v-else>方案备注：{{ item.remark }}</p>
                    </div>
                    <div class="usage flex flex-item-between">
                      <span
                        ><span v-if="detailInfo.his_version !== '4.0'">{{ item.usage_text }}</span></span
                      >
                      <div class="table-sum" style="margin-bottom: 0">
                        <span class="total">共{{ item.attrs.length }}种</span>
                        <span>小计：¥ {{ item.payment_fee }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="pres-table-item" v-else>
                    <Table :columns="colsObj[item.type + 'Cols']" :data="item.attrs">
                      <!-- 单价 -->
                      <template slot-scope="{ row }" slot="image">
                        <viewer :images="[row.image]">
                          <img v-for="src in [row.image]" :src="src | imageStyle" :key="src" style="width: 50px" />
                        </viewer>
                      </template>
                      <!-- 单价 -->
                      <template slot-scope="{ row }" slot="price">
                        <span>￥{{ row.price }}</span>
                        <span v-if="item.type !== 'goods_info' && item.type !== 'zl_info'"
                          >/{{
                            item.type === 'PHYSICAL' || item.type === 'TREAT'
                              ? '个'
                              : (row.medicine_info && row.medicine_info.sales_unit) || '次'
                          }}</span
                        >
                      </template>

                      <!-- 总价 -->
                      <template slot-scope="{ row }" slot="payment_fee"> ￥{{ row.payment_fee }} </template>

                      <!-- 厂商 -->
                      <template slot-scope="{ row }" slot="manufacturer">
                        {{ row.manufacturer || '-' }}
                      </template>

                      <template slot-scope="{ row }" slot="remark">
                        {{ row.remark || '-' }}
                      </template>
                      <template slot-scope="{ row }" slot="freq">
                        {{ row.medicine_info.freq || '-' }}
                      </template>
                      <template slot-scope="{ row }" slot="usage">
                        {{ row.medicine_info.usage || '-' }}
                      </template>
                      <template slot-scope="{ row }" slot="unit_text">
                        {{ row.medicine_info.unit_text || '-' }}
                      </template>
                      <template slot-scope="{ row }" slot="sales_unit">
                        {{ row.medicine_info.sales_unit }}
                      </template>
                      <template slot="cus_unit">
                        <span v-if="item.type === 'goods_info' || item.type === 'zl_info'">
                          {{ item.type === 'goods_info' ? '件' : '次' }}
                        </span>
                        <div v-else>{{ item.type === 'PHYSICAL' || item.type === 'TREAT' ? '个' : '次' }}</div>
                      </template>
                    </Table>
                    <div class="remark" v-if="item.remark || item.medical_advice">
                      <p v-if="item.medical_advice">医嘱和注意事项：{{ item.medical_advice }}</p>
                      <p v-else>方案备注：{{ item.remark }}</p>
                    </div>
                    <div class="flex flex-item-between">
                      <div class="usage" v-if="item.usage_text">
                        <span v-if="detailInfo.his_version !== '4.0'">{{ item.usage_text }}</span>
                      </div>
                      <div v-else></div>
                      <div class="table-sum">
                        <span class="total">共{{ item.attrs.length }}种</span>
                        <span>小计：¥ {{ item.payment_fee }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="panel-item" id="nav-charge">
              <div class="nav-head sticky">
                <h4>收费</h4>
              </div>
              <div class="cost-content">
                <div class="cost-content-item">
                  <div class="pres-head">
                    <h5 class="f-title">收费列表</h5>
                  </div>
                  <div class="flex flex-item-between">
                    <div
                      class="pres-table-item charge-item"
                      style="flex: 1"
                      v-for="(item, index) in chargesList"
                      :key="'chargesList' + index"
                    >
                      <Table :columns="chargeCols" :data="item">
                        <template slot-scope="{ row }" slot="name">{{ row.name }}</template>
                        <template slot-scope="{ row }" slot="money"> ¥{{ row.money }} </template>
                      </Table>
                    </div>
                  </div>

                  <div class="usage flex" style="justify-content: flex-end">
                    <div class="table-sum" style="margin-bottom: 0">
                      <span class="total">共{{ payInfo.total }}项</span>
                      <span>总计：¥ {{ payInfo.payment_fee }}</span>
                    </div>
                  </div>
                </div>
                <div class="cost-content-item" v-for="item in presChargeList" :key="'pres' + item.type">
                  <div class="pres-head">
                    <h5 class="f-title">{{ item.type_text }}</h5>
                  </div>
                  <div class="pres-table-item">
                    <Table :columns="item.type !== 'MEDICINE' ? ointmentCols : medicineBillCols" :data="item.attrs">
                      <!-- 单价 -->
                      <template slot-scope="{ row }" slot="price">
                        <span>￥{{ row.price }}</span>
                        <span v-if="item.type !== 'goods_info' && item.type !== 'zl_info'"
                          >/{{ item.type === 'PHYSICAL' || item.type === 'TREAT' ? '个' : row.unit_name || '次' }}</span
                        >
                      </template>

                      <!-- 总价 -->
                      <template slot-scope="{ row }" slot="payment_fee"> ￥{{ row.payment_fee }} </template>

                      <!-- 厂商 -->
                      <template slot-scope="{ row }" slot="manufacturer">
                        {{ row.manufacturer || '-' }}
                      </template>

                      <template slot-scope="{ row }" slot="cus_unit">
                        <span v-if="item.type === 'goods_info' || item.type === 'zl_info'">
                          {{ item.type === 'goods_info' ? '件' : '次' }}
                        </span>
                        <span v-else>{{
                          item.type === 'PHYSICAL' || item.type === 'TREAT' ? '个' : row.unit_name || '次'
                        }}</span>
                      </template>
                      <template slot-scope="{ row }" slot="remark">
                        {{ row.remark || '-' }}
                      </template>
                    </Table>
                    <div class="remark" v-if="item.remark || item.medical_advice">
                      <p v-if="item.medical_advice">医嘱和注意事项：{{ item.medical_advice }}</p>
                      <p v-else>方案备注：{{ item.remark }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div id="nav-visit">
              <div class="panel-item visit-item" v-for="(item, idx) in visitList" :key="idx + 'visit'">
                <div class="nav-head sticky">
                  <h4>随访{{ idx + 1 }}</h4>
                </div>
                <Row>
                  <Col :span="12" class="visit-col">
                    <div class="panel-content-item">
                      <span class="p-label">随访人</span>
                      <span class="p-value">{{ item.up_info.operator }}</span>
                    </div>
                  </Col>
                  <Col :span="12" class="visit-col">
                    <div class="panel-content-item">
                      <span class="p-label">随访时间</span>
                      <span class="p-value">{{ item.up_info.time | data_format('YYYY-MM-DD HH:mm') }}</span>
                    </div>
                  </Col>
                  <Col :span="12">
                    <div class="panel-content-item">
                      <span class="p-label">随访类型</span>
                      <span class="p-value">{{ item.type_text }}</span>
                    </div>
                  </Col>
                  <Col :span="12">
                    <div class="panel-content-item">
                      <span class="p-label">随访方式</span>
                      <span class="p-value">{{ item.mode_text }}</span>
                    </div>
                  </Col>
                </Row>
                <div class="visit-content">
                  <div class="visit-info-h flex flex-item-align">
                    <h4 class="v-title">全身舒适度</h4>
                    <span :style="getTagStyle(item.mr_info.comfort)" class="v-tag">{{
                      item.mr_info.comfort_text
                    }}</span>
                  </div>
                  <div class="pres-table-item">
                    <Table :columns="feedbackCols" :data="item.mr_info.diag_result">
                      <template slot="take_medicine_day" slot-scope="{ row }"> {{ row.take_medicine_day }}天 </template>
                      <template slot="feedback_type_text" slot-scope="{ row }">
                        {{ row.feedback_type_text || '-' }}
                      </template>
                    </Table>
                  </div>
                  <div class="user-feedback">
                    <div class="panel-content-item">
                      <span class="p-label">复诊时间</span>
                      <span class="p-value">{{ item.mr_info.return_visit_date || '-' }}</span>
                    </div>
                    <div class="panel-content-item">
                      <span class="p-label">用户反馈</span>
                      <span class="p-value">{{ item.mr_info.feedback_desc || '-' }}</span>
                    </div>
                    <div class="panel-content-item">
                      <span class="p-label">随访记录</span>
                      <span class="p-value">{{ item.mr_info.visit_record || '-' }}</span>
                    </div>
                    <div class="panel-content-item">
                      <span class="p-label">随访情况</span>
                      <span class="p-value"
                        >{{ item.mr_info.visit_situation_text || '-'
                        }}{{
                          item.mr_info.visit_situation === 'NOT_REACHED'
                            ? `，${item.mr_info.next_plan_time}继续跟进`
                            : ''
                        }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
              <div class="no-data flex-col" v-show="!visitList.length">
                <div class="nav-head sticky">
                  <h4>随访</h4>
                </div>
                <div class="visit-empty">暂无随访记录</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="visit-info hidden-scroll">
        <div v-if="visitStatus === 'WAIT_FOLLOW'">
          <div class="nav-head">
            <h4>随访信息</h4>
          </div>
          <div class="visit-h">
            <Row>
              <Col :span="14">
                <div class="panel-content-item">
                  <span class="p-label">随访类型</span>
                  <span class="p-value">{{ visitInfo.type_text }}</span>
                </div>
              </Col>
              <Col :span="10">
                <div class="panel-content-item">
                  <span class="p-label">计划时间</span>
                  <span class="p-value">{{ visitInfo.plan_time | data_format('YYYY-MM-DD') }}</span>
                </div>
              </Col>
              <Col :span="14">
                <div class="panel-content-item flex flex-item-align" style="margin-top: 0">
                  <span class="p-label">随访方式</span>
                  <Select class="p-value" placeholder="请选择随访方式" v-model="formData.mode">
                    <Option v-for="item in visitTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                  </Select>
                </div>
              </Col>
            </Row>
            <div class="condition-item">
              <h4 class="v-title">全身舒适度</h4>
              <div class="comfort-box">
                <Row class="comfort-content">
                  <Col :span="6" v-for="item in comfortDesc" :key="item.id">
                    <div
                      class="comfort-option disable-select"
                      @click="changeComfort(item.id)"
                      :style="{
                        backgroundColor: formData.mr_info.comfort === item.id ? item.color : '#fff',
                        color: formData.mr_info.comfort === item.id ? '#fff' : '#333',
                      }"
                    >
                      {{ item.desc }}
                    </div>
                  </Col>
                </Row>
              </div>
              <div class="symptoms-content" v-if="diag_result && diag_result.length">
                <Row class="symptoms-item-h" type="flex" align="middle">
                  <Col :span="16"><h5>初始症状</h5></Col>
                  <Col :span="8"><h5>疗效反馈</h5></Col>
                </Row>
                <Row
                  class="symptoms-item"
                  type="flex"
                  align="middle"
                  v-for="(diag_item, index) in diag_result"
                  :key="'diag_item' + index"
                >
                  <Col :span="16"
                    ><span class="symptoms-text">{{ diag_item.name }}</span></Col
                  >
                  <Col :span="8">
                    <Select placeholder="请选择" v-model="diag_result[index].feedback_type">
                      <Option v-for="item in feedbackDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                    </Select>
                  </Col>
                </Row>
              </div>
              <div class="remarks-item">
                <Row type="flex" align="middle">
                  <div class="flex flex-item-align">
                    <h5 class="remarks-label">复诊时间</h5>
                    <Col :span="14">
                      <DatePicker
                        placeholder="请选择复诊时间"
                        :options="disabledTime"
                        @on-change="changeDate"
                      ></DatePicker>
                    </Col>
                  </div>
                </Row>
              </div>
              <div class="remarks-item">
                <Row type="flex">
                  <Col :span="24">
                    <div class="flex">
                      <h5 class="remarks-label">用户反馈</h5>
                      <Input
                        :autosize="{ minRows: 4, maxRows: 8 }"
                        type="textarea"
                        v-model="formData.mr_info.feedback_desc"
                        style="flex: 1"
                        placeholder="请输入用户诊后其他细节反馈，或针对门店体验等的建议事项"
                      />
                    </div>
                  </Col>
                </Row>
              </div>
              <div class="remarks-item">
                <Row type="flex">
                  <Col :span="24">
                    <div class="flex">
                      <h5 class="remarks-label">随访记录</h5>
                      <Input
                        style="flex: 1"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                        type="textarea"
                        v-model="formData.mr_info.visit_record"
                        placeholder="请输入其他补充记录信息"
                      ></Input>
                    </div>
                  </Col>
                </Row>
              </div>
              <div class="remarks-item">
                <Row type="flex" align="middle">
                  <Col :span="24">
                    <div class="flex">
                      <h5 class="remarks-label" style="line-height: 26px">随访情况</h5>
                      <RadioGroup :value="formData.mr_info.visit_situation">
                        <div class="flex flex-c">
                          <div @click.prevent="changeSituation('REACHED')" style="line-height: 24px">
                            <Radio label="REACHED">
                              <span class="mr-12">已达成随访目标</span>
                            </Radio>
                          </div>
                          <div class="flex flex-item-align" style="line-height: 24px">
                            <div @click.prevent="changeSituation('NOT_REACHED')">
                              <Radio label="NOT_REACHED">
                                <span>未达成随访目标</span>
                              </Radio>
                            </div>
                            <span v-show="formData.mr_info.visit_situation === 'NOT_REACHED'">
                              <InputNumber
                                v-model="formData.mr_info.next_plan_day"
                                :min="0"
                                size="small"
                                @click.prevent
                                style="width: 40px"
                              />
                              <span> 天后继续跟进</span>
                            </span>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="nav-head">
            <h4>随访记录</h4>
          </div>
          <div class="visit-h visit-detail">
            <Row>
              <Col :span="14">
                <div class="panel-content-item">
                  <span class="p-label">随访类型</span>
                  <span class="p-value">{{ echoData.type_text }}</span>
                </div>
              </Col>
              <Col :span="10">
                <div class="panel-content-item">
                  <span class="p-label">随访时间</span>
                  <span class="p-value">{{ echoData.time | data_format('YYYY-MM-DD HH:mm') }}</span>
                </div>
              </Col>
              <Col :span="14">
                <div class="panel-content-item flex flex-item-align" style="margin-top: 0">
                  <span class="p-label">随访方式</span>
                  <span class="p-value">{{ echoData.mode_text }}</span>
                </div>
              </Col>

              <Col :span="10">
                <div class="panel-content-item" style="margin-top: 0">
                  <span class="p-label">随访人</span>
                  <span class="p-value">{{ echoData.operator }}</span>
                </div>
              </Col>
            </Row>
            <div class="condition-item">
              <div class="visit-info-h">
                <h4 class="v-title">全身舒适度</h4>
                <span :style="getTagStyle(echoData.comfort)" class="v-tag">{{ echoData.comfort_text }}</span>
              </div>
              <div class="pres-table-item" v-if="echoData.diag_result && echoData.diag_result.length">
                <Table :columns="feedbackCols" :data="echoData.diag_result">
                  <template slot="take_medicine_day" slot-scope="{ row }">
                    {{ row.take_medicine_day ? row.take_medicine_day + '天' : '-' }}
                  </template>
                  <template slot="feedback_type_text" slot-scope="{ row }">
                    {{ row.feedback_type_text || '-' }}
                  </template>
                </Table>
              </div>
              <div class="remarks-item">
                <div class="panel-content-item">
                  <span class="p-label">复诊时间</span>
                  <span class="p-value">{{ echoData.return_visit_date || '-' }}</span>
                </div>
                <div class="panel-content-item">
                  <span class="p-label">用户反馈</span>
                  <span class="p-value">{{ echoData.feedback_desc || '-' }}</span>
                </div>
                <div class="panel-content-item">
                  <span class="p-label">随访记录</span>
                  <span class="p-value">{{ echoData.visit_record || '-' }}</span>
                </div>
                <div class="panel-content-item">
                  <span class="p-label">随访情况</span>
                  <span class="p-value"
                    >{{ echoData.visit_situation_text || '-'
                    }}{{
                      echoData.visit_situation === 'NOT_REACHED' ? `，${echoData.next_plan_time}继续跟进` : ''
                    }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="fixed-bottom-wrapper">
        <back-button class="mr10"></back-button>
        <Button type="primary" :loading="editLoading" @click="editVisitInfo" v-if="visitStatus === 'WAIT_FOLLOW'"
          >结束随访
        </Button>
      </div>
      <KPatientRecordModal
        :dialogVisible.sync="recordVisible"
        :pt_id="pt_id"
        :is_patient="patientInfo.is_patient"
      ></KPatientRecordModal>
    </div>
    <new-visit-detail v-if="!showOldDetail" />
  </div>
</template>

<script>
import { colsObj } from './common/columns';
import S from 'libs/util';
import { chunk,debounce } from 'lodash-es';
import { isRstClinic } from '@/libs/runtime';
import NewVisitDetail from '@/view/daily/visit/components/v2/newVisitDetail.vue';

export default {
  name: 'detail',
  mixins: [],

  components: {
    NewVisitDetail,
    KPatientRecordModal: () => import('_c/k-patientRecord-modal/k-patientRecord-modal'),
  },

  props: {},

  data() {
    return {
      isRstClinic,
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000;
        },
      },
      recordVisible: false,
      cas_token: '',
      uid: this.$route.query.uid,
      mr_id: this.$route.query.mr_id,
      pt_id: '',
      editLoading: false,
      colsObj,
      Comfort: '', //舒适度
      curIndex: 0,
      navList: [
        { title: '基础信息', id: 0, el_id: 'nav-basic' },
        { title: '病历信息', id: 1, el_id: 'nav-record' },
        { title: '处方', id: 2, el_id: 'nav-pres' },
        { title: '收费', id: 3, el_id: 'nav-charge' },
        { title: '随访', id: 4, num: 0, el_id: 'nav-visit' },
      ],
      chargeCols: [
        { title: '项目名称', slot: 'name', align: 'center' },
        { title: '金额', slot: 'money', align: 'center' },
      ],
      ointmentCols: [
        { title: '收费项目', key: 'name', align: 'center', minWidth: 120 },
        { title: '单价', slot: 'price', align: 'center' },
        { title: '数量', key: 'quantity', align: 'center' },
        { title: '单位', slot: 'cus_unit', align: 'center' },
        { title: '总价', slot: 'payment_fee', align: 'center' },
        // { title: '备注', slot: 'remark', align: 'center' },
      ],
      // 西药收费
      medicineBillCols: [
        { title: '收费项目', key: 'name', align: 'center', minWidth: 120 },
        { title: '类型', key: 'prod_type_text', align: 'center' },
        { title: '处方药', key: 'is_otc', align: 'center' },
        { title: '规格', key: 'prod_spec', align: 'center' },
        { title: '厂商', slot: 'manufacturer', align: 'center' },
        { title: '单价', slot: 'price', align: 'center' },
        { title: '数量', key: 'quantity', align: 'center' },
        { title: '单位', slot: 'cus_unit', align: 'center' },
        { title: '总价', slot: 'payment_fee', align: 'center' },
        // { title: '备注', slot: 'remark', align: 'center' },
      ],
      physicalCols: [
        { title: '收费项目', key: 'name', align: 'center', minWidth: 120 },
        { title: '规格', key: 'spec', align: 'center' },
        { title: '单价(元)', key: 'price', align: 'center' },
        { title: '数量', key: 'quantity', align: 'center' },
        { title: '单位', slot: 'cus_unit', align: 'center' },
        { title: '总价(元)', key: 'payment_fee', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
      ],
      feedbackCols: [
        { title: '初始症状', key: 'name', align: 'center' },
        { title: '疗效反馈', slot: 'feedback_type_text', align: 'center' },
        { title: '疗效时间(服药后时间/天)', slot: 'take_medicine_day', align: 'center' },
      ],
      list: [{}, {}, {}, {}],
      patientInfo: {}, //患者信息
      basicDetail: [], // 基础信息(新)
      basicInfo: {}, //基础信息
      visitInfo: {}, //随访信息
      medical_details: [], //病历信息
      symptom_details: [], //病历信息
      medical_history: [], //病历信息
      body_index: {
        height: '',
        weight: '',
        temperature: '',
        heart_rate: '',
        systolic_pressure: '',
        diastolic_pressure: '',
      },
      record_image_details: [], //病历信息
      presList: [], //处方信息
      presChargeList: [], //处方收费列表
      chargesList: [], //收费列表
      payInfo: {
        payment_fee: '',
        total: '',
      }, //收费总计
      payList: [], //收费列表
      userId: this.$route.query.id,
      visitStatus: '', //随访状态
      diag_result: [], //诊断结果
      visitList: [], //随访列表
      visitTypeDesc: [], //随访方式
      situationDesc: [], //随访完成情况
      feedbackDesc: [], //症状反馈
      comfortDesc: [], //病情反馈
      formData: {
        mr_info: {
          comfort: '', //舒适度
          diag_result: [], //症状 二位数组json
          return_visit_date: '', //复诊时间
          feedback_desc: '', //用户反馈描述
          visit_record: '', //随访记录
          visit_situation: '', //随访情况
          next_plan_day: 0, //随访未达成 多少天跟进
        },
        mode: '', //随访方式
      },
      echoData: {
        mode_text: '',
        type_text: '', //随访方式
        status_text: '', //
        return_visit_date: '', //复诊时间
        comfort_text: '', //舒适度
        comfort: '', //舒适度
        visit_record: '', //随访记录
        feedback_desc: '', //用户反馈
        visit_situation_text: '', //随访情况
        operator: '', //随访人
        time: '', //随访时间
        diag_result: [],
        next_plan_time: '', //下次随访时间
      },
      canScroll: true,
      detailInfo: {},
    };
  },

  computed: {
    showOldDetail() {
      return !isRstClinic() || this.$route.query.is_old;
    },
    showBodyIndex() {
      const values = Object.values(this.body_index);
      console.log('-> %c values  === %o ', 'font-size: 24px;color:#67C23A ', values);
      const hasBodyIndex = values.some(item => !!item);
      return this.medical_history.length || hasBodyIndex;
    },
    getTagStyle(comfort) {
      return comfort => {
        switch (comfort) {
          case 'VERY_GOOD':
            return {
              background: '#6DB62B',
              color: '#FFFFFF',
            };
          case 'UNWELL':
            return {
              background: '#ADADAD',
              color: '#FFFFFF',
            };
          case 'GOOD':
            return {
              background: '#A6C338',
              color: '#FFFFFF',
            };
          case 'COMMONLY':
            return {
              background: '#D7B12E',
              color: '#FFFFFF',
            };
        }
      };
    },
  },

  watch: {},

  created() {
    if (!this.showOldDetail) return;
    this.getVisitOptions();
  },

  mounted() {
    if (!this.showOldDetail) return;
    this.$refs['info-content'].addEventListener('scroll', this.scrollHandle);
  },

  beforeDestroy() {
    if (!this.showOldDetail) return;
    //离开该页面需要移除这个监听的事件
    this.$refs['info-content'].removeEventListener('scroll', this.scrollHandle);
  },

  methods: {
    scrollHandle: debounce(
      function () {
        console.log('dasdsa');
        if (!this.canScroll) {
          this.canScroll = true;
          return;
        }
        this.navList.map((item, index) => {
          let top = document.querySelector('#' + item.el_id).getBoundingClientRect().top;
          if (top < 292) {
            this.curIndex = index;
          }
        });
      },
      100,
      {}
    ),
    changeSituation(type) {
      console.log('-> %c type  === %o ', 'font-size: 15px', type);
      if (type === this.formData.mr_info.visit_situation) {
        this.formData.mr_info.visit_situation = '';
      } else {
        this.formData.mr_info.visit_situation = type;
      }
    },
    changeComfort(id) {
      console.log('-> %c id  === %o ', 'font-size: 15px', id);
      if (id === this.formData.mr_info.comfort) {
        this.formData.mr_info.comfort = '';
      } else {
        this.formData.mr_info.comfort = id;
      }
    },
    changeNav(item, index) {
      this.curIndex = index;
      let offsetTop = document.querySelector('#' + item.el_id).offsetTop;
      let scrollTop = offsetTop - 292;
      this.canScroll = false;
      this.$refs['info-content'].scrollTo({
        top: scrollTop,
      });
    },
    editVisitInfo() {
      if (!this.formData.mode) {
        this.$Message.error('请选择随访方式');
        return;
      }
      if (!this.formData.mr_info.visit_situation) {
        this.$Message.error('请选择随访情况');
        return;
      }
      if (this.formData.mr_info.visit_situation === 'NOT_REACHED' && !this.formData.mr_info.next_plan_day) {
        this.$Message.error('未达成随访目标时，【多少天后随访】不能为空');
        return;
      }
      this.formData.mr_info.diag_result = this.diag_result;
      const params = {
        ...this.formData,
        id: this.userId,
        cas_token: this.cas_token,
      };
      this.editLoading = true;
      this.$api
        .editVisit(params)
        .then(
          res => {
            console.log('-> %c res  === %o ', 'font-size: 15px', res);
            this.$router.push('/daily/visit/list');
          },
          err => {}
        )
        .finally(() => {
          this.editLoading = false;
        });
    },
    changeDate(date) {
      this.formData.mr_info.return_visit_date = date;
    },
    getVisitOptions() {
      this.$api.getVisitOptions().then(
        res => {
          this.visitTypeDesc = S.descToArrHandle(res.modeDesc);
          this.situationDesc = S.descToArrHandle(res.situationDesc);
          this.feedbackDesc = S.descToArrHandle(res.feedbackDesc);
          this.comfortDesc = S.descToArrHandle(res.comfortDesc);
          const styleArr = [
            { color: '#6DB62B', classname: 'well' },
            {
              color: '#A6C338',
              classname: 'good',
            },
            { color: '#D7B12E', classname: 'normal' },
            { color: '#ADADAD', classname: 'normal' },
          ];
          this.comfortDesc.map((item, index) => {
            item.color = styleArr[index].color;
            // item.classname = styleArr[index].classname
          });
          console.log('-> %c this.comfortDesc  === %o ', 'font-size: 15px', this.comfortDesc);

          this.getVisitInfo();
        },
        err => this.$Mesage.error(err.errmsg)
      );
    },
    getVisitInfo() {
      this.$api.getFUDetail({ id: this.userId }).then(
        res => {
          console.log('-> %c res  === %o ', 'font-size: 15px', res);
          const mr_info = res.mr_info;
          this.visitStatus = res.status;
          this.diag_result = res.mr_info.diag_result.map(item => ({
            ...item,
            feedback_type: item.feedback_type || '',
          }));
          this.visitInfo = {
            type_text: res.type_text,
            plan_time: res.plan_time,
            visit_mode: res.mode,
          };
          this.cas_token = res.cas_token;
          this.echoData.diag_result = mr_info.diag_result;
          this.echoData.comfort_text = mr_info.comfort_text;
          this.echoData.comfort = mr_info.comfort;
          this.echoData.visit_record = mr_info.visit_record;
          this.echoData.feedback_desc = mr_info.feedback_desc;
          this.echoData.visit_situation_text = mr_info.visit_situation_text;
          this.echoData.visit_situation = mr_info.visit_situation;
          this.echoData.mode_text = res.mode_text;
          this.echoData.time = res.up_info.time;
          this.echoData.operator = res.up_info.operator;
          this.echoData.return_visit_date = mr_info.return_visit_date;
          this.echoData.type_text = res.type_text;
          this.echoData.next_plan_time = res.mr_info.next_plan_time;
          this.uid = res.uid;
          this.pt_id = res.pt_id;
          this.getMedicalInfo(res.mr_info.mr_id);
        },
        err => {}
      );
    },
    getMedicalInfo(mr_id) {
      this.$api.getVisitMedicalDetail({ mr_id }).then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px', res);
        const info = res.info;
        this.detailInfo = res.info;
        this.medical_details = info.medical_details;
        this.symptom_details = info.symptom_details;
        this.medical_history = info.medical_history;
        this.body_index = info.body_index;
        this.record_image_details = info.record_image_details;
        this.patientInfo = {
          age: info.patient.age,
          mobile: info.patient.mobile,
          name: info.patient.name,
          sex_text: info.patient.sex_text,
          type_text: info.type_text,
          sex: info.patient.sex,
          from_text: info.patient.from_text,
          addr_detail: info.patient.addr_detail,
          avatar: info.patient.avatar,
          is_patient: info.patient.is_patient,
          user_name: info.patient.user_name,
          user_role: info.patient.user_role,
          user_role_text: info.patient.user_role_text,
        };
        this.basicDetail = info.basic_details;
        console.log('=>(detail.vue:887) this.basicDetail', this.basicDetail);
        this.basicInfo = {
          doctor_name: info.doctor_name,
          create_time: info.create_time,
          type_text: info.type_text,
        };
        this.presList = info.pres_list;
        this.payList = info.pay_order.pres_list;
        this.presChargeList = info.pay_order.pres_list;
        this.chargesList = this.handlerChargeList(info.pay_order);
        console.log('-> %c this.chargesList  === %o ', 'font-size: 15px', this.chargesList);
        this.visitList = info.visit_list;
        this.navList[4].num = info.visit_list.length;
      });
    },
    handlerChargeList(order) {
      let payList = [];
      payList = payList.concat([
        { name: '挂号费', money: order.reg_info.payment_fee },
        {
          name: '问诊费',
          money: order.cons_info.payment_fee,
        },
      ]);
      order.pres_list.map(item => {
        console.log('-> %c item  === %o ', 'font-size: 15px', item);
        payList.push({
          name: item.type_text == '加工费' ? item.type_text : `${item.type_text}费`,
          money: item.payment_fee,
        });
      });
      this.payInfo = {
        payment_fee: order.order.payment_fee,
        total: payList.length,
      };
      return chunk(payList, 4);
    },
  },
};
</script>

<style scoped lang="less">
.visit-detail-wrapper {
  background: #ffffff;
  overflow: hidden;

  .customer-info {
    flex: 5;
    border-right: 1px #dfdfdf solid;
    padding-right: 12px;
    overflow-y: scroll;

    .basic-info {
      background: #fafafa;
      margin-bottom: 20px;

      .file-btn {
        margin-right: 30px;

        .btn-item {
          padding: 3px 12px;
          margin-left: 18px;
          align-items: center;
          border: 1px solid #c7c6c6;
          border-radius: 4px;
          background: #f5f6f6;
          cursor: pointer;
          color: #8a8f9c;
          min-width: 50px;

          .txt {
            line-height: 17px;
            padding-top: 2px;
          }
        }
      }

      .user-info-base {
        position: relative;
        box-sizing: border-box;
        // height: 160px;
        background: #fafafa;
        border-radius: 4px;
        padding: 12px;

        .info-left {
          margin-right: 10px;
          width: 56px;

          img {
            width: 56px;
            height: 56px;
            border-radius: 50%;
          }
        }

        .info-right {
          .left-key {
            text-align: right;
            width: 56px;
            min-width: fit-content;
            font-size: 14px;
            font-weight: 300;
            color: #999;
            line-height: 20px;
          }

          .left-value {
            color: #000000;
            font-size: 14px;
            font-weight: 300;
            line-height: 20px;
          }

          .center {
            margin: 6px 0;
          }

          div > p:nth-last-child(1) {
            margin-left: 10px;
          }
        }

        .user-name {
          margin-bottom: 10px;

          p {
            font-size: 14px;
            color: #000000;
          }

          .left-key {
            color: #000000;
            text-align: right;
            font-size: 18px;
            font-weight: bold;
          }
        }
      }
    }

    .info-content {
      overflow: auto;
      height: 100%;
      box-sizing: content-box;

      .panel-box {
        height: calc(100vh - 120px);
        padding-right: 12px;

        .prescription-item {
          .pres-content-item {
            &:first-of-type {
              margin-top: 20px;
            }
            .pres-head {
              padding-left: 12px;
              margin-right: 12px;
              display: flex;
              justify-content: space-between;
              height: 37px;
              align-items: center;
              border-bottom: 1px solid #f2f2f2;

              .f-title {
                font-weight: 500;
                color: #000000;
                line-height: 17px;
              }

              .number {
                font-weight: 300;
                color: #000000;
                line-height: 17px;
              }
            }
          }

          .pres-table-item {
            margin: 16px 12px;

            .usage {
              margin-left: 20px;
            }
          }

          .herbal-item {
            padding-left: 20px;
            margin-top: 10px;

            .ivu-col {
              margin-bottom: 10px;

              .pres-item-text {
                font-weight: 400;
                color: #000000;
                line-height: 17px;
              }
            }
          }

          .usage {
            margin: 6px 0 20px;
            font-weight: 300;
            color: #000000;
            line-height: 17px;
          }
        }

        #nav-visit {
          padding-bottom: 200px;
        }

        .visit-item {
          .visit-col {
            .panel-content-item {
              margin-bottom: 0;
            }
          }

          .visit-content {
            padding-left: 12px;

            .visit-info-h {
              padding: 10px 0 6px;
              border-top: 1px solid #ececec;

              .v-title {
                font-weight: bold;
                color: #000000;
                line-height: 17px;
                margin-right: 8px;
              }

              .v-tag {
                font-size: 11px;
                font-weight: 400;
                padding: 1px 6px;
                border-radius: 9px;
                -webkit-text-size-adjust: none !important;
                display: flex;
                align-items: center;
                justify-content: center;
                transform: scale(0.88);
              }
            }

            .user-feedback {
              .panel-content-item {
                margin-left: 6px;
              }
            }
          }
        }

        .no-data {
          padding-bottom: 200px;

          .visit-empty {
            text-align: center;
            color: #cccccc;
            line-height: 50px;
          }
        }
      }
    }
  }

  .visit-info {
    flex: 3;
    margin-left: 12px;
    height: 100%;
    overflow-y: scroll;

    .visit-h {
      .panel-content-item {
        margin-left: 12px;
      }

      .condition-item {
        margin: 10px 12px;
        padding-bottom: 40px;

        .comfort-box {
          .comfort-option {
            display: flex;
            justify-content: center;
            align-items: center;
            line-height: 32px;
            margin-right: 20px;
            cursor: pointer;
            border-radius: 4px;
            border: 1px solid #cccccc;
          }

          .option-active {
            color: #fff;
            background: #6db62b;
          }
        }

        .visit-info-h {
          padding: 10px 0 6px;
          display: flex;
          align-items: center;

          .v-title {
            font-weight: bold;
            color: #000000;
            line-height: 17px;
            margin-right: 8px;
            margin-bottom: 0;
          }

          .v-tag {
            font-size: 11px;
            font-weight: 400;
            padding: 1px 6px;
            border-radius: 9px;
            -webkit-text-size-adjust: none !important;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: scale(0.88);
          }
        }

        .v-title {
          font-weight: 500;
          color: #000000;
          line-height: 17px;
          margin-bottom: 11px;
        }

        .symptoms-content {
          margin-top: 25px;

          .symptoms-item-h {
            h5 {
              font-weight: bold;
              color: #000000;
              line-height: 12px;
            }
          }

          .symptoms-item {
            padding: 10px 0;
            border-bottom: 1px solid #ececec;
          }
        }

        .remarks-item {
          margin-top: 20px;

          .remarks-label {
            //width: 5px;
            margin-right: 10px;
          }

          ::v-deep .ivu-radio-wrapper {
            margin-right: 0;
          }

          ::v-deep .ivu-input-wrapper,
          .ivu-select {
            max-width: 100% !important;
          }
        }
      }
    }

    .visit-detail {
      .remarks-item {
        margin-top: 10px !important;

        .panel-content-item {
          margin-left: 0;
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
p {
  margin: 0;
  padding: 0;
}

.panel-nav {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 20;

  .active {
    border-bottom: 1px solid #dddddd;
    background: #f2f2f2;
  }
}

.nav-head {
  width: 100%;
  height: 40px;
  background: #f2f2f2;
  line-height: 40px;
  display: flex;
  align-items: center;

  h4 {
    font-size: 12px;
    font-weight: bold;
    color: #000000;
    line-height: 17px;
    margin-left: 12px;
  }
}

.panel-content-item {
  margin: 10px 18px;
  display: flex;

  .p-label {
    text-align: right;
    font-weight: 300;
    color: #888888;
    line-height: 17px;
    min-width: 60px;
  }

  .p-value {
    text-align: left;
    font-weight: 400;
    color: #000000;
    line-height: 17px;
    margin-left: 10px;
  }
}

.panel-item {
  .img-wrapper {
    .custom-img,
    img {
      width: 68px;
      height: 68px;
      border-radius: 5px;
      margin-right: 10px;
      margin-bottom: 12px;
    }
  }

  .cost-content {
    padding-left: 12px;
    padding-right: 12px;
    margin-bottom: 9px;

    .charge-item {
      margin-right: 20px;
    }

    .charge-item:last-of-type {
      margin-right: 0px !important;
    }

    .f-title {
      font-weight: 500;
      color: #000000;
      line-height: 17px;
      margin: 10px 0 6px;
    }
  }

  .symptom-title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin: 12px;
  }

  .basic-title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin: 12px 12px 0;
  }
}

::v-deep .ivu-table {
  td {
    height: 28px;
    line-height: 28px;
  }

  thead {
    .ivu-table-column-center {
      padding: 2px 0;
      font-size: 12px;
    }
  }

  th {
    background: #f9f9f9;
  }
}

.mr-30 {
  margin-right: 30px;
}

.sticky {
  position: sticky;
  top: 40px;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 10;
}

.table-sum {
  text-align: right;
  font-weight: 400;
  color: #000000;
  line-height: 12px;
  margin: 7px 12px 18px 0;

  .total {
    margin-right: 16px;
  }
}
.remark {
  margin-top: 20px;
  background: #fafbff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #666b6f;
  padding: 10px 20px;
  margin-bottom: 10px;
  word-berak: break-all;
}
.p-label-color {
  color: #333 !important;
}
</style>
