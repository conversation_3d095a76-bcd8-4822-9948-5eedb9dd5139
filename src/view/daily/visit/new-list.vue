<template>
  <!-- 随访日历看板主容器 -->
  <div
    class="wrapper new-follow-up"
    :style="{ height: 'fit-content', maxHeight: $store.state.app.clientHeight - 92 + 'px', overflowY: 'auto' }"
  >
    <!-- 左侧日历区域 -->
    <div class="left-wrapper" :style="{ minHeight: $store.state.app.clientHeight - 92 + 'px', height: 'fit-content' }">
      <!-- 顶部操作栏：包含视角选择和创建随访按钮 -->
      <div class="top-actions">
        <div class="left-actions">
          <!-- 视角选择下拉框：支持诊所、员工、患者三种视角 -->
          <Select
            v-model="formData.view"
            placeholder="请选择视角"
            style="width: 150px; margin-right: 10px"
            @on-change="onViewTypeChange"
          >
            <Option :value="item.id" v-for="item in options.view_desc" :key="item.id">
              {{ item.desc }}
            </Option>
          </Select>
          <!-- 目标对象选择：当选择员工或患者视角时显示，支持搜索 -->
          <Select
            v-if="formData.view === 'OPERATION' || formData.view === 'PT'"
            v-model="formData.view_id"
            style="width: 150px"
            filterable
            clearable
            :remote-method="remoteMethod"
            :loading="searchLoading"
            :placeholder="(formData.view === 'OPERATION' && '请选择员工') || (formData.view === 'PT' && '请选择患者')"
            @on-change="onTargetChange"
            @on-clear="remoteMethod('')"
          >
            <Option v-for="option in targetList" :value="option.id" :key="option.id">{{ option.name }}</Option>
          </Select>
        </div>
        <div class="right-actions">
          <!-- 创建随访按钮 -->
          <Button type="primary" @click="handleAdd">创建随访</Button>
        </div>
      </div>

      <!-- 日历容器：包含月份切换、星期标题和日期网格 -->
      <div class="calendar-container" :style="{ minHeight: 'calc(100vh - 204px)', height: 'fit-content'}">
        <!-- 月份切换头部：左右箭头和当前月份显示 -->
        <div class="calendar-header">
          <!-- 上一月按钮 -->
          <div class="nav-btn" @click="previousMonth">
            <svg-icon :size="22" iconClass="follow-l"></svg-icon>
          </div>
          <!-- 当前年月显示 -->
          <h3 class="current-month">{{ currentYear }}年{{ currentMonth }}月</h3>
          <!-- 下一月按钮 -->
          <div class="nav-btn" @click="nextMonth">
            <svg-icon :size="22" iconClass="follow-r"></svg-icon>
          </div>
        </div>

        <!-- 星期标题行：周一到周日 -->
        <div class="weekdays">
          <div class="weekday" v-for="day in weekdays" :key="day">{{ day }}</div>
        </div>

        <!-- 日期网格：动态行数，支持隐藏最后一行全是下月日期的情况 -->
        <div class="calendar-grid" :style="{ gridTemplateRows: `repeat(${calendarGridRows}, 1fr)` }">
          <!-- 单个日期格子 -->
          <div
            class="calendar-day"
            v-for="date in filteredCalendarDates"
            :key="date.key"
            :class="{
              'other-month': !date.isCurrentMonth,
              today: date.isToday,
              selected: selectedDateKey(date),
            }"
            @click.stop="selectDate(date)"
          >
            <!-- 日期头部：显示日期数字 -->
            <div class="calendar-day-header">
              <!-- 每月1号显示月日格式，其他日期只显示日 -->
              <div v-if="date.day === 1 && date.dateKey" class="day-text">
                {{ moment(date.dateKey).format('M月D日') }}
              </div>
              <div v-else class="day-text">{{ date.day }}日</div>
            </div>
            <!-- 随访状态内容：仅当前月份的日期显示随访信息 -->
            <div v-if="date.info && date.isCurrentMonth" class="calendar-day-content">
              <!-- 待随访数量：橙色背景突出显示 -->
              <div class="wait-follow-up-count" v-if="+date.info.WAIT_FOLLOW > 0">
                <div>待随访</div>
                <div>{{ date.info.WAIT_FOLLOW }}</div>
              </div>
              <!-- 占位元素：保持布局一致性 -->
              <div v-else style="width: 100%; height: 24px"></div>
              <!-- 已随访数量：绿色图标 -->
              <div v-if="+date.info.FOLLOW_UP > 0">
                <img src="@/assets/image/follow-up/done.png" alt="已随访" />
                <span class="count">{{ date.info.FOLLOW_UP }}</span>
              </div>
              <!-- 超时未随访数量：红色图标 -->
              <div v-if="+date.info.TIMEOUT > 0">
                <img src="@/assets/image/follow-up/undone.png" alt="超时未随访" />
                <span class="count">{{ date.info.TIMEOUT }}</span>
              </div>
              <!-- 已取消随访数量：灰色图标 -->
              <div v-if="+date.info.CANCEL > 0">
                <img src="@/assets/image/follow-up/canceled.png" alt="已取消" />
                <span class="count">{{ date.info.CANCEL }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部图例说明：解释各种状态图标的含义 -->
      <div class="tip-panel">
        <div>
          <img src="@/assets/image/follow-up/done.png" alt="已随访" />
          <span>已随访</span>
        </div>
        <div>
          <img src="@/assets/image/follow-up/undone.png" alt="未随访" />
          <span>未随访</span>
        </div>
        <div>
          <img src="@/assets/image/follow-up/canceled.png" alt="已取消" />
          <span>已取消</span>
        </div>
      </div>
    </div>
    <!-- 右侧详情区域：显示选中日期的随访详细列表 -->
    <div class="right-wrapper" :style="{ height: $store.state.app.clientHeight - 92 + 'px'}">
      <follow-up-list-detail
        :detail="detail"
        :selected-day="moment(selectedDate).format('YYYY-MM-DD')"
        :form-data="formData"
        :loading="detailLoading"
      />
    </div>
    <!-- 新增随访弹窗组件 -->
    <new-add-visit :visible.sync="addVisitModalVisible" :options="options" @success="loadFollowUpData" />
  </div>
</template>

<script>
// 导入依赖
import moment from 'moment'; // 日期处理库
import FollowUpListDetail from '@/view/daily/visit/components/v2/list-detail.vue'; // 随访详情列表组件
import NewAddVisit from '@/view/daily/visit/components/v2/add-visit.vue'; // 新增随访弹窗组件
import S from '@/libs/util'; // 工具函数库
import { debounce, isEmpty  } from 'lodash-es'; // lodash工具函数

// 初始查询表单数据结构
const init_query_form_data = {
  view: '', // 视角类型
  view_id: '', // 视角对应的ID
  date: '', // 日期
};

export default {
  name: 'newVisitList',
  components: { NewAddVisit, FollowUpListDetail },
  data() {
    return {
      moment, // moment实例，用于模板中的日期格式化
      selectedDate: new Date(), // 当前选中的日期
      // 表单数据：包含视角类型、视角ID和当前日期
      formData: {
        view: 'CLINIC', // 默认诊所视角
        view_id: '', // 视角对应的具体ID（员工ID或患者ID）
        date: new Date(), // 当前显示的月份日期
      },
      viewType: '1', // 视图类型（暂未使用）
      weekdays: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'], // 星期标题
      calendarDates: [], // 日历日期数据数组
      targetList: [], // 目标对象列表（员工或患者）
      searchLoading: false, // 搜索加载状态
      queryFormData: { ...init_query_form_data }, // 查询表单数据副本
      addVisitModalVisible: false, // 新增随访弹窗显示状态
      // 选项数据：存储各种下拉选项的配置
      options: {
        type_desc: [], // 随访类型选项
        sub_type_desc: [], // 随访子类型选项
        visit_method_desc: [], // 随访方式选项
        normal_method_desc: [], // 常规方式选项
        comfort_desc: [], // 安慰选项
        visit_operation_desc: [], // 随访操作选项
        normal_operation_desc: [], // 常规操作选项
        status_desc: [], // 状态选项
        effect_desc: [], // 效果选项
        engagement_desc: [], // 参与度选项
        result_desc: [], // 结果选项
        view_desc: [], // 视角选项
        sms_template_desc: [], // 短信模板选项
      },
      loading: false, // 日历数据加载状态
      detailLoading: false, // 详情数据加载状态
      detail: {}, // 选中日期的详情数据
    };
  },
  computed: {
    /**
     * 当前显示年份
     * @returns {number} 年份数字
     */
    currentYear() {
      return this.formData.date.getFullYear();
    },
    /**
     * 当前显示月份
     * @returns {number} 月份数字（1-12）
     */
    currentMonth() {
      return this.formData.date.getMonth() + 1;
    },
    /**
     * 选中日期的字符串键值
     * @returns {string|null} 格式为 'YYYY-M-D' 的日期字符串
     */
    selectedDateKey() {
      return date => {
        if (!this.selectedDate) return null;
        const year = this.selectedDate.getFullYear();
        const month = this.selectedDate.getMonth() + 1;
        const day = this.selectedDate.getDate();
        return date.dateKey === `${year}-${month}-${day}`;
      };
    },
    /**
     * 过滤后的日历日期数组
     * 功能：当最后一行全是下个月日期时，自动隐藏该行
     * @returns {Array} 过滤后的日期数组
     */
    filteredCalendarDates() {
      if (!this.calendarDates || this.calendarDates.length === 0) return [];

      // 检查最后一行（第6行）是否全是下个月的日期
      const lastRowStart = 35; // 第6行开始的索引 (5*7)
      const lastRowDates = this.calendarDates.slice(lastRowStart, 42);

      // 如果最后一行全是非当前月份的日期，则隐藏最后一行
      const allLastRowAreNextMonth = lastRowDates.every(date => !date.isCurrentMonth);

      if (allLastRowAreNextMonth) {
        return this.calendarDates.slice(0, lastRowStart);
      }

      return this.calendarDates;
    },
    /**
     * 日历网格行数
     * 根据过滤后的日期数量动态计算需要显示的行数
     * @returns {number} 网格行数
     */
    calendarGridRows() {
      return Math.ceil(this.filteredCalendarDates.length / 7);
    },
  },
  /**
   * 组件挂载后的初始化操作
   */
  mounted() {
    this.getOptions(); // 获取下拉选项数据
    this.getFollowUpDetailByDay(); // 获取当前日期的随访详情
  },
  methods: {
    /**
     * 获取随访相关的选项数据
     * 包括随访类型、状态、方式等下拉选项
     */
    getOptions() {
      this.$api.getVisitOptionsV2().then(res => {
        const keys = Object.keys(res || {});
        for (let i = 0; i < keys.length; i++) {
          const list = S.descToArrHandle(res?.[keys[i]] || {});
          this.$set(this.options, keys[i], list);
        }
        // console.log(this.options.view_desc, res.view_desc,  'this.options.view_desc')
        this.options.view_desc = this.options.view_desc.sort((a, b) => a.sort - b.sort);
        console.log(this.options.view_desc, 'this.options.view_desc')
        this.generateCalendarDates(); // 获取选项后生成日历
      });
    },
    /**
     * 生成日历日期数据
     * 创建6行7列的日历网格，包含上月末尾、当月全部、下月开头的日期
     * @param {boolean} flag - 是否跳过加载随访数据
     */
    generateCalendarDates(flag) {
      const year = this.currentYear;
      const month = this.currentMonth;
      const firstDay = new Date(year, month - 1, 1); // 当月第一天
      const lastDay = new Date(year, month, 0); // 当月最后一天
      const daysInMonth = lastDay.getDate(); // 当月天数

      // 获取第一天是星期几（0=周日，1=周一...）
      let firstDayOfWeek = firstDay.getDay();
      // 转换为周一开始（0=周一，1=周二...）
      firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

      const dates = [];

      // 添加上个月的日期（填充当月第一周的空白）
      // 正确获取上个月的年份和月份
      const prevYear = month === 1 ? year - 1 : year;
      const prevMonth = month === 1 ? 12 : month - 1;
      const prevMonthLastDay = new Date(prevYear, prevMonth, 0);
      const prevMonthDays = prevMonthLastDay.getDate();

      for (let i = firstDayOfWeek - 1; i >= 0; i--) {
        dates.push({
          day: prevMonthDays - i,
          isCurrentMonth: false, // 标记为非当前月
          isToday: false,
          key: `prev-${prevMonthDays - i}`,
        });
      }

      // 添加当前月的日期
      const today = new Date();
      for (let day = 1; day <= daysInMonth; day++) {
        const currentDateObj = new Date(year, month - 1, day);
        const isToday = today.getFullYear() === year && today.getMonth() === month - 1 && today.getDate() === day;
        const dateKey = `${year}-${month}-${day}`;

        dates.push({
          day,
          isCurrentMonth: true, // 标记为当前月
          isToday,
          key: `current-${day}`,
          date: currentDateObj,
          dateKey,
        });
      }

      // 添加下个月的日期，补齐6行
      const totalCells = 42; // 6行 × 7列
      const remainingCells = totalCells - dates.length;
      for (let day = 1; day <= remainingCells; day++) {
        dates.push({
          day,
          isCurrentMonth: false, // 标记为非当前月
          isToday: false,
          key: `next-${day}`,
        });
      }

      this.calendarDates = dates;

      // 如果传入flag参数，清空详情数据并跳过加载随访数据
      if (flag) {
        this.detail = {};
        return;
      }
      this.loadFollowUpData(); // 加载随访统计数据
    },
    /**
     * 切换到上一个月
     */
    previousMonth() {
      this.formData.date = new Date(this.formData.date.getFullYear(), this.formData.date.getMonth() - 1, 1);
      this.generateCalendarDates();
    },
    /**
     * 切换到下一个月
     */
    nextMonth() {
      this.formData.date = new Date(this.formData.date.getFullYear(), this.formData.date.getMonth() + 1, 1);
      this.generateCalendarDates();
    },
    /**
     * 视角类型变更处理
     * @param {string} val - 新的视角类型（CLINIC/PT/OPERATION）
     */
    onViewTypeChange(val) {
      this.formData.view_id = ''; // 清空视角ID
      if (val === 'CLINIC') {
        // 诊所视角：直接加载随访数据
        this.loadFollowUpData();
      }
      if (val === 'PT') {
        // 患者视角：清空日历数据并获取患者列表
        this.generateCalendarDates(true);
        this.getPatientList();
      }
      if (val === 'OPERATION') {
        // 员工视角：清空日历数据并获取员工列表
        this.generateCalendarDates(true);
        this.getStaffList();
      }
    },
    /**
     * 远程搜索方法（防抖处理）
     * 根据当前视角类型搜索员工或患者
     * @param {string} key - 搜索关键词
     */
    remoteMethod: debounce(function (key) {
      if (this.formData.view === 'OPERATION') {
        this.getStaffList(key); // 员工视角：搜索员工
      }
      if (this.formData.view === 'PT') {
        this.getPatientList(key); // 患者视角：搜索患者
      }
    }, 300),
    /**
     * 目标对象变更处理
     * @param {string} val - 选中的目标对象ID
     */
    onTargetChange(val) {
      if (!val) {
        this.remoteMethod(''); // 清空选择时重新搜索
        return;
      }
      this.loadFollowUpData(); // 重新加载随访统计数据
      this.getFollowUpDetailByDay(this.selectedDate); // 重新加载详情数据
    },
    /**
     * 获取员工列表
     * @param {string} key - 搜索关键词
     */
    getStaffList(key = '') {
      this.$api
        .getVisitSearchStaffList({
          keyword: key,
        })
        .then(res => {
          this.targetList = res?.list || [];
        });
    },
    /**
     * 获取患者列表
     * @param {string} key - 搜索关键词
     */
    getPatientList(key = '') {
      this.$api
        .getVisitSearchPatientList({
          keyword: key,
        })
        .then(res => {
          this.targetList = res?.list || [];
        });
    },
    /**
     * 加载随访统计数据
     * 获取当前月份的随访统计信息并更新日历显示
     */
    loadFollowUpData() {
      this.loading = true;
      const searchDate = moment(this.formData.date).format('YYYY-MM-DD');
      this.$api
        .getVisitCountListV2({
          ...this.formData,
          month: moment(searchDate).format('YYYY-MM'),
        })
        .then(res => {
          // 创建新的日期对象数组，避免修改已冻结的对象
          this.calendarDates = this.calendarDates.map(date => {
            // 查找对应日期的随访统计数据
            const targetDate = res?.find(item => moment(item.date + ' 00:00:00').isSame(date.date));
            if (!isEmpty(targetDate)) {
              return Object.freeze({
                ...date,
                info: {
                  FOLLOW_UP: +targetDate?.FOLLOW_UP || 0, // 已随访数量
                  TIMEOUT: +targetDate?.TIMEOUT || 0, // 超时未随访数量
                  CANCEL: +targetDate?.CANCEL || 0, // 已取消随访数量
                  WAIT_FOLLOW: +targetDate?.WAIT_FOLLOW || 0, // 待随访数量
                },
              });
            }
            return Object.freeze({ ...date });
          });
          // 冻结整个数组
          this.calendarDates = Object.freeze(this.calendarDates);
          this.getFollowUpDetailByDay(this.selectedDate); // 加载选中日期的详情
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /**
     * 获取指定日期的随访详情数据
     * @param {Date} date - 要查询的日期
     */
    getFollowUpDetailByDay(date) {
      this.detailLoading = true;
      this.$api
        .getVisitPanelV2({
          ...this.formData,
          date: this.selectedDate ? moment(date).format('YYYY-MM-DD') : moment().format('YYYY-MM-DD'),
        })
        .then(res => {
          this.detail = res || {}; // 更新详情数据
        })
        .finally(() => {
          this.detailLoading = false;
        });
    },
    /**
     * 选择日期处理
     * @param {Object} date - 日期对象，包含日期信息和是否为当前月等属性
     */
    selectDate(date) {
      const { view, view_id } = this.formData;
      // 只允许选择当前月份的日期
      if (date.isCurrentMonth && date.date) {
        // 员工或患者视角下必须先选择目标对象
        if ((view === 'OPERATION' || view === 'PT') && !view_id) {
          this.$Message.warning(`请先选择${view === 'OPERATION' ? '员工' : '患者'}`);
          return;
        }
        this.selectedDate = date.date; // 更新选中日期
        this.getFollowUpDetailByDay(date.date); // 加载该日期的详情数据
      }
    },

    /**
     * 处理新增随访按钮点击
     * 显示新增随访弹窗
     */
    handleAdd() {
      this.addVisitModalVisible = true;
      // 这里可以添加新增逻辑
    },
  },
};
</script>

<style scoped lang="less">
/* 主容器样式 */
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  /* 左侧日历区域 */
  .left-wrapper {
    flex: 1;
    height: 100%;
    padding: 16px;
    border-right: 10px solid #f2f2f2;
  }
  /* 右侧详情区域 */
  .right-wrapper {
    width: 24%;
    min-width: 305px;
    height: 100%;
    flex-shrink: 0;
  }
}
.wrapper::-webkit-scrollbar {
  display: none;
}

/* 顶部操作栏样式 */
.top-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 8px;
}

/* 日历容器样式 */
.calendar-container {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  height: ~'calc(100% - 72px)';
  display: flex;
  flex-direction: column;
}

/* 日历头部（月份切换）样式 */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 12px;
  background: transparent;
  border-bottom: 1px solid #ebeef5;

  /* 当前月份显示 */
  .current-month {
    margin: 0 30px;
    font-size: 20px;
    font-weight: 500;
    color: #303133;
  }

  /* 月份切换按钮 */
  .nav-btn {
    color: #606266;
    font-size: 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #155bd4;
    }
  }
}

/* 星期标题行样式 */
.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f5f7fa;

  .weekday {
    padding: 12px 0;
    text-align: center;
    font-weight: 500;
    color: #606266;
    font-size: 14px;
  }
}

/* 日历网格样式 */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  flex: 1;
  border-top: 1px solid #ebeef5;
  border-left: 1px solid #ebeef5;

  /* 单个日期格子样式 */
  .calendar-day {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    transition: all 0.2s;

    /* 鼠标悬停效果 */
    &:hover {
      background-color: #f9fafb;
    }

    /* 非当前月份日期样式 */
    &.other-month {
      background-color: #f9fafb;
      .calendar-day-header {
        color: #bbbbbb;
      }
    }

    /* 今天日期样式 */
    &.today {
      //background-color: rgba(21, 91, 212, 0.05);
      color: white;
      font-weight: bold;
    }

    /* 选中日期样式 */
    &.selected {
      background-color: rgba(21, 91, 212, 0.1);
      color: white;
      font-weight: bold;
    }

    /* 今天且选中的日期样式 */
    &.today.selected {
      background-color: rgba(21, 91, 212, 0.1);
    }

    /* 日期格子头部（日期数字）样式 */
    .calendar-day-header {
      width: 100%;
      color: #333333;
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: auto;

      .day-text {
        padding: 4px 10px 4px 0;
        margin-left: auto;
        font-weight: 500;
        font-size: 14px;
      }
    }

    /* 日期格子内容（随访状态）样式 */
    .calendar-day-content {
      width: 100%;
      flex: 1;
      padding: 0 12px;

      /* 随访状态项样式 */
      > div {
        width: 45%;
        display: inline-flex;
        align-items: center;
        margin-top: 4px;
        font-size: 13px;
        color: #333333;

        /* 状态图标样式 */
        > img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }

        /* 数量文字样式 */
        .count {
          font-size: 12px;
        }
      }

      /* 待随访数量特殊样式（橙色背景） */
      .wait-follow-up-count {
        width: 100% !important;
        height: 24px;
        background: #ffaa00;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
        line-height: 18px;
        margin-top: 0 !important;
      }
    }
  }
}

/* 底部图例说明样式 */
.tip-panel {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 8px;

  /* 单个图例项样式 */
  > div {
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24px;

    /* 图例图标样式 */
    > img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  }
}
</style>
