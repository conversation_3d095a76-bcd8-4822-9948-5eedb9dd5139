<template>
  <div class="follow-record-wrapper">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="search-section">
          <div class="search-header">
            <Button type="primary" @click="addVisitModalVisible = true">创建随访</Button>
            <Button @click="showHistoryModal = true">查看历史</Button>
          </div>
          <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem>
              <Input v-model="queryFormData.keyword" placeholder="请输入用户姓名/手机号" />
            </FormItem>
            <FormItem>
              <Input v-model="queryFormData.member_name" placeholder="随访人" />
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.type_id" placeholder="随访类型" clearable>
                <Option v-for="(item, key) in typeDesc" :value="key" :key="key">{{ item.desc }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Button type="primary" @click="onSearch">筛选</Button>
              <Button @click="onResetSearch" style="margin-left: 8px">重置</Button>
            </FormItem>
          </Form>
        </div>
        <div class="flex flex-item-between flex-item-align">
          <div class="panel-nav" v-if="Object.keys(statusDesc).length">
            <a
              class="nav"
              :class="{
                active: !$route.query.status,
              }"
              @click.prevent.capture="onStatusChange('')"
              >全部</a
            >
            <a
              v-for="(item, key) in statusDesc"
              :key="key"
              class="nav"
              :class="{
                active: $route.query.status == key,
              }"
              @click.prevent.capture="onStatusChange(key)"
            >
              {{ item.desc }}
              <Tag :color="getColorByKey(key)">{{ status_count[key] }}</Tag>
            </a>
          </div>
          <div></div>
        </div>
      </template>
      <!-- 用户信息 -->
      <template slot-scope="{ row }" slot="user">
        <div class="flex user flex-item-align">
          <div>
            <div class="flex width-perecnt-100">
              <span class="user-label">姓名：</span><span class="user-text">{{ row.user.real_name }}</span>
            </div>
            <div class="flex">
              <span class="user-label">手机号：</span><span class="user-text">{{ row.user.mobile || '-' }}</span>
            </div>
          </div>
        </div>
      </template>

      <!-- 随访信息 -->
      <template slot-scope="{ row }" slot="followInfo">
        <div>
          <div class="flex">
            <span class="user-label">随访人：</span><span class="user-text">{{ row.operator_name || '-' }}</span>
          </div>
          <div class="flex">
            <span class="user-label">随访时间：</span
            ><span class="user-text">{{ row.operator_time | data_format }}</span>
          </div>
        </div>
      </template>

      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>

      <template slot-scope="{ row }" slot="mode_text">
        {{ row.mode_text || '-' }}
      </template>

      <template slot-scope="{ row }" slot="plan_time">
        {{ row.date }}
      </template>
      <template slot-scope="{ row }" slot="visit_time">
        {{ row.visit_time | data_format('YYYY-MM-DD') }}
      </template>

      <!-- 最近操作信息 -->
      <template slot-scope="{ row }" slot="operator">
        <div v-if="$route.query.status == 'FOLLOW_UP'">
          <p class="flex">
            <span class="user-label">随访人：</span><span class="user-text">{{ row.up_info.operator }}</span>
          </p>
          <p class="flex">
            <span class="user-label">随访时间：</span
            ><span class="user-text">{{ row.up_info.time | data_format('YYYY-MM-DD HH:mm') }}</span>
          </p>
        </div>
        <div v-else-if="$route.query.status == 'CANCEL'">
          <p class="flex">
            <span class="user-label">取消人：</span><span class="user-text">{{ row.cancel.operator }}</span>
          </p>
          <p class="flex">
            <span class="user-label">取消时间：</span
            ><span class="user-text">{{ row.cancel.time | data_format('YYYY-MM-DD HH:mm') }}</span>
          </p>
        </div>
        <div v-else>-</div>
      </template>

      <template slot-scope="{ row }" slot="action">
        <div v-if="row.status !== 'CANCEL'">
          <p v-if="row.status === 'WAIT_FOLLOW'">
            <a @click="goVisit(row)">随访</a>
          </p>
          <p v-if="row.status === 'FOLLOW_UP'" @click="goVisit(row)"><a>详情</a></p>
        </div>
        <div v-else>-</div>
      </template>
    </standard-table>

    <!-- 表格区域 -->
    <!-- 随访历史记录弹窗 -->
    <HistoryModal v-model="showHistoryModal" />
    <new-add-visit :visible.sync="addVisitModalVisible" :options="options" @success="getsList" />
  </div>
</template>

<script>
import renderHeader from '@/mixins/renderHeader';
import search from '@/mixins/search';
import S from '@/libs/util'; // Some commonly used tools
import HistoryModal from './components/HistoryModal.vue';
import NewAddVisit from '../visit/components/v2/add-visit.vue';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '',
  type_id: '',
  followType: '',
  status: '',
};

export default {
  name: 'FollowUpRecord',
  components: {
    HistoryModal,
    NewAddVisit,
  },
  mixins: [renderHeader, search],
  data() {
    return {
      apiName: 'getFollowRecordList',
      queryFormData: { ...init_query_form_data },
      tableLoading: false,
      showHistoryModal: false,
      // 表格列定义
      tableCols: [
        { title: 'ID', key: 'id', width: 50, align: 'center' },
        { title: '用户', slot: 'user', width: 180, align: 'center' },
        { title: '类型', key: 'type_text', width: 100, align: 'center' },
        { title: '目标', key: 'target', width: 120, align: 'center' },
        { title: '随访方式', slot: 'mode_text', width: 100, align: 'center' },
        { title: '状态', key: 'status_text', width: 100, align: 'center' },
        {
          title: '计划日期',
          slot: 'plan_time',
          width: 120,
          align: 'center',
          renderHeader: (h, params) => {
            return this._renderHeader(
              h,
              params,
              '随访计划日期：该随访记录在计划日期未随访，记录将自动变成未随访状态',
              '300'
            );
          },
        },
        { title: '随访信息', slot: 'followInfo', width: 220, align: 'center' },
        { title: '备注', slot: 'remark', width: 150, align: 'center' },
        { title: '操作', slot: 'action', width: 100, align: 'center' },
      ],
      statusDesc: {},
      typeDesc: {},
      status_count: {},
      addVisitModalVisible: false,
      options: {
        type_desc: [],
        sub_type_desc: [],
        visit_method_desc: [],
        normal_method_desc: [],
        comfort_desc: [],
        visit_operation_desc: [],
        normal_operation_desc: [],
        status_desc: [],
        effect_desc: [],
        engagement_desc: [],
        result_desc: [],
        view_desc: [],
        sms_template_desc: [],
      },
    };
  },
  computed: {
    tableHeight() {
      return this.$store.state.app.clientHeight - 300;
    },
    getColorByKey() {
      const colorMap = {
        WAIT_FOLLOW: 'warning',
        FINISHED: 'success',
        TIMEOUT: 'error',
      };
      return key => {
        return colorMap[key] || 'default';
      };
    },
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getOptions();
  },
  mounted() {},
  methods: {
    goVisit({ uid, id, type }) {
      this.$router.push({
        path: type === 'ACTIVITY_VISIT' ? '/daily/visit/activity-visit' : '/daily/visit/detail',
        query: { id, uid },
      });
    },
    handlerListData(list) {
      this.status_count = list.status_count;
    },
    createFollowUp() {
      this.$router.push('/daily/visit/create');
    },
    getOptions() {
      this.$api.getFollowUpOptions().then(res => {
        const keys = Object.keys(res || {});
        for (let i = 0; i < keys.length; i++) {
          const list = S.descToArrHandle(res?.[keys[i]] || {});
          this.$set(this.options, keys[i], list);
        }
        this.statusDesc = res.status_desc;
        this.typeDesc = res.type_desc;
      });
    },
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },
    // 查看详情
    viewDetail(row) {
      // 打开新标签页查看详情
      const routeData = this.$router.resolve({
        path: '/daily/visit/detail',
        query: { id: row.id },
      });
      window.open(routeData.href, '_blank');
    },

    // 查看历史详情
    viewHistoryDetail(row) {
      this.viewDetail(row);
    },

    // 获取类型文本
    getTypeText(type) {
      const typeMap = {
        DIAGNOSIS: '诊疗随访',
        TREATMENT: '治疗随访',
        RECOVERY: '康复随访',
        PREVENTION: '预防随访',
      };
      return typeMap[type] || type;
    },

    // 获取方式文本
    getMethodText(method) {
      const methodMap = {
        PHONE: '电话随访',
        WECHAT: '微信随访',
        VISIT: '到店随访',
      };
      return methodMap[method] || method;
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        PENDING: '待随访',
        COMPLETED: '已随访',
        NOT_VISITED: '未随访',
        CANCELLED: '已取消',
      };
      return statusMap[status] || status;
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        PENDING: 'orange',
        COMPLETED: 'green',
        NOT_VISITED: 'red',
        CANCELLED: 'default',
      };
      return colorMap[status] || 'default';
    },
  },
  filters: {
    dateFormat(value) {
      if (!value || value === '-') return '-';
      return value.includes('-') ? value : new Date(value).toLocaleDateString();
    },
    dateTimeFormat(value) {
      if (!value || value === '-') return '-';
      return value.includes(':') ? value : new Date(value).toLocaleString();
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.follow-record-wrapper {
  background: #fff;

  .search-header {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
  }
  .user-label {
    color: #999;
    min-width: 70px;
    text-align: right;
  }
}
</style>
