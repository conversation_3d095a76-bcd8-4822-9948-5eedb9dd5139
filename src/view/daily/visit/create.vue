<template>
  <div class="wrapper">
    <Tooltip :content="tipText" max-width="180" theme="light" class="tip-wrapper" placement="left">
      <p class="flex flex-item-center">
        <svg-icon iconClass="tip" class="helpIcon"></svg-icon>
        <span class="cursor tip-text"></span>
      </p>
    </Tooltip>
    <Form ref="formData" :model="formData" label-colon :label-width="100" class="mt10">
      <FormItem prop="plan_time" label="计划随访时间" class="width-percent-40">
        <DatePicker
          class="width-percent-100"
          type="date"
          :value="formData.plan_time"
          @on-change="timeChange"
          placeholder="请选择计划随访时间"
        ></DatePicker>
      </FormItem>

      <FormItem prop="fv_type" label="随访类型" class="width-percent-40">
        <Select v-model="formData.fv_type" @on-change="fvTypeChange">
          <Option v-for="(item, key) in typeDesc" :value="key" :key="key">{{ item.desc }}</Option>
        </Select>
      </FormItem>

      <FormItem label="随访用户" class="customer">
        <div class="flex flex-item-between">
          <RadioGroup v-model="formData.customRange" @on-change="radioChange">
            <Radio label="all">全部用户</Radio>
            <Radio label="part">部分用户</Radio>
          </RadioGroup>
          <p class="ml10" v-if="hasSearchShow && formData.customRange === 'all'">
            已查询到<span class="mlr10 prominent-number">{{ total }}</span
            >位符合类型及条件要求的用户
          </p>
        </div>
      </FormItem>

      <FormItem label="" v-if="formData.customRange === 'part'" class="customer">
        <div class="flex requirenent">
          <p class="requirenent-title flex flex-item-align">设置筛选条件：</p>
          <div class="requirement-item-wrapper~ width-percent-100">
            <div class="requirement-item mt10 width-percent-100" v-for="(item, index) in requirementList" :key="index">
              <Row class="flex-1" :gutter="9">
                <Col span="2" class="max-width100">
                  <Select v-model="item.relationValue" v-if="index !== 0" disabled>
                    <Option value="OR">或</Option>
                    <Option value="AND">且</Option>
                  </Select>
                </Col>

                <Col class="ml10 min-width200" span="4">
                  <Select v-model="item.requirementKey" @on-change="keyValueChange($event, index)">
                    <Option :value="key" v-for="(item, key) in conditionObj" :key="key">{{ item.desc }}</Option>
                  </Select>
                </Col>

                <Col class="ml10" span="3">
                  <Select v-model="item.requirementSymbol">
                    <Option :value="key" v-for="(item, key) in getSectionCondition(item.requirementKey)" :key="key">{{
                      item.desc
                    }}</Option>
                  </Select>
                </Col>

                <Col class="ml10" span="8">
                  <!-- input框 -->
                  <template
                    v-if="
                      (getValueCondition(item.requirementKey).label === 'input' ||
                        getValueCondition(item.requirementKey).label == '') &&
                      item.requirementSymbol !== 'between'
                    "
                  >
                    <Input v-model="item.requirementValue1" placeholder="请输入" class="width-percent-100"></Input>
                  </template>

                  <template
                    v-if="
                      getValueCondition(item.requirementKey).label === 'input' && item.requirementSymbol == 'between'
                    "
                  >
                    <div class="flex">
                      <Input v-model="item.requirementValue1" placeholder="请输入" class="width-percent-100"></Input>
                      -
                      <Input v-model="item.requirementValue2" placeholder="请输入" class="width-percent-100"></Input>
                    </div>
                  </template>

                  <!-- 选择框 -->
                  <template v-if="getValueCondition(item.requirementKey).label === 'select'">
                    <Select v-model="item.requirementValue1">
                      <Option
                        :value="key.toString()"
                        v-for="(item, key) in getValueCondition(item.requirementKey).value"
                        :key="key"
                        >{{ item.desc }}</Option
                      >
                    </Select>
                  </template>

                  <!-- 时间框 -->
                  <template
                    v-if="
                      getValueCondition(item.requirementKey).label === 'dateTime' &&
                      item.requirementSymbol !== 'between'
                    "
                  >
                    <DatePicker
                      type="date"
                      class="width-percent-100"
                      :value="item.requirementValue1"
                      @on-change="item.requirementValue1 = arguments[0]"
                      placeholder="请选择时间"
                    />
                  </template>

                  <template
                    v-if="
                      getValueCondition(item.requirementKey).label === 'dateTime' && item.requirementSymbol == 'between'
                    "
                  >
                    <div class="flex">
                      <DatePicker
                        type="date"
                        class="width-percent-100"
                        :value="item.requirementValue1"
                        @on-change="item.requirementValue1 = arguments[0]"
                        placeholder="请选择时间"
                      />
                      -
                      <DatePicker
                        type="date"
                        class="width-percent-100"
                        :value="item.requirementValue2"
                        @on-change="item.requirementValue2 = arguments[0]"
                        placeholder="请选择时间"
                      />
                    </div>
                  </template>

                  <!-- 生日 -->
                  <template
                    v-if="
                      getValueCondition(item.requirementKey).label === 'monthDay' &&
                      item.requirementSymbol !== 'between'
                    "
                  >
                    <!-- <monthes-days ref="day" @getValue="getBirthday($event,'requirementValue1',index)"></monthes-days> -->
                    <KMonthesDays
                      v-model="item.requirementValue1"
                      class="width-percent-100"
                      @getValue="getBirthday($event, 'requirementValue1', index)"
                    ></KMonthesDays>
                  </template>

                  <div
                    v-show="
                      getValueCondition(item.requirementKey).label === 'monthDay' && item.requirementSymbol == 'between'
                    "
                  >
                    <keep-alive>
                      <div class="flex">
                        <!-- <monthes-days class="width-percent-100" @getValue="getBirthday($event,'requirementValue1',index)"></monthes-days>
                          -
                          <monthes-days class="width-percent-100" @getValue="getBirthday($event,'requirementValue2',index)"></monthes-days> -->

                        <KMonthesDays
                          v-model="item.requirementValue1"
                          class="width-percent-100"
                          @getValue="getBirthday($event, 'requirementValue1', index)"
                        ></KMonthesDays>
                        -
                        <KMonthesDays
                          v-model="item.requirementValue2"
                          class="width-percent-100"
                          @getValue="getBirthday($event, 'requirementValue2', index)"
                        ></KMonthesDays>
                      </div>
                    </keep-alive>
                  </div>
                </Col>

                <div class="ml10 flex flex-item-align">
                  <p class="symbol-wrapper" @click="addItem" v-show="requirementList.length < 5">
                    <svg-icon shape="circle" iconClass="plus" class="symbol-icon"></svg-icon>
                  </p>
                  <p class="symbol-wrapper ml10" @click="removeItem(index)" v-show="requirementList.length > 1">
                    <svg-icon iconClass="remove" class="symbol-icon"></svg-icon>
                  </p>
                </div>

                <div class="flex ml18" v-show="requirementList.length - 1 === index">
                  <Button type="primary" @click="searchCustomer">查询</Button>
                  <!-- <p class="ml10" v-if="hasSearchShow">已查询到<span class="mlr10 prominent-number">{{ total }}</span>位符合类型及条件要求的用户</p> -->
                </div>
              </Row>
            </div>
          </div>
        </div>
      </FormItem>

      <!-- 符合条件的随访用户 -->
      <FormItem label="">
        <div :class="{ height30: hasSearchShow }" class="mt10 height10" v-if="formData.customRange === 'part'">
          <p class="flex flex-item-end" v-if="hasSearchShow">
            已查询到<span class="mlr10 prominent-number">{{ total }}</span
            >位符合类型及条件要求的用户
          </p>
        </div>
        <Table :row-class-name="rowClassName" :loading="tableLoading" :columns="tableCols" :data="list" class="mt10">
          <!-- 待服务金额 -->
          <template slot-scope="{ row, index }" slot="serv_remain_card_money">
            <p>{{ row.serv_remain_card_money || '-' }}</p>
          </template>

          <!-- 消费次数 -->
          <template slot-scope="{ row, index }" slot="purchase_num">
            <p>{{ row.purchase_num || '-' }}</p>
          </template>

          <!-- 消费金额 -->
          <template slot-scope="{ row, index }" slot="purchase_money">
            <p>{{ row.purchase_money || '-' }}</p>
          </template>

          <!-- 上次到店时间 -->
          <template slot-scope="{ row, index }" slot="last_arrival_time">
            <p>{{ row.last_arrival_time || '-' }}</p>
          </template>

          <!-- 就诊记录 -->
          <template slot-scope="{ row, index }" slot="medical_record">
            <Select v-model="row.currentRecordId" @on-change="ptRecordChange($event, row)">
              <Option :value="item.id" v-for="(item, index) in row.medical_record" :key="index + 'se'">{{
                item.create_time
              }}</Option>
            </Select>
          </template>

          <template slot-scope="{ row, index }" slot="action">
            <a @click="delWillVisit(row, index)" v-if="can_del(row.pt_id)">删除</a>
            <p v-else>
              <span>已删除</span>
              <a @click="cancelDel(row, index)" class="ml10">撤销</a>
            </p>
          </template>
        </Table>

        <KPage
          :total="total"
          class="mt10 flex flex-item-center"
          :page-size.sync="queryFormData.pageSize"
          :current.sync="queryFormData.page"
          @on-change="onPageChange"
        />
      </FormItem>
    </Form>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Dvd />
      <Dvd />
      <Button type="primary" @click="submitPlan" :loading="submitLoading">提交计划</Button>
    </div>

    <!-- 随访计划提交成功 -->
    <!-- <SuccessTip v-model="successVisible">
      <div slot="tip">
        <p v-if="false">本次计划共提交9人的随访计划，已全部完成创建</p>
        <p v-else>本次计划共提交9人的随访计划，其中8人已成功创建，1人创建失败，<span class="error-style">失败原因：同一人在1天内，一种类型下仅能新建1次随访计划</span></p>
      </div>
    </SuccessTip> -->
  </div>
</template>

<script>
import SuccessTip from './components/success-tip';
import KMonthesDays from '@/components/k-monthes-days';
import { debounce } from 'lodash-es';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  r: '',
};
export default {
  name: 'create',
  components: {
    SuccessTip,
    KMonthesDays,
  },
  mixins: [],
  props: {},
  data() {
    return {
      tipText: '随访限制：针对一个用户，1天1种随访类型下仅可设置1条计划。', // 问号标识的提示文字
      formData: {
        plan_time: this.$moment(new Date()).format('YYYY-MM-DD'), // 计划随访时间
        fv_type: '', // 随访类型
        customRange: 'part', // 随访用户类型
      },

      queryFormData: { ...init_query_form_data },

      typeDesc: [], // 随访类型枚举

      conditionObj: {}, // 筛选条件

      /**
       * description: 筛选条件相关数据
       * */
      relationValueList: [], // 筛选关联条件
      requirementKeyList: [], // 条件
      requirementSymbolList: [], // 条件符号
      requirementValueList: [], // 条件值
      isSearch: false, // 是否点击了查询

      // 筛选条件值
      requirementList: [
        {
          relationValue: 'AND',
          requirementKey: '',
          requirementSymbol: '',
          requirementValue1: '',
          requirementValue2: '',
        },
      ],

      /**
       * description: 表格数据
       */
      // 随访用户表格
      tableCols: [],
      // 诊后随访col
      visit_tableCols: [
        { title: '姓名', key: 'pt_name', width: 150, align: 'center' },
        { title: '手机号', key: 'pt_mobile', align: 'center' },
        { title: '待服务卡券数', key: 'serv_remain_card_num', align: 'center' },
        { title: '待服务金额(元)', slot: 'serv_remain_card_money', align: 'center' },
        { title: '消费次数', slot: 'purchase_num', align: 'center' },
        { title: '消费金额(元)', slot: 'purchase_money', align: 'center' },
        { title: '上次到店时间', slot: 'last_arrival_time', align: 'center' },
        { title: '注册时间', key: 'pt_reg_time', align: 'center' },
        { title: '就诊记录', slot: 'medical_record', align: 'center', width: 170 },
        { title: '操作', slot: 'action', align: 'center', width: 150 },
      ],
      // 活动通知的col
      activity_tableCols: [
        { title: '姓名', key: 'pt_name', width: 150, align: 'center' },
        { title: '手机号', key: 'pt_mobile', align: 'center' },
        { title: '待服务卡券数', key: 'serv_remain_card_num', align: 'center' },
        { title: '待服务金额(元)', slot: 'serv_remain_card_money', align: 'center' },
        { title: '消费次数', slot: 'purchase_num', align: 'center' },
        { title: '消费金额(元)', slot: 'purchase_money', align: 'center' },
        { title: '上次到店时间', slot: 'last_arrival_time', align: 'center' },
        { title: '注册时间', key: 'pt_reg_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center', width: 150 },
      ],
      tableLoading: false,
      list: [],
      total: 0,
      delIdList: [], // 删除的用户id数据
      pt_id_mr_map: {}, // 就诊记录有变更的用户记录和就诊记录数据

      submitLoading: false, // 提交计划loading
      successVisible: false, // 随访计划提交成功弹窗
    };
  },
  computed: {
    // 是都显示已经查询到多少位用户
    hasSearchShow() {
      const { isSearch } = this;
      return isSearch;
    },
    can_del() {
      return pt_id => {
        return this.delIdList.indexOf(pt_id) > -1 ? false : true;
      };
    },
    // 根据条件的key返回对应的区间条件
    getSectionCondition() {
      return key => {
        const { conditionObj } = this;
        if (key) {
          return conditionObj[key].sign;
        }
        return {};
      };
    },
    // 根据条件的key返回对应的value条件
    getValueCondition() {
      return key => {
        const { conditionObj } = this;
        if (key) {
          return conditionObj[key].controls;
        }
        return {
          label: '',
          type: '',
          value: '',
        };
      };
    },

    // 用来显示单个组件还是区间组件
    // isSingleComponent () {
    //   return (currentLable, expectLabel, signValue = '') => {
    //     if ( currentLable.label == expectLabel && signValue == 'between' ) {
    //       return true
    //     }
    //     return false
    //   }
    // }
  },
  watch: {
    // 活动通知和诊后随访的col不同
    'formData.fv_type': {
      immediate: true,
      handler(val) {
        if (val === 'ACTIVITY_VISIT') {
          this.tableCols = this.activity_tableCols;
        } else {
          this.tableCols = this.visit_tableCols;
        }
      },
    },
  },
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getVisitOptions();
      // 获取筛选条件选项
      this.getVisitConditions();
    },
    // 计划随访时间
    timeChange(time) {
      this.formData.plan_time = time;
      this.resetPage();
      const { plan_time, fv_type, customRange } = this.formData;
      if (plan_time && fv_type && customRange == 'all') {
        this.getFUScreeningList();
      }
    },

    // 随访类型切换
    fvTypeChange(val) {
      const { customRange } = this.formData;
      // 当用户选择了全部用户的时候
      if (customRange === 'all') {
        this.getFUScreeningList();
      }
    },

    /**
     * 随访用户切换时，重置列表，total,
     * 切换回部分用户时，不手动给用户查询数据
     * */
    // 随访用户切换
    radioChange(val) {
      this.resetPage();
      if (val == 'all') {
        this.getFUScreeningList();
      } else {
        // let cloneRequirement = []
        // if (this.requirementList.length > 0) {
        //   cloneRequirement = this.$lodash.cloneDeep(this.requirementList)
        // }
        // this.requirementList = []

        // this.requirementList = cloneRequirement
        this.isSearch = false;
      }
    },

    /**
     * description: 筛选条件功能
     */

    // 新增条件
    addItem() {
      this.requirementList.push({
        relationValue: 'AND',
        requirementKey: '',
        requirementSymbol: '',
        requirementValue1: '',
        requirementValue2: '',
      });
    },
    // 减少条件
    removeItem(index) {
      this.requirementList.splice(index, 1);
    },
    // formValidated
    formDataValidated() {
      const { plan_time, fv_type } = this.formData;
      if (plan_time == '') {
        this.$Message.error('计划随访时间不可为空');
        return false;
      }
      if (fv_type == '') {
        this.$Message.error('随访类型不可为空');
        return false;
      }
      return true;
    },
    // 条件是否都填写完成
    requirementValidated() {
      const { requirementList } = this;
      let validateFlag = true;
      requirementList.some((item, index) => {
        let item_key_list = Object.keys(item);
        item_key_list.some((item_key, item_index) => {
          if (item[item_key] !== 'between') {
            // 当条件为非区间的时候,requirementValue2不必校验
            if (!item[item_key] && item[item_key] !== 0 && item_key !== 'requirementValue2') {
              this.$Message.error('筛选条件请填写完整');
              validateFlag = false;
              return true;
            }
          } else {
            if (!item[item_key]) {
              this.$Message.error('筛选条件请填写完整');
              validateFlag = false;
              return true;
            }
          }
        });
        if (!validateFlag) {
          return true;
        }
      });
      return validateFlag;
    },
    // 第一个条件切换时，对应的第二个和第三个条件清空
    keyValueChange(e, index) {
      this.requirementList[index].requirementSymbol = '';
      this.requirementList[index].requirementValue1 = '';
      this.requirementList[index].requirementValue2 = '';
    },
    // 生日控件
    getBirthday(e, key, index) {
      this.requirementList[index][key] = e;
    },
    // 将本地条件数据整合成符合接口参数的格式
    getCond() {
      const { requirementList } = this;
      /**
       * @description: expect Format of cond
       * params : {
       *     "cond_type":"AND",
       *     "details":[
       *       {"item":"pt_birthday","sign":">","val":"1977-01-01"},
       *       {"item":"pt_sex","sign":"=","val":"1"}
       *     ]
       *  }
       * */
      let cond = {
        cond_type: 'AND',
        details: [],
      };
      requirementList.forEach(req_item => {
        let val =
          req_item.requirementSymbol == 'between'
            ? [req_item.requirementValue1, req_item.requirementValue2]
            : req_item.requirementValue1;
        cond.details.push({
          item: req_item.requirementKey,
          sign: req_item.requirementSymbol,
          val: val,
        });
      });
      return cond;
    },
    // 查询符合条件的用户
    searchCustomer() {
      if (this.requirementValidated()) {
        this.delIdList = [];
        this.getFUScreeningList();
      }
    },
    /**
     * 表格功能
     * */
    // 删除将要随访的用户
    delWillVisit(row, index) {
      this.delIdList.push(row.pt_id);
    },
    // 撤销
    cancelDel(row, index) {
      let delIndex = this.delIdList.indexOf(row.pt_id);
      this.delIdList.splice(delIndex, 1);
    },
    // 给表格行设置样式
    rowClassName(row, index) {
      let idList = this.findDelIndex();
      return idList.indexOf(index) > -1 ? 'del-cell' : '';
    },
    // 返回当前数据中哪些数据已经被删除置灰了id集合
    findDelIndex() {
      let resultIndexList = [];
      this.delIdList.forEach(id_item => {
        this.list.forEach((item, index) => {
          if (id_item === item.pt_id) {
            resultIndexList.push(index);
          }
        });
      });
      return resultIndexList;
    },

    // 不是默认的第一条就诊记录的集合,在其子集中要过滤掉本地删除的患者记录
    ptRecordChange(e, row) {
      // 如果当前选中的是这个就诊记录数组的第一条，将其从pt_id_mr_map移除，反之，加入
      const { pt_id_mr_map } = this;

      // 病人多条就诊记录的第一条(最新的一条)记录id
      let recordFirstId = '';
      if (row.medical_record.length > 0) {
        row.medical_record[0].id;
      }
      let pt_id = row.pt_id;

      if (e != recordFirstId) {
        this.pt_id_mr_map = {
          ...pt_id_mr_map,
          [pt_id]: e,
        };
      }
      // 如果又选回第一条就诊记录，删除
      if (e == recordFirstId) {
        let mrMapKeyList = Object.keys(pt_id_mr_map);
        if (mrMapKeyList.indexOf(pt_id) > -1) {
          this.$delete(this.pt_id_mr_map, pt_id);
        }
      }
    },
    // 当前要提交的列表是否有有效数据
    isEffectiveData() {
      if (!this.list.length) {
        return false;
      }
      if (this.list.length == this.delIdList.length) {
        return false;
      }
      return true;
    },

    // 分页
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;

      const { customRange } = this.formData;
      const { isSearch } = this;
      // 分页的条数切换时,只有当条件填写完整并且随访类型选择了，才可调取接口
      if (customRange === 'all') {
        this.getFUScreeningList();
      } else {
        if (this.requirementValidated() && isSearch) {
          this.getFUScreeningList();
        }
      }
    },

    // 重置分页
    resetPage() {
      this.delIdList = [];
      this.list = [];
      this.total = 0;
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = 20;
    },

    back() {
      this.$router.push('/daily/visit/list');
    },
    // 提交计划
    submitPlan() {
      const { delIdList, formData, pt_id_mr_map } = this;

      if (!this.isEffectiveData()) {
        this.$Message.error('请至少选择一位用户设置计划');
        return;
      }

      let params = {
        plan_time: formData.plan_time,
        fv_type: formData.fv_type,
        cond: JSON.stringify({
          cond_type: 'AND',
          filter_pt_ids: delIdList,
          pt_id_mr_map,
          details: this.formData.customRange == 'all' ? [] : this.getCond().details,
        }),
      };
      if (this.formDataValidated()) {
        this.submitLoading = true;
        this.$api
          .SubmitFUPlan(params)
          .then(res => {
            this.$Message.success('随访计划已成功提交');
            this.$router.back();
          })
          .catch(rej => {})
          .finally(() => {
            this.submitLoading = false;
          });
      }
    },

    // 用户从接口获取的将要随访的用户信息做处理
    handleList(list) {
      this.list = list;
      this.list.forEach(list_item => {
        if (list_item.medical_record.length > 0) {
          list_item.currentRecordId = list_item.medical_record[0].id;
        }
      });
    },

    /**
     * @description: 接口api
     * */
    // 获取随访类型选项信息
    getVisitOptions() {
      this.$api.getVisitOptions().then(res => {
        this.typeDesc = res.typeDesc;
        // 过滤掉活动通知-open
        // this.$delete(this.typeDesc, 'ACTIVITY_VISIT')
      });
    },
    // 获取随访条件
    getVisitConditions() {
      this.$api.getVisitConditions().then(res => {
        this.conditionObj = res;
      });
    },
    // 根据筛选条件获取用户
    getFUScreeningList() {
      const { plan_time, fv_type, customRange } = this.formData;
      if (!plan_time) {
        this.$Message.error('请选择计划随访时间');
        return;
      }
      if (!fv_type) {
        this.$Message.error('请选择随访类型');
        return;
      }
      this.tableLoading = true;
      let params = {
        plan_time,
        ...this.queryFormData,
        fv_type,
        cond: JSON.stringify(customRange == 'all' ? {} : this.getCond()),
      };
      this.$api
        .getFUScreeningList(params)
        .then(res => {
          this.handleList(res.list);
          this.total = res.total;
          this.isSearch = true;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.wrapper {
  position: relative;

  .customer {
    margin-bottom: 0px !important;
  }

  .tip-wrapper {
    position: absolute;
    right: 20px;
    top: 0px;
    .helpIcon {
      width: 30px;
      height: 30px;
      color: #999;
      cursor: pointer;
    }
    .tip-text {
      color: #999;
      margin-left: 4px;
    }
  }

  .requirenent {
    .requirenent-title {
      width: 90px;
      min-width: 90px;
      height: 30px;
      margin-top: 12px;
    }
    .requirement-item-wrapper {
      .requirement-item {
      }
    }
  }
  .symbol-wrapper {
    border: 1px solid #1157e5;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2px;
    width: 22px;
    height: 22px;
    box-sizing: border-box;
    cursor: pointer;
    &:hover {
      opacity: 0.6;
    }
    .symbol-icon {
      font-size: 20px;
      fill: #115bd4;
    }
  }
}
</style>

<style lang="less" scoped>
.width-percent-100 {
  width: 100% !important;
  max-width: 100% !important;
}
.width-percent-40 {
  width: 40% !important;
  max-width: 40% !important;
}
.max-width100 {
  width: 100px !important;
  max-width: 100px !important;
}
.min-width200 {
  width: 200px !important;
  min-width: 200px !important;
}
.error-style {
  color: red;
}
.mt10 {
  margin-top: 10px;
}
.mt20 {
  margin-top: 20px;
}
.ml10 {
  margin-left: 10px;
}
.ml18 {
  margin-left: 18px;
}
.mlr10 {
  margin-left: 10px;
  margin-right: 10px;
}
.height10 {
  height: 10px;
}
.height30 {
  height: 30px;
}
.cursor {
  cursor: pointer;
}
// 通用的突出文字颜色
.prominent-number {
}
.border50 {
  border-radius: 50%;
}
p {
  margin: 0;
}

::v-deep .ivu-table .del-cell td {
  background-color: #f6f6f6 !important;
  color: #cccccc !important;
  // text-decoration: line-through;
}

::v-deep .mouth-days-value {
  width: 100%;
}
::v-deep .el-input,
::v-deep .el-input__inner {
  height: 33px !important;
}
</style>
<style lang="less">
.ivu-tooltip-inner {
  white-space: normal !important;
}
.el-input__icon {
  line-height: 34px;
}
</style>
