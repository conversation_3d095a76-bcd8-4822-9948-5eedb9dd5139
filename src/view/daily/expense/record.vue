<template>
  <div class="wrapper">
    <!-- Header -->
    <header class="head-block ceiling" style="background: #fff">
      <div class="flex flex-item-align">
        <p>选择月份：</p>
        <customDatePicker
          v-model="formDate"
          :is_current_date="true"
          :type-options="['month']"
          @getDateType="getDateType"
        ></customDatePicker>
      </div>
    </header>

    <!-- 整体情况 -->
    <div style="padding-bottom: 0px">
      <h4 class="block-title flex flex-align-center">本月成本构成</h4>
      <!-- 卡片数据 -->
      <div class="mt16 flex flex-warp" style="position: relative">
        <Spin class="overview-spin" v-if="loading"></Spin>
        <div class="cost-card" v-for="(item, index) in overview_list" :key="index">
          <div class="cost-card-item">
            <div class="cost-item-top">
              <div class="cost-item-title">
                <div class="title">
                  <div>
                    {{ item.desc }}
                  </div>
                  <Tooltip v-if="item.tooltip" :transfer="true" theme="light" placement="bottom">
                    <div slot="content">
                      <div class="custom-tooltip" v-html="item.tooltip"></div>
                    </div>
                    <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
                  </Tooltip>
                </div>
                <div class="tip">{{ item.contain }}</div>
              </div>
              <div class="cost-item-text">{{ Number(item.total_fee || 0).toFixed(2) }}</div>
            </div>

            <div class="cost-detail-box">
              <div class="item">
                <div class="item-label">长期</div>
                <div class="item-value">{{ Number(item.long_fee || 0).toFixed(2) }}</div>
              </div>

              <div class="item">
                <div class="item-label">一次性</div>
                <div class="item-value">{{ Number(item.one_fee || 0).toFixed(2) }}</div>
              </div>
            </div>
          </div>
          <div class="btn-box">
            <Button type="primary" class="record-btn" @click="cardChange(item)" :disabled="!is_current_month"
              >记录</Button
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 本月费用明细 -->
    <div class="mt16">
      <div class="flex flex-item-between">
        <p class="block-title">本月费用明细</p>
      </div>
      <Table class="mt9" height="520" :columns="feeCols" :data="fee_list" :loading="feeTabLoading">
        <template slot-scope="{ row, index }" slot="amount">
          <div class="flex flex-item-center">
            <p>{{ Number(row.amount || 0).toFixed(2) }}</p>
            <p style="margin-left: 4px" v-if="row.type == '2'">{{ `[${row.progress} / ${row.divide_month}]` }}</p>
          </div>
        </template>
      </Table>
      <div class="page-wrapper">
        <KPage
          :total="fee_total"
          :page-size.sync="fee_queryFormData.pageSize"
          :current.sync="fee_queryFormData.page"
          @on-change="OnPageChange"
        />
      </div>
    </div>

    <!-- 本月成本明细 -->
    <div class="mt16">
      <div class="flex flex-item-between">
        <p class="block-title">本月成本明细</p>
      </div>
      <Table class="mt9 header-text" border :columns="costDetailCols" :data="cost_detail_list" :loading="loading">
        <template slot-scope="{ row, index }" :slot="item" v-for="item in getDays">
          <div
            class="flex flex-item-center"
            :style="{ color: row[item]?.change === '1' ? 'red' : row[item]?.change === '2' ? 'green' : '' }"
          >
            <div>{{ Number(row[item]?.amount || 0).toFixed(2) }}</div>
            <div v-if="row[item]?.change == '1' || row[item]?.change == '2'">
              <img class="change-img" :src="row[item]?.change === '1' ? up_img : down_img" />
            </div>
          </div>
        </template>
      </Table>
    </div>

    <!-- 记录 -->
    <record-modal v-model="recordVisible" :current_item="current_overview_item" @success="refresh"></record-modal>

    <Modal v-model="remindVisible" :closable="false" width="400">
      <div class="remind-modal-context">
        <p>提交成功，请在【费用记录审核】里查看审核进度,</p>
        <p>审核通过后会开始计算成本。</p>
      </div>

      <template #footer>
        <div style="text-align: center">
          <Button type="primary" @click="remindVisible = false">知道了</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import RecordModal from '@/view/statistics/components/record/recordModal.vue';
import S from '@/libs/util';
import dateChange from './mixins/dateChange';

const init_query_form_data = {
  page: 1,
  pageSize: 10,
};
export default {
  name: 'record',
  components: { RecordModal },
  mixins: [dateChange],
  props: {},
  data() {
    return {
      remindVisible: false,
      overview_list: [], // 概况
      loading: true, // 构成loading
      recordVisible: false,
      current_overview_item: {},

      /* 本月费用明细 */
      feeCols: [
        { title: '序号', type: 'index', align: 'center' },
        { title: '费用类型', key: 'fee_type_text', align: 'center' },
        { title: '费用金额', slot: 'amount', align: 'center' },
        { title: '平摊时间', key: 'divide_month_text', align: 'center' },
        { title: '记录时间', key: 'record_time', align: 'center', minWidth: 100 },
        { title: '费用来源', key: 'accounts_type', align: 'center' },
        { title: '操作人', key: 'operator', align: 'center' },
      ],
      fee_list: [],
      fee_queryFormData: { ...init_query_form_data },
      fee_total: 0, // 表格总条数
      feeTabLoading: false,

      /* 本月成本明细 */
      cost_detail_list: [],
      up_img: 'https://static.rsjxx.com/image/2025/0712/135440_77555.png',
      down_img: 'https://static.rsjxx.com/image/2025/0712/135440_29852.png',
    };
  },
  computed: {
    // 是否是当前月
    is_current_month() {
      return this.$moment(this.formDate[0]).month() == this.$moment().month();
    },
    // 设置cols
    costDetailCols() {
      let children = [];
      let cols = [
        { title: '序号', type: 'index', width: 100, align: 'center', fixed: 'left' },
        { title: '费用类型', key: 'desc', width: 130, align: 'center', fixed: 'left' },
        {
          title: ' ',
          slot: 'cost',
          children: [],
          align: 'center',
        },
      ];
      for (let day = 1; day <= this.getDays; day++) {
        children.push({ title: `${day}号`, slot: day, width: 100, align: 'center' });
      }

      cols[2].children = children;
      return cols;
    },

    getDays() {
      let st = this.formDate[0];
      return st ? this.$moment(st).daysInMonth() : [];
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    refresh() {
      this.fee_queryFormData = { ...init_query_form_data };

      this.remindVisible = true;

      this.getFeeOverview();
      this.getFeeDetailslist();
    },
    // 当时间发生变化，进行得操作
    dateChange() {
      // 概况
      this.getFeeOverview();
      // 本月费用明细
      this.getFeeDetailslist();
    },

    /* 卡片切换事件 */
    cardChange(item) {
      if (!this.is_current_month) return;
      this.recordVisible = true;
      this.current_overview_item = item;
    },

    // 表格分页
    OnPageChange(page, pageSize) {
      this.fee_queryFormData.page = page;
      this.fee_queryFormData.pageSize = pageSize;
      this.getFeeDetailslist();
    },

    // 本月费用明细
    getFeeDetailslist() {
      this.feeTabLoading = true;
      let params = {
        ...{
          st: this.formDate[0],
          et: this.formDate[1],
        },
        ...this.fee_queryFormData,
        date_type: this.date_type,
      };
      this.$api
        .getFeeDetailslist(params)
        .then(res => {
          this.fee_list = res.list;
          this.fee_total = Number(res.total);
        })
        .finally(() => (this.feeTabLoading = false));
    },

    // 处理本月成本明细
    handleCostDetailData(source = []) {
      let list = [];
      source?.forEach((item, index) => {
        list.push({
          ...item,
        });
        item.list?.forEach((c_item, c_index) => {
          list[index][`${c_index + 1}`] = c_item;
        });
      });
      this.cost_detail_list = list;
    },

    /* API */
    // api-概况
    getFeeOverview() {
      this.loading = true;
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        date_type: this.date_type,
      };
      this.$api
        .getFeeOverview(params)
        .then(res => {
          this.overview_list = S.descToArrHandle(res.overview);
          this.handleCostDetailData(res.cost_detail_list);
        })
        .finally(() => (this.loading = false));
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
@import url('./style/common.less');
.header-text {
  &:after {
    content: '每日成本';
    width: fit-content;
    height: 20px;
    position: absolute;
    left: calc(~'50% + 100px');
    top: 10px;
    z-index: 3;
    font-size: 13px;
    color: #323232;
    font-weight: bold;
  }
}
.overview-spin {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.cost-card {
  position: relative;
  left: 0.5%;
  width: 19%;
  min-width: 19%;
  margin-right: 1%;
  margin-bottom: 16px;
  .cost-card-item {
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #ecedf0;
    padding: 4px;

    .cost-item-top {
      .cost-item-title {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-top: 16px;
        text-align: center;
        .title {
          display: flex;
          justify-content: center;
          gap: 4px;
        }
        .tip {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 16px;
          text-align: center;
          height: 16px;
        }
      }
      .cost-item-text {
        font-weight: 600;
        font-size: 22px;
        color: #333333;
        line-height: 30px;
        text-align: center;
      }
    }

    .cost-detail-box {
      margin-top: 40px;
      background: #f9fafb;
      border-radius: 2px;
      padding: 6px 12px;
      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .item-label {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 18px;
        }
        .item-value {
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          line-height: 18px;
          text-align: right;
        }
      }
    }
  }
  .btn-box {
    text-align: center;
    .record-btn {
      margin-top: 16px;
    }
  }
}
.change-img {
  margin-left: 4px;
  height: 12px;
  width: 12px;
}
.custom-tooltip {
  white-space: normal;
}

.remind-modal-context {
  text-align: center;
  padding: 20px;
  font-size: 14px;
  p {
    padding-bottom: 8px;
  }
}
</style>
