// common less

/* 头部吸顶区域 */
.head-block {
  padding: 20px 0px 16px 0px;
}

/* 最大的白块样式 */
.block {
  background: #FFFFFF;
  border-radius: 4px;
  padding: 20px;
}
.block-bg {
  background-size: 130px 130px;
  background-repeat: no-repeat;
  background-position: bottom right;
}
.block-mt16 {
  margin-top: 16px;
}
.block-title {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 20px;
}

/* 卡片，内边距，字体样式 */
.card-block {
  padding: 20px 6px 16px 40px;
  border: 1px solid #EAEAEA;
  background: #FFFFFF;
  border-radius: 4px;
  width: 19.2%;
  margin-right: 1%;
  min-height: 121px;
  &:nth-of-type(5n) {
    margin-right: 0px !important;
  }
}
/* 卡片悬浮阴影样式 */
.card-block--hover {
  &:hover {
    box-shadow: 0px 2px 10px rgba(108, 129, 166, 0.12);
  }
}
/* 卡片选中样式 */
.card-block--actived {
  border: 2px solid #1157E5;
}

.solid-card-block {
  padding: 20px 0px 16px 40px;
  background: #FFFFFF;
  width: 20%;
  border-bottom: 1px solid #EFEFEF;

  .solid-right-line {
    border-right: 1px solid #EFEFEF;
  }
  &:nth-of-type(5n) {
    .solid-right-line {
      border-right: 0px !important;
    }
  }
  &:nth-last-child(1) {
    .solid-right-line {
      border-right: 0px !important;
    }
  }
}

.solid-bottom--none {
  border-bottom: 0px !important;
}

.card-title {
  font-size: 12px;
  font-weight: 400;
  color: #444444;
  line-height: 17px;
}
.card-content {
  font-size: 20px;
  font-weight: 500;
  color: #000000;
  line-height: 29px;
  word-wrap:break-word;
  font-family: OPPOSans;
}
.card-tip {
  font-size: 12px;
  font-weight: 400;
  color: #AAAAAA;
  line-height: 17px;
}

/* 自定义tag默认和选中样式 */
.custom-tag--default {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid rgba(188, 195, 215, 0.8);
  padding: 6px 12px;
  color: #333333;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  cursor: pointer;
  &:nth-of-type(1) {
    margin-left: 0px !important;
  }
}
.custom-tag--actived {
  background: #1157E5 !important;
  color: #FFFFFF !important;
  border: 1px solid #1157E5;
}

/* tag悬浮变色 */
.custom-tag--hover {
  &:hover {
    border: 1px solid #1157E5 !important;
    color: #1157E5 !important;
  }
}

/* 默认盒子边框 */
.border--default {
  border-radius: 4px;
  border: 1px solid #F4F4F4;
}

/* 卡片展示数据 */
.card-show-block {
  padding: 28px 50px;
  border-radius: 4px;
  width: 100%;
  border: 1px solid #F4F4F4;


  .show-title {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    line-height: 20px;
  }

  .show-number {
    font-size: 24px;
    font-weight: 500;
    color: #000000;
    line-height: 29px;
  }
}
.card-show-block--small {
  padding: 16px 65px !important;
}

/* 悬浮手指 */
.cursor {
  cursor: pointer;
}

/* 分页 */
.page-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 通用的外边距和内边距 */
.mt6 {
  margin-top: 6px;
}
.mt7 {
  margin-top: 7px;
}
.mt9 {
  margin-top: 9px;
}
.mt12 {
  margin-top: 12px;
}
.mt16 {
  margin-top: 16px;
}
.mt26 {
  margin-top: 26px;
}
.mt30 {
  margin-top: 30px;
}

.mt32 {
  margin-top: 32px;
}

.mt50 {
  margin-top: 50px;
}

.mb16 {
  margin-bottom: 16px;
}

.ml6 {
  margin-left: 6px;
}
.ml10 {
  margin-left: 10px;
}
.ml16 {
  margin-left: 16px;
}
.ml20 {
  margin-left: 20px;
}
.ml30 {
  margin-left: 30px;
}
.ml50 {
  margin-left: 50px;
}

.mr16 {
  margin-right: 16px;
}
.mr20 {
  margin-right: 20px;
}
.pt0 {
  padding-top: 0px !important;
}
.pl60 {
  padding-left: 60px;
}
.pr30 {
  padding-right: 30px;
}

p{
  margin: 0px;
}
.helpIcon {
  width: 16px;
  height: 16px;
}
.height31 {
  height: 31px;
}
.width40 {
  width: 40%;
}
.width50 {
  width: 50%;
}
.width60 {
  width: 60%;
}
.width100 {
  width: 100%;
}
.height100 {
  height: 100%;
}
.svg-block20 {
  width: 20px;
  height: 20px;
  display: inline-block;
}
/* 小圆圈 */
.circle {
  border-radius: 50%;
  border: 2px solid red;
  width: 10px;
  height: 10px;
  display: inline-block;
}

/* 横向分割线 */
.transverse-line {
  width: 100%;
  height: 1px;
  background: #efefef;
  margin: 30px 0;
}
/* 右边框 */
.right-line {
  border-right: 1px solid #EFEFEF;
}

// 重写tooltip悬浮icon,icon不居中显示
.custom-tooltip {
  display: flex !important;
  justify-content: center;
  align-items: center;
}

/* 自定义table样式 */
.custom-table {
  ::v-deep .ivu-table {
    thead {
      th {
        background: #FFFFFF;
        color: #999999;
      }

      td {
        height: 40px;
      }
    }

    &::before {
      height: 0;
    }

    td {
      border-bottom: none;
      height: 40px;
    }

    th {
      border-bottom: none;
      height: 40px;
    }
  }
}

.custom-table-line {
  ::v-deep .ivu-table {
    thead {
      th {
        background: #FFFFFF;
        color: #999999;
      }

      td {
        height: 40px;
      }
    }

    &::before {
      height: 0;
    }

    td {
      height: 40px;
    }

    th {
      height: 40px;
    }
  }
}

/* 表格里面的前三图片大小样式 */
.top-three {
  > img {
    width: auto;
    height: 20px;
    margin: 0px;
  }
}

/* 自定义组件得标签页样式 */
::v-deep .custom-tabs {
  .ivu-tabs-tab-active {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #000000 !important;
    line-height: 22px !important;
  }
  .ivu-tabs-tab {
    font-size: 12px;
    font-weight: 400;
    color: #000000;
    line-height: 17px;
    padding: 8px 0;
  }

  .ivu-tabs-bar {
    border-bottom: 0px !important;
    margin-bottom: 0px !important;
  }
}

/* 小型tab切换  */
.custom-tabs-small {
  .custom-tabs-small--default {
    font-size: 12px;
    font-weight: 400;
    color: #000000;
    line-height: 17px;
    display: flex;
    align-items: flex-end;
  }
  .custom-tabs-small--actived {
    font-size: 14px !important;
    font-weight: 600 !important;
    line-height: 20px !important;
    color:#155BD4;
  }
}

// 带高亮条的标题
.light-title {
  font-weight: 600;
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  padding-left: 12px;
  position: relative;
  &:after {
    content: ' ';
    width: 4px;
    height: 18px;
    background: linear-gradient( 90deg, #155BD4 0%, #4988FD 100%);
    border-radius: 2px;
    position: absolute;
    left: 0px;
    top: 3px;
  }
}
