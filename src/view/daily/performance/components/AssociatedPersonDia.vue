<template>
  <div class="associated-wrapper">
    <Modal
      :value="visible"
      title="添加关联人"
      lock-scroll
      :mask-closable="false"
      :before-close="closeModal"
      @on-cancel="closeModal">
      <div class="content" style="padding-left: 40px;">
        <CheckboxGroup v-model="choosePeople" @on-change="groupChange" class="flex" style="margin-top: 20px;align-items:center;flex-wrap: wrap;">
          <Checkbox style="width: 42%;margin-bottom: 20px;"  :label="person_item.id" v-for="(person_item, person_index) in staffList" :key="person_index+'connect'">
            <p class="item flex">
              <span class="item_name" style="margin-right: 24px;">{{ person_item.name }}</span>
              <span class="item_position" style="color:#999;">{{ person_item.role }}</span>
            </p>
          </Checkbox>
        </CheckboxGroup>
      </div>
      <div class="footer" slot="footer">
        <Button type="default" @click="closeModal">取消</Button>
        <Button type="primary" @click="confirmAdd"  :loading="loading">确认关联人</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';
export default {
  name: 'AssociatedPersonDia',
  mixins: [],

  components: {},

  props: {
    visible: {
      type: Boolean,
      default: () => false
    },
    addPerson:{
      type: Function
    },
    clearCheckedStaffs:{
      type: Function
    },
    staffList: {
      type: Array,
      default: ()=>[]
    },
    checkedStaffs: {
      type: Array,
      default: ()=>[]
    }
  },

  data() {
    return {
      connetPersonList: [], // 所有关联人的列表
      choosePeople: [], // 选中的关联人
      loading: false,
    }
  },

  computed: {
    confirmDis(){
      return !Boolean(this.choosePeople.length)
    }
  },

  watch: {
    visible (val) {
      if (!val) {
        this.choosePeople = []
        this.loading = false
      }
    },
    checkedStaffs: {
      handler(val){
        console.log("-> %cval %o", "font-size: 15px", val)
        this.choosePeople = cloneDeep(val)
      }
    }
  },

  created() {
  },

  mounted() {
  },

  destroyed() {
  },

  methods: {
    closeModal(){
      this.$emit('update:visible',false)
      this.clearCheckedStaffs()
    },
    confirmAdd(){
      this.addPerson(this.choosePeople)
      this.closeModal()
    },

    groupChange (val) {
      console.log({val});
    },
  },
}
</script>

<style scoped lang="less">
.item {
  margin-left: 8px;
  width: 80%;
  &:hover  {
    color: #155BD4;
  }
}
</style>
<style scoped lang="less">
.ivu-checkbox-group-item {
  display: flex;
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0px;
  }
}
::v-deep .ivu-modal{
  .ivu-modal-body{
    height: 300px;
    overflow-y: auto;
  }
}
</style>
