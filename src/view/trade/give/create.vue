<template>
  <div class="detail-wrapper">
    <Row>
      <Col :span="16">
        <Form :label-width="200" label-colon :model="formData" v-if="!isRst">
          <FormItem label="用户姓名">
            <el-autocomplete
              ref="customRst"
              v-model="formData.name"
              :debounce="600"
              :popper-append-to-body="false"
              :fetch-suggestions="querySearchAsync"
              :trigger-on-focus="true"
              @blur="blur"
              placeholder="请输入用户姓名/手机号"
              @select="handleSelect"
            >
              <template slot-scope="{ item }">
                <div v-if="!item.empty" style="white-space: pre-wrap">
                  <span>{{ item.patient_name }}</span>
                  <span class="mr-8">{{ item.mobile }}</span>
                  <span v-if="item.show_staging_mobile === '1'" style="font-size: 12px; color: #999"
                    >暂存 {{ item.staging_mobile }}</span
                  >
                </div>
                <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>
                  <p class="flex flex-c">
                    <span>{{ formData.name }}</span>
                    <span class="tip">尚无该用户</span>
                  </p>
                  <a>创建用户</a>
                </div>
              </template>
            </el-autocomplete>
          </FormItem>

          <FormItem label="用户手机">
            <Input type="text" disabled v-model="formData.mobile" />
          </FormItem>

          <FormItem label="储值活动" v-if="!isRst">
            <Select v-model="formData.recharge_activity_id" placeholder="请选择储值活动" style="width: 100%">
              <Option v-for="item in activityList" :key="item.id" :value="item.id" @click.native="getActiveItems(item)"
                >{{ item.name }}
              </Option>
            </Select>

            <div class="mt10" v-show="formData.recharge_activity_id">
              <p class="label">充值赠送规则：{{ type_desc }}</p>
              <!-- <p v-if="give === 'not_give'">-</p> -->

              <div class="text" v-if="give === 'ladder_give'">
                <ladder-give-detail :giveList="giveList"></ladder-give-detail>
                <!--                <p v-for="(item, index) in giveList" :key="index">-->
                <!--                  第{{ customIndex(index) }}档：用户实充{{ item.real_fee }}元，送{{ item.given_fee }}元余额-->
                <!--                  <br />-->
                <!--                </p>-->
              </div>

              <div class="text" v-if="give === 'full_give'">
                <p>用户充值每满 {{ real_fee }} 元，送 {{ given_fee }} 元</p>
                <full-give-detail :full_info="full_info"></full-give-detail>
              </div>
            </div>
          </FormItem>

          <FormItem label="实充金额">
            <InputNumber
              v-model="formData.total_fee"
              :precision="2"
              :active-change="false"
              placeholder="请输入"
              :min="0"
              class="ml4 mr4"
              style="width: 100%"
            />
          </FormItem>

          <FormItem label="赠送金额" v-if="!isRst">
            <p v-text-format.number="custom_given_fee"></p>
          </FormItem>

          <FormItem label="储值金额">
            <p v-text-format.number="custom_total_fee"></p>
          </FormItem>

          <!--          <FormItem label="备注">-->
          <!--            <Input type="textarea" maxlength="300" show-word-limit :autosize="{minRows: 2,maxRows: 5}" v-model="formData.remark"/>-->
          <!--          </FormItem>-->
        </Form>
        <Form :model="formData" v-if="isRst" @submit.native.prevent>
          <div class="sub-title">储值用户</div>
          <FormItem>
            <div class="search-box" ref="searchBoxRef">
              <div class="current-user" v-if="current_user_info.uid">
                <div class="current-user-left">
                  <div
                    class="avatar-box"
                    :class="{ 'vip-avatar-box': current_user_info?.vip_info?.length > 0 }"
                    :style="{ borderColor: getVipBorderColor(current_user_info.vip_info) }"
                  >
                    <img
                      v-if="current_user_info?.vip_info?.length"
                      class="vip-icon"
                      :src="getVipIcon(current_user_info.vip_info)"
                    />
                    <img
                      class="avatar"
                      :src="
                        current_user_info.avatar
                          | imageStyle(
                            'B.w300',
                            'https://img-sn-i01s-cdn.rsjxx.com/image/2024/1218/095042_17701.png-B.w300'
                          )
                      "
                    />
                  </div>

                  <div class="user-info">
                    <div class="user-info-top">
                      <div class="user-info-name">{{ current_user_info.real_name }}</div>
                      <div class="user-info-sex">
                        <span>{{ current_user_info.sex_text }}</span>
                        <span v-if="current_user_info.sex_text && current_user_info.age">｜</span>
                        <span>{{ current_user_info.age ? `${current_user_info.age}岁` : current_user_info.age }}</span>
                      </div>
                    </div>
                    <div class="user-info-mobile-box">
                      <div class="info-mobile">{{ current_user_info.mobile }}</div>
                      <div class="info-stage-mobile" v-if="current_user_info.show_staging_mobile == '1'">
                        <span class="stage-tag">暂存</span>
                        <span class="stage-mobile">{{ current_user_info.staging_mobile }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="delete-user-icon-box">
                  <Tooltip content="移除" placement="top">
                    <img
                      class="delete-user-icon"
                      @click="deleteUserInfo"
                      src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/102709_78163.png"
                    />
                  </Tooltip>
                </div>
              </div>

              <el-autocomplete
                v-else
                class="custom-user-autocomplete"
                ref="custom"
                v-model="nickname"
                :debounce="600"
                :popper-append-to-body="false"
                :fetch-suggestions="querySearchAsync"
                :trigger-on-focus="true"
                @blur="rstBlur"
                placeholder="输入用户姓名、手机号搜索"
                @select="handleAutoSelect"
              >
                <template slot-scope="{ item }">
                  <div class="autocomplete" v-if="!item.empty" style="white-space: pre-wrap">
                    <div
                      class="avatar-box"
                      :class="{ 'vip-avatar-box': item.vip_info.length > 0 }"
                      :style="{ borderColor: getVipBorderColor(item.vip_info) }"
                    >
                      <img v-if="item.vip_info.length > 0" class="vip-icon" :src="getVipIcon(item.vip_info)" />
                      <img
                        class="avatar-icon"
                        :src="
                          item.avatar
                            | imageStyle(
                              'B.w300',
                              'https://img-sn-i01s-cdn.rsjxx.com/image/2024/1218/095042_17701.png-B.w300'
                            )
                        "
                      />
                    </div>
                    <div class="info-content">
                      <span class="name">{{ item.patient_name }}</span>
                      <span class="info">
                        <span>{{ item.sex_text }}</span>
                        <span v-if="item.age && item.sex_text"> | </span>
                        <span>{{ item.age ? `${item.age}岁` : '' }}</span>
                      </span>
                      <span class="mobile">{{ item.mobile }}</span>
                      <span class="stage-mobile-box" v-if="item.show_staging_mobile === '1'">
                        <span class="stage-icon">暂存</span>
                        <span class="stage-mobile">{{ item.staging_mobile }}</span>
                      </span>
                    </div>
                  </div>
                  <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>
                    <p class="flex flex-c">
                      <span>{{ nickname }}</span>
                      <span class="tip">尚无该用户</span>
                    </p>
                    <a>创建用户</a>
                  </div>
                </template>
              </el-autocomplete>
            </div>
          </FormItem>
          <div class="sub-title">储值方案</div>
          <div class="scheme-box">
            <div
              class="scheme-item"
              :class="{ active: currentScheme === item.config_id }"
              v-for="(item, index) in rechargeConfig"
              :key="index"
              @click="changeScheme(item)"
            >
              <img :src="item.config_present_img" class="scheme-present" />
              <div class="scheme-title">储值</div>
              <div class="scheme-price">￥{{ item.config_price }} <span class="price-unit">元</span></div>
              <div class="scheme-coupon">{{ item.desc }}</div>
              <div class="scheme-desc">
                <img src="https://img-sn01.rsjxx.com/image/2025/0614/165219_22013.png" alt="" class="enjoy-icon" />
                充值赠送券包含
              </div>
              <div class="scheme-coupon-list">
                <div class="scheme-coupon-item" v-for="(coupon_item, coupon_index) in item.info" :key="coupon_index">
                  <div class="scheme-left">
                    <div class="coupon-name">{{ coupon_item.name }}</div>
                    <div class="coupon-limit">
                      满{{ coupon_item.type_rule?.full_amount }}元可用
                      <Tooltip content="限商品、理疗服务可用" placement="top">
                        <div style="margin-left: 5px">></div>
                      </Tooltip>
                    </div>
                  </div>
                  <div class="scheme-right">{{ coupon_item.num }}</div>
                </div>
              </div>
            </div>
          </div>
        </Form>
      </Col>
    </Row>

    <div style="height: 35px">
      <div class="fixed-bottom-wrapper">
        <back-button>取消</back-button>
        <Button class="ml10" @click="submit" type="primary" :loading="submitLoading">提交</Button>
      </div>
    </div>
    <!-- 支付弹窗 -->
    <!--    <PayDialog :payVisible.sync="payVisible" :order_id="order_id"></PayDialog>-->

    <!-- v2版本支持优惠券的支付弹窗 -->
    <pay-modal v-model="payVisible" :order_id="order_id" :isShowOfflinePay="!isRst"></pay-modal>

    <!-- 创建用户 -->
    <create-user-modal
      :showCreateInfo="optionChange"
      :source-list="sourceList"
      :visible.sync="consumerVisibleDia"
      :level-list="levelList"
      :name="creatName"
    ></create-user-modal>

    <!--未通过主体验证 拦截收款弹窗-->
    <auth-warning-modal ref="authWarningRef"></auth-warning-modal>
  </div>
</template>

<script>
import S from '@/libs/util';
import { cloneDeep } from 'lodash-es';
import { $operator } from '@/libs/operation';
// import PayDialog from './components/pay-dialog';
import PayModal from './components/payModal.vue';
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';
import ladderGiveDetail from './components/ladderGiveDetail.vue';
import fullGiveDetail from './components/fullGiveDetail.vue';
import { isRstClinic } from '@/libs/runtime';
import AuthWarningModal from '@/components/AuthWarning/AuthWarningModal.vue';

const regRole = tel => {
  let flag;
  let reg = /^1[3456789]\d{9}$/;
  flag = reg.test(tel);
  return flag;
};
export default {
  name: 'create',
  components: {
    CreateUserModal,
    // PayDialog,
    PayModal,
    ladderGiveDetail,
    fullGiveDetail,
    AuthWarningModal,
  },
  mixins: [],
  props: {},
  data() {
    return {
      formData: {
        uid: '', // 选中的用户,如果为自建用户，则为空
        mobile: '', // 手机号
        name: '', // 用户名
        recharge_activity_id: '', // 储值活动
        total_fee: null, // 实充金额
      },

      activityList: [], // 储值活动枚举
      give: '', // 充值赠送规则
      type_desc: '',
      giveList: [], // 规则
      full_info: {}, // 满赠
      real_fee: '',
      given_fee: '',
      custom_given_fee: null,

      submitLoading: false,

      order_id: '', // 用于支付的id
      payVisible: false,

      userList: [], // 搜索得出的用户信息列表
      searchTimes: 0,

      // 创建用户
      name: '',
      creatName: '',
      consumerVisibleDia: false, // 创建用户的弹窗
      sourceList: [],
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],

      current_user_info: {},
      nickname: '',
      rechargeConfig: [
        {
          config_id: '1',
          config_price: '1000',
          config_present_img: 'https://img-sn01.rsjxx.com/image/2025/0614/164906_98278.png',
          desc: '',
          info: [],
        },
        {
          config_id: '2',
          config_price: '2000',
          config_present_img: 'https://img-sn01.rsjxx.com/image/2025/0614/171606_465.png',
          desc: '',
          info: [],
        },
        {
          config_id: '3',
          config_price: '3000',
          config_present_img: 'https://img-sn01.rsjxx.com/image/2025/0716/135131_35719.png',
          desc: '',
          info: [],
        },
        {
          config_id: '4',
          config_price: '5000',
          config_present_img: 'https://img-sn01.rsjxx.com/image/2025/0716/135131_86004.png',
          desc: '',
          info: [],
        },
      ],
      currentScheme: '1',
    };
  },
  computed: {
    isRst() {
      return isRstClinic();
    },
    customIndex() {
      return index => {
        switch (index) {
          case 0:
            return '一';
          case 1:
            return '二';
          case 2:
            return '三';
          case 3:
            return '四';
          case 4:
            return '五';
          case 5:
            return '六';
          case 6:
            return '七';
          case 7:
            return '八';
          case 8:
            return '九';
          case 9:
            return '十';
        }
      };
    },

    // 储值金额
    custom_total_fee() {
      return S.mathAdd(Number(this.custom_given_fee), Number(this.formData.total_fee));
    },

    getVipBorderColor() {
      return vipInfo => {
        return vipInfo?.findIndex(item => item.user_type === '3') > -1 ? '#FAD88F' : '#B0C3DD';
      };
    },

    getVipIcon() {
      return vipInfo => {
        // 980会员与榕粉会员合并使用相同icon
        let isVip = vipInfo?.findIndex(item => item.user_type === '1' || item.user_type === '4') > -1;
        if (isVip) {
          return require(`@/assets/image/order/vip_980.png`);
        }
        let is9800Vip = vipInfo?.findIndex(item => item.user_type === '3') > -1;
        if (is9800Vip) {
          return require(`@/assets/image/order/vip_9800.png`);
        }

        return '';
      };
    },
  },
  watch: {
    'formData.total_fee': {
      immediate: true,
      deep: true,
      handler(val) {
        this.autoSet();
      },
    },
  },
  created() {
    this.init();
    if (this.$route.query.uid) {
      this.handleDefaultUID(this.$route.query.uid);
    }
  },
  mounted() {},
  methods: {
    init() {
      this.getArrivalOptions();
      // 获取活动列表
      this.getROrderActivities();
      if (this.isRst) {
        this.getRstRechargeConfig();
      }
    },

    // 选中指定的用户
    handleDefaultUID(uid = '') {
      this.getUserList({ uid }).then(res => {
        if (this.isRst) {
          this.handleAutoSelect(res.users[0] || {});
        } else {
          this.handleSelect(res.users[0] || {});
        }
      });
    },

    autoSet() {
      let type = this.give;

      if (type == 'not_give') {
        this.custom_given_fee = '';
      }

      if (type == 'ladder_give') {
        this.setCustomGivenFee();
      }

      if (type == 'full_give') {
        let result = $operator.divide(Number(this.formData.total_fee), Number(this.real_fee), 8);
        this.custom_given_fee = $operator.multiply(Math.floor(result), this.given_fee);
      }
    },

    getActiveItems(item) {
      let recharge = item.recharge;
      this.give = recharge.type;
      this.type_desc = recharge.type_desc;

      if (recharge.type == 'ladder_give') {
        this.giveList = recharge.rule;
      }

      if (recharge.type == 'full_give') {
        this.real_fee = recharge.rule && recharge.rule.real_fee;
        this.given_fee = recharge.rule && recharge.rule.given_fee;
        this.full_info = recharge.rule;
      }

      this.autoSet();
    },

    setCustomGivenFee() {
      console.log('this.formData.total_fee', this.formData.total_fee);
      let list = this.giveList;

      list.some((item, index) => {
        let max_fee = Number(this.giveList[this.giveList.length - 1].real_fee);
        let min_fee = Number(this.giveList[0].real_fee);

        if (min_fee > Number(this.formData.total_fee)) {
          this.custom_given_fee = '';
          return true;
        }

        if (Number(this.formData.total_fee) >= max_fee) {
          this.custom_given_fee = this.giveList[this.giveList.length - 1].given_fee;
          return true;
        }
        if (
          Number(item.real_fee) <= Number(this.formData.total_fee) &&
          Number(this.formData.total_fee) < Number(this.giveList[index + 1].real_fee)
        ) {
          this.custom_given_fee = item.given_fee;
          return true;
        }
      });
    },

    cancel() {
      this.$router.push('/trade/give/list');
    },
    // 提交
    async submit() {
      if (this.isSubmit()) {
        this.submitLoading = true;
        try {
          await this.$refs.authWarningRef.verify();
          const res = await this.$api.getROrderCreate({
            ...this.formData,
            gift_config_id: this.isRst ? this.currentScheme : '',
          });
          this.payVisible = true;
          this.order_id = res.id;
        } catch (error) {
          console.error('err', error);
        } finally {
          this.submitLoading = false;
        }
      }
    },

    /**
     * @description:远程搜索用户信息
     * */
    querySearchAsync(keyword, cb) {
      this.creatName = cloneDeep(keyword);
      if (this.searchTimes && !keyword) {
        cb(this.userList);
      } else {
        let copyName = this.name || 'none';
        if (keyword !== copyName) {
          this.getUserList({ search: keyword }, cb);
        } else {
          cb(this.userList);
        }
        this.getUserList({ search: keyword }, cb);
      }
    },

    // 点击创建用户，显示弹窗
    creatConsumer(val) {
      this.consumerVisibleDia = true;
      this.$refs.custom.close();
    },

    // 创建用户返回的数据
    optionChange(item) {
      this.name = item.patient_name;
      this.formData.mobile = item.mobile;
      this.formData.name = item.patient_name;
      this.formData.uid = item.uid;
    },
    handleSelect(item) {
      this.name = item.patient_name;
      this.formData.mobile = item.mobile;
      this.formData.name = item.patient_name;
      this.formData.uid = item.uid;
    },

    // 当搜索的人不存在时,失焦清除绑定数据,不允许自建
    blur() {
      setTimeout(() => {
        if (!this.name) {
          if (!this.consumerVisibleDia) {
            this.formData.name = '';
            this.$refs.custom.getData();
          }
        } else {
          this.formData.name = this.name;
        }
      }, 200);
    },

    // 提交之前对页面必填数据判断
    isSubmit() {
      // if (!regRole(this.formData.mobile)) {
      //   this.$Message.error('请输入正确的手机号')
      //   return false
      // }

      if (!this.formData.name) {
        this.$Message.error('用户姓名不可为空');
        return false;
      }

      if (!this.isRst && !this.formData.recharge_activity_id) {
        this.$Message.error('储值活动不可为空');
        return false;
      }

      if (!this.formData.total_fee) {
        this.$Message.error('实充金额不可为空');
        return false;
      }

      return true;
    },

    // api-获取用户列表-用户手机号带出用户信息
    getUserList({ search = '', uid = '' }, cb) {
      return new Promise(resolve => {
        let params = {
          page: 1,
          pageSize: 20,
          search,
          uid: search ? '' : uid,
        };
        this.searchTimes++;
        if (search) {
          this.searchTimes = 0;
        }
        this.$api.getUserList(params).then(res => {
          resolve(res);
          // 获取用户数据
          this.handleUserList(res.users, cb);
        });
      });
    },

    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      typeof cb === 'function' && cb(data);
    },

    /* api-获取活动列表 */
    getROrderActivities() {
      this.$api.getROrderActivities().then(res => {
        this.activityList = res.list;
      });
    },

    // api-获取options
    getArrivalOptions() {
      this.$api.getArrivalOptions().then(res => {
        // 用户来源
        this.sourceList = S.descToArrHandle(res.userFromDesc);
      });
    },

    deleteUserInfo() {
      this.current_user_info = {};
      this.formData.name = '';
      this.formData.mobile = '';
      this.formData.uid = '';
      this.getUserList({ search: '' });
    },

    handleAutoSelect(item) {
      this.current_user_info = item;
      this.formData.name = item.patient_name;
      this.formData.mobile = item.mobile;
      this.formData.uid = item.uid;
    },
    getRstRechargeConfig() {
      this.$api.getRstRechargeConfig().then(res => {
        this.rechargeConfig = this.rechargeConfig.map(item => {
          res.config.forEach(config_item => {
            if (item.config_id === config_item.config_id) {
              item.desc = config_item.desc;
              item.info = config_item.info;
            }
          });

          return item;
        });
        this.changeScheme(this.rechargeConfig[0]);
      });
    },
    rstBlur() {
      // customRst
    },
    changeScheme(item) {
      this.currentScheme = item.config_id;
      this.formData.total_fee = item.config_price;
    },
  },
  filters: {},
};
</script>

<style lang="less" scoped>
p {
  margin: 0;
}

.label {
  // width: 80px;
  // text-align: right;
  // margin-bottom: 16px;
  font-size: 12px;
  color: #000;
}

.mt10 {
  margin-top: 10px;
}

.mr-8 {
  margin: 0 8px;
}

// deep  components style
.ivu-date-picker,
.el-autocomplete {
  width: 100%;
}

::v-deep .el-input__inner {
  height: 32px;
  font-size: 12px;
  padding: 4px 7px;
  border: 1px solid #bcc3d7;
  border-radius: 2px;

  &:hover {
    border-color: #447cdd;
  }

  &:focus {
    border-color: #447cdd;
    outline: 0;
    // box-shadow: 0 0 0 2px rgb(21 91 212 / 20%);
    box-shadow: 0 0 0 2px rgba(68, 124, 221, 0.2);
  }
}
.sub-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 15px;
}

.search-box {
  //margin-top: -10px;
  min-height: 64px;
  min-width: fit-content;
  width: 100%;
  display: flex;
  //justify-content: center;
  align-items: center;
  .current-user {
    background: #f9fafb;
    border: 1px solid #d2d4d6;
    width: 60%;
    min-width: 400px;
    //box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .current-user-left {
      display: flex;
      align-items: center;

      .avatar-box {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        position: relative;
        box-sizing: content-box;
        //border: 1px solid #EBEDF0;
        //background: #EBEDF0;
        display: flex;
        align-items: center;
        justify-content: center;
        .vip-icon {
          width: 34px;
          min-width: 30px;
          height: 14px;
          position: absolute;
          bottom: -5px;
          left: 0;
        }
        .avatar {
          width: 36px;
          min-width: 36px;
          height: 36px;
          border-radius: 50%;
        }
      }
      .vip-avatar-box {
        border: 2px solid;
      }

      .user-info {
        margin-left: 10px;

        .user-info-top {
          display: flex;
          align-items: center;
          .user-info-name {
            font-weight: 600;
            font-size: 16px;
            color: #303133;
            //line-height: 24px;
          }

          .user-info-sex {
            margin-left: 12px;
            font-weight: 400;
            font-size: 12px;
            color: #909399;
            //line-height: 18px;
          }
        }

        .user-info-mobile-box {
          display: flex;
          align-items: center;
          margin-top: 4px;

          .info-mobile {
            font-weight: 400;
            font-size: 13px;
            color: #303133;
            line-height: 20px;
          }

          .info-stage-mobile {
            margin-left: 20px;
            font-weight: 400;
            font-size: 13px;
            color: #909399;
            line-height: 20px;
            display: flex;
            align-items: center;

            .stage-tag {
              background: #fff3df;
              border-radius: 2px;
              padding: 1px 4px;
              font-weight: 400;
              font-size: 12px;
              color: #ffa300;
              line-height: 18px;
              min-width: fit-content;
            }

            .stage-mobile {
              margin-left: 6px;
              font-weight: 400;
              font-size: 13px;
              color: #909399;
              line-height: 20px;
            }
          }
        }
      }
    }
    .delete-user-icon {
      display: none;
      width: 16px;
      height: 16px;
      cursor: pointer;
    }

    .delete-user-icon-box {
      background: url('https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0905/165420_32644.png') no-repeat;
      background-size: 20px 20px;
      width: 16px;
      height: 16px;
      cursor: pointer;
      &:hover {
        .delete-user-icon {
          display: block;
        }
      }
    }
  }

  .autocomplete {
    display: flex;
    align-items: center;
    padding: 8px 0px;
    .avatar-box {
      box-sizing: content-box;
      width: 30px;
      min-width: 30px;
      height: 30px;
      //background: #D8D8D8;
      border-radius: 50%;
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      .vip-icon {
        width: 34px;
        min-width: 30px;
        height: 14px;
        position: absolute;
        bottom: -6px;
        left: -4px;
        z-index: 2;
      }
      .avatar-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        position: absolute;
      }
    }
    .vip-avatar-box {
      border: 1px solid;
    }

    .info-content {
      display: flex;
      margin-top: 5px;
      align-items: center;
      flex-wrap: wrap;
      .name {
        margin-left: 16px;
        font-weight: 600;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
      }

      .info {
        margin-left: 12px;
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 18px;
      }

      .mobile {
        margin-left: 12px;
        font-weight: 400;
        font-size: 13px;
        color: #606266;
        line-height: 18px;
      }
      .stage-mobile-box {
        margin-left: 12px;
        .stage-icon {
          padding: 1px 4px;
          background: #fff3df;
          border-radius: 2px;
          font-weight: 400;
          font-size: 12px;
          color: #ffa300;
          line-height: 18px;
          transform: scale(0.8);
        }
        .stage-mobile {
          margin-left: 6px;
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 18px;
        }
      }
    }
  }
}
// 搜索用户的样式优化
::v-deep .custom-user-autocomplete {
  width: 80%;
  min-width: 400px;
  .el-input__inner {
    height: 40px;
    padding: 0 7px;
    //border-color: #bbb;
    font-size: 12px;
    border-radius: 4px;
  }

  .is-disabled {
    .el-input__inner {
      background: #f3f3f3;
      color: #ccc;
    }
  }
}

.scheme-box {
  display: flex;
  .scheme-item {
    position: relative;
    padding: 28px 10px;

    border-radius: 4px;
    border: 1px solid #e5e5e5;
    min-width: 170px;
    min-height: 442px;
    margin-right: 16px;
    cursor: pointer;
    &:hover {
      background-color: #f5f6f8;
    }

    .scheme-present {
      position: absolute;
      top: -8px;
      left: -1px;
      width: 64px;
      height: 21px;
    }
    .scheme-title {
      font-weight: 600;
      font-size: 16px;
      color: #1b1b1b;
      margin-bottom: 18px;
      text-align: center;
    }
    .scheme-price {
      font-weight: 600;
      font-size: 22px;
      color: #1b1b1b;
      text-align: center;
      margin-bottom: 10px;
      .price-unit {
        font-weight: 400;
        font-size: 10px;
        color: #333333;
      }
    }
    .scheme-coupon {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      margin-bottom: 30px;
      text-align: center;
    }
    .scheme-desc {
      display: flex;
      align-items: center;
      background: linear-gradient(90deg, #fff9ef 0%, #feedef 100%);
      border-radius: 4px;
      border: 1px solid #ffdece;
      padding: 5px;
      margin-bottom: 15px;
      .enjoy-icon {
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
    }
    .scheme-coupon-item {
      display: flex;
      justify-content: space-between;
      border-top: 1px dashed #d6d6d6;
      padding: 10px 0;
      //margin-bottom: 10px;
      //margin-top: 10px;
      //&:nth-child(n + 1) {
      //  border-top: 1px dashed #d6d6d6;
      //}
      &:first-child {
        border-top: 0;
        padding-top: 0;
      }

      .scheme-left {
        .coupon-name {
          font-weight: 400;
          font-size: 14px;
          color: #333333;
        }
        .coupon-limit {
          width: 100%;
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
        }
      }
      .scheme-right {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }
  }
  .active {
    border: 1px solid #155bd4;
    background-color: rgba(21, 91, 212, 0.1) !important;
    .scheme-coupon {
      color: #155bd4;
    }
  }
}
</style>
