<!--  -->
<template>
  <div class="create-wrapper">
    <div style="width: 60%">
      <Form :label-width="170" :model="formData" ref="formData" :rules="formDataRules">
        <FormItem label="用户姓名">
          <el-autocomplete
            ref="custom"
            v-model="nickname"
            :debounce="600"
            :popper-append-to-body="false"
            :fetch-suggestions="querySearchAsync"
            :trigger-on-focus="true"
            @blur="blur"
            placeholder="请输入用户姓名/手机号"
            @select="handleSelect"
          >
            <template slot-scope="{ item }">
              <div v-if="!item.empty" style="white-space: pre-wrap">
                <span>{{ item.patient_name }}</span>
                <span class="mr-8">{{ item.mobile }}</span>
                <span v-if="item.show_staging_mobile === '1'" style="font-size: 12px; color: #999"
                  >暂存 {{ item.staging_mobile }}</span
                >
              </div>
              <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>
                <p class="flex flex-c">
                  <span>{{ nickname }}</span>
                  <span class="tip">尚无该用户</span>
                </p>
                <a>创建用户</a>
              </div>
            </template>
          </el-autocomplete>
        </FormItem>

        <FormItem label="用户手机">
          <Input type="text" disabled v-model="mobile" />
        </FormItem>

        <FormItem label="订单类型:">
          <RadioGroup v-model="goods_type" @on-change="radioChange">
            <Radio label="10">实体商品订单</Radio>
            <Radio label="15">虚拟商品订单</Radio>
            <Radio label="25">通兑券订单</Radio>
            <Radio label="20" v-if="canUsePackage">套餐商品</Radio>
          </RadioGroup>
          <div v-if="goods_type === '25'" style="color: red; font-size: 12px">
            通兑券商品由于其特殊性，每个订单只允许购买1件。
          </div>
        </FormItem>

        <FormItem label="购买商品:">
          <Select
            ref="goodSelect"
            v-model="good"
            filterable
            clearable
            :placeholder="orderPlaceholder"
            :remote-method="remoteGood"
            @on-select="goodsOptionChange"
            @on-open-change="openChange"
          >
            <Option v-for="(item, key) in goods_list" :value="JSON.stringify(item)" :key="key">
              <span>{{ item.name }}</span>
              <span class="tip" v-show="item.source_platform_text">（{{ item.source_platform_text }}）</span>
            </Option>
          </Select>
        </FormItem>

        <FormItem label="">
          <div class="talbe" v-show="add_goods.length">
            <Table
              border
              :columns="columns"
              :data="add_goods"
              row-key="id"
              :indent-size="100"
              show-summary
              :summary-method="handleSummary"
              class="tc-table-style"
              :row-class-name="rowClassName"
            >
              <template slot-scope="{ row }" slot="name">
                <div>
                  <span>
                    <span>{{ row.name }}</span>
                    <span v-show="row.isChild">
                      <span v-show="row.goods_type === '10'">(实物)</span>
                      <span v-show="row.goods_type === '15'">(虚拟)</span>
                    </span>
                  </span>
                  <p class="subName">{{ row.subName }}</p>
                </div>
              </template>

              <template slot-scope="{ row }" slot="source_platform_text">
                <span>{{ row.source_platform_text || '-' }} </span>
              </template>

              <template slot-scope="{ row }" slot="price">
                <span v-if="row.isChild">-</span>
                <span v-else> ￥{{ row.price || 0 }} </span>
              </template>

              <template slot-scope="{ row }" slot="stored_price">
                <span v-if="row.isChild">-</span>
                <span v-else>
                  {{
                    row.is_recharge_buy === 'yes'
                      ? row.stored_price
                        ? `￥${row.stored_price}`
                        : '-'
                      : '不支持使用储值余额购买'
                  }}
                </span>
              </template>
              <template slot-scope="{ row, index }" slot="num">
                <span v-if="row.isChild" style="margin-left: 5px">{{ taocanChildNum(row) }}</span>
                <Input-Number
                  v-else
                  v-model="row.num"
                  :min="1"
                  placeholder="请输入购买数量"
                  :precision="0"
                  @on-blur="blur(index)"
                  @on-change="numberChange($event, index)"
                  style="width: 120px"
                  :disabled="goods_type === '25'"
                />
                <!-- @keyup.native="banNegative($event, index)" -->
              </template>

              <template slot-scope="{ row }" slot="total_price">
                <span v-if="row.isChild">-</span>
                <span v-else>￥{{ row.total_price || 0 }}</span>
              </template>

              <template slot-scope="{ row, index }" slot="total_stored_price">
                <span v-if="row.isChild">-</span>
                <span v-else>{{
                  row.is_recharge_buy === 'yes' ? `￥${row.total_stored_price || 0}` : '不支持使用储值余额购买'
                }}</span>
              </template>

              <template slot-scope="{ row, index }" slot="action">
                <span v-if="row.isChild">-</span>
                <Poptip v-else confirm title="是否确认移除?" @on-ok="removeGood(index)">
                  <a>移除</a>
                </Poptip>
              </template>
            </Table>
          </div>
        </FormItem>

        <FormItem label="参与活动:" v-if="coupon_pay === '1'">
          <Select
            v-model="activie_ticket"
            clearable
            :not-found-text="activeLoading ? '' : '无匹配数据'"
            :loading="activeLoading"
            @on-open-change="activeChange($event, true)"
          >
            <Option v-for="item in activie_ticket_list" :value="item.id" :label="item.name" :key="item.id">
              <div>
                <div class="mb-5">{{ item.name }}</div>
                <div class="active-desc">购买【{{ item.goods_name }}】可获得优惠券</div>
              </div>
            </Option>
          </Select>
          <div v-if="activie_ticket" class="active-desc">{{ active_desc }}</div>
        </FormItem>

        <FormItem label="配送方式:">
          <RadioGroup v-model="distributionType">
            <Radio label="1" v-show="isNeedLogistics">物流发货</Radio>
            <Radio label="0">无需配送</Radio>
          </RadioGroup>
        </FormItem>

        <div v-show="distributionType === '1'">
          <FormItem label="收件人姓名:" :prop="distributionType === '1' ? 'name' : ''">
            <Input v-model="formData.name" placeholder="输入收件人姓名"></Input>
          </FormItem>

          <FormItem label="收件人手机号:" :prop="distributionType === '1' ? 'mobile' : ''">
            <Input v-model.trim="formData.mobile" placeholder="输入收件人手机号" :maxlength="11" />
          </FormItem>

          <FormItem label="收件地址" :prop="distributionType === '1' ? 'selectedAddress' : ''">
            <div class="flex">
              <div class="address flex-1">
                <el-cascader
                  v-model="formData.selectedAddress"
                  :options="options"
                  clearable
                  placeholder="请选择收件地址"
                  size="small"
                  popper-class="address-com"
                  style="width: 100%"
                  @change="regionChange"
                >
                </el-cascader>
              </div>
              <div class="flex-1 ml10">
                <Input v-model.trim="formData.consignee_info.address.detail" placeholder="详细地址"></Input>
              </div>
            </div>
          </FormItem>
        </div>
      </Form>
    </div>
    <div style="height: 60px"></div>
    <div class="fixed-bottom-wrapper">
      <Button class="btnStyle" @click="cancel">取消</Button>
      <Button class="btnStyle" type="primary" style="margin-left: 16px" @click="submit">提交</Button>
    </div>
    <!-- 具有多个sku的商品时，弹窗  -->
    <EntityOrderDialog
      :visible.sync="entityOrderVisible"
      v-model="entityOrderObject"
      @checkedList="checkedList"
    ></EntityOrderDialog>
    <!-- 支付弹窗 -->
    <!--    <PayDialog :payVisible.sync="payVisible" :order_id="order_id" :balance="balance"></PayDialog>-->
    <!-- 创建用户 -->
    <create-user-modal
      :showCreateInfo="optionChange"
      :source-list="sourceList"
      :visible.sync="consumerVisibleDia"
      :level-list="levelList"
      :name="creatName"
    ></create-user-modal>

    <!-- v2版本支持优惠券的支付弹窗 -->
<!--    <pay-modal v-model="payVisible" :order_id="order_id"></pay-modal>-->
    <k-pay-dialog v-model="payVisible" :order_id="order_id"></k-pay-dialog>

    <choose-tip-modal v-model="chooseTipVisible" @success="confirmSubmit"></choose-tip-modal>
  </div>
</template>

<script>
// import PayDialog from './components/pay-dialog'
import { getClinicid } from '@/libs/runtime';
import PayModal from './components/payModal.vue';
import EntityOrderDialog from './components/entity-order-dialog';
import { CodeToText, regionData, TextToCode } from '@/libs/chinaMap';
import S from '@/libs/util';
import { cloneDeep } from 'lodash-es';
// import CreateConsumerDia from '@/components/k-create-user';
import ChooseTipModal from './components/ChooseTipModal.vue';
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';
import { $operator } from '@/libs/operation';
import { canUsePackageProd } from '@/libs/runtime';
import KPayDialog from "@/components/k-pay-dialog/index.vue";
const regRole = tel => {
  let flag;
  let reg = /^1[3456789]\d{9}$/;
  flag = reg.test(tel);
  return flag;
};
const phoneValidate = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入收件人手机号'));
  }
  if (!regRole(value)) {
    callback(new Error('请输入正确的手机号'));
  } else {
    callback();
  }
};

const addressValidate = (rule, value, callback) => {
  let detail = rule.detail();
  if (value[0] == undefined) {
    callback(new Error('请选择收件地址'));
  } else if (detail === '') {
    callback(new Error('请输入详细地址'));
  } else {
    callback();
  }
};

export default {
  name: 'create',
  components: {
    CreateUserModal,
    // PayDialog,
    // PayModal,
    KPayDialog,
    EntityOrderDialog,
    ChooseTipModal,
  },
  mixins: [],
  props: {},
  data() {
    return {
      options: regionData,
      userList: [], // 用户数据
      mobile: '', // 用户手机号
      nickname: '', //用户姓名
      uid: '', // 选中的用户,如果为自建用户，则为空
      goods_list: [], // 服务商品list
      good: '',
      balance: '', // 余额
      goods_type: '10', // 订单类型 10实体商品15虚拟商品
      distributionType: '1', // 配送方式

      columns: [
        { title: '商品名称', slot: 'name', key: 'name', tree: true, align: 'center' },
        { title: '来源', slot: 'source_platform_text', key: 'source_platform_text', align: 'center' },
        { title: '单价', slot: 'price', key: 'price', align: 'center' },
        { title: '储值价', slot: 'stored_price', key: 'stored_price', align: 'center', minWidth: 40 },
        { title: '购买数量', slot: 'num', key: 'num', minWidth: 40 },
        { title: '合计', slot: 'total_price', key: 'total_price', align: 'center' },
        { title: '储值价合计', slot: 'total_stored_price', key: 'total_stored_price', align: 'center', minWidth: 40 },
        { title: '操作', slot: 'action', key: 'action', align: 'center' },
      ],
      add_goods: [], // 表格加入的商品
      entity_add_goods: [], // 表格加入的实体商品
      fictitious_add_goods: [], // 表格加入的虚拟商品
      exchangeCouponGoods: [], // 通兑券列表
      taocanGoods: [], // 套餐列表
      payVisible: false, // 支付弹窗显示的标识
      order_id: '', // 订单id,

      entityOrderVisible: false, // 获取实体订单的弹窗，一个spu对应多个sku时，显示弹窗
      entityOrderObject: {}, // 一个spu对应的多个sku

      userAddress: {}, // 获取用户的收获地址

      formData: {
        name: '', // 收件人姓名
        mobile: '', // 收件人手机号
        selectedAddress: [], // 收件地址
        consignee_info: {
          consignee: '',
          mobile: '',
          address: {
            prov: {
              name: '',
              code: '',
            },
            city: {
              name: '',
              code: '',
            },
            county: {
              name: '',
              code: '',
            },
            detail: '',
          },
        },
      },
      formDataRules: {
        name: [{ required: true, message: '请输入收件人姓名', trigger: 'change' }],
        mobile: [{ required: true, validator: phoneValidate, trigger: 'change' }],
        selectedAddress: [
          {
            required: true,
            validator: addressValidate,
            detail: () => {
              return this.formData.consignee_info.address.detail; /*为外部提供值*/
            },
            trigger: 'blur',
          },
        ],
      }, // 表单校验

      searchTimes: 0,
      // 创建用户
      name: '',
      creatName: '',
      consumerVisibleDia: false, // 创建用户的弹窗
      sourceList: [],
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],

      // 参与活动
      activie_ticket_list: [],
      activie_ticket: '',
      activeLoading: false,

      // 未选择活动提示
      chooseTipVisible: false,

      coupon_pay: '2', // 1：可用使用优惠，2：不可以使用优惠
    };
  },
  computed: {
    canUsePackage() {
      return canUsePackageProd();
    },
    // 是否展示物流
    isNeedLogistics() {
      if (this.goods_type === '10') {
        return true;
      }
      // 如果是套餐，套餐包含实物，可以勾选
      if (this.goods_type === '20') {
        for (let index in this.add_goods) {
          for (let child_index in this.add_goods[index].tc_infos) {
            if (this.add_goods[index].tc_infos[child_index].goods_type === '10') {
              return true;
            }
          }
        }
      }
      return false;
    },
    // 套餐子商品购买数量
    taocanChildNum() {
      return row => {
        let father_num = this.add_goods[row.fatherIndex]?.num;
        let child_num = $operator.multiply(Number(father_num || 0), Number(row.num || 0));
        return `x${child_num}`;
      };
    },
    // 展示的活动描述
    active_desc() {
      let chooseActiveItem = this.activie_ticket_list.find(item => item.id === this.activie_ticket);
      return chooseActiveItem?.goods_name ? `购买【${chooseActiveItem?.goods_name}】可获得优惠券` : '';
    },
    // 购买商品的placeholder提示
    orderPlaceholder() {
      switch (this.goods_type) {
        case '10':
          return '请选择实体商品';
        case '15':
          return '请选择服务商品';
        case '25':
          return '请选择通兑券';
        case '20':
          return '请选择套餐商品';
        default:
          return '请选择';
      }
    },
  },
  watch: {},
  created() {
    this.getActivityCheckclinic();
    this.getArrivalOptions();
    // 初始化拉取商品列表
    this.getCureRecordList();
  },
  mounted() {},
  methods: {
    // 给表格行设置样式
    rowClassName(row, index) {
      if (row.children && row.children.length) {
        return 'ivu-table-row-tree';
      }
      if (row.isChild) {
        return 'ivu-table-row-tree-child';
      }
    },

    // 检测诊所是否使用优惠券支付
    getActivityCheckclinic() {
      let params = {
        clinic_id: getClinicid(),
      };
      this.$api.getActivityCheckclinic(params).then(res => {
        this.coupon_pay = res.coupon_pay;
      });
    },

    activeChange(val, isSelect = false) {
      if (val && this.coupon_pay === '1') {
        let params = {
          goods_ids: this.getGoodsIds(),
        };
        this.activie_ticket_list = [];
        this.activeLoading = true;
        return this.$api
          .getGoodsBindactivity(params)
          .then(res => {
            this.activie_ticket_list = res.list;
            if (!isSelect) {
              if (this.activie_ticket) {
                let isHasIndex = this.activie_ticket_list.findIndex(item => item.id === this.activie_ticket);
                if (isHasIndex < 0) {
                  this.activie_ticket = '';
                }
              } else {
                if (this.activie_ticket_list?.length) {
                  this.activie_ticket = this.activie_ticket_list[0]?.id;
                }
              }
            }
            return new Promise(resolve => resolve(res.list));
          })
          .finally(() => (this.activeLoading = false));
      }
    },

    // 获取已选商品的id合集
    getGoodsIds() {
      let ids = [];
      let id_key = {
        10: 'goodsid',
        15: 'id',
        25: 'id',
        20: 'id',
      };
      this.add_goods.forEach(good_item => {
        ids.push(good_item[id_key[this.goods_type]]);
      });
      return ids || [];
    },
    // 地址
    regionChange(address) {
      console.log(address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.consignee_info.address.prov = prov;
        this.formData.consignee_info.address.city = city;
        this.formData.consignee_info.address.county = county;
      } else {
        this.formData.consignee_info.address = {
          prov: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          detail: '',
        };
      }

      // 手动触发校验
      // this.$refs.formData.validateField('selectedAddress')
    },
    // 订单类型单选触发
    radioChange(checkedVal) {
      this.add_goods = [];
      this.activie_ticket_list = [];
      this.activie_ticket = '';
      const { add_goods, good } = this;
      if (checkedVal === '15') {
        // 当订单类型为虚拟商品订单时，重置配送方式
        this.resetDistributionType('0');
        this.add_goods = this.fictitious_add_goods;
      } else if (checkedVal === '10') {
        this.resetDistributionType('1');
        this.add_goods = this.entity_add_goods;
      } else if (checkedVal === '25') {
        this.resetDistributionType('0');
        this.add_goods = this.exchangeCouponGoods;
      } else if (checkedVal === '20') {
        this.resetDistributionType('0');
        this.add_goods = this.taocanGoods;
      }
      this.getCureRecordList(good);
    },

    resetDistributionType(val) {
      this.distributionType = val;
    },

    checkedList(list, name) {
      list.forEach(item => {
        console.log('-> %c item  === %o ', 'font-size: 15px', item);
        if (!this.isHasGood(item.id)) {
          this.add_goods.push({
            ...item,
            num: 1,
            total_price: item.price,
            name: name,
            subName: item.spec,
            total_stored_price: item.stored_price || item.price,
          });
          this.activeChange(true);
        } else {
          this.$Message.error(`${name}[${item.spec}]已经存在,不可重复添加`);
        }
      });
    },

    // 取消
    cancel() {
      this.$router.push({
        path: '/trade/order/list',
      });
    },
    submit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          console.log('-> this.isSubmit()', this.isSubmit());
          if (this.isSubmit()) {
            if (!this.activie_ticket && this.coupon_pay === '1') {
              this.activeChange(true, true).then(list => {
                // 如果检测到有可以参与赠券活动的商品，但没有选择参与，则弹窗提醒
                if (list.length && !this.activie_ticket) {
                  this.chooseTipVisible = true;
                } else {
                  this.confirmSubmit();
                }
              });
            } else {
              this.confirmSubmit();
            }
          }
        }
      });
    },
    confirmSubmit() {
      let params = {
        mobile: this.mobile,
        name: this.nickname,
        uid: this.uid,
        listof: this.handleListof(),
        need_address: this.distributionType, // 是否需要配送
        consignee_address: this.getJsonAddress(),
        activity_id: this.activie_ticket, // 参与活动id
      };

      this.$api.orderCreate(params).then(res => {
        this.order_id = res.id;
        this.uid = res.uid;
        this.payVisible = true;
      });
    },

    // 处理要提交的商品数据
    handleListof() {
      let listof = {};
      this.add_goods.forEach(item => {
        if (this.goods_type == '15' || this.goods_type == '25' || this.goods_type == '20') {
          listof = { ...listof, ...{ [item.goods_attr_ids[0]]: item.num } };
        } else {
          listof = { ...listof, ...{ [item.id]: item.num } };
        }
      });
      return JSON.stringify(listof);
    },
    // 手机号校验正则
    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },
    // 提交之前对页面必填数据判断
    isSubmit() {
      if (!this.mobile) {
        this.$Message.error('用户手机号不可为空');
        return false;
      }
      if (!this.regRole(this.mobile)) {
        this.$Message.error('请输入正确的手机号');
        return false;
      }

      if (!this.nickname) {
        this.$Message.error('姓名不可为空');
        return false;
      }
      if (!this.add_goods.length) {
        this.$Message.error('购买商品不可为空');
        return false;
      }

      let flag = this.add_goods.some(item => {
        if (item.num === 0 || item.num === null) {
          this.$Message.error(`${item.name}的数量最小为1`);
          return true;
        }
      });

      if (flag) {
        return false;
      }
      return true;
    },

    // 组合JSON收件地址，包括收件人，手机号码
    getJsonAddress() {
      let address = {
        consignee: this.formData.name,
        mobile: this.formData.mobile,
        location: {
          province: this.formData.consignee_info.address.prov,
          city: this.formData.consignee_info.address.city,
          county: this.formData.consignee_info.address.county,
          other: this.formData.consignee_info.address.detail,
        },
      };
      return JSON.stringify(address);
    },

    /**
     * 表格逻辑
     * */
    // 移除表格商品数据
    removeGood(index) {
      this.add_goods.splice(index, 1);
      this.activeChange(true);
    },
    // 表格商品计算合计
    caclTotalPrice(index) {
      if (index === '' || index === undefined || index === null) return 0;
      const { stored_price, num, price } = this.add_goods[index];
      this.add_goods[index].total_price = S.mathMul(Number(num), Number(price));
      this.add_goods[index].total_stored_price = stored_price
        ? S.mathMul(Number(num), Number(stored_price))
        : S.mathMul(Number(num), Number(price));
    },
    // 商品数量改变，对应合计发生变化
    numberChange(e, index) {
      console.log('e', e, e == null);
      this.add_goods[index].num = e;
      this.caclTotalPrice(index);
    },
    // blur (index) {
    //   // console.log('this.add_goods[index].num', this.add_goods[index].num);
    //   // if (this.add_goods[index].num == null || this.add_goods[index].num == '' || this.add_goods[index].num == 0) {
    //   //   this.add_goods[index].num = 0
    //   // }
    // },
    // 确保input最小为0，不可为空
    // banNegative (e, index) {
    //   console.log('wr', e.target.value, e.target.value == '');
    //   const value = e.target.value
    //   if (value == '') {
    //     // e.target.value = 0
    //     // this.add_goods[index].num = 0
    //   }
    // },
    // 表格总计
    handleSummary({ columns, data }) {
      console.log('-> %c data  === %o ', 'font-size: 15px', data);
      let cloneData = this.$lodash.cloneDeep(data);
      cloneData.map(item => {
        console.log('-> %c item  === %o ', 'font-size: 15px', item);
        if (item.is_recharge_buy === 'no') {
          item.total_stored_price = 0;
        }
      });
      console.log('-> %c data  === %o ', 'font-size: 15px', data);
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '总计',
          };
          return;
        }
        if (index === 1 || index === 2 || index === 3) {
          sums[key] = {
            key,
            value: '-',
          };
          return;
        }
        const values = cloneData.map(item => Number(item[key]));
        if (!values.every(value => isNaN(value))) {
          console.log(data);
          const v = cloneData.reduce((prev, curr) => {
            console.log('-> %c curr  === %o ', 'font-size: 15px', curr);
            const value = Number(curr[key]);
            if (!isNaN(value)) {
              return S.mathAdd(prev, Number(curr[key]));
            } else {
              return prev;
            }
          }, 0);
          if (index === 4) {
            sums[key] = {
              key,
              value: Number(v) + '',
            };
          } else if (index === 5 || index === 6) {
            sums[key] = {
              key,
              value: '￥' + v,
            };
          } else {
            sums[key] = {
              key,
              value: v,
            };
          }
        } else {
          sums[key] = {
            key,
            value: '-',
          };
        }
      });
      return sums;
    },

    /**
     * 手机号远程搜索逻辑
     * */
    // 选中的用户手机号option变化触发
    // optionChange(item) {
    //   console.log("-> %c item  === %o ", "font-size: 15px", item)
    //   this.mobile = item.mobile;
    //   this.nickname = item.patient_name;
    //   this.uid = item.uid
    //   this.balance = item.recharge_money
    //   this.getUserAddress()
    // },
    // // 搜索词发生变化时触发
    // queryChange(keyword,cb) {
    //   this.getUserList(keyword,cb);
    // },

    /**
     * @description:远程搜索用户信息
     * */
    querySearchAsync(keyword, cb) {
      this.creatName = cloneDeep(keyword);
      if (this.searchTimes && !keyword) {
        cb(this.userList);
      } else {
        let copyName = this.name || 'none';
        if (keyword !== copyName) {
          this.getUserList(keyword, cb);
        } else {
          cb(this.userList);
        }
        this.getUserList(keyword, cb);
      }
    },

    // 点击创建用户，显示弹窗
    creatConsumer(val) {
      this.consumerVisibleDia = true;
      this.$refs.custom.close();
    },

    // 创建用户返回的数据
    optionChange(item) {
      this.balance = '';
      this.name = item.patient_name;
      this.mobile = item.mobile;
      this.nickname = item.patient_name;
      this.uid = item.uid;
    },
    handleSelect(item) {
      this.name = item.patient_name;
      this.mobile = item.mobile;
      this.nickname = item.patient_name;
      this.uid = item.uid;
      this.balance = item.recharge_money;
      this.getUserAddress();
    },
    // 当搜索的人不存在时,失焦清除绑定数据,不允许自建
    blur() {
      setTimeout(() => {
        if (!this.name) {
          if (!this.consumerVisibleDia) {
            this.nickname = '';
            this.$refs.custom.getData();
          }
        } else {
          this.nickname = this.name;
        }
      }, 200);
    },

    // api-获取用户列表-用户手机号带出用户信息
    getUserList(search = '', cb) {
      let params = {
        page: 1,
        pageSize: 20,
        search,
      };
      this.searchTimes++;
      if (search) {
        this.searchTimes = 0;
      }
      this.$api.getUserList(params).then(res => {
        // 获取用户数据
        this.handleUserList(res.users, cb);
      });
    },
    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      cb(data);
    },

    /**
     * 商品远程搜索逻辑
     */
    // 商品远程搜索
    remoteGood(keyword) {
      this.getCureRecordList(keyword);
    },

    openChange(val) {
      if (!val) {
        this.getCureRecordList();
      }
    },

    // addGoods
    addGoods(item) {
      if (!this.isHasGood(item.id)) {
        if (this.goods_type === '25') {
          this.add_goods = [];
        }
        this.add_goods.push({
          ...item,
          num: 1,
          total_price: item.price,
          total_stored_price: item.stored_price || item.price,
        });
        console.log('-> this.goods_type', this.goods_type);
        if (this.goods_type === '10') {
          this.entity_add_goods = this.add_goods;
        } else if (this.goods_type === '15') {
          this.fictitious_add_goods = this.add_goods;
        } else if (this.goods_type === '25') {
          this.exchangeCouponGoods = this.add_goods;
        } else if (this.goods_type === '20') {
          this.taocanGoods = this.add_goods;
          this.taocanGoods.forEach((item, index) => {
            item.tc_infos?.forEach(c_item => {
              c_item.source_platform_text = c_item.goods?.source_platform_text;
              c_item.isChild = true;
              c_item.fatherIndex = index;
            });
            item.children = item.tc_infos;
            item._showChildren = true;
          });
        }
        this.activeChange(true);
      } else {
        this.$Message.error(`${item.name}已经存在,不可重复添加`);
      }
    },
    // 商品options选中
    goodsOptionChange(params) {
      console.log('-> params', JSON.parse(params.value));
      this.$refs.goodSelect.clearSingleSelect();
      const { goods_type } = this;
      const item = JSON.parse(params.value);
      if (item.is_recharge_buy === 'no') {
        item.stored_total_fee = 0;
      }

      // 如果你此时选择的时实体商品订单，一个spu对应多个sku的情况下，要出现弹窗手动选择
      if (goods_type == '10') {
        // 一个spu对应多个sku,在弹框内部选择
        if (Object.keys(item.attrs).length > 1) {
          this.entityOrderVisible = true;
          this.entityOrderObject = item;
          console.log('-> %c item  === %o ', 'font-size: 15px', item);
        } else {
          // 如果一个spu对应一个sku，直接添加
          let _attrs = [];
          Object.keys(item.attrs).forEach(key => {
            _attrs.push({
              ...item.attrs[key],
              is_recharge_buy: item.is_recharge_buy,
              source_platform_text: item.source_platform_text,
            });
          });
          this.checkedList(_attrs, item.name);
        }
      } else {
        this.addGoods(item);
      }
    },
    // 商品列表是否已经有该商品
    isHasGood(id) {
      return this.add_goods.some(item => item.id == id);
    },

    // 获取接口返回的地址，转换成当前的地址
    handleAddress({ consignee, mobile, location }) {
      if (!Object.keys(location).length) return;
      // 有默认地址
      if (location.province.name) {
        this.formData.name = consignee;
        this.formData.mobile = mobile;
        this.formData.consignee_info.address.prov = location.province;
        this.formData.consignee_info.address.city = location.city;
        this.formData.consignee_info.address.county = location.county;

        // 澳门特别行政区地区直接返回空数组，小程序地址过于老旧
        if (location.province.name === '澳门特别行政区') return [];
        this.formData.consignee_info.address.detail = location.other;

        this.formData.selectedAddress = [
          TextToCode[location.province.name].code,
          TextToCode[location.province.name][location.city.name].code,
          TextToCode[location.province.name][location.city.name][location.county.name].code,
        ];

        this.$refs.formData.validateField('selectedAddress');
      }
    },

    // api-获取服务商品列表
    getCureRecordList(keyword = '') {
      const { goods_type } = this;
      let params = {
        goods_type,
        status: 200,
        xn_scope: goods_type == '15' ? '1,9' : '',
        shelf_scope: '1,9',
        name: keyword,
        filter_pms_self_goods: '1',
      };
      this.$api.getCureRecordList(params).then(res => {
        this.goods_list = res.goods_items;
      });
    },
    // api-获取用户收件地址
    getUserAddress() {
      let params = {
        uid: this.userList.length > 0 ? this.uid : '',
      };
      this.$api.getUserAddress(params).then(res => {
        this.userAddress = res.location;
        // 处理地址
        this.handleAddress(res);
      });
    },
    // api-获取options
    getArrivalOptions() {
      this.$api.getArrivalOptions().then(res => {
        // 用户来源
        this.sourceList = S.descToArrHandle(res.userFromDesc);
      });
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.tip {
  color: #999;
}

.talbe {
  width: 900px;

  .subName {
    font-size: 12px;
    color: #ccc;
  }
}

.btn {
  margin: 20px auto;
  width: 300px;
  display: flex;
  justify-content: space-around;
}

.btnStyle {
  width: 100px;
}

.active-desc {
  color: #f65554;
}

/deep/ .popper-class .el-scrollbar__wrap {
  height: auto !important;
}
</style>

<style lang="less" scoped>
::v-deep .ivu-select-item {
  width: 200px !important;
  background: red;
}

p {
  margin: 0px;
}

::v-deep .el-autocomplete {
  .is-disabled {
    .el-input__inner {
      background: #f3f3f3;
      color: #ccc;
    }
  }

  .el-input__inner {
    padding: 0 7px;
    border-color: #bbb;
    font-size: 12px;
    border-radius: 2px;
  }
}

.mr-8 {
  margin: 0 8px;
}

// deep  components style
.ivu-date-picker,
.el-autocomplete {
  width: 100%;
}

::v-deep .el-input__inner {
  height: 32px;
  font-size: 12px;
  padding: 4px 7px;
  border: 1px solid #bcc3d7;
  border-radius: 2px;

  &:hover {
    border-color: #447cdd;
  }

  &:focus {
    border-color: #447cdd;
    outline: 0;
    // box-shadow: 0 0 0 2px rgb(21 91 212 / 20%);
    box-shadow: 0 0 0 2px rgba(68, 124, 221, 0.2);
  }
}

::v-deep .ivu-table-row-tree {
  td:nth-child(1) {
    .ivu-table-cell {
      display: flex;
    }
  }
}

::v-deep .ivu-table-row-tree {
  .ivu-table-cell-tree {
    position: relative;
    left: -3px;
    top: 8px;
  }
}

::v-deep .ivu-table-cell-tree-empty {
  display: none;
}
</style>
