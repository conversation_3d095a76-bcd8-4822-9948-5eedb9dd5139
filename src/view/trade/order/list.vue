<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      stripe
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button class="mr10" type="primary" @click="createOrder('new')">
            <div class="flex flex-item-align">
              <img class="add-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0911/172120_60929.png" />
              <span>新版手工建单</span>
              <img
                v-if="!is_rst"
                class="alpha-icon"
                src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0925/173435_43528.png"
              />
            </div>
          </Button>
          <Button type="default" v-if="!is_rst" class="alpha-btn" @click="createOrder()">
            <div class="flex flex-item-align">
              <!--          <img class="add-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0911/172120_60929.png" />-->
              <span>手动创建订单</span>
            </div>
          </Button>
        </div>
        <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <FormItem :label-width="0">
              <Input type="text" v-model="queryFormData.user_keyword" placeholder="姓名/注册名/手机号" />
            </FormItem>
            <FormItem>
              <Input type="text" v-model="queryFormData.out_trade_no" placeholder="订单编号" />
            </FormItem>
            <FormItem>
              <DatePicker
                type="daterange"
                v-model="orderTime"
                @on-change="times => handleTimeChange(times, 'create_time_st', 'create_time_et')"
                placeholder="下单时间"
              ></DatePicker>
            </FormItem>
            <FormItem>
              <DatePicker
                type="daterange"
                v-model="payTime"
                @on-change="times => handleTimeChange(times, 'pay_time_st', 'pay_time_et')"
                placeholder="支付时间"
              ></DatePicker>
            </FormItem>
            <FormItem>
              <DatePicker
                type="daterange"
                v-model="closeTime"
                @on-change="times => handleTimeChange(times, 'closed_time_st', 'closed_time_et')"
                placeholder="关闭时间"
              ></DatePicker>
            </FormItem>
          </Row>

          <Row>
            <FormItem>
              <Select v-model="queryFormData.status" placeholder="订单状态">
                <Option value="">全部状态</Option>
                <Option v-for="(desc, status) in statusDesc" :key="desc.kw" :value="status">{{ desc.desc }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.refund_status" placeholder="退款状态">
                <Option v-for="(desc, status) in orderRefundStatusDesc" :key="desc.kw" :value="status">
                  {{ desc.desc }}
                </Option>
              </Select>
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.pay_type" placeholder="支付方式">
                <Option value="">全部方式</Option>
                <Option v-for="(desc, status) in payPlatformDesc" :key="desc.kw" :value="status">
                  {{ desc.desc }}
                </Option>
              </Select>
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.source" placeholder="订单来源">
                <Option value="">全部来源</Option>
                <Option v-for="(desc, status) in sourceDesc" :key="desc.kw" :value="status">{{ desc.desc }}</Option>
              </Select>
            </FormItem>
            <FormItem v-if="!isRstClinic">
              <Select v-model="queryFormData.relate_staff_id" placeholder="关联人">
                <Option value="">全部</Option>
                <Option
                  v-for="staff in staff_list"
                  :key="staff.id"
                  :value="staff.id"
                  :label="staff.role ? `${staff.name}·${staff.role}` : staff.name"
                >
                  <div class="flex flex-item-between">
                    <span>{{ staff.name }}</span>
                    <span style="font-size: 12px; color: #999999">{{ staff.role }}</span>
                  </div>
                </Option>
              </Select>
            </FormItem>
          </Row>
          <Row>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <Button type="default" class="mr10" @click="downloadExcel(queryFormData)">导出</Button>
              <span class="list-reset-btn" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </FormItem>
          </Row>
        </Form>
        <div class="panel-nav flex flex-item-between" v-if="Object.keys(statusDesc).length">
          <div>
            <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')">
              全部
              <Tag color="default">{{ statusTotal.TOTAL }}</Tag>
            </a>
            <a
              class="nav"
              :class="{ active: $route.query.status == status }"
              v-for="(desc, status) in statusDesc"
              :key="status"
              @click.prevent.capture="onStatusChange(status)"
            >
              {{ desc.desc }}
              <Tag :color="getStatusColor(status)">
                {{ statusTotal[desc.kw.slice(7, 20)] || statusTotal[desc.kw.slice(11, 20)] || '0' }}
              </Tag>
            </a>
          </div>
          <div></div>
        </div>
      </template>

      <!--商品信息-->
      <template slot-scope="{ row }" slot="goods_info">
        <div v-if="row['class'] === 'shop_order'">
          <div v-for="item in row.attrs" :key="item.goods_id + item.id" style="margin-bottom: 8px">
            <div class="flex flex-item-align">
              <img
                :src="item.is_goods == '0' ? getVirtualImage(row) : item.image_src | imageStyle"
                :alt="item.name"
                width="50"
                height="50"
                style="margin-right: 6px; padding: 5px; box-sizing: content-box"
              />
              <div class="flex flex-c">
                <!--  <KLink-->
                <!--    v-if="item.goods_id && item.is_goods != '0'"-->
                <!--    :to="{ path: '/goods/item/edit', query: { id: item.goods_id } }"-->
                <!--    target="_blank"-->
                <!--    >{{ item.name }}-->
                <!--  </KLink>-->
                <a v-if="item.goods_id && item.is_goods != '0'" @click="showGoodsDetail(item)">{{ item.name }}</a>
                <span v-else>{{ item.name }}</span>
                <span v-if="item.goods_type == '10'">规格：{{ item.spec || '-' }}</span>
                <span>
                  售价：￥{{ Number(item.price || 0).toFixed(2) }} ×
                  <span style="color: #f42f26">{{ item.quantity }}件</span>
                </span>
                <span>金额：￥{{ item.total_fee }}</span>
                <span>
                  状态：<b>{{ item.status_text }}</b>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="flex-c">
          <div v-for="(item, index) in row.attrs" :key="index" style="margin-bottom: 8px">
            <div class="flex flex-item-align">
              <img
                :src="getHisTypeImg(item.type) | imageStyle"
                :alt="item.name"
                width="50"
                height="50"
                style="margin-right: 6px; padding: 5px; box-sizing: content-box"
              />
              <div class="flex flex-c">
                <poptip-table :key="index" :columns="columns" :data="item.attrs_info" width="500">
                  <div class="serve-name">
                    <a target="_blank">{{ item.name }}</a>
                    <img class="type-tag" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0918/163140_76344.png" />
                  </div>
                </poptip-table>

                <span>金额：￥{{ item.total_fee }}</span>
                <span>
                  状态：<b>{{ item.status_text }}</b>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!--   todo 保留样式逻辑，留给以后切换商品展示模式     -->
        <!--        <div v-if="row['class'] === 'shop_order'" class="flex">-->
        <!--          <div v-for="(item, index) in row.attrs" style="margin-bottom: 8px">-->
        <!--            <div class="flex flex-item-align" style="position: relative">-->
        <!--              <img-->
        <!--                v-if="index < 4"-->
        <!--                :src="item.image_src"-->
        <!--                :alt="item.name"-->
        <!--                width="50"-->
        <!--                height="50"-->
        <!--                style="margin-right: 6px; padding: 5px; box-sizing: content-box"-->
        <!--              />-->
        <!--              <div class="more-detail-mask" v-if="index === 3 && row.attrs?.length > 4" @click="jumpToGoodsInfo(row)">-->
        <!--                <div class="scale">共{{ row.attrs?.length }}件<Icon type="ios-arrow-forward" /></div>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--        <div v-else class="flex">-->
        <!--          <div v-for="(item, index) in row.attrs" style="margin-bottom: 8px">-->
        <!--            <div class="flex flex-item-align" style="position: relative">-->
        <!--              <img-->
        <!--                v-if="index < 4"-->
        <!--                :src="getHisTypeImg(item.type)"-->
        <!--                :alt="item.name"-->
        <!--                width="50"-->
        <!--                height="50"-->
        <!--                style="margin-right: 6px; padding: 5px; box-sizing: content-box"-->
        <!--              />-->
        <!--              <div class="more-detail-mask" v-if="index === 3 && row.attrs?.length > 4" @click="jumpToGoodsInfo(row)">-->
        <!--                <div class="scale">共{{ row.attrs?.length }}件<Icon type="ios-arrow-forward" /></div>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
      </template>
      <!--金额-->
      <template slot-scope="{ row }" slot="price">
        <div>
          <span
            class="justified-text"
            :class="Number(row.payment_cancel) > 0 || Number(row.coupon_discount) > 0 ? 'justified-text-5' : ''"
          >
            订单金额
          </span>
          <span>：￥{{ row.total_fee }}</span>
        </div>
        <div v-if="row.status !== '7000' && row.status !== 'CLOSED'">
          <div v-if="Number(row.payment_cancel) > 0">收款前取消：￥{{ row.payment_cancel }}</div>
          <div v-if="Number(row.coupon_discount) > 0">优惠券抵扣：￥{{ row.coupon_discount }}</div>
          <div v-if="Number(row.order_discount_fee) > 0">
            <span
              class="justified-text"
              :class="Number(row.payment_cancel) > 0 || Number(row.coupon_discount) > 0 ? 'justified-text-5' : ''"
            >
              下单优惠
            </span>
            <span>：￥{{ row.order_discount_fee }}</span>
          </div>
          <div
            v-if="
              Number(row.payment_cancel) > 0 || Number(row.coupon_discount) > 0 || Number(row.order_discount_fee) > 0
            "
          >
            <span
              class="justified-text"
              :class="Number(row.payment_cancel) > 0 || Number(row.coupon_discount) > 0 ? 'justified-text-5' : ''"
            >
              实际应付
            </span>
            <span>：￥{{ row.payment_fee }}</span>
          </div>
          <div>
            <span
              class="justified-text"
              :class="
                Number(row.payment_cancel) > 0 || Number(row.coupon_discount) > 0
                  ? 'justified-text-5'
                  : 'justified-text-4'
              "
            >
              已支付
            </span>
            <span> ：{{ row.received_payment ? `￥${row.received_payment}` : '-' }} </span>
          </div>
          <div v-if="Number(row.wait_payment_fee) > 0" style="color: red">
            <span
              class="justified-text"
              :class="Number(row.payment_cancel) > 0 || Number(row.coupon_discount) > 0 ? 'justified-text-5' : ''"
            >
              还需支付
            </span>
            <span>：￥{{ row.wait_payment_fee }}</span>
          </div>
        </div>
      </template>
      <!--客户信息-->
      <template slot-scope="{ row }" slot="customer_info">
        <span>注册名：</span>
        <KLink :to="{ path: '/user/detail', query: { uid: row.uid } }" target="_blank">
          {{ row.nickname || '-' }}
        </KLink>
        <br />
        <span style="display: inline-block; width: 39px" class="justified-text">姓名</span>
        <span>：{{ row.real_name || '-' }}</span>
        <br />
        手机号：{{ row.user_mobile || '-' }}<br />
        <span v-if="!(isRstClinic && (row.source === 'rst_pc' || row.source === 'rst_weapp'))">
          关联人：{{ row.relate_staff_text || '-' }}
          <br />
        </span>
      </template>
      <template slot-scope="{ row }" slot="status">
        <mark-status :type="getMarkStatusType(row.status_text)">{{ row.status_text || '' }}</mark-status>
        <span v-if="row.closed_type_text" style="color: #999999">({{ row.closed_type_text }})</span>
      </template>
      <template slot-scope="{ row }" slot="out_trade_no">
        <div><span style="display: inline-block" class="justified-text">订单来源：</span>{{ row.source_text }}</div>
        <div>
          <span style="display: inline-block" class="justified-text">订单编号：</span>
          <KLink
            v-if="row.source_from === 'hospital'"
            :to="{ path: '/internet-hospital/order/detail', query: { order_code: row.transaction_no } }"
            target="_blank"
          >
            {{ row.transaction_no }}
          </KLink>
          <span v-else>{{ row.out_trade_no }}</span>
        </div>

        <div class="flex">
          <span style="min-width: fit-content">支付方式：</span>
          <span>
            <span v-for="(item, index) in row.pay_type_text" :key="index">
              <span>{{ item }}</span>
              <span v-show="index + 1 !== row.pay_type_text?.length" style="margin: 0 3px">/</span>
            </span>
            <span v-if="is_rst && row.ap_pay_type_desc" style="color: #999">（{{ row.ap_pay_type_desc }}）</span>
          </span>
          <span v-if="row.pay_type_text?.length === 0">-</span>
        </div>
        <div>
          <span>配送信息：</span>
          <!-- 诊所订单配送信息  -->
          <div v-if="row.class === 'shop_order'" style="display: inline-block">
            <!--      虚拟商品      -->
            <template v-if="typeDesc[row.type] && typeDesc[row.type].kw === 'TYPE_BUY_VIRTUAL_GOODS'">
              <span class="text-muted">无需物流</span><br />
            </template>
            <!--      通兑券      -->
            <template v-else-if="typeDesc[row.type] && typeDesc[row.type].kw === 'TYPE_BUY_VIRTUAL_EXCHANGE'">
              <span class="text-muted">无需物流</span><br />
            </template>
            <!--      无需物流      -->
            <template v-else-if="!row.consignee_address.location">
              <span class="text-muted">无需物流</span><br />
            </template>
            <template v-else-if="row.sale_type === 'USER_SELF'"><span>到店自提</span><br /></template>
            <template v-else-if="row.sale_type === 'PMS_SELF'"><span>总部代发</span><br /></template>
            <template v-else><span class="text-muted">物流快递</span><br /></template>
          </div>
          <!-- his订单配送信息  -->
          <div style="display: inline-block" v-else>
            <span class="text-muted">{{ row.consignee_address }}</span>
          </div>
        </div>
        <div>
          订单备注：<b style="color: red">{{ row.remark || '-' }}</b>
        </div>
      </template>
      <template slot-scope="{ row }" slot="time">
        <div>下单时间：{{ row.create_time | data_format }}</div>
        <div>支付时间：{{ row.pay_time | data_format }}</div>
        <div>关闭时间：{{ row.closed_time | data_format }}</div>
      </template>

      <!-- <template slot-scope="{ row }" slot="date"></template>-->
      <template slot-scope="{ row }" slot="refund_status_text">
        {{ row.refund_status_text || '-' }}
      </template>
      <template slot-scope="{ row }" slot="refund_goods">
        <div v-if="row['class'] === 'shop_order'">
          <div v-for="item in row.refund_goods" :key="item.goods_id + item.id" style="margin-bottom: 8px">
            <div class="flex flex-item-align">
              <img
                :src="item.is_goods == '0' ? getVirtualImage(row) : item.image_src"
                :alt="item.name"
                width="50"
                height="50"
                style="margin-right: 6px; padding: 5px; box-sizing: content-box"
              />
              <div class="flex flex-c">
                <KLink
                  v-if="item.goods_id && item.is_goods != '0'"
                  :to="{ path: '/goods/item/edit', query: { id: item.goods_id } }"
                  target="_blank"
                >
                  {{ item.name }}
                </KLink>
                <span v-else>{{ item.name }}</span>
                <span v-if="item.goods_type == '10'">规格：{{ item.spec || '-' }}</span>
                <span>
                  售价：￥{{ Number(item.price || 0).toFixed(2) }} ×
                  <span style="color: #f42f26">{{ item.amount }}件</span>
                </span>
                <span>金额：￥{{ item.total_fee }}</span>
                <span>
                  状态：<b>{{ item.status_text }}</b>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="flex-c">
          <div v-for="(item, index) in row.refund_goods" style="margin-bottom: 8px" :key="index">
            <div class="flex flex-item-align">
              <img
                :src="getHisTypeImg(item.type) | imageStyle"
                :alt="item.name"
                width="50"
                height="50"
                style="margin-right: 6px; padding: 5px; box-sizing: content-box"
              />
              <div class="flex flex-c">
                <a target="_blank">{{ item.name }}</a>
                <span>金额：￥{{ item.total_fee }}</span>
                <span>
                  状态：<b>{{ item.status_text }}</b>
                </span>
              </div>
            </div>
          </div>
        </div>
        <!--   todo 保留样式逻辑，留给以后切换商品展示模式     -->
        <!--        <div v-if="row['class'] === 'shop_order'" class="flex">-->
        <!--          <div v-for="(item, index) in row.refund_goods" style="margin-bottom: 8px">-->
        <!--            <div class="flex flex-item-align" style="position: relative">-->
        <!--              <img-->
        <!--                v-if="index < 4"-->
        <!--                :src="item.image_src"-->
        <!--                :alt="item.name"-->
        <!--                width="50"-->
        <!--                height="50"-->
        <!--                style="margin-right: 6px; padding: 5px; box-sizing: content-box"-->
        <!--              />-->
        <!--              <div-->
        <!--                class="more-detail-mask"-->
        <!--                v-if="index === 3 && row.refund_goods?.length > 4"-->
        <!--                @click="jumpToRefundInfo"-->
        <!--              >-->
        <!--                <div class="scale">共{{ row.refund_goods?.length }}件<Icon type="ios-arrow-forward" /></div>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--        <div v-else class="flex">-->
        <!--          <div v-for="(item, index) in row.refund_goods" style="margin-bottom: 8px">-->
        <!--            <div class="flex flex-item-align" style="position: relative">-->
        <!--              <img-->
        <!--                v-if="index < 4"-->
        <!--                :src="getHisTypeImg(item.type)"-->
        <!--                :alt="item.name"-->
        <!--                width="50"-->
        <!--                height="50"-->
        <!--                style="margin-right: 6px; padding: 5px; box-sizing: content-box"-->
        <!--              />-->
        <!--              <div-->
        <!--                class="more-detail-mask"-->
        <!--                v-if="index === 3 && row.refund_goods?.length > 4"-->
        <!--                @click="jumpToRefundInfo"-->
        <!--              >-->
        <!--                <div class="scale">共{{ row.refund_goods?.length }}件<Icon type="ios-arrow-forward" /></div>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div v-if="!row.refund_goods?.length">-</div>
      </template>
      <template slot-scope="{ row }" slot="refund_info">
        <div v-if="row.refund_last_time">
          退款时间：{{ row.refund_last_time | data_format }} <br />
          退款金额：￥{{ row.refunded_fee }}
        </div>
        <div v-else>-</div>
      </template>
      <template slot-scope="{ row }" slot="operate">
        <div class="flex-c">
          <a
            v-if="row.status == '2000' && row.sale_type !== 'PMS_SELF'"
            class="mb10"
            size="small"
            @click="onClickDelivery(row.id)"
          >
            发货
          </a>
          <!--          <a-->
          <!--            class="ml10 mb10"-->
          <!--            @click="toAccount(row)"-->
          <!--            v-if="isRstClinic && (row.source === 'rst_pc' || row.source === 'rst_weapp')"-->
          <!--            >核算业绩</a-->
          <!--          >-->

          <a
            v-if="!isRstClinic && row.is_forbidden !== '1'"
            style="margin-left: 10px"
            @click="editRelatedPerson(row)"
            class="mb10"
          >
            {{ row.relate_staff_list.length ? '编辑' : '添加' }}关联人
            <!-- 核算业绩 -->
          </a>
          <a v-if="isRstClinic && row.is_can_divide == '1'" @click="toAccount(row)" class="mb10"> 核算业绩 </a>

          <KLink
            :to="{ path: '/trade/order/detail', query: { orderid: row.id, orderType: row['class'], mr_id: row.mr_id } }"
            class="mb10"
          >
            详情
          </KLink>
          <a @click="pay(row)" v-if="row.status === '1000' && row.source !== 'rst_weapp'" class="mb10">支付</a>
          <Poptip
            confirm
            width="180"
            title="是否确认取消订单?"
            @on-ok="cancelOrder(row)"
            v-if="row.status === '1000' && row.is_forbidden !== '1'"
          >
            <a>取消订单</a>
          </Poptip>
        </div>
      </template>
    </standard-table>

    <!-- 发货组件 -->
    <DeliveryModal
      v-model="deliveryModal.modal"
      :orderId="deliveryModal.orderId"
      :company_code="deliveryModal.company_code"
      :modifyOrderAttrIds="deliveryModal.orderAttrIds"
      @on-success="onDeliverySuccess"
    ></DeliveryModal>
    <!-- 支付弹窗 -->
    <!--    <PayDialog :payVisible.sync="payVisible" :order_id="order_id"></PayDialog>-->

    <!-- v2版本支持优惠券的支付弹窗 -->
    <template v-if="is_rst">
      <rst-pay-dialog
        v-model="rstPayVisible"
        :disabled-discount="disabledDiscount"
        :orderId="order_id"
        :is_rst="is_rst"
        :is_ry_order="currentRow.is_ry_order"
      ></rst-pay-dialog>
    </template>
    <template v-else>
      <k-pay-dialog v-model="payVisible" :order_id="order_id" :is_rst="is_rst"></k-pay-dialog>
    </template>
    <editRelateModal
      :order_id="relatedModalFormData.editOrderId"
      :checked-staff-list="relatedModalFormData.relateStaffIds"
      :staff-list="staff_list"
      :class-type="relatedModalFormData.classType"
      :relate-visible.sync="relatedModalFormData.relateVisible"
    ></editRelateModal>
    <goods-snap-shot-modal :visible.sync="snapshotVisible" :attr_id="snap_attr_id"></goods-snap-shot-modal>

    <!--未通过主体验证 拦截收款弹窗-->
    <auth-warning-modal ref="authWarningRef"></auth-warning-modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
/* eslint-disable */
import DeliveryModal from '@/view/trade/components/delivery-modal';
import KGoodsSelectMultiple from '@/components/k-goods-select-multiple';
import PayDialog from './components/pay-dialog';
import PayModal from './components/payModal.vue';
import downloadExcel from '@/mixins/downloadExcel';
import search from '@/mixins/search';
import editRelateModal from './components/edit-relates';
import KPayDialog from '@/components/k-pay-dialog/index.vue';
import poptipTable from '@/components/poptipTable';
import { isRstClinic } from '@/libs/runtime';
import RstPayDialog from '@/components/k-pay-dialog/rst-pay-dialog.vue';
import StandardTable from '@/components/StandardTable/index.vue';
import goodsSnapShotModal from '@/view/trade/order/components/goodsSnapShotModal/index.vue';
import AuthWarningModal from '@/components/AuthWarning/AuthWarningModal.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  id: '',
  uid: '',
  relate_staff_id: '', //关联人id
  user_keyword: '', // 姓名/手机号
  source: '', // 来源
  out_trade_no: '',
  status: '',
  date_name: 'create_time',
  username: '',
  create_time_st: '', //下单时间
  create_time_et: '', // 下单时间
  pay_time_st: '', // 支付时间
  pay_time_et: '', // 支付时间
  closed_time_st: '', // 关闭时间
  closed_time_et: '', // 关闭时间
  pay_type: '', // 支付方式
  // goods_name: '',
  mobile: '',
  consignee: '',
  express_no: '',
  refund_status: '', // 退款状态
  r: '',
};
const init_relate_form_data = {
  editOrderId: '',
  relateVisible: false,
  relateStaffIds: [],
  classType: '',
};
export default {
  name: 'list',
  components: {
    StandardTable,
    DeliveryModal,
    KGoodsSelectMultiple,
    PayDialog,
    PayModal,
    editRelateModal,
    KPayDialog,
    poptipTable,
    RstPayDialog,
    goodsSnapShotModal,
    AuthWarningModal,
  },
  mixins: [downloadExcel, search],
  data() {
    return {
      relatedModalFormData: {
        ...init_relate_form_data,
      },
      downloadApiName: 'downloadOrderExcel',
      queryFormData: { ...init_query_form_data },
      apiName: 'getOrderList',
      selectGoodsModal: false,
      payVisible: false, // 支付弹窗
      rstPayVisible: false, // 榕树堂支付弹窗
      payTime: [], // 支付时间
      closeTime: [], // 关闭时间
      orderTime: [], // 下单时间
      tableCols: [
        { title: '商品', slot: 'goods_info', width: 220 },
        { title: '金额', slot: 'price', width: 160 },
        { title: '客户', slot: 'customer_info', width: 180 },
        { title: '状态', slot: 'status', width: 100, align: 'center' },
        { title: '订单信息', slot: 'out_trade_no', width: 280 },
        { title: '时间', slot: 'time', width: 280 },
        { title: '退款状态', slot: 'refund_status_text', width: 80 },
        { title: '退款商品', slot: 'refund_goods', width: 220 },
        { title: '退款信息', slot: 'refund_info', width: 220 },
        { title: '操作', slot: 'operate', width: 120, align: 'center', fixed: 'right' },
      ],
      tableLoading: false,

      list: [],
      sourceDesc: [],
      total: 0,
      users: {},
      statusDesc: {},
      typeDesc: {},
      orderAttrStatusDesc: {},
      payTypeDesc: {},
      payPlatformDesc: {},
      userLevelDesc: {},
      statusTotal: {},
      closeTypeDesc: {},
      deliveryModal: {
        modal: false,
        orderId: '',
        orderAttrIds: [],
        company_code: '',
      },
      order_id: '', // 用于支付的id
      staff_list: [], // 关联人列表
      orderRefundStatusDesc: [],

      columns: [
        { title: '名称', key: 'name', width: 200, align: 'left' },
        { title: '类型', key: 'type_text', slot: 'type_text', textColor: '#909399', align: 'left' },
        {
          title: '来源',
          key: 'source_platform_text',
          slot: 'source_platform_text',
          textColor: '#909399',
          align: 'left',
        },
        { title: '数量', key: 'quantity', align: 'right' },
        { title: '单位', key: 'unit', align: 'left' },
      ],
      isRstClinic: isRstClinic(),
      currentRow: {},
      snap_attr_id: '', // 订单商品快照id
      snapshotVisible: false,
    };
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.getOrderOptions();
    this.getStaffList();
    this.submitQueryForm(true);
  },

  watch: {},
  computed: {
    // 如果是榕易卡（980和298），并且是数字化诊所，则不显示优惠。
    disabledDiscount() {
      if (this.is_rst && this.currentRow.is_ry_order == '1') {
        return true;
      }
      return false;
    },
    getVirtualImage() {
      return item => {
        if (item.is_ry_order == '1') {
          return 'https://img-sn-i01s-cdn.rsjxx.com/image/2025/0219/101535_43264.jpeg-B.200';
        } else {
          return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0923/100618_15681.png-B.200';
        }
      };
    },
    getStatusColor() {
      return status => {
        switch (status) {
          case 'WAIT_SHIPMENT':
          case 'WAIT_PAY':
          case 'IN_PAY':
            return 'warning';
          case 'SHIPMENT':
          case 'FINISHED':
            return 'success';
          case 'PART_PAY':
            return 'primary';
          default:
            return 'default';
        }
      };
    },
    getMarkStatusType() {
      // 由于后端status与status_text不完全匹配，是由多个条件组成后得出的status_text，所以使用status_text进行区分
      return status_text => {
        switch (status_text) {
          case '待付款':
          case '待发货':
          case '支付中':
            return 'warn';
          case '已发货':
          case '已完成':
            return 'success';
          case '已关闭':
            return 'gray';
          case '部分付款':
            return 'blue';
          default:
            return 'default';
        }
      };
    },
    getHisTypeImg() {
      return type => {
        switch (type) {
          case 'HERBS': // 中药饮片
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0410/161315_74810.png';
          case 'MEDICINE': // 中成药
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0411/103813_53063.png';
          case 'PLASTER': // 膏方
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0411/104539_11464.png';
          case 'TREAT_QS': // 祛湿
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0411/104539_58750.png';
          case 'TREAT_AJ': // 艾灸
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0411/104539_20553.png';
          case 'TREAT_TJL': // 通经络
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0411/104539_81216.png';
          case 'TREAT_BJG': // 膏膜保健
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0411/104539_52135.png';
          case 'PHYSICAL': // 调理产品
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0411/104539_85001.png';
          case 'TREAT': // 其他服务
            return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0411/104539_15545.png';
          case 'GOODS_SERVICE': // rst的服务商品类型
            return 'https://img-sn-i01s-cdn.rsjxx.com/image/2025/0303/171247_64104.png';
          case 'zl_info': // 诊疗项目
            return 'https://img-sn-i01s-cdn.rsjxx.com/image/2025/0303/171247_64104.png';
          case 'goods_info': // 调理产品/商品
            return 'https://static.rsjxx.com/image/2025/0305/135321_22248.png';
        }
      };
    },
    is_rst() {
      return isRstClinic();
    },
  },
  methods: {
    editRelatedPerson(row) {
      console.log('-> %c row  === %o ', 'font-size: 15px', row);
      this.relatedModalFormData = { ...init_relate_form_data };
      this.relatedModalFormData.editOrderId = row.id;
      this.relatedModalFormData.relateStaffIds = [];
      this.relatedModalFormData.relateStaffIds = row.relate_staff_list;
      this.relatedModalFormData.classType = row.class;
      this.relatedModalFormData.relateVisible = true;
    },
    getStaffList() {
      this.$api.getStaffList().then(res => {
        this.staff_list = res.staff_list;
      });
    },
    // 创建订单
    createOrder(type = '') {
      let query = {};
      if (type) {
        query.type = type;
      }
      this.$router.push({
        path: '/trade/order/create',
        query: query,
      });
    },

    pay(row) {
      this.currentRow = row;
      this.order_id = row.id;
      if (this.is_rst) {
        this.$refs.authWarningRef.verify().then(res => {
          this.rstPayVisible = true;
        });
      } else {
        this.payVisible = true;
      }
    },
    // 取消订单
    cancelOrder(row) {
      let params = {
        id: row.id,
      };
      this.$api.getOrderClose(params).then(res => {
        this.$Message.success('取消订单成功');
        this.submitQueryForm(true);
      });
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.payTime = [];
      this.orderTime = [];
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    onSelectedGoods: function (items) {
      if (items.length <= 0) {
        return;
      }
      items.forEach(item => {
        if (this.queryFormData.goods_ids) this.queryFormData.goods_ids += ',';
        this.queryFormData.goods_ids += item.id;
      });
      this.selectGoodsModal = false;
    },

    onClickDelivery: function (orderId, orderAttrIds = [], company_code) {
      console.log('-> %c company_code  === %o ', 'font-size: 15px', company_code);
      this.deliveryModal.company_code = company_code;
      this.deliveryModal.orderId = orderId;
      this.deliveryModal.orderAttrIds = orderAttrIds;

      this.deliveryModal.modal = true;
    },

    onDeliverySuccess: function () {
      this.deliveryModal.modal = false;
      this.getsList();
      this.$Message.success('发货成功');
    },
    getOrderOptions() {
      this.$api.getOrderOptions().then(data => {
        console.log('-> %c res  === %o ', 'font-size: 15px', data);
        this.statusDesc = data.statusDesc;
        console.log('-> %c this.statusDesc  === %o ', 'font-size: 15px', this.statusDesc);
        this.typeDesc = data.typeDesc;
        this.orderAttrStatusDesc = data.orderAttrStatusDesc;
        this.payTypeDesc = data.payTypeDesc;
        this.payPlatformDesc = data.searchPayTypeDesc;
        this.userLevelDesc = data.userLevelDesc;
        this.closeTypeDesc = data.closeTypeDesc;
        this.orderRefundStatusDesc = data.orderRefundStatusDesc;
        // 来源数据
        this.sourceDesc = data.sourceDesc;
      });
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.list = [];
          this.list = data.orders;
          console.log('-> %c this.list  === %o ', 'font-size: 15px', this.list);
          this.total = data.total;
          this.users = data.users;
          this.statusTotal = data.statusTotal;
          this.tableLoading = false;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },

    handleList: function (list) {
      return list;
    },

    // todo 保留
    jumpToGoodsInfo(row) {
      this.$router.push({
        path: '/trade/order/detail',
        query: { orderid: row.id, orderType: row['class'], anchor: 'goods' },
      });
    },
    // todo 保留
    jumpToRefundInfo(row) {
      this.$router.push({
        path: '/trade/order/detail',
        query: { orderid: row.id, orderType: row['class'], anchor: 'refund' },
      });
    },
    //==========================
    toAccount(row) {
      if (row.is_can_divide === '2') {
        this.$Message.error('订单未支付');
        return;
      }
      this.$router.push({
        path: '/trade/order/account',
        query: {
          order_id: row.id,
          source: row.class === 'shop_order' ? 'SHOP' : 'HIS',
        },
      });
    },

    showGoodsDetail(item) {
      console.log('=>(list.vue:930) item', item);
      this.snapshotVisible = true;
      if (item.tc_infos?.length > 0) {
        this.snap_attr_id = item.tc_infos.map(tc_item => tc_item.id).join(',');
      } else {
        this.snap_attr_id = item.id;
      }
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange('create_time_st', 'create_time_et', 'orderTime');
    this.getTimeRange('pay_time_st', 'pay_time_et', 'payTime');
    this.getTimeRange('closed_time_st', 'closed_time_et', 'closeTime');
    this.getsList();
    next();
  },
};
</script>

<style lang="less">
.order_attrs_table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
}

.order_attrs_table td {
  padding: 5px 5px;
  border-color: transparent;
}
</style>
<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 12px;
}

.more-detail-mask {
  position: absolute;
  top: 5px;
  left: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  padding-left: 2px;
  box-sizing: border-box;
  background: rgba(46, 46, 46, 0.7);
  color: #ffffff;
  cursor: pointer;

  &:hover {
    top: 3px;
    left: 2px;
    box-sizing: unset;
    border: 2px solid #155bd3;
    //.scale {
    //  transform: scale(1.1);
    //}
  }
}

.add-icon {
  width: 12px;
  min-width: 12px;
  height: 12px;
  margin-right: 8px;
}

.alpha-icon {
  width: 26px;
  min-width: 26px;
  height: 14px;
  margin-left: 2px;
  transform: scale(1.1);
}

.alpha-btn {
  //border: 1px solid #3088ff;
  position: relative;
}

.serve-name {
  display: flex;
  align-items: center;
  cursor: pointer;

  .name {
    color: #3088ff;
    line-height: 22px;
  }

  .type-tag {
    margin-left: 8px;
    width: 32px;
    height: 16px;
  }
}
</style>
