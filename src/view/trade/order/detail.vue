<template>
  <div>
    <!--  后台订单详情  -->
    <div v-if="$route.query.orderType === 'shop_order' && tableLoading">
      <div class="basic-info-form">
        <div class="sub-title" style="display: flex; align-items: center">
          订单信息
          <Tooltip
            max-width="400"
            theme="light"
            placement="bottom"
            :offset="100"
            class="custom-tooltip tooltip-left-arrow ml6"
            style="margin-left: 6px"
          >
            <div slot="content">
              <div>订单金额=商品金额+加工费+挂号费+诊疗费+运费</div>
              <div>实际应付=订单金额-收款前取消-优惠券抵扣-下单优惠</div>
              <div>收款前取消：HIS订单在付款前单项作废部分的金额</div>
              <div>退款金额不能超过实际支付金额</div>
            </div>
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
          </Tooltip>
        </div>
        <Form :model="order" label-position="right" :label-width="65">
          <div class="basic-form-item">
            <Row :gutter="40">
              <Col span="8">
                <FormItem label="订单编号:">{{ order.out_trade_no }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="下单时间:">{{ order.create_time | data_format }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="注册名:">
                  <KLink :to="{ path: '/user/detail', query: { uid: order.uid } }" target="_blank"
                    >{{ order.uid && users[order.uid].nickname }}
                  </KLink>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="姓名:">{{ order.uid && users[order.uid].real_name }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="手机号:">{{ order.uid && users[order.uid].mobile }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="关联人:">{{ order.relate_staff_text || '-' }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="订单备注:">{{ order.user_note_info || '-' }}</FormItem>
              </Col>
            </Row>
          </div>
        </Form>
        <div class="price-table flex">
          <div class="flex-c">
            <div class="price-table-item">
              <div class="price-table-title">商品金额</div>
              <div class="price-table-content">￥{{ order.total_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">加工费</div>
              <div class="price-table-content">-</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">挂号费</div>
              <div class="price-table-content">-</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">诊疗费</div>
              <div class="price-table-content">-</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">运费</div>
              <div class="price-table-content">￥{{ order.post_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">订单金额</div>
              <div class="price-table-content">￥{{ order.total_fee }}</div>
            </div>
          </div>
          <div class="flex-c">
            <div class="price-table-item">
              <div class="price-table-title">收款前取消</div>
              <div class="price-table-content" style="color: red">-￥{{ order.payment_cancel }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">优惠券抵扣</div>
              <div class="price-table-content" style="color: red">-￥{{ order.coupon_discount }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">下单优惠</div>
              <div class="price-table-content" style="color: red">
                -￥{{ order.order_discount_fee }}
                <Tooltip :content="order.sales_promotion?.name" v-if="order.sales_promotion?.name">
                  <Icon type="md-help-circle" style="color: #000" size="16" />
                </Tooltip>
              </div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">实际应付</div>
              <div class="price-table-content">￥{{ order.payment_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">已支付</div>
              <div class="price-table-content">￥{{ order.received_payment }}</div>
            </div>
            <div class="price-table-item" v-if="Number(order.wait_payment_fee) > 0">
              <div class="price-table-title">还需支付</div>
              <div class="price-table-content">￥{{ order.wait_payment_fee }}</div>
            </div>
          </div>
          <div class="flex-c">
            <div class="price-table-item">
              <div class="price-table-title">退款金额</div>
              <div class="price-table-content">￥{{ order.refunded_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">退款中金额</div>
              <div class="price-table-content">￥{{ order.refunded_in_money }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 支付信息 -->
      <div class="basic-info-form">
        <div class="sub-title">支付信息</div>
        <Form :model="order" label-position="right" :label-width="65">
          <div class="basic-form-item">
            <Row :gutter="40">
              <Col span="8">
                <FormItem label="支付状态:">
                  <mark-status :type="getMarkStatusType(order.status)" style="display: inline-block"
                    >{{ order.status && statusDesc[order.status].desc }}
                  </mark-status>

                  <span v-if="statusDesc[order.status]?.kw === 'STATUS_CLOSED'"
                    >({{ closedTypeDesc[order.closed_type].desc }}）</span
                  >
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
        <Table :columns="payTableCols" :data="order.pay_info" border>
          <template v-slot:money="{ row }">
            {{ Number(row.money || 0).toFixed(2) }}
          </template>

          <template v-slot:pay_platform="{ row }">
            <span
              >{{ row.pay_platform_text }}
              <span v-if="row.uid !== row.trade_uid"
                >（共享<k-link
                  :to="{
                    path: '/user/detail',
                    query: {
                      uid: row.trade_uid,
                    },
                  }"
                  target="_blank"
                  >{{ row.trade_user }}</k-link
                >的储值余额抵扣）</span
              >
            </span>
          </template>
          <template v-slot:create_time="{ row }">
            {{ row.create_time | data_format }}
          </template>
          <template v-slot:remark="{ row }"> -</template>
        </Table>
      </div>

      <!-- 配送信息 -->
      <div class="basic-info-form">
        <div class="sub-title">配送信息</div>
        <Form :model="order" label-position="right" :label-width="65">
          <div class="basic-form-item">
            <Row :gutter="40">
              <Col span="8">
                <FormItem label="配送信息:">
                  <div v-if="typeDesc[order.type] && typeDesc[order.type]?.kw === 'TYPE_BUY_VIRTUAL_GOODS'">
                    <span>虚拟商品无需物流</span>
                  </div>
                  <div v-else-if="typeDesc[order.type] && typeDesc[order.type]?.kw === 'TYPE_BUY_VIRTUAL_EXCHANGE'">
                    <span>通兑券商品无需物流</span>
                  </div>
                  <div v-else-if="!order.consignee_address || !order.consignee_address.location">
                    <span>无需物流</span>
                  </div>
                  <div v-else-if="order.sale_type === 'USER_SELF'"><span>到店自提</span></div>
                  <div v-else-if="order.sale_type === 'PMS_SELF'"><span>总部代发</span></div>
                  <div v-else>
                    <span>{{ order.express_info ? order.express_info.name : '普通快递' }}</span>
                  </div>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="最后发货/发药时间:" :label-width="125"
                  >{{ order.delivery_time | data_format }}
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="签收时间:">{{ order.sign_time | data_format }}</FormItem>
              </Col>
            </Row>
          </div>
        </Form>
        <Table :columns="distributeTableCols" :data="distributeList" border v-show="distributeList.length > 0"></Table>
      </div>

      <!-- 订单商品 -->
      <div class="basic-info-form">
        <div class="sub-title" id="good_info">订单商品</div>
        <div class="panel-nav">
          <a class="nav" :class="{ active: curTab === 0 }" @click.prevent.capture="curTab = 0"> 商品信息 </a>
          <a class="nav" :class="{ active: curTab === 1 }" @click.prevent.capture="curTab = 1"> 退款信息 </a>
          <a class="nav" :class="{ active: curTab === 2 }" @click="curTab = 2"> 包裹信息 </a>
        </div>
        <!-- 商品信息-->
        <div v-show="curTab === 0">
          <el-table
            class="expand-table"
            :data="order.orderAttrs"
            header-cell-class-name="table_header"
            style="margin-bottom: 20px"
            :row-key="rowKeyFn"
            border
            :tree-props="{ children: 'tc_infos', hasChildren: 'hasChildren' }"
          >
            <el-table-column align="center" label="商品图片" width="120px">
              <template slot-scope="{ row }">
                <img
                  v-if="!row.isChild && row.image_src"
                  :src="row.is_goods == 0 ? getVirtualImage : row.image_src | imageStyle('B.100')"
                  width="50"
                  height="50"
                  :style="{ marginBottom: row.goods_type === '20' || row.goods_type == 35 ? '20px' : 0 }"
                  :title="'id:' + row.id"
                />
              </template>
            </el-table-column>

            <el-table-column prop="name" label="商品" align="center">
              <template slot-scope="{ row }">
                {{ row.name }}
              </template>
            </el-table-column>
            <el-table-column prop="spec" label="规格" align="center">
              <template slot-scope="{ row }">
                {{ ['10', '40'].includes(row.goods_type) ? row.spec : '-' }}
              </template>
            </el-table-column>

            <el-table-column prop="goods_type_text" label="单价" align="center">
              <template slot-scope="{ row }">
                {{ Number(row.price || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="数量" align="center">
              <template slot-scope="{ row }">
                {{ row.amount }}<br />
                <!--              <span v-if="row.id"-->
                <!--                >可退数量:{{ canRefundOrderAttrs[row.id] && canRefundOrderAttrs[row.id].amount }}</span-->
                <!--              >-->
              </template>
            </el-table-column>

            <el-table-column prop="num" label="金额" align="center" width="100px">
              <template slot-scope="{ row, $index }">
                {{ row.total_fee | number_format(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="discount_fee" label="优惠金额" align="center" width="100px"></el-table-column>
            <el-table-column prop="num" label="应付金额" align="center" width="100px">
              <template slot-scope="{ row, $index }">
                <div v-if="order.pay_time > 0">{{ row.payment_fee | number_format(2) }}</div>
                <div v-else>0.00</div>
              </template>
            </el-table-column>

            <el-table-column prop="address" label="状态" align="center">
              <template slot-scope="{ row }">
                {{ orderAttrStatusDesc[row.status].desc }}
              </template>
            </el-table-column>
            <el-table-column prop="address" label="操作" align="center">
              <template slot-scope="{ row }">
                <template
                  v-if="
                    row.id &&
                    (canRefundOrderAttrs[row.id]?.amount > 0 || canRefundOrderAttrs[row.id]?.money > 0) &&
                    (row.status === '200' || row.status === '600' || row.status === '712') &&
                    !(isTodayOrder && isRstClinic && ap_channel_is_b_c) &&
                    order.pay_platform !== 'offline_month_pay'
                  "
                >
                  <!--                  <Tooltip-->
                  <!--                    v-if="order.source === 'hospital' || order.source === 'hospital_weapp'"-->
                  <!--                    placement="top"-->
                  <!--                    content="互联网医院商品订单退款正在开发中"-->
                  <!--                  >-->
                  <!--                    <a disabled="true">退款</a>-->
                  <!--                  </Tooltip>-->

                  <Poptip
                    v-if="ap_channel_is_b_c && !isRstClinic && order.is_ry_order != '1' && isTodayOrder"
                    placement="top-end"
                    :ref="'refundPoptip' + row.id"
                    @on-popper-show="showRefundPop"
                    width="400"
                  >
                    <a href="javascript:;">退款</a>
                    <div slot="content" class="refund-pop">
                      <div class="title">
                        <Icon type="md-alert" size="16" color="#ff9900" />
                        当前扫码枪收款的订单仅支持整单作废和全额退款?
                      </div>
                      <k-widget label="售后原因" :label-width="70" class="mt-12 mb-12">
                        <Input
                          v-model="refund_remark"
                          type="textarea"
                          ref="remarkInput"
                          placeholder="请输入售后原因"
                          :autosize="{
                            minRows: 3,
                            maxRows: 4,
                          }"
                        />
                      </k-widget>
                      <div class="btn-box">
                        <Button type="text" size="small" @click="closeRefundPopTip(row.id)">取消</Button>
                        <Button
                          type="primary"
                          size="small"
                          class="ml-12"
                          :loading="confirmLoading"
                          @click="confirmRefundAll(row.id)"
                          >退款
                        </Button>
                      </div>
                    </div>
                  </Poptip>
                  <a href="javascript:;" @click="confirmRefund(row.id, row.is_use_service_card)" v-else>退款</a>
                </template>

                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column width="150px">
              <template slot-scope="{ row }">
                <div
                  class="flex flex-item-align mb-6"
                  v-if="refund_order_attrs_form_data[row.id] && order.is_ry_order !== '1'"
                >
                  <span style="width: 36px; min-width: 36px">退货:</span>
                  <InputNumber
                    type="number"
                    v-model="refund_order_attrs_form_data[row.id].refund_amount"
                    class="rinput"
                    style="width: 80px; margin-right: 2px"
                    :disabled="row.is_vip_goods == '1'"
                  />
                  件
                </div>
                <div class="flex flex-item-align" v-if="refund_order_attrs_form_data[row.id]">
                  <span style="width: 36px; min-width: 36px">退款: </span>
                  <InputNumber
                    v-model="refund_order_attrs_form_data[row.id].refund_money"
                    class="rinput"
                    style="width: 80px"
                    :precision="2"
                    :active-change="false"
                    :disabled="row.is_vip_goods == '1' || order.is_ry_order === '1'"
                  />
                  元
                </div>
              </template>
            </el-table-column>
            <div slot="append">
              <div class="flex flex-item-between freight" style="border-bottom: 1px solid #ebeef5">
                <div class="flex">
                  <div style="width: 220px; text-align: center; border-right: 1px solid #ebeef5"><b>运费</b></div>
                  <div
                    style="
                      display: flex;
                      width: 291px;
                      border-right: 1px solid #ebeef5;
                      text-align: center;
                      padding: 0 14px;
                    "
                  >
                    {{ order.post_fee }}
                    可退金额:{{ canRefundPostFee }}
                  </div>
                  <div style="display: flex; width: 182px; border-right: 1px solid #ebeef5">
                    <div style="display: inline-block; width: 120px; height: 100%"></div>
                    <div>
                      <!--                      <Tooltip-->
                      <!--                        placement="top"-->
                      <!--                        v-if="order.source === 'hospital' || order.source === 'hospital_weapp'"-->
                      <!--                        content="互联网医院商品订单退款正在开发中"-->
                      <!--                      >-->
                      <!--                        <a disabled="true">退款</a>-->
                      <!--                      </Tooltip>-->
                      <a
                        v-if="order.pay_platform !== 'offline_month_pay'"
                        href="javascript:;"
                        @click="confirmRefund('POST_FEE')"
                        >退款</a
                      >
                    </div>
                  </div>
                  <div class="tinput" style="padding-left: 10px">
                    <div v-show="refund_post_fee_form_data.show">
                      退款: <Input v-model="refund_post_fee_form_data.money" style="width: 75px" />元
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div slot="append" v-if="order.pay_platform !== 'offline_month_pay'">
              <div class="flex" style="flex-direction: row-reverse; margin: 5px">
                <!--                <Tooltip-->
                <!--                  v-if="order.source === 'hospital' || order.source === 'hospital_weapp'"-->
                <!--                  placement="top"-->
                <!--                  content="互联网医院商品订单退款正在开发中"-->
                <!--                >-->
                <!--                  <Button disabled type="primary" :loading="statemanager.saveBtnLoading">提交</Button>-->
                <!--                </Tooltip>-->
                <Button type="primary" @click="onRefundOrderOpen" v-if="showRefundAllAction">提交</Button>
                <Button type="primary" v-if="showRefundAllButton" @click="refundAll" class="mr-12">全部退款</Button>
              </div>
            </div>
          </el-table>
          <div class="good-total-box">
            <div>
              <div class="flex" style="justify-content: flex-end">
                <div class="total-title">订单总计：</div>
                <div class="total-price">
                  <div>￥{{ order.total_fee }}</div>
                  <div class="discount" v-show="Number(order.discount_fee) > 0 && order.pay_info?.length > 0">
                    已优惠：¥{{ order.discount_fee }}
                  </div>
                </div>
                <div v-show="order.pay_info?.length > 0">
                  <a @click="showPayDetailButton = false" v-if="showPayDetailButton"
                    >收起
                    <Icon type="ios-arrow-up" style="margin-left: 4px" />
                  </a>
                  <a @click="showPayDetailButton = true" v-else
                    >详情
                    <Icon type="ios-arrow-down" style="margin-left: 4px" />
                  </a>
                </div>
              </div>
              <div class="total-detail-box" v-show="showPayDetailButton">
                <div>金额合计：¥{{ order.total_fee }}</div>
                <div v-show="Number(order.discount_fee) > 0">优惠抵扣：¥{{ order.discount_fee }}</div>
                <div class="flex flex-item-between">
                  <div>个人支付：¥{{ order.payment_fee }}</div>
                  <!--                  <div class="pay-time">{{ order.pay_time | data_format }}</div>-->
                </div>
                <div>支付方式：{{ order.pay_platform_text || '-' }}</div>
                <div>应收金额：¥{{ order.payment_fee }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 退款信息-->
        <div v-show="curTab === 1">
          <el-table
            class="expand-table"
            :data="order.refunded_data?.list"
            header-cell-class-name="table_header"
            style="margin-bottom: 20px"
            border
          >
            <el-table-column align="center" label="商品图片" width="120px">
              <template slot-scope="{ row }">
                <img
                  v-if="row.image_src"
                  :src="row.is_goods == 0 ? getVirtualImage : row.image_src | imageStyle('B.100')"
                  width="50"
                  height="50"
                  :title="'id:' + row.id"
                />
              </template>
            </el-table-column>

            <el-table-column prop="name" label="商品" align="center">
              <template slot-scope="{ row }">
                {{ row.name }}
              </template>
            </el-table-column>
            <el-table-column prop="spec" label="规格" align="center">
              <template slot-scope="{ row }">
                {{ ['10', '40'].includes(row.goods_type) ? row.spec : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="购买数量" align="center"></el-table-column>
            <el-table-column prop="total_fee" label="购买金额" align="center" width="100px">
              <template slot-scope="{ row, $index }">
                {{ row.payment_fee | number_format(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="refund_amount" label="已退数量" align="center"></el-table-column>
            <el-table-column prop="refunded_fee" label="退款金额" align="center" width="100px">
              <template slot-scope="{ row, $index }">
                <div>{{ row.refunded_fee | number_format(2) }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="状态" align="center">
              <template slot-scope="{ row }">
                {{ orderAttrStatusDesc[row.status].desc }}
              </template>
            </el-table-column>
            <el-table-column label="售后原因" show-overflow-tooltip>
              <template slot-scope="{ row }">
                {{ row.refund_remark || '-' }}
              </template>
            </el-table-column>
          </el-table>
          <div class="good-total-box" v-show="order.refunded_data?.list?.length > 0">
            <div>
              <div class="flex" style="justify-content: flex-end">
                <div class="total-title">退款总额：</div>
                <div class="total-price">
                  <div>￥{{ order.refunded_data?.refunded_fee }}</div>
                </div>
                <a @click="showRefundDetailButton = false" v-if="showRefundDetailButton"
                  >收起
                  <Icon type="ios-arrow-up" style="margin-left: 4px" />
                </a>
                <a @click="showRefundDetailButton = true" v-else
                  >详情
                  <Icon type="ios-arrow-down" style="margin-left: 4px" />
                </a>
              </div>
              <div class="total-detail-box" v-show="showRefundDetailButton">
                <div
                  class="flex flex-item-between"
                  v-for="(item, index) in order.refunded_data?.refunded_detail"
                  :key="index"
                >
                  <div>-{{ item.offline_pay_platform_text }}：¥{{ item.money }}</div>
                  <!--                  <div class="pay-time">{{ item.update_time | data_format }}</div>-->
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 包裹信息-->
        <div v-show="curTab === 2">
          <el-table
            class="expand-table"
            :data="group_express"
            header-cell-class-name="table_header"
            style="margin-bottom: 20px"
            border
          >
            <el-table-column align="center">
              <template slot-scope="{ row, $index }"> 包裹{{ $index + 1 }}</template>
            </el-table-column>
            <el-table-column label="商品" prop="" align="center" width="300">
              <template slot-scope="{ row, $index }">
                <div v-for="(item, index) in row.attr" class="flex" :key="item.id + index">
                  <span class="cell-table">{{ item.name }}</span>
                  <span>{{ item.spec }} * {{ item.express_amount }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="物流公司" prop="express_name" align="center"></el-table-column>
            <el-table-column label="物流单号" prop="express_no" align="center"></el-table-column>
            <el-table-column label="发货时间" prop="" align="center" width="150">
              <template slot-scope="{ row }">
                {{ row.time | data_format }}
              </template>
            </el-table-column>
            <el-table-column label="操作人" prop="operator" align="center"></el-table-column>
            <el-table-column label="操作" prop="" align="center">
              <template slot-scope="{ row }">
                <a href="javascript:;" @click="onClickDelivery(row)">修改发货信息</a>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 会员信息 -->
      <div class="basic-info-form" v-if="Number(is_ry_order)">
        <div class="sub-title">会员信息</div>
        <Form :model="order" label-position="right" :label-width="65">
          <div class="basic-form-item">
            <Row :gutter="40">
              <Col span="5">
                <FormItem label="会员卡状态:" :label-width="125">{{ vip_info.status_text || '-' }}</FormItem>
              </Col>
              <Col span="5">
                <FormItem label="会员卡有效期:" :label-width="125">{{ vip_info.period_text || '-' }}</FormItem>
              </Col>
              <Col span="5">
                <FormItem label="生效日期:">{{ vip_info.start_at || '-' }}</FormItem>
              </Col>
              <Col span="5">
                <FormItem label="过期日期:">{{ vip_info.expire_at || '-' }}</FormItem>
              </Col>
              <Col span="4">
                <FormItem label="关联赠品:">
                  <k-link
                    v-if="Number(vip_info.gift_order_id)"
                    :to="{
                      path: '/trade/order/detail',
                      query: { orderid: vip_info.gift_order_id, orderType: 'shop_order' },
                    }"
                    target="_blank"
                    >查看
                  </k-link>
                  <k-link
                    v-else-if="Number(vip_info.user_type === '4')"
                    :to="{
                      path: '/service/card/list',
                      query: { user_name: order.uid && users[order.uid].mobile },
                    }"
                    target="_blank"
                    >查看
                  </k-link>
                  <div v-else>-</div>
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>

      <!-- 交易记录 -->
      <div class="basic-info-form" v-if="records">
        <div class="sub-title">交易记录</div>
        <Table :loading="recordTableLoading" :columns="recordTableCols" :data="records">
          <template v-slot:index="{ row, index }">
            {{ records.length - index }}
          </template>
          <template v-slot:type="{ row, index }">
            <span v-if="recordStatus[row.type].kw === 'TYPE_PAYMENT'" class="text-success">{{
              recordStatus[row.type].desc
            }}</span>
            <span v-else class="text-danger">{{ recordStatus[row.type].desc }}</span>
          </template>
          <template v-slot:specs="{ row, index }">
            {{ row.specs || '-' }}
          </template>
          <template v-slot:amount="{ row, index }">
            {{ row.amount }}
          </template>
          <template v-slot:money="{ row, index }"> ￥{{ row.money }}</template>
          <template v-slot:create_time="{ row, index }">
            {{ row.create_time | data_format }}
          </template>
          <template v-slot:desc="{ row, index }">
            <span>{{ row.desc }}</span>
            <span v-if="row.operator"> - {{ row.operator }}</span>
          </template>
        </Table>
      </div>
    </div>
    <!--  his订单详情  -->
    <div v-else>
      <!-- 订单信息 -->
      <div class="basic-info-form">
        <div class="sub-title" style="display: flex; align-items: center">
          订单信息
          <Tooltip
            max-width="400"
            theme="light"
            placement="bottom"
            :offset="100"
            class="custom-tooltip tooltip-left-arrow ml6"
            style="margin-left: 6px"
          >
            <div slot="content">
              <div>订单金额=商品金额+加工费+挂号费+诊疗费+运费</div>
              <div>实际应付=订单金额-收款前取消-优惠券抵扣-下单优惠</div>
              <div>收款前取消：HIS订单在付款前单项作废部分的金额</div>
              <div>退款金额不能超过实际支付金额</div>
            </div>
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
          </Tooltip>
        </div>
        <Form :model="hisOrderInfo" label-position="right" :label-width="65">
          <div class="basic-form-item">
            <Row :gutter="40">
              <Col span="8">
                <FormItem label="订单编号:">{{ hisOrderInfo.out_trade_no }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="下单时间:">{{ hisOrderInfo.create_time | data_format }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="注册名:">
                  <KLink :to="{ path: '/user/detail', query: { uid: hisOrderInfo.uid } }" target="_blank">
                    {{ hisOrderInfo.nickname || '-' }}
                  </KLink>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="姓名:">{{ hisOrderInfo.real_name || '-' }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="手机号:">{{ hisOrderInfo.user_mobile || '-' }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="关联人:">{{ hisOrderInfo.relate_staff_text || '-' }}</FormItem>
              </Col>
              <Col span="8">
                <FormItem label="订单备注:">{{ hisOrderInfo.remark || '-' }}</FormItem>
              </Col>
            </Row>
          </div>
        </Form>
        <div class="price-table flex">
          <div class="flex-c">
            <div class="price-table-item">
              <div class="price-table-title">商品金额</div>
              <div class="price-table-content">￥{{ hisOrderInfo.pres_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">加工费</div>
              <div class="price-table-content">￥{{ hisOrderInfo.decoct_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">挂号费</div>
              <div class="price-table-content">￥{{ hisOrderInfo.reg_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">诊疗费</div>
              <div class="price-table-content">￥{{ hisOrderInfo.con_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">运费</div>
              <div class="price-table-content">￥{{ hisOrderInfo.post_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">订单金额</div>
              <div class="price-table-content">￥{{ hisOrderInfo.total_fee }}</div>
            </div>
          </div>
          <div class="flex-c">
            <div class="price-table-item">
              <div class="price-table-title">收款前取消</div>
              <div class="price-table-content" style="color: red">-￥{{ hisOrderInfo.payment_cancel }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">优惠券抵扣</div>
              <div class="price-table-content" style="color: red">-￥{{ hisOrderInfo.coupon_discount }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">下单优惠</div>
              <div class="price-table-content" style="color: red">-￥{{ hisOrderInfo.order_discount_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">实际应付</div>
              <div class="price-table-content">￥{{ hisOrderInfo.payment_fee }}</div>
            </div>
            <div class="price-table-item">
              <div class="price-table-title">已支付</div>
              <div class="price-table-content">￥{{ hisOrderInfo.received_payment }}</div>
            </div>
            <div class="price-table-item" v-if="Number(hisOrderInfo.wait_payment_fee) > 0">
              <div class="price-table-title">还需支付</div>
              <div class="price-table-content">￥{{ hisOrderInfo.wait_payment_fee }}</div>
            </div>
          </div>
          <div class="flex-c">
            <div class="price-table-item">
              <div class="price-table-title">退款金额</div>
              <div class="price-table-content">￥{{ hisOrderInfo.refunded_fee }}</div>
            </div>

            <div class="price-table-item">
              <div class="price-table-title">退款中金额</div>
              <div class="price-table-content">￥{{ hisOrderInfo.refunded_in_money }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 支付信息 -->
      <div class="basic-info-form">
        <div class="sub-title">支付信息</div>
        <Form :model="hisOrderInfo" label-position="right" :label-width="65">
          <div class="basic-form-item">
            <Row :gutter="40">
              <Col span="8">
                <FormItem label="支付状态:">
                  <mark-status
                    :type="
                      hisOrderInfo.status !== 'HAS_PAY'
                        ? getHisMarkStatusType(hisOrderInfo.status)
                        : getHisMarkStatusTextType(hisOrderInfo.status_text)
                    "
                    style="display: inline-block"
                    >{{ hisOrderInfo.status_text }}
                  </mark-status>
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
        <Table :columns="payTableCols" :data="hisOrderInfo.pay_info" border>
          <template v-slot:money="{ row }">
            {{ Number(row.money || 0).toFixed(2) }}
          </template>

          <template v-slot:create_time="{ row }">
            <!--    his订单使用finish_time        -->
            {{ row.finish_time | data_format }}
          </template>
          <template v-slot:pay_platform="{ row }">
            <span
              >{{ row.pay_platform_text }}
              <span v-if="row.uid !== row.trade_uid"
                >（共享<k-link
                  :to="{
                    path: '/user/detail',
                    query: {
                      uid: row.trade_uid,
                    },
                  }"
                  target="_blank"
                  >{{ row.trade_user }}</k-link
                >的储值余额抵扣）</span
              >
            </span>
          </template>
          <template v-slot:remark="{ row, index }">
            <div v-if="row.trade_channel === 'insure'">
              <a @click="showInsuranceModal(row)" style="margin-left: 10px">医保订单明细</a>
            </div>
            <div v-else>-</div>
          </template>
        </Table>
      </div>

      <!-- 配送信息 -->
      <div class="basic-info-form">
        <div class="sub-title">配送信息</div>
        <Form :model="hisOrderInfo" label-position="right" :label-width="65">
          <div class="basic-form-item">
            <Row :gutter="40">
              <Col span="8">
                <FormItem label="配送信息:">
                  <div>
                    <span>{{ hisOrderInfo.consignee_address || '-' }}</span>
                  </div>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="最后发货/发药时间:" :label-width="125"
                  >{{ hisOrderInfo.delivery_time | data_format }}
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="签收时间:">{{ hisOrderInfo.received_at }}</FormItem>
              </Col>
            </Row>
          </div>
        </Form>

        <!-- 收件信息 -->
        <div v-if="hisOrderInfo.source_from === 'hospital' && hisOrderInfo.shipping_method == 1">
          <el-table
            class="expand-table"
            :data="his_consignee_info"
            header-cell-class-name="table_header"
            style="margin-bottom: 20px"
            border
          >
            <el-table-column label="收件人姓名" prop="name" align="left">
              <template slot-scope="{ row, $index }">
                <span class="cell-table">{{ row.name || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="收件人手机号" prop="mobile" align="left">
              <template slot-scope="{ row, $index }">
                <span class="cell-table">{{ row.mobile || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="收件地址" prop="address" align="left">
              <template slot-scope="{ row, $index }">
                <span class="cell-table">{{ row.address || '-' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 订单商品 -->
      <div class="basic-info-form">
        <div class="sub-title" id="his_good_info">订单商品</div>
        <div class="flex flex-item-between flex-item-align">
          <div class="panel-nav">
            <a class="nav" :class="{ active: curTab === 0 }" @click.prevent.capture="curTab = 0"> 商品信息 </a>
            <a class="nav" :class="{ active: curTab === 1 }" @click="curTab = 1"> 退款信息 </a>
            <a
              class="nav"
              :class="{ active: curTab === 2 }"
              @click="curTab = 2"
              v-if="hisOrderInfo.source_from === 'hospital' && hisOrderInfo.shipping_method == 1"
            >
              包裹信息
            </a>
          </div>
          <a @click="toNewClinic('/his/charge/charge_details', `id=${orderid}&his_version=${his_version}`)"
            >查看诊疗消费详情</a
          >
        </div>

        <!-- 商品信息-->
        <div v-show="curTab === 0">
          <Table :columns="hisGoodTableCols" :data="hisPresList" style="margin-bottom: 10px">
            <template v-slot:type_text="{ row }">
              <poptip-table v-if="row.type" :columns="columns" :data="row.attrs_info" width="500">
                <a>{{ row.type_text }}</a>
                <div class="category-icon" v-if="row.type">类目</div>
              </poptip-table>
              <div v-else>{{ row.type_text }}</div>
            </template>
            <template v-slot:remark="{ row }">
              {{ row.remark || '-' }}
            </template>
            <template v-slot:action="{ row }">
              <a
                @click="toNewClinic('/his/charge/charge_details', `id=${orderid}&his_version=${his_version}`)"
                v-if="row.status !== 'WAIT_PAY' && row.status !== 'RETURN_BACK' && row.his_pay_status !== 'WAIT_PAY'"
                >退款</a
              >
              <div v-else>-</div>
            </template>
          </Table>
          <div class="good-total-box">
            <div>
              <div class="flex" style="justify-content: flex-end">
                <Button v-if="hisOrderInfo.status === 'HAS_PAY'" @click="hisOrderRefund" type="primary" class="mr-12"
                  >退款</Button
                >
                <div class="total-title">订单总计：</div>
                <div class="total-price">
                  <div>￥{{ hisPresInfo.total_fee }}</div>
                  <div
                    class="discount"
                    v-show="Number(hisPresInfo.discount_fee) > 0 && hisOrderInfo.pay_info?.length > 0"
                  >
                    已优惠：¥{{ hisPresInfo.discount_fee }}
                  </div>
                </div>
                <div v-show="Number(hisOrderInfo.received_payment) > 0 && hisOrderInfo.status !== 'PART_PAY'">
                  <a @click="showPayDetailButton = false" v-if="showPayDetailButton"
                    >收起
                    <Icon type="ios-arrow-up" style="margin-left: 4px" />
                  </a>
                  <a @click="showPayDetailButton = true" v-else
                    >详情
                    <Icon type="ios-arrow-down" style="margin-left: 4px" />
                  </a>
                </div>
              </div>
              <div class="total-detail-box" v-show="showPayDetailButton">
                <div>金额合计：¥{{ hisPresInfo.total_fee }}</div>
                <div v-show="Number(hisPresInfo.discount_fee) > 0">优惠抵扣：¥{{ hisPresInfo.discount_fee }}</div>
                <div v-show="hisOrderInfo.is_yb_order === '1' && Number(hisPresInfo.yb_pay_fee) > 0">
                  <div class="flex flex-item-between">
                    <div>医保支付：¥{{ hisPresInfo.yb_pay_fee }}</div>
                    <!--                    <div class="pay-time">{{ hisPresInfo.pay_time | data_format }}</div>-->
                  </div>
                  <div v-for="(item, index) in hisPresInfo.payment_detail" :key="index">
                    <div class="flex" style="margin-left: 5px">-{{ item.name }}: ￥{{ item.fee }}</div>
                  </div>
                </div>

                <div class="flex flex-item-between" v-show="Number(hisPresInfo.self_pay_fee) > 0">
                  <div>个人支付：¥{{ hisPresInfo.self_pay_fee }}</div>
                  <!--                  <div class="pay-time">{{ hisPresInfo.pay_time | data_format }}</div>-->
                </div>
                <div v-show="Number(hisPresInfo.yb_pay_fee) > 0 || Number(hisPresInfo.self_pay_fee) > 0">
                  支付方式：{{ hisPresInfo.pay_type_text || '-' }}
                </div>
                <div>应收金额：¥{{ hisPresInfo.payment_fee }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 退款信息-->
        <div v-show="curTab === 1">
          <Table :columns="hisRefundTableCols" :data="hisRefundData.list" style="margin-bottom: 10px">
            <template v-slot:type_text="{ row }">
              <poptip-table v-if="row.type" :columns="columns" :data="row.attrs_info" width="500">
                <a>{{ row.type_text }}</a>
                <div class="category-icon" v-if="row.type">类目</div>
              </poptip-table>
              <div v-else>{{ row.type_text }}</div>
            </template>
            <template v-slot:remark="{ row }">
              {{ row.remark || '-' }}
            </template>
          </Table>
          <div class="good-total-box" v-show="hisRefundData.list?.length > 0">
            <div>
              <div class="flex" style="justify-content: flex-end">
                <div class="total-title">退款总额：</div>
                <div class="total-price">
                  <div>￥{{ hisRefundData.refunded_fee }}</div>
                </div>
                <a @click="showRefundDetailButton = false" v-if="showRefundDetailButton"
                  >收起
                  <Icon type="ios-arrow-up" style="margin-left: 4px" />
                </a>
                <a @click="showRefundDetailButton = true" v-else
                  >详情
                  <Icon type="ios-arrow-down" style="margin-left: 4px" />
                </a>
              </div>
              <div class="total-detail-box" v-show="showRefundDetailButton">
                <div class="flex flex-item-between" v-for="(item, index) in hisRefundData.refunded_detail" :key="index">
                  <div>-{{ item.offline_pay_platform_text }}：¥{{ item.money }}</div>
                  <!--                  <div class="pay-time">{{ item.update_time | data_format }}</div>-->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 包裹信息-->
        <div v-show="curTab === 2 && hisOrderInfo.source_from === 'hospital' && hisOrderInfo.shipping_method == 1">
          <el-table
            class="expand-table"
            :data="his_express_list"
            header-cell-class-name="table_header"
            style="margin-bottom: 20px"
            border
          >
            <el-table-column align="center">
              <template slot-scope="{ row, $index }"> 包裹{{ $index + 1 }}</template>
            </el-table-column>
            <el-table-column label="商品" prop="goods_name" align="center" width="300">
              <template slot-scope="{ row, $index }">
                <span class="cell-table">{{ row.goods_name || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="物流公司" prop="express_name" align="center"></el-table-column>
            <el-table-column label="物流单号" prop="express_no" align="center"></el-table-column>
            <el-table-column label="发货时间" prop="" align="center" width="150">
              <template slot-scope="{ row }">
                {{ row.shipping_at || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作人" prop="hos_member_name" align="center">
              <template slot-scope="{ row }">
                {{ row.hos_member_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" prop="" align="center">
              <template slot-scope="{ row }">
                <a href="javascript:;" @click="updateHospitalShipInfo(row)">修改发货信息</a>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div class="block_20"></div>

      <!-- 交易记录 -->
      <div class="basic-info-form" v-if="hisTradeRecordList.length">
        <div class="sub-title">交易记录</div>
        <Table :loading="recordTableLoading" :columns="recordTableCols" :data="hisTradeRecordList">
          <template v-slot:index="{ row, index }">
            {{ hisTradeRecordList.length - index }}
          </template>
          <template v-slot:type="{ row, index }">
            <span class="text-success">{{ row.type_text }}</span>
          </template>
          <template v-slot:specs="{ row, index }">
            {{ row.spec || '-' }}
          </template>
          <template v-slot:amount="{ row, index }">
            {{ $route.query.orderType === 'shop_order' ? row.quantity : '-' }}
          </template>
          <template v-slot:money="{ row, index }"> ￥{{ row.money }}</template>
          <template v-slot:create_time="{ row, index }">
            {{ row.create_time | data_format }}
          </template>
          <template v-slot:desc="{ row, index }">
            <span>{{ row.remark || '-' }}</span>
          </template>
        </Table>
      </div>

      <!-- footer area -->
      <div style="height: 52px"></div>
    </div>
    <div class="fixed-bottom-wrapper">
      <!--            <back-button ></back-button>-->
      <Button @click="back">返回</Button>
      <Button class="btnStyle" @click="editRelatedPerson" style="margin-left: 10px">编辑关联人</Button>
      <Button
        class="btnStyle"
        type="primary"
        @click="sendCard"
        style="margin-left: 10px"
        v-if="order.send_coupon_status == '1' || hisOrderInfo.send_coupon_status === '1'"
        >赠送卡券
      </Button>

      <Button
        class="btnStyle"
        v-if="
          isRstClinic &&
          (order.source === 'rst_pc' || order.source === 'rst_weapp' || hisOrderInfo.source_from === 'his') &&
          !isInsurePay &&
          order.is_can_divide == '1'
        "
        type="primary"
        @click="jumpToAccount"
        style="margin-left: 10px"
        >核算业绩
      </Button>
      <Button
        class="btnStyle"
        type="primary"
        @click="outPrint"
        style="margin-left: 10px"
        v-if="order.show_print_button === '1' || hisOrderInfo.show_print_button === '1'"
        >打印收款凭证
      </Button>

      <!--      <Button class="btnStyle" @click="getToken">拿token</Button>-->
    </div>
    <edit-relate-modal
      :order_id="relatedModalFormData.editOrderId"
      :checked-staff-list="relatedModalFormData.relateStaffIds"
      :staff-list="staff_list"
      :class-type="relatedModalFormData.classType"
      :relate-visible.sync="relatedModalFormData.relateVisible"
      :is-detail="true"
    ></edit-relate-modal>

    <delivery-modal
      v-model="deliveryModal.modal"
      :orderId="deliveryModal.orderId"
      :company_code="deliveryModal.company_code"
      :express_no="deliveryModal.express_no"
      ship-title="修改订单发货信息"
      :modifyOrderAttrIds="deliveryModal.orderAttrIds"
      @on-success="onDeliverySuccess"
    ></delivery-modal>
    <refund-modal
      v-model="refundVisible"
      :refund-goods-id="refundGoodsId"
      :is_origin_pay="is_origin_pay"
      @reloadDetail="initOrderDetail"
      :isRefundAll="isRefundAll"
      :isRstClinic="isRstClinic"
      :is_ry_order="is_ry_order"
    ></refund-modal>
    <rst-refund-modal
      v-model="rstRefundVisible"
      :refund-goods-id="refundGoodsId"
      :is_origin_pay="is_origin_pay"
      @reloadDetail="initOrderDetail"
      :isRefundAll="isRefundAll"
      :isRstClinic="true"
      :is_ry_order="is_ry_order"
    ></rst-refund-modal>
    <insurance-modal
      v-model="insuranceVisible"
      :order_id="$route.query.orderid"
      :order_code="insuranceOrderCode"
    ></insurance-modal>
    <div v-show="false">
      <lodopPrintDiv ref="lodopPrintDiv" :defaultParams="defaultPrintParams" />
    </div>

    <send-modal
      v-model="sendModalVisible"
      :order="this.$route.query.orderType === 'shop_order' ? order : hisOrderInfo"
      :type_order="this.$route.query.orderType"
      @success="initOrderDetail"
    ></send-modal>
    <Modal
      v-model="refundOrderVisible"
      :mask-closable="false"
      title="是否退款"
      @on-ok="onRefundSave"
      @on-cancel="onRefundOrderCancel"
      :loading="statemanager.saveBtnLoading"
    >
      <Form ref="SubmitFormValidate" :label-width="70">
        <FormItem label="售后原因">
          <Input
            type="textarea"
            v-model="refund_remark"
            placeholder="请输入售后原因"
            maxlength="200"
            :rows="4"
            show-word-limit
          ></Input>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
/* eslint-disable */
import EditRelateModal from './components/edit-relates';
import DeliveryModal from '@/view/trade/components/ModifyShippingModal.vue';
import RefundModal from './components/RefundModal.vue';
import insuranceModal from './components/insuranceModal.vue';
import { isDirectClinic } from '@/libs/runtime';
import { definCss } from './lodop';
import lodopPrintDiv from './components/lodopPrintDiv.vue';
import '@/libs/LodopFuncs';
import sendModal from './components/sendModal';
import poptipTable from '@/components/poptipTable';
import { isEmpty } from '../../../utils/helper';
import { isRstClinic } from '@/libs/runtime';
import config from '@/config';
import RstRefundModal from '@/view/trade/order/components/RstRefundModal.vue';

export default {
  name: 'detail',
  components: {
    lodopPrintDiv,
    EditRelateModal,
    DeliveryModal,
    RefundModal,
    insuranceModal,
    sendModal,
    poptipTable,
    RstRefundModal,
  },
  data() {
    const init_query_form_data = {
      orderid: this.$route.query.orderid,
      r: '',
      class: this.$route.query.orderType,
    };
    const init_relate_form_data = {
      editOrderId: this.$route.query.orderid,
      relateVisible: false,
      relateStaffIds: [],
      classType: this.$route.query.orderType,
    };
    return {
      tableLoading: false,
      refundOrderVisible: false,
      relatedModalFormData: {
        ...init_relate_form_data,
      },
      queryFormData: { ...init_query_form_data },
      getLoading: true,
      order: {},
      sharer_uid: '',
      statusDesc: {},
      typeDesc: {},
      orderAttrStatusDesc: {},
      allowRefundStatus: {},
      closedTypeDesc: {},
      payTypeDesc: {},
      records: [],
      recordStatus: {},
      rebate_money: '',
      users: {},
      userLevelDesc: {},
      canRefundOrderAttrs: {},
      canRefundPostFee: 0,
      refund_order_attrs: {},
      orderid: this.$route.query.orderid,
      ap_channel_is_b_c: false, //是否是聚合支付
      // 退款表单数据
      refund_order_attrs_form_data: {},
      refund_remark: '',
      refund_post_fee_form_data: {
        show: false,
        money: 0,
      },

      statemanager: {
        saveRefundBtnLoading: false,
      },
      staff_list: [],
      relate_staff_list: [],
      hisOrderInfo: {},
      hisPresList: [],
      hisPresInfo: {},
      hisTradeRecordList: [],
      checkRepeatObj: {},
      curTab: 0,
      group_express: [],
      deliveryModal: {
        modal: false,
        orderId: '',
        orderAttrIds: [],
        company_code: '',
        express_no: '',
      },
      refundVisible: false, //退款弹窗
      rstRefundVisible: false, //退款弹窗
      refundGoodsId: '',
      is_origin_pay: false,

      payTableCols: [
        { title: '支付金额', slot: 'money', width: 200 },
        { title: '支付时间', slot: 'create_time', width: 200 },
        { title: '支付方式', slot: 'pay_platform' },
        { title: '支付单号', key: 'trade_no', width: 200 },
        { title: '补充信息', slot: 'remark', minWidth: 200 },
      ],
      hisGoodTableCols: [
        { title: '序号', type: 'index', width: 100 },
        { title: '商品', slot: 'type_text', width: 220 },
        { title: '数量', key: 'total_quantity', width: 80 },
        { title: '状态', key: 'status_text' },
        { title: '应付金额', key: 'total_fee' },
        { title: '优惠金额', key: 'discount_fee' },
        { title: '医保支付', key: 'yb_pay_fee', isYBColumn: true },
        { title: '个人支付', key: 'self_pay_fee' },
        // { title: '操作', slot: 'action', width: 120 },
      ],
      showPayDetailButton: false,
      showRefundDetailButton: false,
      recordTableLoading: false,
      recordTableCols: [
        { title: '序号', slot: 'index', width: 60 },
        { title: '类型', slot: 'type' },
        { title: '规格', slot: 'specs' },
        { title: '数量', slot: 'amount' },
        { title: '金额', slot: 'money' },
        { title: '时间', slot: 'create_time' },
        { title: '描述', slot: 'desc' },
      ],

      hisRefundTableCols: [
        { title: '序号', type: 'index', width: 100 },
        { title: '商品', slot: 'type_text' },
        { title: '已退数量', key: 'total_quantity' },
        { title: '已退金额', key: 'refunded_fee' },
        { title: '医保退款', key: 'yb_refund_fee', isYBColumn: true },
        { title: '个人退款', key: 'self_refund_fee' },
        { title: '状态', key: 'status_text' },
      ],
      hisRefundData: {},
      insuranceVisible: false,
      insuranceOrderCode: '',
      distributeTableCols: [
        { title: '收件人姓名', key: 'name' },
        { title: '收件人手机号', key: 'mobile' },
        { title: '收件地址', key: 'address', minWidth: 300 },
      ],
      distributeList: [],
      // lopdop 用于打印
      LODOP: null,
      defaultPrintParams: { orderid: '', class: '' },
      his_express_list: [], // his的包裹信息
      his_consignee_info: [],
      sendModalVisible: false,

      columns: [
        { title: '名称', key: 'name', width: 200, align: 'left' },
        { title: '类型', key: 'type_text', slot: 'type_text', textColor: '#909399', align: 'left' },
        {
          title: '来源',
          key: 'source_platform_text',
          slot: 'source_platform_text',
          textColor: '#909399',
          align: 'left',
        },
        { title: '数量', key: 'quantity', align: 'right' },
        { title: '单位', key: 'unit', align: 'left' },
      ],
      confirmLoading: false,
      isRefundAll: false,
      is_can_refund: '',
      vip_info: {},
      is_ry_order: '',
      his_version: '',
    };
  },
  computed: {
    isTodayOrder() {
      function isToday(payTime) {
        if (!payTime) return false;

        // 将秒级时间戳转换为毫秒
        const payDate = new Date(payTime * 1000);
        const today = new Date();
        // 重置时间部分，只比较年月日
        return (
          payDate.getFullYear() === today.getFullYear() &&
          payDate.getMonth() === today.getMonth() &&
          payDate.getDate() === today.getDate()
        );
      }
      return isToday(this.order?.pay_time);
    },
    getVirtualImage() {
      if (this.is_ry_order == '1') {
        return 'https://img-sn-i01s-cdn.rsjxx.com/image/2025/0219/101535_43264.jpeg-B.200';
      } else {
        return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0923/100618_15681.png-B.200';
      }
    },
    showRefundAllButton() {
      return this.isRstClinic && this.is_can_refund === '1';
    },
    showRefundAllAction() {
      return this.refund_post_fee_form_data.show;
    },
    isDirect() {
      return isDirectClinic();
    },
    isRstClinic: () => isRstClinic(),
    getMarkStatusType() {
      return status => {
        switch (status) {
          case '7000': // 已关闭
            return 'gray';
          case '1000': // 待付款
          case '2000': // 待发货
            return 'warn';
          case '9999': // 已完成
            return 'success';
          case '1100': // 已付款
          case '6000': // 已发货
            return 'blue';
          default:
            return 'default';
        }
      };
    },
    getHisMarkStatusType() {
      return status => {
        switch (status) {
          case 'CLOSED': // 已关闭
            return 'gray';
          case 'WAIT_PAY': // 代付款
          case 'IN_PAY': // 支付中
            return 'warn';
          case 'PART_PAY': // 部分支付
            return 'blue';
          default:
            return 'default';
        }
      };
    },
    // his订单状态HAS_PAY在后端中返回对应的status_text不一致，保证样式统一，做区分处理
    getHisMarkStatusTextType() {
      return status_text => {
        switch (status_text) {
          case '已完成':
            return 'success';
          case '待发货':
            return 'warn';
          default:
            return 'default';
        }
      };
    },
    // 是否医保支付
    isInsurePay() {
      if (this.hisOrderInfo.payment_detail?.length > 0) {
        return this.hisOrderInfo.payment_detail.some(item => item.trade_channel === 'INSURE');
      } else {
        return false;
      }
    },
  },
  created() {
    this.getStaffList();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.initOrderDetail();
  },

  mounted() {},

  methods: {
    hisOrderRefund() {
      this.$Modal.confirm({
        title: '温馨提示',
        content: `如需要继续退款，请在患者详情中先对退款内容进行作废？`,
        okText: '继续退款',
        onOk: () => {
          this.toNewClinic('/his/patient/patientinfo', `id=${this.hisOrderInfo.pt_id}`);
        },
      });
    },
    showRefundPop() {
      this.$nextTick(() => {
        this.$refs.remarkInput.focus();
      });
    },
    // 聚合支付订单只能整单退款
    confirmRefundAll(id) {
      this.confirmLoading = true;
      const refund_order_attrs = {};
      for (const refundOrderAttrsKey in this.canRefundOrderAttrs) {
        const info = this.canRefundOrderAttrs[refundOrderAttrsKey];
        refund_order_attrs[refundOrderAttrsKey] = {
          id: refundOrderAttrsKey,
          refund_money: info.money,
          refund_amount: info.amount,
        };
      }
      const formData = {
        orderId: this.$route.query.orderid,
        refund_order_attrs,
        pay_platform: 'refund_old_way',
        refund_remark: this.refund_remark,
      };

      console.log('退款=》', 111);

      io.post('clinic/order.order.refund', formData)
        .then(
          () => {
            this.$Message.success('提交成功');
            this.initOrderDetail();
          },
          error => {}
        )
        .finally(() => {
          this.closeRefundPopTip(id);
          this.confirmLoading = false;
        });
    },
    closeRefundPopTip(id) {
      const refs = this.$refs[`refundPoptip${id}`];
      this.refund_remark = '';
      refs && (refs.visible = false);
    },
    sendCard() {
      this.sendModalVisible = true;
    },
    updateHospitalShipInfo(row) {
      this.$router.push({
        path: '/internet-hospital/order/detail',
        query: {
          order_code: row.order_code,
          updateLogistics: true,
        },
      });
    },
    onClickDelivery(row) {
      console.log('=>(detail.vue:1427) row', row);
      this.deliveryModal.company_code = row.express_company_code;
      this.deliveryModal.express_no = row.express_no;
      this.deliveryModal.orderId = this.$route.query.orderid;
      this.deliveryModal.orderAttrIds = row.orderAttrIds;
      this.deliveryModal.modal = true;
    },
    onDeliverySuccess() {
      this.deliveryModal.modal = false;
      this.initOrderDetail();
    },
    toNewClinic(path, query) {
      const a = document.createElement('a');
      a.target = '_blank';
      // /his/charge/charge_details
      // id=${this.orderid}&his_version=${this.his_version}
      a.href = `${config.HISDomain}${path}?${query}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    initOrderDetail() {
      const { orderid, orderType } = this.$route.query;
      this.refundVisible = false;
      this.rstRefundVisible = false;
      if (orderType === 'his_pay_order') {
        this.getHisOrder();
      } else if (orderType === 'shop_order') {
        this.getShopOrder();
      }
    },
    editRelatedPerson(row) {
      this.relatedModalFormData.relateStaffIds = this.relate_staff_list;
      this.relatedModalFormData.relateVisible = true;
    },
    getStaffList() {
      this.$api.getStaffList().then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px', res);
        this.staff_list = res.staff_list;
      });
    },
    refundAll() {
      this.isRefundAll = true;
      const hasServiceUsed = this.order.orderAttrs?.some(item => item.is_use_service_card === '1');
      if (hasServiceUsed) {
        const isCard = this.order.orderAttrs?.some(
          attr => attr.is_use_service_card === '1' && attr.goods_type === '45'
        );
        this.$Modal.confirm({
          title: isCard ? '有部分已被兑换成卡券的不可退款，是否继续退剩余部分?' : '有部分卡券已被使用，是否继续退款？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            this.confirmRefund('');
          },
        });
        return;
      }
      this.confirmRefund('');
    },
    confirmRefund(orderAttrid, isUse = '0') {
      console.log(
        '%c [ orderAttrid ]-1595-「detail.vue」',
        'font-size:13px; background:#cba848; color:#ffec8c;',
        orderAttrid
      );

      orderAttrid && (this.isRefundAll = false);
      // 说明是全部退款
      if (!orderAttrid && this.isRstClinic) {
        this.refundGoodsId = '';
        this.rstRefundVisible = true;
        return;
      }
      //  直营诊所退款逻辑
      if ((this.isDirect || this.isRstClinic) && isUse !== '1' && orderAttrid !== 'POST_FEE' && orderAttrid) {
        this.refundGoodsId = orderAttrid;
        if (this.isRstClinic) {
          this.rstRefundVisible = true;
        } else {
          this.refundVisible = true;
        }
        return;
      }
      //  直营诊所退款逻辑
      let flag = S.isUndefined(this.refund_order_attrs_form_data[orderAttrid]);

      if (isUse === '0' || orderAttrid == 'POST_FEE') {
        this.onClickRefund(orderAttrid);
      }
      if (isUse === '1' && orderAttrid !== 'POST_FEE') {
        if (flag === false) {
          this.onClickRefund(orderAttrid);
        } else {
          const isCard = this.order.orderAttrs?.some(attr => attr.id === orderAttrid && attr.goods_type === '45');
          this.$Modal.confirm({
            title: isCard
              ? '有部分已被兑换成卡券的不可退款，是否继续退剩余部分?'
              : '有部分卡券已被使用，是否继续退款？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              if (this.isDirect || this.isRstClinic) {
                this.refundGoodsId = orderAttrid;
                if (this.isRstClinic) {
                  this.rstRefundVisible = true;
                } else {
                  this.refundVisible = true;
                }
                return;
              }
              this.onClickRefund(orderAttrid);
            },
          });
        }
      }
    },
    back() {
      if (this.$route.query.from) {
        this.$router.back();
        return;
      }
      this.$router.push({
        path: '/trade/order/list',
      });
    },
    onClickRefund: function (orderAttrid) {
      if (orderAttrid == 'POST_FEE') {
        if (this.refund_post_fee_form_data.show) {
          this.refund_post_fee_form_data.show = false;
          this.refund_post_fee_form_data.money = 0;
        } else {
          this.refund_post_fee_form_data.show = true;
          this.refund_post_fee_form_data.money = this.canRefundPostFee;
        }
      } else {
        if (S.isUndefined(this.refund_order_attrs_form_data[orderAttrid])) {
          this.$set(this.refund_order_attrs_form_data, orderAttrid, {
            id: this.canRefundOrderAttrs[orderAttrid].id,
            refund_amount: +this.canRefundOrderAttrs[orderAttrid].amount,
            refund_money: +this.canRefundOrderAttrs[orderAttrid].money,
          });
        } else {
          this.$delete(this.refund_order_attrs_form_data, orderAttrid);
        }
      }
    },

    getShopOrder() {
      this.tableLoading = true;
      this.$api
        .getShopOrderDetail(this.queryFormData)
        .then(data => {
          console.log('-> %c data  ===    %o', 'font-size: 15px;color: #fa8c16 ;', data);
          this.order = data.order;
          this.vip_info = data.vip_info || {};
          this.is_ry_order = data.is_ry_order;
          this.sharer_uid = data.sharer_uid;
          this.statusDesc = data.statusDesc;
          this.typeDesc = data.typeDesc;
          this.orderAttrStatusDesc = data.orderAttrStatusDesc;
          this.allowRefundStatus = data.allowRefundStatus;
          this.closedTypeDesc = data.closedTypeDesc;
          this.payTypeDesc = data.payTypeDesc;
          this.records = data.records;
          this.recordStatus = data.recordStatus;
          this.rebate_money = data.rebate_money;
          this.users = data.users;
          this.ap_channel_is_b_c = data.order.ap_channel_is_b_c === '1';
          this.userLevelDesc = data.userLevelDesc;
          this.canRefundOrderAttrs = data.canRefundOrderAttrs;
          this.canRefundPostFee = data.canRefundPostFee;
          this.refund_order_attrs = data.refund_order_attrs;
          this.getLoading = false;
          this.refund_remark = '';
          this.refund_post_fee_form_data.money = 0;
          this.refund_post_fee_form_data.show = false;
          this.relate_staff_list = data.relate_staff_list;
          this.group_express = data.group_express;
          this.is_origin_pay = data.is_origin_pay === '1';
          this.is_can_refund = data.is_can_refund;
          if (data.order?.consignee_address?.consignee) {
            let { province, city, county, other } = data.order.consignee_address?.location;
            let address = `${province.name}${city.name}${county.name}${other}`;
            this.distributeList = [
              {
                name: data.order.consignee_address.consignee,
                mobile: data.order.consignee_address.mobile,
                address: address,
              },
            ];
          }
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.scrollToAnchor();
        });
    },
    getHisOrder() {
      this.tableLoading = true;
      this.$api
        .getHisOrderDetail(this.queryFormData)
        .then(data => {
          if (this.isRstClinic) {
            this.hisGoodTableCols = this.hisGoodTableCols.filter(item => !item.isYBColumn);
            this.hisRefundTableCols = this.hisRefundTableCols.filter(item => !item.isYBColumn);
          }
          this.his_version = data.his_version;
          this.hisOrderInfo = data.order;
          this.getLoading = false;
          this.hisPresList = data.pres_list.list;
          this.hisPresInfo = data.pres_list;
          this.hisRefundData = data.refunded_data;
          this.his_express_list = data.order.express_list;
          this.his_consignee_info = [
            {
              name: data.order.consignee_info?.name || '-',
              mobile: data.order.consignee_info?.mobile || '-',
              address: data.order.consignee_info?.address || '-',
            },
          ];
          console.log('=>(detail.vue:953) this.hisPresList', this.hisPresList);
          this.hisTradeRecordList = data.trade_record_list;
          this.relate_staff_list = data.order.relate_staff_list;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.scrollToAnchor();
        });
    },

    onRefundOrderOpen() {
      if (isEmpty(this.refund_order_attrs_form_data) || Object.keys(this.refund_order_attrs_form_data).length === 0) {
        this.$Message.error('请选择退款项');
        return;
      }
      this.refundOrderVisible = true;
      this.refund_remark = '';
    },
    onRefundOrderCancel() {
      this.refundOrderVisible = false;
      this.refund_remark = '';
    },
    onRefundSave: function () {
      if (isEmpty(this.refund_order_attrs_form_data) || Object.keys(this.refund_order_attrs_form_data).length === 0) {
        this.$Message.error('请选择退款项');
        return;
      }
      this.statemanager.saveBtnLoading = true;
      let formData = {
        orderId: this.order.id,
        refund_post_fee: this.refund_post_fee_form_data.money,
        refund_order_attrs: JSON.stringify(this.refund_order_attrs_form_data),
        refund_remark: this.refund_remark,
      };
      console.log('🚀 ~ formData=>', formData);

      io.post('clinic/order.order.refund', formData)
        .then(
          () => {
            this.$Message.success('提交成功');
            this.initOrderDetail();
          },
          error => {
            {
            }
          }
        )
        .finally(() => {
          this.statemanager.saveBtnLoading = false;
        });
    },

    submitQueryForm: function (replace) {
      // 通过修改url参数，触发路由前置守卫(beforeRouteUpdate)，在前置守卫中获取列表数据
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      if (replace) {
        this.$router.replace({ query: this.queryFormData });
      } else {
        this.$router.push({ query: this.queryFormData });
      }
    },
    rowKeyFn(row) {
      if (!this.checkRepeatObj) {
        this.checkRepeatObj = {};
      }
      if (row) {
        if (row.id) {
          if (this.checkRepeatObj[row.id]) {
            if (!row._secondId) {
              row._secondId = S.random() + '';
            }
            /** 方便根据key重用元素 */
            return row._secondId;
          } else {
            this.checkRepeatObj[row.id] = 1;
            return row.id;
          }
        }
        console.log('row.id为空');
        if (!row._secondId) {
          row._secondId = S.random() + '';
        }
        return row._secondId;
      } else {
        console.log('row为空');
        return S.random() + '';
      }
    },
    // 滚动到锚点
    scrollToAnchor() {
      if (this.$route.query.anchor) {
        if (this.$route.query.orderType === 'his_pay_order') {
          let goodInfoDom = document.querySelector('#his_good_info');
          window.scrollTo({ top: goodInfoDom.offsetTop - 56 - 5 });
        } else {
          let goodInfoDom = document.querySelector('#good_info');
          window.scrollTo({ top: goodInfoDom.offsetTop - 56 - 5 });
        }

        if (this.$route.query.anchor === 'refund') {
          this.curTab = 1;
        }
      }
    },

    showInsuranceModal(row) {
      this.insuranceVisible = true;
      this.insuranceOrderCode = row.trade_no;
    },

    outPrint() {
      if (this.validLodop()) {
        this.defaultPrintParams.class = this.$route.query.orderType;
        this.defaultPrintParams.orderid = this.$route.query.orderid;
        this.$refs.lodopPrintDiv.getPrintData().then(res => {
          this.definePrint(() => {
            // this.LODOP.PRINT();
            // this.LODOP.PREVIEW();
            this.LODOP.PREVIEW();
          });
        });
      }
    },
    printPreview() {
      if (this.validLodop()) {
        this.definePrint(() => {
          this.LODOP.PREVIEW();
        });
      }
    },

    definePrint(cb) {
      let strStyleCSS = definCss;
      console.log(this.$refs.lodopPrintDiv.$el);
      console.log(this.domToString(this.$refs.lodopPrintDiv.$el));
      let strFormHtml = strStyleCSS + '<body>' + this.domToString(this.$refs.lodopPrintDiv.$el) + '</body>';
      // let strHtml = domToString(lodopPrintDiv.value)
      this.LODOP.ADD_PRINT_HTM(0, 0, '100%', '100%', strFormHtml);
      // LODOP.value.SET_PRINT_PAGESIZE(3)
      this.LODOP.SET_PRINT_PAGESIZE(3, 1385, 50, ''); //这里3表示纵向打印且纸高"按内容的高度"；1385表示纸宽138.5mm；45表示页底空白4.5mm
      console.log(strFormHtml);
      cb();
    },
    domToString(node) {
      return new XMLSerializer().serializeToString(node);
    },
    validLodop() {
      let LODOP_OBJ = (window?.getLodop && window.getLodop()) || null;
      if (LODOP_OBJ == null) {
        // this.$Message.error('未添加打印服务');
        this.$Modal.confirm({
          cancelText: '关闭',
          okText: '下载插件',
          onOk: () => {
            window.open('https://static.rsjxx.com/file/printer_drive/CLodop_Setup_for_Win64NT_6.579EN.zip');
          },
          render: h => this.dontLodop(h, '检测到未安装打印插件', '安装完成后刷新页面至「订单管理」中进行收费凭证打印'),
        });
        return false;
      } else {
        this.LODOP = LODOP_OBJ;
        // this.LODOP.SELECT_PRINTER();
        let printCount = typeof this.LODOP?.GET_PRINTER_COUNT === 'function' ? this.LODOP.GET_PRINTER_COUNT() : 0;
        const availablePrinterNames = ['GP-C200 Series', 'XP-80T', 'XP-80C'];
        // const defaultPrinterName = 'TSC TE4503';
        console.log(printCount);
        let printNameArr = [];
        for (let index = 0; index < printCount; index++) {
          printNameArr.push(this.LODOP.GET_PRINTER_NAME(index));
        }
        console.log(printNameArr);
        // item === defaultPrinterName
        let findPrintIndex = printNameArr.findIndex(item => availablePrinterNames.includes(item));
        this.LODOP.SET_PRINTER_INDEXA(findPrintIndex);
        // let printerName = this.LODOP.GET_PRINTER_NAME(-1);
        // console.log(printerName, defaultPrinterName);
        if (findPrintIndex === -1) {
          // this.$Message.error('请设置默认打印机为：' + defaultPrinterName);
          this.$Modal.confirm({
            cancelText: '关闭',
            okText: '下载驱动',
            onOk: () => {
              window.open(
                'https://static.rsjxx.com/file/printer_drive/GP-C200%20%E7%B3%BB%E5%88%97%E9%A9%B1%E5%8A%A8%20V1.1.zip'
              );
            },
            render: h => this.dontLodop(h, '未检测到小票打印机', '请先检查设备连接及驱动安装情况'),
          });
          return false;
        }
        return true;
      }
    },
    dontLodop(h, context1, context2) {
      return h('p', {}, [
        h('p', {}, [
          h('svg-icon', {
            attrs: {
              iconClass: 'tips',
              width: '20',
              height: '20',
            },
            style: {
              color: '#ff9900',
              marginRight: '5px',
            },
          }),
          context1,
        ]),
        h('p', context2),
      ]);
    },
    jumpToAccount() {
      if (this.order.is_can_divide === '2') {
        this.$Message.error('订单未支付');
        return;
      }

      this.$router.push({
        path: '/trade/order/account',
        query: {
          order_id: this.$route.query.orderid,
          source: this.$route.query.orderType === 'shop_order' ? 'SHOP' : 'HIS',
        },
      });
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.get();
    next();
  },
};
</script>

<style lang="less" scoped>
.refund-pop {
  padding: 12px 13px;

  .btn-box {
    text-align: right;
    margin-top: 16px;
  }

  :deep(.widget-form-label) {
    font-size: 12px;
  }
}

.text-center {
  text-align: center;
}

.record-table {
  td {
    text-align: center;
  }
}

.sub-title {
  font-weight: 500;
  font-size: 16px;
  color: #333333;
  margin-bottom: 10px;
}

.basic-info-form {
  margin-bottom: 40px;

  .basic-form-item {
    //background-color: #f7f8fa;
    .ivu-form-item {
      margin-bottom: 0;
    }

    ::v-deep .ivu-form-item-label {
      color: #aaaaaa;
      line-height: 32px;
      padding: 0 12px 0 0;
    }

    .ivu-form-item-content {
      line-height: 18px;
    }

    .ivu-divider {
      margin: 0;
    }
  }
}

.price-table {
  display: flex;
  flex-wrap: wrap;
  width: 904px;
  //max-width: 1000px;
  border: 1px solid #dfdfdf;
  box-sizing: border-box;

  .price-table-item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dfdfdf;

    &:nth-child(6n) {
      border-bottom: none;
    }

    &:nth-last-child(2) {
      //border-bottom: none;
    }

    .price-table-title {
      width: 100px;
      padding: 9px 16px;
      background-color: #f7f7f7;
      border-right: 1px solid #dfdfdf;
      box-sizing: border-box;
      text-align: center;
    }

    .price-table-content {
      width: 200px;
      padding: 9px 0 9px 8px;
      box-sizing: border-box;
    }
  }

  .flex-c {
    border-right: 1px solid #dfdfdf;

    &:last-child {
      border: 0;
    }
  }
}

.good-total-box {
  display: flex;
  justify-content: flex-end;

  .total-title {
    font-weight: 500;
    font-size: 14px;
    color: #333333;
  }

  .total-price {
    font-weight: 500;
    font-size: 14px;
    margin-right: 6px;
    color: red;
    text-align: right;

    .discount {
      font-size: 12px;
      color: #999999;
    }
  }

  .total-detail-box {
    margin-top: 10px;
    width: 190px;
    padding: 10px;
    box-sizing: border-box;
    background-color: #fafafb;
    color: #818692;

    .pay-time {
      color: #bbbbbb;
    }
  }
}

.category-icon {
  display: inline-block;
  border: 1px solid #0059ff;
  border-radius: 4px;
  color: #0059ff;
  padding: 2px 5px;
  transform: scale(0.8);
  cursor: pointer;
}
</style>
<style lang="less" scoped>
.expand-table {
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}

.record-table {
  td {
    text-align: center;
  }
}

.freight {
  height: 60px;
  line-height: 60px;
  padding: 0 20px;
}

::v-deep .table_header {
  background-color: #f2f2f2 !important;
  color: #333;
}

.orderItem {
  width: 900px;
  font-weight: 700;
  text-align: center;
  background-color: #ccc;
}

::v-deep .el-table {
  font-size: 12px;
  color: #333;
  line-height: 0;

  .expanded {
    position: relative;

    .cell {
      //  display: inline-flex;
      align-items: center;
    }
  }
}

::v-deep .el-table [class*='el-table__row--level'] .el-table__expand-icon {
  position: absolute;
  top: 65px;
  left: 12px;
  width: 90px;
  color: #27f;
}

::v-deep .el-icon-arrow-right:before {
  content: '展开套餐明细';
}

::v-deep .el-table__expand-icon--expanded {
  transform: rotate(0);
}

// 修改展开之后的图标
::v-deep .el-table__expand-icon--expanded .el-icon-arrow-right:before {
  content: '收起套餐明细';
}

.el-table--border::after,
.el-table--group::after {
  width: 2px;
}
</style>
<style>
.ivu-modal-confirm-head {
  display: flex;
  align-items: center;
}
</style>
