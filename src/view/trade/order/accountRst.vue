<template>
  <div v-loading="pLoading" class="rst-account-page">
    <!-- 顶部筛选 -->
    <div class="filter-bar" v-show="showSalaes">
      <Button type="primary" @click="onSelectSsles">请选择商品销售人员</Button>
    </div>

    <!-- 订单业绩表格 -->
    <!-- :Loading="pLoading" -->
    <div class="order-table">
      <!-- 平台 -->
      <div v-for="(serivce, key) in serivceMap" :key="key">
        <Table
          :columns="colums"
          :data="serivce?.list"
          class="mb20"
          border
          disabled-hover
          :span-method="tableSpanMethod"
        >
          <template v-slot:prod_name="{ row }">{{ row.prod_name || '-' }}</template>
          <template v-slot:belongs_name="{ row }">{{ row.belongs_name || row.goods_name || '-' }}</template>

          <template v-slot:money="{ row }">
            {{ format_money(row.money) }}
          </template>

          <!-- 销售人员 -->
          <template v-slot:divide_members="{ row }">
            <div class="cell">
              <div :class="{ 'color-999': !row.divide_member.id }">
                {{ row.divide_member.name || '-' }}
              </div>
              <Tag v-if="row.divide_member.sales_level" style="min-width: 50px">
                {{ levelFormatter(row.divide_member) }}
              </Tag>
            </div>
          </template>

          <!-- 等级 -->
          <template v-slot:level="{ row }">
            <span> {{ row.divide_member?.level || '-' }}</span>
          </template>

          <!-- 预估 -->
          <template v-slot:esti_divide_money="{ row }">
            <span v-text-format.number="row.divide_member.esti_divide_money"></span>
          </template>

          <!-- 实际 -->
          <template v-slot:divide_money="{ row }">
            <span v-if="row.divide_member?.is_gen_fixed_factor == 0" class="color-999">次月后生成 </span>
            <template v-else>
              <div v-text-format.number="row.divide_member.divide_money"></div>
              <Tooltip v-if="row.divide_member?.calc_rules_text" placement="bottom">
                <a>查看计算公式</a>
                <div slot="content">
                  <div class="tip-inner">{{ row.divide_member?.calc_rules_text }}</div>
                </div>
              </Tooltip>
            </template>
          </template>

          <!-- 商品状态 -->
          <template v-slot:card_no="{ row }">
            <div v-if="row.goods_status == 0" class="color-999">{{ row.goods_status_desc || '-' }}</div>
            <div v-if="row.goods_status == 1">
              <div>{{ row.goods_status_desc || '-' }}</div>
              <a v-if="row.divide_member.card_no" @click="onLink(row.divide_member, '/service/writeDetail/list')">{{
                row.divide_member.card_no
              }}</a>
            </div>
          </template>

          <!-- 结算状态 -->
          <template v-slot:status_desc="{ row }">
            <div :class="{ 'color-999': row.status != 2 }">{{ row.status_desc || '-' }}</div>
          </template>

          <!-- 分佣类型 -->
          <template v-slot:divide_type="{ row }">
            {{ row.divide_member.divide_type_desc || '-' }}
          </template>

          <!-- 结算时间 -->
          <template v-slot:settle_time="{ row }">
            {{ formatSettleTime(row) || '-' }}
          </template>

          <!-- 结算规则 -->
          <template v-slot:settle_rules="{ row }">
            <span class="color-999">{{ row.settle_rules || '-' }}</span>
          </template>
        </Table>
      </div>

      <!-- his -->
      <div v-if="hisTableList.length > 0">
        <Table :columns="colums" :data="hisTableList" class="mb20" border disabled-hover :span-method="tableSpanMethod">
          <template v-slot:prod_name="{ row }">{{ row.prod_name || '-' }}</template>
          <template v-slot:belongs_name="{ row }">{{ row.belongs_name || row.goods_name || '-' }}</template>

          <template v-slot:money="{ row }">
            <span>¥{{ row.money || '-' }}</span>
          </template>

          <template v-slot:divide_members="{ row }">
            <a
              v-if="row.divide_member.id"
              class="cell"
              :class="{ check: isCheck(row, row.divide_member) }"
              @click="onMultipleSales(row, row.divide_member)"
            >
              {{ row.divide_member.name || '-' }}</a
            >
            <div
              v-else
              class="cell"
              :class="{ check: isCheck(row, { type: 1 }), 'color-999': !isCheck(row, { type: 1 }) }"
              @click="onMultipleSales(row, { type: 1 })"
            >
              {{ row.divide_member.name }}
            </div>
          </template>

          <template v-slot:level="{ row }">
            <div>{{ row.divide_member.level || '-' }}</div>
          </template>

          <!-- 商品状态 -->
          <template v-slot:card_no="{ row }">
            <div v-if="row.goods_status == 0" class="color-999">{{ row.goods_status_desc || '-' }}</div>
            <div v-if="row.goods_status == 1">
              <div>{{ row.goods_status_desc || '-' }}</div>
              <a v-if="row.divide_member.card_no" @click="onLink(row.divide_member, '/service/writeDetail/list')">
                {{ row.divide_member.card_no }}
              </a>
            </div>
          </template>

          <!-- 结算状态 -->
          <template v-slot:status_desc="{ row }">
            <div :class="{ 'color-999': row.status != 2 }">{{ row.status_desc || '-' }}</div>
          </template>

          <!-- 结算时间 -->
          <template v-slot:settle_time="{ row }">
            {{ formatSettleTime(row) || '-' }}
          </template>

          <!-- 预估 -->
          <template v-slot:esti_divide_money="{ row }">
            <span v-text-format.number="row.divide_member.esti_divide_money"></span>
          </template>

          <!-- 实际 -->
          <template v-slot:divide_money="{ row }">
            <span v-if="row.divide_member?.is_gen_fixed_factor == 0" class="color-999">次月后生成 </span>
            <template v-else>
              <div v-text-format.number="row.divide_member.divide_money"></div>
              <Tooltip v-if="row.divide_member?.calc_rules_text" placement="bottom">
                <a>查看计算公式</a>
                <div slot="content">
                  <div class="tip-inner">{{ row.divide_member?.calc_rules_text }}</div>
                </div>
              </Tooltip>
            </template>
          </template>
          <!-- 结算规则 -->
          <template v-slot:settle_rules="{ row }">
            <span class="color-999">{{ row.settle_rules || '-' }}</span>
          </template>

          <!-- 分佣类型 -->
          <template v-slot:divide_type="{ row }">
            {{ row.divide_member.divide_type_desc || '-' }}
          </template>
        </Table>
      </div>
    </div>

    <div v-if="serivceList.length == 0 && his_list.length == 0" class="empty" style="height: 50vh">暂无数据</div>

    <div class="fixed-bottom-wrapper">
      <Button @click="back">返回</Button>
    </div>

    <!-- 理疗师 -->
    <selectStaffModal v-model="staffVisible" :divide_type="divideType" @success="changeStaff" />
    <!--
      width="25rem" -->

    <!-- 主-辅-销售选择器 -->
    <Modal
      v-model="showSelectStaff"
      :header-hide="false"
      :footer-hide="true"
      :closable="false"
      :mask-closable="false"
      :lock-scroll="true"
    >
      <!-- v-if="showSelectStaff" -->
      <div class="select-staff-modal">
        <div class="modal-content">
          <!-- <input v-model="staffSearch" placeholder="搜索销售人员" class="staff-search" @input="onStaffSearch" /> -->
          <div class="staff-select-panel">
            <div>
              <div class="panel-title">请选择主要销售人员</div>
              <div class="staff-list-scroll">
                <div
                  :class="['staff-item', !sales_master ? 'active' : '']"
                  @click="onChangeSales({ id: '' }, 'master')"
                >
                  不选择销售
                </div>
                <div
                  v-for="s in salesList"
                  :key="s.id"
                  :class="['staff-item', sales_master === s.id ? 'active' : '']"
                  @click="onChangeSales(s, 'master')"
                >
                  <!--  sales_master = s.id -->
                  {{ s.name }}
                </div>
              </div>
            </div>

            <div>
              <div class="panel-title">请选择辅助销售人员</div>
              <div class="staff-list-scroll">
                <div :class="['staff-item', !sales_slave ? 'active' : '']" @click="onChangeSales({ id: '' }, 'slave')">
                  不选择辅助销售
                </div>
                <div
                  v-for="s in salesList"
                  :key="s.id"
                  :class="['staff-item', sales_slave === s.id ? 'active' : '', sales_master === s.id ? 'disabled' : '']"
                  @click="onChangeSales(s, 'slave')"
                >
                  <!-- sales_master !== s.id && (sales_slave = s.id) -->
                  {{ s.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="modal-actions">
            <Button type="primary" :loading="salesLoading" @click="confirmStaff">确定</Button>
            <Button :disabled="salesLoading" @click="cancelStaff">取消</Button>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
// status 1=已结算
// status_desc 结算状态
// goods_status 商品状态 0 = 未核销,1=已核销
// goods_status_desc 商品状态文案
// divide_members.*.calc_rules_text 分账金额计算规则
import S from 'libs/util'; // Some commonly used tools
import renderHeader from '@/mixins/renderHeader';
import selectStaffModal from './components/account/selectStaff.vue';
import cloneDeep from 'lodash.clonedeep';

export default {
  name: 'RstAccount',
  mixins: [renderHeader],
  components: { selectStaffModal },
  data() {
    return {
      tip: {
        actule: `指在次月统计处人员的评价系数和职级系数的情况下的计算出记账业绩
服务消耗业绩：实收金额  × 产品比例 × 【（用户评价系数+职级系数）÷2】
服务销售业绩：实收金额  × 产品比例 × 销售比例 × 【（用户评价系数+职级系数）÷2】
实物销售业绩：实收金额  × 产品比例 × 销售比例 × 【（用户评价系数+职级系数）÷2】
药品销售业绩：实收金额  × 产品比例 × 【（用户评价系数+职级系数）÷2】
问诊费和挂号费业绩：实收金额  × 50% × 【（用户评价系数+职级系数）÷2】`,
        estimate: `指在未统计人员的评价系数和职级系数的情况下的预估记账业绩
服务消耗业绩：实收金额  × 产品比例
服务销售业绩：实收金额  × 产品比例 × 销售比例
实物销售业绩：实收金额  × 产品比例 × 销售比例
药品销售业绩：实收金额  × 产品比例
问诊费和挂号费业绩：实收金额  × 50%`,
      },

      pLoading: false,

      salesLoading: false,

      showSelectStaff: false,
      staffVisible: false,
      divideType: '1', //员工类型

      is_confirm: '0', //1时不可选择销售
      is_disable_sales: '0', //1不可选择销售

      sales_master: '', //主
      sales_slave: '', //辅
      salesList: [],

      sales: [], //分帐人
      tempSales: [], //用于回显示的分账人

      // 医生/理疗师/药剂师
      physio_id: '',

      //销售人员单选时-区分销售人类型
      sales_level: '',

      // 服务
      serivceList: [],
      dis_serivceList: [],

      // his
      his_list: [],

      currenRow: {},

      cur_master_sale_id: '',

      colums: [
        { title: '项目', slot: 'prod_name', minWidth: 100, align: 'center', fixed: 'left' },
        { title: '所属商品', slot: 'belongs_name', minWidth: 100, align: 'center' },
        { title: '商品金额', slot: 'money', minWidth: 100, align: 'center' },
        { title: '人员', slot: 'divide_members', minWidth: 120, align: 'center' },
        // { title: '等级', slot: 'level', align: 'center', minWidth: 80 },
        {
          title: '预估记账金额',
          slot: 'esti_divide_money',
          minWidth: 100,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, this.tip.estimate, 300, 'custom_white_space'),
        },
        {
          title: '实际记账金额',
          slot: 'divide_money',
          minWidth: 100,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, this.tip.actule, 300, 'custom_white_space'),
        },
        { title: '商品状态', slot: 'card_no', minWidth: 120, align: 'center' },
        { title: '结算状态', slot: 'status_desc', minWidth: 120, align: 'center' },
        { title: '结算时间', slot: 'settle_time', minWidth: 130, align: 'center' },
        {
          title: '结算规则',
          slot: 'settle_rules',
          minWidth: 100,
          align: 'center',
        },
        { title: '记账类型', slot: 'divide_type', minWidth: 100, align: 'center' },
      ],
      orders: [],
      selectOrderIdx: null,
      mainStaff: '',
      assistStaff: '',

      staffSearch: '',
    };
  },

  computed: {
    /**是否可修改销售人员 */
    showSalaes() {
      let f = true;
      if (this.is_disable_sales == 1) {
        f = false;
      }

      if (this.is_confirm == 1) {
        f = false;
      }

      return f;
    },

    filteredSalesList() {
      if (!this.staffSearch) return this.salesList;
      return this.salesList.filter(s => s.name.includes(this.staffSearch));
    },
    serivceMap() {
      // 用 dis_serivceList 进行分组，并保持顺序
      const map = {};
      const order = [];
      const list = this.getDisSerivceList();
      list.forEach(item => {
        if (!map[item.divide_id]) {
          map[item.divide_id] = {
            status: item.status,
            divide_scope: item.divide_scope,
            list: [],
          };
          order.push(item.divide_id); // 记录顺序
        }
        map[item.divide_id].list.push(item);
      });
      // 返回二维数组，顺序与 dis_serivceList 一致
      return order.map(id => map[id]);
    },

    // ...其他
    hisTableList() {
      console.log('getHisList', this.getHisList());

      return this.getHisList();
    },
  },

  mounted() {
    this.getOrderRevenueDetail();

    this.$api
      .getOrderRstRevenueMembers({ divide_type: this.divideType })
      .then(res => {
        this.salesList = res.list || [];
      })
      .finally(() => {
        // this.searchLoading = false;
      });
  },
  methods: {
    format_money(money) {
      if (money) {
        return `¥ ${money}`;
      } else {
        return '-';
      }
    },
    /**
     * 合并单元格
     */
    tableSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 要合并的列索引
      const mergeColumns = ['prod_name', 'belongs_name', 'money', 'settle_rules'];
      // 找到当前列的 slot/key
      const colKey = this.colums[columnIndex]?.slot || this.colums[columnIndex]?.key;
      if (mergeColumns.includes(colKey)) {
        if (row._isFirst) {
          return {
            rowspan: row._rowspan,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },

    /**
     * 处理非his订单数据
     */
    getDisSerivceList() {
      const list = [];
      this.serivceList.forEach(item => {
        let members = item.divide_members && item.divide_members.length > 0 ? [...item.divide_members] : null;

        // 规则：divide_members为空时，生成默认对象
        if (!members) {
          if (item.divide_scope == 0) {
            // 服务/销售
            members = [
              {
                id: '',
                name: '服务待核销',
                type: 1, //服务
                level: '',
                divide_money: '',
                esti_divide_money: '',
                divide_type_desc: '',
                card_no: '',
                calc_rules_text: '',
                sales_level: '',
                settle_fail_reason: '',
                s_gen_fixed_factor: '',
              },
              {
                id: '',
                name: this.showSalaes ? '请选择销售' : '未选择销售',
                type: 2, //销售
                level: '',
                divide_money: '',
                esti_divide_money: '',
                divide_type_desc: '',
                card_no: '',
                calc_rules_text: '',
                sales_level: '',
                settle_fail_reason: '',
                s_gen_fixed_factor: '',
              },
            ];
          } else if (item.divide_scope == 1) {
            // 仅销售
            members = [
              {
                id: '',
                name: this.showSalaes ? '请选择销售' : '未选择销售',
                type: 2,
                level: '',
                divide_money: '',
                divide_type_desc: '',
                card_no: '',
                calc_rules_text: '',
                sales_level: '',
                settle_fail_reason: '',
                s_gen_fixed_factor: '',
                esti_divide_money: '',
              },
            ];
          } else {
            members = [
              {
                id: '',
                name: '',
                type: '',
                level: '',
                divide_money: '',
                divide_type_desc: '',
                card_no: '',
                calc_rules_text: '',
                sales_level: '',
                settle_fail_reason: '',
                s_gen_fixed_factor: '',
                esti_divide_money: '',
              },
            ];
          }
        } else if (item.divide_scope == 0) {
          // divide_members存在，且是服务类型，判断是否有服务人员
          const hasService = members.some(m => m.type != 2);
          if (!hasService) {
            // 没有服务人员，补充一条“服务待核销”
            members.unshift({
              id: '',
              name: '服务待核销',
              type: 1,
              level: '',
              divide_money: '',
              divide_type_desc: '',
              card_no: '',
              calc_rules_text: '',
              sales_level: '',
              settle_fail_reason: '',
              s_gen_fixed_factor: '',
              esti_divide_money: '',
            });
          }
        }

        members.forEach((member, idx) => {
          list.push({
            ...item,
            divide_member: member,
            _rowspan: members.length,
            _isFirst: idx === 0,
          });
        });
      });
      console.log('🚀 ~ accountRst.vue:559 ~ getDisSerivceList ~ this.serivceList---->', this.serivceList);
      console.log('🚀 ~ accountRst.vue:559 ~ getDisSerivceList ~ list---->', list);
      return list;
    },

    /**HIS订单数据处理 */
    getHisList() {
      const list = [];
      this.his_list.forEach(item => {
        console.log('🚀 ~ accountRst.vue:571 ~ getHisList ~ item---->', item);
        let members = item.divide_members && item.divide_members.length > 0 ? [...item.divide_members] : null;

        // divide_members为空时，补一条“请选择人员”或“未选择人员”
        if (!members) {
          members = [
            {
              id: '',
              name: this.showSalaes ? '请选择人员' : '未选择人员',
              type: '', // 医生/药剂师类型可根据 divide_scope 设定
              level: '',
              divide_money: '',
              divide_type_desc: '',
              card_no: '',
              calc_rules_text: '',
              sales_level: '',
              settle_fail_reason: '',
            },
          ];
        }

        members.forEach((member, idx) => {
          list.push({
            ...item,
            divide_member: member,
            _rowspan: members.length,
            _isFirst: idx === 0,
          });
        });
      });

      console.log(this.his_list);

      console.log('🚀 ~ accountRst.vue:602 ~ getHisList ~ list---->', list);
      return list;
    },
    back() {
      this.$router.back();
    },

    onLink(row, path) {
      // /service/writeDetail/list?page=1&pageSize=20&card_no=**********
      this.$router.push({
        path,
        query: {
          card_no: row.card_no,
        },
      });
    },

    formatSettleTime(row) {
      let txt = '-';
      const item = row.divide_member;
      if (item?.settle_fail_reason) {
        txt = item.settle_fail_reason;
      } else {
        const date = row.settle_time;
        if (date && date != 0) {
          txt = S.moment(date * 1000).format('YYYY-MM-DD HH:mm');
        } else {
          txt = '-';
        }
      }

      return txt;
    },

    levelFormatter(item) {
      if (item.type == '2') {
        return item.sales_level == 'master' ? '主销' : '副销';
      } else {
        return '';
      }
    },

    /**
     * 是否可点击更改
     */
    isCheck(row, item) {
      // 销售不可单个修改
      if (item.type == 2) return false;

      // 服务人员可以单个修改
      if (item.type == 1) {
        // 分账状态2 确认不可修改
        if (row.status == 2) {
          return false;
        }
        // 分账状态1 可修改
        if (row.status == 1) {
          return true;
        }
      }

      return false;
    },

    // 是否存在-服务核销人员
    isSerivce(arr) {
      return !!arr.find(item => item.type != 2);
    },

    getOrderRevenueDetail() {
      let params = {
        order_id: this.$route.query.order_id,
        source: this.$route.query.source,
      };

      this.pLoading = true;

      this.$api
        .getOrderRevenueDetail(params)
        .then(res => {
          console.log('🚀 ~ this.$api.getOrderRevenueDetail ~ res=>', res);
          this.serivceList = res.list || [];
          this.dis_serivceList = this.getDisSerivceList();
          this.his_list = res.his_list || [];
          this.sales = res.sales || [];

          this.tempSales = cloneDeep(res.sales);

          this.is_confirm = res.is_confirm || '';
          this.is_disable_sales = res.is_disable_sales || '';
        })
        .finally(() => {
          this.pLoading = false;
        });
    },

    async updateOrderRevenue(divide_ids) {
      try {
        let params = {
          order_id: this.$route.query.order_id,
          divide_ids: divide_ids,
          physio_id: this.physio_id,
          sales: this.sales,
          source: this.$route.query.source,
        };
        const res = await this.$api.updateOrderRevenue(params);

        return Promise.resolve(res);
      } catch (error) {
        return Promise.reject(error);
      } finally {
        this.sales_level = '';
        this.cur_sale_id = '';
        this.currenRow = {};
      }
    },
    /**
     *
     * @param sales_level master主销售  slave副销售
     */
    onChangeSales(val, sales_level) {
      const _this = this;

      if (sales_level === 'master') {
        if (_this.sales_slave == val.id) {
          _this.sales_slave = '';
          _this.sales = [];
        }

        _this.sales_master = val.id;
        _this.sales[0] = {
          sales_id: _this.sales_master,
          sales_level: 'master',
        };
      }

      if (sales_level === 'slave') {
        _this.sales_slave = _this.sales_master != val.id ? val.id : '';
        _this.sales[1] = {
          sales_id: _this.sales_slave,
          sales_level: 'slave',
        };
      }

      console.log(_this.sales);
    },

    onSelectSsles() {
      this.divideType = '2';
      this.showSelectStaff = true;

      if (this.tempSales.length > 0) {
        this.sales = cloneDeep(this.tempSales);
        this.sales_master = this.tempSales?.find(item => item.sales_level == 'master')?.sales_id;
        this.sales_slave = this.tempSales?.find(item => item.sales_level == 'slave')?.sales_id;
      }
    },

    /**
     *
     * @param row
     * @param item
     */
    onMultipleSales(row, item) {
      console.log('🚀 ~ onMultipleSales ~ row=>', row);
      console.log('🚀 ~ onMultipleSales ~ item=>', item);
      // 分账状态2 确认不可修改
      if (row.status == 2) return;

      // 分账状态1 销售不可修改
      if (row.status == 1 && item.type != 1) return;

      this.staffVisible = true;

      // 医生
      if (row.divide_scope == '2') {
        this.divideType = '3';
      }

      // 药剂师
      if (row.divide_scope == '3') {
        // 可以是所有人-废弃
        // 药剂师
        this.divideType = '4';
      }

      this.currenRow = row;
    },

    changeStaff(e) {
      if (!e) return;

      this.physio_id = e.id;

      let divide_ids = [this.currenRow.divide_id];

      this.sales = [];

      this.updateOrderRevenue(divide_ids).then(() => {
        this.getOrderRevenueDetail();
      });
    },

    /**
     * 批量添加销售人员
     */
    confirmStaff() {
      const _this = this;
      _this.salesLoading = true;
      _this.physio_id = '';

      // status 0未分账、divide_scope 0服务/销售 1销售 - 可添加
      let divide_ids = this.serivceList
        .filter(item => {
          return [1, 0].includes(+item.status) && [1, 0].includes(+item.divide_scope);
        })
        ?.map(item => item.divide_id);

      if (divide_ids.length === 0) return;
      this.updateOrderRevenue(divide_ids)
        .then(res => {
          this.$Message.success('销售人员设置成功');
          console.log('🚀 ~ this.updateOrderRevenue ~ res=>', res);
          this.getOrderRevenueDetail();
        })
        .finally(() => {
          this.showSelectStaff = false;
          this.salesLoading = false;
        });
    },

    cancelStaff() {
      this.showSelectStaff = false;
      this.sales_master = '';
      this.sales_slave = '';
    },
  },
};
</script>

<style scoped lang="less">
.cell {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
}

.level-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  // white-space: pre-wrap;
}

.rst-account-page {
  padding: 0.25rem 0;
  background: #fff;
  height: 82vh;
  overflow-y: auto;
}

.filter-bar {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;

  label {
    font-size: 1rem;
    // margin-right: .75rem;
  }

  select {
    min-width: 13.75rem;
    height: 2rem;
    border: 0.0625rem solid #ccc;
    border-radius: 0.25rem;
    padding: 0 0.5rem;
    font-size: 0.9375rem;
  }
}

.order-table {
  margin-bottom: 1.5rem;
}

.select-staff-modal {
  .modal-content {
    background: #fff;
    border-radius: 0.5rem;

    .staff-select-panel {
      display: flex;
      gap: 2rem;
      height: 22.5rem;
      overflow-y: auto;
    }

    .staff-select-panel {
      display: flex;
      gap: 2rem;

      > div {
        width: 13.75rem;
      }

      .panel-title {
        font-size: 0.9375rem;
        margin-bottom: 0.75rem;
        color: #757575;
        text-align: center;
      }

      .staff-list-scroll {
        max-height: 20rem;
        overflow-y: auto;
        padding-right: 0.25rem;
        color: #333;
      }

      .staff-item {
        background: #eff9ff;
        margin-bottom: 0.5rem;
        padding: 0.5rem 0;
        text-align: center;
        border-radius: 0.25rem;
        cursor: pointer;
        font-size: 0.875rem;

        &.active {
          background: #1157e5;
          color: #fff;
        }

        &.disabled {
          background: #e5e6eb;
          color: #bbb;
          cursor: not-allowed;
        }
      }
    }

    .modal-actions {
      margin-top: 1.5rem;
      display: flex;
      justify-content: center;
      gap: 16px;
      // button {
      //   min-width: 5rem;
      //   height: 2rem;
      //   border-radius: .25rem;
      //   border: none;
      //   margin-left: 1rem;
      //   font-size: .9375rem;
      //   cursor: pointer;
      //   &:first-child {
      //     background: #1157e5;
      //     color: #fff;
      //   }
      //   &:last-child {
      //     background: #f5f5f5;
      //     color: #222;
      //   }
      // }
    }
  }
}

.check {
  color: #1157e5;
  cursor: pointer;
}

// :deep(.cusot-cell .ivu-table-cell) {
//   padding: 0;
// }

.tip-inner {
  white-space: pre-wrap;
}
</style>
