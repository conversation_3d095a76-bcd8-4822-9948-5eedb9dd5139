<template>
  <div>
    <div class="type-box" v-for="(box_item, box_index) in list" :key="'serivce' + box_index">
      <div :ref="box_item.serv_type">
        <div class="type-name">{{ box_item.serv_type_text }}</div>
        <div class="card-box">
          <div
            class="card-item"
            :class="{ 'has-add-item': isHadAdd(item) }"
            v-for="(item, index) in box_item.data"
            :key="'item' + index"
            @click="addService(item)"
          >
            <div class="name-box">
              <div class="name">
                <div class="text ecs ecs-2">{{ item.name }}</div>
                <Tooltip
                  v-for="channel in getOutChannelList(item)"
                  :key="channel.channel"
                  placement="top"
                  :content="channel.tooltip"
                >
                  <img class="douyin-icon" :src="channel.icon" :alt="channel.name" />
                </Tooltip>
              </div>
              <div class="price-box">
                <div class="price">¥{{ Number(item.price || 0).toFixed(2) }}</div>
              </div>
            </div>
            <div class="tag-box">
              <div class="card-tag" v-if="item.serv_type_text">{{ item.serv_type_text }}</div>
              <div class="card-tag" v-if="item.source_platform_text">{{ item.source_platform_text }}</div>
              <div style="flex: 1; text-align: right">
                <Tooltip theme="light" placement="top" v-if="!is_rst && item.sale_promotion_price > 0">
                  <div slot="content">
                    980专享 <span style="color: red">￥{{ item.sale_promotion_price }}</span>
                  </div>
                  <div class="vip-tag-box">
                    <svg-icon name="980_service_tag" class="vip-tag"></svg-icon>
                    <span class="vip-price">￥{{ item.sale_promotion_price }}</span>
                  </div>
                </Tooltip>
                <Tooltip theme="light" placement="top" v-if="is_rst && is_vip">
                  <div slot="content">
                    会员专享 <span style="color: red">￥{{ item.vip_price || 0 }}</span>
                  </div>
                  <div class="vip-tag-box">
                    <img src="https://img-sn01.rsjxx.com/image/2025/0616/175205_87738.png" alt="" class="vip-tag" />
                    <span class="rst-vip-price">￥{{ item.vip_price || 0 }}</span>
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce  } from 'lodash-es';

export default {
  name: 'serviceContent',
  components: {},
  mixins: [],
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    add_goods: {
      type: Array,
      default: () => [],
    },
    scrollTop: {
      type: [String, Number],
      default: 0,
    },
    is_980_vip: {
      type: Boolean,
      default: false,
    },
    is_rst: {
      type: Boolean,
      default: false,
    },
    is_vip: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 外部渠道配置
      outChannelConfig: {
        '1': {
          name: '美团',
          icon: 'https://static.rsjxx.com/image/2025/0619/144358_21725.png'
        },
        '2': {
          name: '抖音',
          icon: 'https://static.rsjxx.com/image/2025/0619/144358_49841.png'
        }
      },
      // 保持向后兼容
      meituan: 'https://static.rsjxx.com/image/2025/0619/144358_21725.png',
      douyin: 'https://static.rsjxx.com/image/2025/0619/144358_49841.png',
    };
  },
  computed: {
    // 获取外部渠道信息列表（用于循环渲染）
    getOutChannelList() {
      return (item) => {
        if (!item?.out_sales_goods_names) return [];

        return item.out_sales_goods_names
          .filter(goods => this.outChannelConfig[goods.out_sales_channel])
          .map(goods => ({
            channel: goods.out_sales_channel,
            name: this.outChannelConfig[goods.out_sales_channel].name,
            icon: this.outChannelConfig[goods.out_sales_channel].icon,
            tooltip: goods.out_goods_name
          }));
      };
    },

    // 向后兼容：获取外部渠道信息的辅助函数
    getOutChannelInfo() {
      return (item) => {
        if (!item?.out_sales_goods_names) return { meituan: null, douyin: null };

        const meituan = item.out_sales_goods_names.find(goods => goods.out_sales_channel === '1');
        const douyin = item.out_sales_goods_names.find(goods => goods.out_sales_channel === '2');

        return {
          meituan: meituan ? {
            show: true,
            tooltip: meituan.out_goods_name,
            icon: this.meituan
          } : null,
          douyin: douyin ? {
            show: true,
            tooltip: douyin.out_goods_name,
            icon: this.douyin
          } : null
        };
      };
    },

    // 向后兼容：检查是否有美团渠道
    hasMeituanChannel() {
      return (item) => {
        return item?.out_sales_goods_names?.some(goods => goods.out_sales_channel === '1');
      };
    },

    // 向后兼容：检查是否有抖音渠道
    hasDouyinChannel() {
      return (item) => {
        return item?.out_sales_goods_names?.some(goods => goods.out_sales_channel === '2');
      };
    },

    // 向后兼容：获取美团商品名称
    getMeituanGoodsName() {
      return (item) => {
        return item?.out_sales_goods_names?.find(goods => goods.out_sales_channel === '1')?.out_goods_name;
      };
    },

    // 向后兼容：获取抖音商品名称
    getDouyinGoodsName() {
      return (item) => {
        return item?.out_sales_goods_names?.find(goods => goods.out_sales_channel === '2')?.out_goods_name;
      };
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 检测滚动
    handleNodeScroll: debounce(function () {
      this.$nextTick(() => {
        this.list.some((title_item, title_index) => {
          const ref = this.$refs[title_item.serv_type][0];
          if (ref) {
            let top = this.$refs[title_item.serv_type][0].getBoundingClientRect().top;
            if (top <= 270) {
              this.$emit('getIndex', title_index);
            }
          }
        });
      });
    }, 100),
    // 添加服务
    addService(item) {
      this.$emit('addService', { ...item, is_service: true });
    },
    // 滚动到指定服务区域
    scrollView(ref) {
      if (this.$refs[ref][0]) {
        this.$refs[ref][0].scrollIntoView({ behavior: 'smooth' });
      }
    },
    // 已经添加
    isHadAdd(item) {
      let isAdd = false;
      const serviceList = this.add_goods.filter(a_item => a_item.is_service);
      serviceList.some(service_item => {
        if (item.id == service_item.id) {
          isAdd = true;
          return true;
        }
      });
      return isAdd;
    },
  },
};
</script>

<style lang="less" scoped>
.type-box {
  .type-name {
    margin-bottom: 16px;
  }

  .card-box {
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    margin-right: 10px;

    .card-item {
      width: 49%;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #ebedf0;
      padding: 10px 12px;
      margin-bottom: 10px;
      cursor: pointer;

      &:hover {
        background: rgba(48, 136, 255, 0.04);
      }

      &:nth-child(2n) {
        margin-left: 2%;
      }

      .name-box {
        display: flex;
        justify-content: space-between;

        .name {
          display: flex;
          font-weight: 400;
          font-size: 13px;
          color: #303133;
          line-height: 20px;
          .text {
            width: fit-content;
          }

          .douyin-icon {
            width: 16px;
            height: 16px;
            object-fit: cover;
            margin-left: 4px;
            margin-top: -2px;
          }
        }

        .price-box {
          margin-left: 10px;

          .price {
            font-size: 14px;
            color: #303133;
            line-height: 20px;
          }
        }
      }

      .tag-box {
        margin-top: 12px;
        display: flex;
        align-items: center;

        .card-tag {
          background: #f5f6f8;
          border-radius: 2px;
          padding: 0px 4px;
          font-weight: 300;
          font-size: 12px;
          color: #909399;
          margin-right: 4px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:last-child {
            margin-right: 0px;
          }
        }
        .vip-tag-box {
          position: relative;
          .vip-price {
            position: absolute;
            right: 6px;
            top: 0;
            color: #fff;
          }
          .rst-vip-price {
            position: absolute;
            right: 4px;
            top: 1px;
            color: #fff;
          }
        }
      }
    }

    .has-add-item {
      border: 1px solid #3088ff !important;
    }
  }
}

.vip-tag {
  width: 90px;
  height: 18px;
}
</style>
