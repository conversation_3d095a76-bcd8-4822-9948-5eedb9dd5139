<template>
  <Modal
    ref="editActivityModal"
    :value="visible"
    title="商品信息"
    :mask-closable="false"
    width="800px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
  >
    <div v-if="!detailLoading">
      <Row>
        <Col span="24">
          <Form ref="formData" :model="formData" :label-width="130" :label-colon="true">
            <div class="block-header"><span>基本信息</span></div>
            <FormItem label="商品名">
              <p>{{ formData?.name }}</p>
            </FormItem>

            <FormItem label="商品类型">
              <p>{{ typeDesc[formData.goods_type]?.desc }}</p>
            </FormItem>

            <FormItem label="商品图">
              <MaterialPicture v-model="formData.slide_imgs" :limit="0" :disabled="true" style="height: 65px" />
            </FormItem>

            <FormItem label="商品详情" prop="leading_person">
              <MaterialPicture v-model="formData.detail_imgs" :limit="0" :disabled="true" />
            </FormItem>

            <template>
              <!-- 实体商品详情 -->
              <physical-goods-detail
                v-if="formData.goods_type == 10 || formData.goods_type == 40"
                :echo-data="physicalEchoData"
                :is_rst="formData.goods_type == 40"
              ></physical-goods-detail>

              <!-- 虚拟商品详情 -->
              <virtual-goods-detail
                v-if="formData.goods_type == 15 || formData.goods_type == 30"
                :echo-data="virtualEchoData"
                :is_rst="formData.goods_type == 30"
              ></virtual-goods-detail>

              <!-- 通兑券详情 -->
              <exchange-coupon-detail
                v-if="formData.goods_type == 25 || formData.goods_type == 45"
                :echo-data="exchangeCouponEchoData"
                :is_rst="formData.goods_type == 45"
              ></exchange-coupon-detail>

              <!-- 套餐详情 -->
              <tao-can-goods-detail
                v-if="formData.goods_type == 20 || formData.goods_type == 35"
                :echo-data="taoCanEchoData"
                :is_rst="formData.goods_type == 35"
              ></tao-can-goods-detail>
            </template>

            <!--          <FormItem label="售卖主体">-->
            <!--            <p>{{ formData.sell_ent_type }}</p>-->
            <!--          </FormItem>-->
            <FormItem label="上架范围" v-if="formData.goods_type === '10'">
              <p v-if="formData.shelf_scope !== '9'">
                {{ formData.shelf_scope == '1' ? '零售服务' : formData.shelf_scope == '2' ? '问诊治疗' : '-' }}
              </p>
              <p v-else>零售服务 问诊治疗</p>
            </FormItem>

            <FormItem label="上架范围" v-else>
              <p v-if="formData.xn_scope !== '9'">
                {{ formData.xn_scope == '1' ? '零售服务' : formData.xn_scope == '2' ? '问诊治疗' : '-' }}
              </p>
              <p v-else>零售服务 问诊治疗</p>
            </FormItem>

            <FormItem label="储值购买" v-if="![30, 35, 40, 45].includes(+formData.goods_type)">
              <p>{{ isRechargeBuyDesc[formData.is_recharge_buy]?.desc }}</p>
            </FormItem>

            <FormItem label="销售方式" v-if="formData.goods_type === '10'">
              <p>{{ formData.sale_type_text }}</p>
            </FormItem>
          </Form>
        </Col>
      </Row>
    </div>
    <div v-else style="height: 480px; position: relative">
      <Spin fix style="height: 480px"></Spin>
    </div>

    <div slot="footer" style="text-align: center">
      <Button @click="cancel" class="mr10">关闭</Button>
      <KLink
        v-if="formData.is_del === '0'"
        :to="{ path: '/goods/item/detail', query: { id: formData.id } }"
        target="_blank"
      >
        <Button type="primary">查看商品详情</Button>
      </KLink>
    </div>
  </Modal>
</template>

<script>
import S from 'libs/util';
import moment from 'moment';
import { cloneDeep } from 'lodash-es';
import TaoCanGoodsDetail from '@/view/goods/warehouse/components/taocanGoods/TaoCanGoodsDetail.vue';
import PhysicalGoodsDetail from '@/view/goods/warehouse/components/physicalGoods/PhysicalGoodsDetail.vue';
import ExchangeCouponDetail from '@/view/goods/warehouse/components/exchangeCoupon/ExchangeCouponDetail.vue';
import VirtualGoodsDetail from '@/view/goods/warehouse/components/virtralGoods/VirtualGoodsDetail.vue';
const initFormData = {
  is_del: '0',
  service_info: {
    expiration: {
      type: '1', // 1 购买后${days}天内有效；2 ${date}日期前有效；
      date: '', //
      days: 365, //
    },
    is_appointment: '1', // 1免预约 2需要预约
    is_can_refund: '1', // 1不可退 2可退
    working_time: '', // 工作时间
    not_working_date: '', // 不可使用日期：
    note_rule: '', // 使用规则
  },
  shelf_scope: '1', // 上架范围
};

export default {
  name: 'editActivityModal',
  mixins: [],
  components: { VirtualGoodsDetail, ExchangeCouponDetail, PhysicalGoodsDetail, TaoCanGoodsDetail },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    attr_id: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      formData: cloneDeep(initFormData),
      submitLoading: false,
      detailLoading: false,

      storedList: [], // 储值购买枚举
      isRechargeBuyDesc: {},
      xnScopeDesc: [],
      // 详情数据
      physicalEchoData: {}, // 实体商品详情回显数据
      virtualEchoData: {}, // 虚拟商品回显数据
      exchangeCouponEchoData: {}, // 通兑券详情
      taoCanEchoData: {}, // 套餐详情
      typeDesc: {}, // 商品类型
      sellEntType: {}, // 售卖主体
      beforeLogInfo: {},
      afterLogInfo: {},
      diffColumns: [],

      source_platform: '',
      isDropShoppingDesc: [], // 平台代发枚举
    };
  },

  computed: {},

  watch: {},

  created() {
    this.getIndexOptions();
    this.getGoodsLibOptions();
  },

  mounted() {},

  methods: {
    changeVisible(val) {
      if (val) {
        this.getOrderGoodsSnapShot();
      } else {
        this.formData = cloneDeep(initFormData); // 重置数据
        // this.$refs['modal'].$el.querySelector('.ivu-modal-body').scrollTop = 0; // 重置滚动条高度
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {},
    getOrderGoodsSnapShot() {
      this.detailLoading = true;
      let params = {
        attr_id: this.attr_id,
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOrderGoodsSnapShot(params)
        .then(res => {
          console.log('=>(detail.vue:242) 1111res', res);
          let goods = res.goods;
          // 公共数据
          this.formData.is_del = goods.is_del;
          this.formData.source_platform = goods.source_platform; // 商品来源
          this.formData.is_audit = goods.is_audit; // 是否审核中
          this.formData.status = goods.status;
          this.formData.id = goods.id;
          this.formData.name = goods.name;
          console.log('=>(index.vue:184) this.formData', this.formData);
          this.formData.goods_type = goods.goods_type;
          this.formData.sell_ent_type = goods.sell_ent_type_text;
          this.formData.slide_imgs = goods.slide_imgs;
          this.formData.detail_imgs = goods.detail_imgs;

          // 设置的公共数据
          this.formData.xn_scope = goods.xn_scope;
          this.formData.is_recharge_buy = goods.is_recharge_buy;
          this.formData.is_drop_shopping = goods.is_drop_shopping;
          this.formData.sw_scope_tmp = goods.shelf_scope == '9' ? ['1', '2'] : [goods.shelf_scope];
          this.formData.shelf_scope = goods.shelf_scope;

          this.formData.sale_type_text = goods.sale_type && this.handleSaleType(goods.sale_type);

          this.formData.service_info = !S.isEmptyObject(goods.service_info)
            ? goods.service_info
            : this.formData.service_info;

          this.formData.service_info.expiration.days = Number(this.formData.service_info.expiration.days);
          this.formData.service_info.expiration.date =
            this.formData.service_info.expiration.date === ''
              ? '-'
              : moment(this.formData.service_info.expiration.date).format('YYYY-MM-DD');

          // 实体商品数据
          if (this.formData.goods_type === '10') {
            this.formData.xn_scope = goods.shelf_scope;
            this.physicalEchoData = {
              specs_data: goods.specs_data,
              tableData: goods.attrs,
              is_recharge_buy: goods.is_recharge_buy,
              is_drop_shopping: goods.is_drop_shopping,
              relation_card: goods.relation_card,
              grant_type: goods.grant_type,
              services: goods.services,
            };
          }
          // rst实体商品数据
          if (this.formData.goods_type === '40') {
            this.formData.xn_scope = goods.shelf_scope;
            this.physicalEchoData = {
              specs_data: goods.specs_data,
              tableData: goods.attrs,
              is_recharge_buy: goods.is_recharge_buy,
              is_drop_shopping: goods.is_drop_shopping,
              relation_card: goods.relation_card,
              grant_type: goods.grant_type,
              services: goods.services,
            };
          }

          // 虚拟商品数据
          if (this.formData.goods_type === '15') {
            for (let key in goods.services ? goods.services : []) {
              goods.services[key].times = Number(goods.services[key].times);
            }

            this.virtualEchoData = {
              services: goods.services,
              price: goods.price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              service_info: this.formData.service_info,
            };
          }

          // rst虚拟商品数据
          if (this.formData.goods_type === '30') {
            for (let key in goods.services ? goods.services : []) {
              goods.services[key].times = Number(goods.services[key].times);
            }

            this.virtualEchoData = {
              services: goods.services,
              price: goods.price,
              vip_price: goods.vip_price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              service_info: this.formData.service_info,
            };
          }

          // 通兑券数据
          if (this.formData.goods_type === '25') {
            this.exchangeCouponEchoData = {
              exchange_num: goods.exchange_num,
              services: goods.services,
              remark: goods.remark,
              price: goods.price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              service_info: this.formData.service_info,
            };
          }

          // rst通兑券数据
          if (this.formData.goods_type === '45') {
            this.exchangeCouponEchoData = {
              exchange_num: goods.exchange_num,
              services: goods.services,
              remark: goods.remark,
              price: goods.price,
              vip_price: goods.vip_price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              service_info: this.formData.service_info,
            };
          }

          // 套餐数据
          if (this.formData.goods_type === '20') {
            this.taoCanEchoData = {
              tc_infos: goods.tc_infos,
              price: goods.price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              relation_card: goods.relation_card,
              grant_type: goods.grant_type,
              services: goods.services,
            };
          }

          console.log('goods111111===>', goods);

          // rst套餐数据
          if (this.formData.goods_type === '35') {
            this.taoCanEchoData = {
              tc_infos: goods.tc_infos,
              price: goods.price,
              vip_price: goods.vip_price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              relation_card: goods.relation_card,
              grant_type: goods.grant_type,
              services: goods.services,
            };
          }

          if (goods.source_platform === 'PLAT') {
            this.source_platform = 'PLAT_GOODS';
          } else if (goods.source_platform === 'COM') {
            this.source_platform = 'COM_GOODS';
          }
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => {
          this.detailLoading = false;
        });
    },

    // 储值购买枚举值
    getGoodsLibOptions() {
      this.$api.getGoodsLibOptions().then(res => {
        this.storedList = S.descToArrHandle(res.isRechargeBuyDesc);
        this.isDropShoppingDesc = res.isDropShoppingDesc;
        this.isRechargeBuyDesc = res.isRechargeBuyDesc;
        this.sellEntType = res.sellEntType;
        this.typeDesc = res.typeDesc;
        this.xnScopeDesc = res.xnScopeDesc;
      });
    },
    // 储值购买枚举值
    getIndexOptions() {
      this.$api.getIndexOptions().then(res => {
        this.storedList = S.descToArrHandle(res.isRechargeBuyDesc);
        this.sellEntTypes = res.saleTypeDesc;
        // this.optionsList.servTypeDesc = S.descToArrHandle(res.servTypeDesc);
        // this.optionsList.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc);
        console.log('-> %c this.optionsList  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.optionsList);
      });
    },

    handleSaleType(sale_type) {
      return sale_type.map(key => this.sellEntTypes[key]?.desc).join('，'); // 用中文逗号连接成字符串
    },
  },

  destroyed() {},
};
</script>

<style lang="less" scoped>
::v-deep .block-header {
  background-color: #efefef;
  padding: 10px;
  //margin: 20px 0px;
  margin-bottom: 20px;
  position: relative;
}

::v-deep .ivu-modal-body {
  padding: 20px;
  max-height: 500px;
  min-height: 520px;
  overflow-y: auto;
}
.goods-item-wrapper {
  .ivu-input-wrapper,
  .ivu-select {
    width: 80%;
    max-width: none;
  }

  .ivu-input {
    max-width: none;
  }

  .ks-goods-type {
    position: relative;
    display: inline-block;
    cursor: pointer;
    margin: 0 0 0 10px;
    text-align: center;
    width: 115px;
    border-radius: 2px;
    border: 1px solid #cacaca;
    padding: 8px 0;
  }

  .ks-goods-type.active {
    border-color: #155bd4;
  }

  .ks-goods-type.active:after {
    content: '';
    display: inline-block;
    position: absolute;
    width: 24px;
    height: 24px;
    bottom: -1px;
    right: -1px;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/image/2021/0224/155801_7284881.png) no-repeat;
    background-size: 24px auto;
  }

  .ks-goods-type_name {
    display: block;
    font-weight: bold;
  }

  .ks-goods-type_desc {
    display: block;
    color: #999;
  }

  .ks-input-number {
    width: 100px;

    .ivu-input-number-input {
      text-align: center;
    }
  }

  .ivu-date-picker-editor {
    width: 100% !important;
  }
}

::v-deep .picture-display {
  display: inline-block;
}

::v-deep .ivu-form-item {
  margin-bottom: 10px;

  // .ivu-form-item-label {
  //   width: 170px !important;
  // }

  // .ivu-form-item-content {
  //   margin-left: 170px !important;
  // }
}

.ivu-table-first-col {
  border-left: 1px solid #e8eaec;
  border-right: 1px solid #e8eaec;
}
</style>
