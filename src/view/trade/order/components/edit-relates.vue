<template>
  <div class="edit-relates-wrapper">
    <Modal
      :value="relateVisible"
      :mask-closable="false"
      @on-visible-change="relateVisibleChange"
      title="编辑/添加  订单关联人"
      class-name="relate-modal"
      transfer
    >
      <div class="record-content">
        <Form ref="recordParams" :label-width="70">
          <Form-item label="关联人：">
            <Select v-model="selectedStaffList" multiple style="width: 80%">
              <!--              <Option v-for="(staff, index) in staffList" :key="staff.id" :value="staff.id"-->
              <!--                      :label="`${staff.name}·${staff.role}`">-->
              <!--                <div class="flex flex-item-between">-->
              <!--                  <span>{{ staff.name }}</span>-->
              <!--                  <span style="font-size: 12px;color:#999999;">{{ staff.role }}</span>-->
              <!--                </div>-->
              <!--              </Option>-->
              <Option
                v-for="(item, index) in staffList"
                class="options-item"
                :value="item.id"
                :key="item.id"
                :disabled="item.disabled === '1'"
                >{{ item.role ? item.name + '-' + item.role : item.name }}
              </Option>
            </Select>
          </Form-item>
        </Form>
      </div>
      <div slot="footer">
        <Button type="default" @click="relateVisibleChange(false)">取消</Button>
        <Button type="primary" :loading="relateConfirmLoading" @click="relateConfirm">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';

export default {
  name: 'edit-relates',
  mixins: [],

  components: {},

  props: {
    relateVisible: {
      type: Boolean,
      default: false,
    },
    staffList: {
      type: Array,
      default: () => [],
    },
    checkedStaffList: {
      type: Array,
      default: () => [],
    },
    order_id: {
      required: true,
      type: String,
      default: '',
    },
    classType: {
      required: true,
      type: String,
      default: '',
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      relateConfirmLoading: false,
      selectedStaffList: [],
    };
  },

  computed: {},

  watch: {
    checkedStaffList: {
      handler(val) {
        console.log('-> %c val  === %o ', 'font-size: 15px', val);
        // if (val.length > 0) {
        //   let staffList = cloneDeep(val);
        //   console.log('-> %c staffList  === %o ', 'font-size: 15px', staffList);
        //   this.selectedStaffList = staffList.map(item => item.id);
        //   this.staffList.map(item => {
        //     console.log('-> %c item  === %o ', 'font-size: 15px', item);
        //     staffList.map(staff => {
        //       console.log('-> %c staff  === %o ', 'font-size: 15px', staff);
        //       if (item.id === staff.id && staff.disabled === '1') {
        //         item.disabled = '1';
        //       }
        //     });
        //   });
        //   console.log('-> %c this.staffList  === %o ', 'font-size: 15px', this.staffList);
        // } else {
        //   this.selectedStaffList = [];
        // }
      },
      deep: true,
    },
  },

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    relateConfirm() {
      const params = {
        staff_ids: this.selectedStaffList,
        class: this.classType,
        order_id: this.order_id,
      };
      this.relateConfirmLoading = true;
      this.$api
        .editStaff(params)
        .then(
          res => {
            this.$Message.success('编辑关联人成功');
            this.relateVisibleChange(false);
            if (this.isDetail) {
              this.$parent.initOrderDetail();
            } else {
              this.$parent.submitQueryForm();
            }
          },
          err => {
            {
            }
          }
        )
        .finally(() => {
          this.relateConfirmLoading = false;
        });
    },
    relateVisibleChange(val) {
      if (val) {
        console.log('=>(edit-relates.vue:153) this.checkedStaffList', this.checkedStaffList);
        if (this.checkedStaffList.length > 0) {
          let staffList = cloneDeep(this.checkedStaffList);
          this.selectedStaffList = staffList.map(item => item.id);
          this.staffList.map(item => {
            staffList.map(staff => {
              if (item.id === staff.id && staff.disabled === '1') {
                item.disabled = '1';
              }
            });
          });
        } else {
          this.selectedStaffList = [];
        }
      } else {
        this.$emit('update:relateVisible', val);
      }
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  min-height: 200px;
}
.options-item {
  white-space: pre-line;
  word-break: break-all;
  padding-right: 40px;
  max-width: 400px;
  text-align: justify;
}
</style>
