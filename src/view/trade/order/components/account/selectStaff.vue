<template>
  <div>
    <Modal
      :value="value"
      width="400px"
      :title="placeholder"
      :footer-hide="false"
      :mask-closable="false"
      :lock-scroll="true"
      @on-visible-change="changeVisible"
    >
      <div class="content flex flex-item-align">
        <Select v-model="staff_id" :placeholder="placeholder" clearable filterable @on-change="selectSup">
          <Option v-for="(item, index) in staffList" :key="index" :label="item.name" :value="item.id"
            >{{ item.name }}<span v-if="item.mobile">({{ item.mobile }})</span></Option
          >
        </Select>
      </div>

      <div slot="footer">
        <Button @click="closeModal">取消</Button>
        <Button :loading="confirmLoading" type="primary" @click="confirm">确认</Button>
      </div>
    </Modal>
    <tips-modal
      v-model="warnVisible"
      title=""
      :isCustomContent="true"
      :confirmText="'去完善'"
      @onOk="goStaffList"
      :class-name="''"
    >
      <template #content>
        <div class="flex flex-item-align">
          <Icon type="md-alert" color="#f90" size="25" class="mr10" />
          <div style="font-size: 14px; font-weight: 500">当前员工信息不完善</div>
        </div>
      </template>
    </tips-modal>
  </div>
</template>

<script>
import tipsModal from '@/components/confirmModal/TipsModal';
export default {
  name: 'relateAdvisor',
  mixins: [],

  components: { tipsModal },
  props: {
    value: {
      type: Boolean,
      default: false,
    },

    divide_type: {
      type: String,
      default: '1',
    },
  },

  data() {
    return {
      staffList: [],
      confirmLoading: false,
      searchLoading: false,
      staff_id: '',
      currentItem: {},
      warnVisible: false,
      staffInfo: {},
    };
  },

  computed: {
    placeholder() {
      let s = '请选择人员';
      switch (this.divide_type) {
        case '1':
          s = '请选择理疗师';
          break;
        case '3':
          s = '请选择医生';
          break;
        case '4':
          s = '请选择药剂师';
          break;
        default:
          s = '请选择人员';
          break;
      }

      return s;
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    visibleChange(val) {
      if (!val && this.value) {
        this.getOrderRstRevenueMembers(this.currentItem?.name);
      }
    },
    selectSup(val) {
      this.staff_id = val;
      this.currentItem = this.staffList.find(item => item.id === val);
    },
    changeVisible(visible) {
      if (visible) {
        this.getOrderRstRevenueMembers('');
      } else {
        this.closeModal();
      }
    },
    getOrderRstRevenueMembers(keyword) {
      // console.log('=>(relateAdvisor.vue:105) keyword', keyword);
      this.searchLoading = true;
      let params = {
        // keyword,
        divide_type: this.divide_type,
      };
      this.$api
        .getOrderRstRevenueMembers(params)
        .then(res => {
          this.staffList = res.list;
        })
        .finally(() => {
          this.searchLoading = false;
        });
    },
    clearData() {
      this.staff_id = '';
      this.currentItem = {};
      this.staffList = [];
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      if (!this.staff_id) {
        this.$Message.error(`请选择${this.divide_type === '1' ? '理疗师' : '销售人员'}`);
        return;
      }
      this.staffInfo = this.staffList.find(item => item.id === this.staff_id);
      if (this.divide_type === '2' && this.staffInfo.into_status !== '1') {
        this.warnVisible = true;
        return;
      }
      this.$emit('success', this.staffInfo);
      this.closeModal();
    },
    goStaffList() {
      this.closeModal();
      this.$router.push({
        path: '/setting/member/list',
        query: { componentsName: 'staff', keywords: this.staffInfo.name },
      });
    },
  },
};
</script>

<style scoped lang="less">
.content {
  padding: 20px 20px 40px;
}

.option-item {
  //text-align: center;
  //padding: 4px 0;
  //height: auto;

  .avatar {
    border-radius: 50%;
  }
}

.row-header {
  position: sticky;
  top: 0px;
  z-index: 2;
  padding: 8px 0;
  background-color: #f5f5f5;
  font-weight: bold;
}
</style>

<style lang="less">
.relateAdvisorSelect {
  .el-select-dropdown__list {
    padding: 0px;
  }
}
</style>
