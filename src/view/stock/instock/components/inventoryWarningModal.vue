<template>
  <Modal
    ref="customModal"
    :value="value"
    width="800px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div slot="header">
      <Tabs v-model="currentTab" class="tab-wrapper">
        <TabPane :label="item.label" :name="item.key" v-for="(item, index) in tabList" :key="index"> </TabPane>
      </Tabs>
      <!--      <p-->
      <!--        class="tag dynamics-tag"-->
      <!--        v-if="config_info.is_set_dynamics === '1'"-->
      <!--        :style="{ color: config_info.status_text === '生效中' ? '#19be6b' : '#ffad33' }"-->
      <!--      >-->
      <!--        {{ config_info.status_text }}-->
      <!--      </p>-->
      <!--      <p class="tag regular-tag" v-if="config_info.is_set_regular === '1'">{{ config_info.status_text }}</p>-->
    </div>
    <div class="content" v-if="value">
      <!-- 动态预警 -->
      <div class="dynamics-block tab-block" v-show="currentTab === 'dynamics'">
        <dynamics-tab ref="dynamics" @dynamicsSuccess="setTabConfig"></dynamics-tab>
      </div>

      <!-- 固定预警 -->
      <div class="regular-block tab-block" v-show="currentTab === 'regular'">
        <regular-tab
          ref="regular"
          @getRegularList="getRegularList"
          @getExcelUploadLoading="getExcelUploadLoading"
          @uploadEnd="uploadEnd"
          @reset="refresh"
        ></regular-tab>
      </div>
    </div>
    <div slot="footer" class="flex flex-item-end">
      <Button @click="closeModal" class="mr10">取消</Button>

      <div v-if="currentTab === 'dynamics'">
        <Tooltip placement="top" :content="dynamicsBtnTipTitle" v-if="dynamicsBtnIsDisabled()">
          <Button :loading="saveLoading" type="primary" disabled>提交</Button>
        </Tooltip>
        <Poptip confirm width="200" :title="dynamicsBtnTipTitle" v-else @on-ok="confirm">
          <Button :loading="saveLoading" type="primary">提交</Button>
        </Poptip>
      </div>

      <div v-if="currentTab === 'regular'">
        <Tooltip
          placement="top"
          content="销售数据计算中，00:00-06:00期间无法提交固定库存预警方案"
          v-if="dynamicsBtnIsDisabled()"
        >
          <Button :loading="saveLoading" type="primary" disabled>提交</Button>
        </Tooltip>
        <Button
          v-else
          :loading="confirmLoading"
          type="primary"
          :disabled="!is_has_regular_list || confirmLoading"
          @click="confirm"
          >{{ confirmLoading ? '提交中...' : '提交' }}</Button
        >
      </div>
    </div>
  </Modal>
</template>

<script>
import dynamicsTab from './dynamicsTab.vue';
import regularTab from './regularTab.vue';
export default {
  name: 'emptyModal',
  mixins: [],

  components: {
    dynamicsTab,
    regularTab
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '设置库存预警方案'
    }
  },

  data() {
    return {
      tabList: [
        { label: '动态库存预警', key: 'dynamics' },
        { label: '固定库存预警', key: 'regular' }
      ],
      currentTab: 'dynamics',
      config_info: {},
      is_has_regular_list: false,
      confirmLoading: false
    };
  },

  computed: {
    dynamicsBtnTipTitle() {
      if (this.dynamicsBtnIsDisabled()) {
        return '销售数据计算中，00:00-06:00期间无法提交新的动态库存预警方案';
      } else {
        return '是否提交该预警方案（动态库存预警方案隔日生效）';
      }
    },
    saveLoading() {
      if (this.currentTab === 'dynamics') {
        return this.$refs.dynamics?.loading;
      } else {
        return this.$refs.regular?.loading;
      }
    }
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    uploadEnd() {
      this.refresh();
    },
    refresh() {
      this.$emit('refresh');
    },
    getRegularList(val) {
      if (val.length > 0) {
        this.is_has_regular_list = true;
      } else {
        this.is_has_regular_list = false;
      }
    },
    getExcelUploadLoading(val) {
      this.confirmLoading = val;
    },
    setTabConfig(config) {
      this.config_info = config;
    },
    // 是否处于静止提交方案的时间段
    dynamicsBtnIsDisabled() {
      const date = this.$moment().startOf('day').format('YYYY-MM-DD');
      const start_time = '00:00';
      const end_time = '06:00';
      return this.$moment().isBetween(`${date} ${start_time}`, `${date} ${end_time}`);
    },

    /**
     * @description: 弹窗滚动条回归顶部
     * @note: 如果全局没有处理，弹窗内部执行此方法处理
     * */
    MODAL_SCROLL_TOP() {
      let MODAL_EL = this.$el.getElementsByClassName('ivu-modal-body')[0];
      if (MODAL_EL) {
        MODAL_EL.scrollTop = 0;
      }
    },
    /**
     * @description: 弹窗状态检测
     * @params  { Boolean } visible true: 弹窗打开 false:弹窗关闭
     * */
    changeVisible(visible) {
      if (visible) {
        // todo
      } else {
        this.closeModal();
      }
    },

    /**
     * @description: 弹窗数据清除
     * */
    clearData() {
      this.currentTab = this.tabList[0].key;
    },

    /**
     * @description: 弹窗关闭
     * */
    closeModal() {
      this.clearData();
      this.MODAL_SCROLL_TOP();
      this.$emit('input', false);
    },

    /**
     * @description: 确定事件
     * */
    confirm() {
      // 动态库存预警
      if (this.currentTab === 'dynamics') {
        this.$refs.dynamics.setSavestockconfig()?.then(res => {
          if (res === 'success') {
            this.refresh();
            this.closeModal();
          }
        });
      }

      // 固定库存预警
      if (this.currentTab === 'regular') {
        this.$refs.regular.importConfirm();
      }
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal {
  min-width: 800px;
}
::v-deep .ivu-modal-body {
  height: 500px;
  min-height: 500px;
  overflow-y: auto;
}
::v-deep .ivu-modal-header {
  padding-bottom: 0px;
}
::v-deep .ivu-tabs-bar {
  border-bottom: none;
}
::v-deep .ivu-tabs-tab {
  margin-right: 10px;
}
.content {
  height: 100%;
  .tab-block {
    height: 100%;
  }
}
.tag {
  position: absolute;
  top: 14px;
  background: rgba(255, 155, 11, 0.1);
  border-radius: 2px;
  font-size: 10px;
  font-weight: 400;
  line-height: 12px;
  padding: 2px 4px;
  width: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
}
.dynamics-tag {
  left: 100px;
  transform: scale(0.9);
}
.regular-tag {
  left: 220px;
}

::v-deep .ivu-tabs-tab {
  //margin-right: 30px;
}
::v-deep .ivu-tabs-ink-bar {
  width: 30px !important;
  left: 36px;
}
</style>
