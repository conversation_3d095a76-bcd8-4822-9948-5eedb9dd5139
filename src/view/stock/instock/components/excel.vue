<template>
  <div class="wrapper">
    <div class="buttonBox">
      <el-upload
        ref="upload"
        type="button"
        action
        accept=".xlsx, .xls"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handle"
        :limit="1"
        :class="['flex', 'flex-item-align', { 'custom-upload-disabled': <PERSON><PERSON><PERSON>(name) || excelUploadLoading }]"
        :disabled="<PERSON><PERSON><PERSON>(name)"
      >
        <Button
          type="default"
          v-if="name == '' || excelUploadLoading"
          class="space6 upload-btn"
          :loading="excelUploadLoading"
        >
          <Icon type="ios-cloud-upload-outline" size="16" />
          {{ btnText }}</Button
        >
        <div class="had-upload" v-else>
          <img src="@/assets/image/warning/excel-icon.png" />
        </div>
      </el-upload>
      <div v-if="name && !excelUploadLoading" class="file-name">
        <span>{{ this.name }},或点此</span>
        <a @click="updateFile">更换文件</a>
      </div>
      <p class="upload-tip" v-else>点击按钮上传库存批量设置文件</p>
    </div>
  </div>
</template>

<script>
// import xlsx from 'xlsx'
import { utils, read } from 'xlsx';
export default {
  name: 'excel',
  components: {},
  mixins: [],
  props: {
    excelUploadLoading: {
      type: Boolean,
      default: false
    },
    btnText: {
      type: String,
      default: '上传,'
    }
  },
  data() {
    return {
      name: ''
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    updateFile() {
      this.$refs.upload.clearFiles();
      this.name = '';
      this.$emit('updateFile');
    },
    // 将流数据转化为json数据
    async handle(file) {
      console.log('change');
      //读取FILE中的数据（变为JSON格式）
      let excelJsonList = await this.readFile(file.raw);
      let workbook = read(excelJsonList, { type: 'binary' }),
        worksheet = workbook.Sheets[workbook.SheetNames[0]];
      this.html = utils.sheet_to_html(worksheet);
      excelJsonList = utils.sheet_to_json(worksheet);
      // 批量库存预警
      this.excelWarnUpload(excelJsonList, file.name);
    },
    // 读取数据
    readFile(file) {
      return new Promise(resolve => {
        let reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = ev => {
          resolve(ev.target.result);
        };
      });
    },
    // 将excel读取的日期数字改为时间格式
    formatDate(numb, format) {
      if (numb.toString().length == 5) {
        const old = numb - 1;
        const t = Math.round((old - Math.floor(old)) * 24 * 60 * 60);
        const time = new Date(1900, 0, old, 0, 0, t);
        const year = time.getFullYear();
        const month = time.getMonth() + 1;
        const date = time.getDate();
        return year + format + (month < 10 ? '0' + month : month) + format + (date < 10 ? '0' + date : date);
      } else {
        return numb;
      }
    },
    // 单独处理excel的日期
    handleDate(time) {
      if (time == '' || time == undefined || time.toString().includes('.')) return '';
      if (time.toString().includes('-') || time.toString().includes('/')) {
        return time;
      } else {
        return this.formatDate(time, '/');
      }
    },

    /* 批量库存预警的excel数据逻辑 */
    excelWarnUpload(excelJsonList, fileName) {
      let hasHandleExcelList = this.handleWarnExcelJsonList(excelJsonList);
      if (!hasHandleExcelList.length) {
        this.$Message.error('导入表格数据无效');
        return;
      }
      this.name = fileName;
      this.$emit('excelUpload', hasHandleExcelList);
    },

    /**
     * 将excel的json数据按照以下标准进行过滤
     * 库存预警数量不为空的数据视为有效数据
     * 过滤出对应的商品编号，库存预警数量，两个字段，
     * 数据格式为二位数组
     */
    handleWarnExcelJsonList(excelJsonList) {
      // 如果待处理的数据为空则直接返回空数组
      if (!excelJsonList || !excelJsonList.length) {
        return [];
      }
      let resultArr = [];
      const prod_id = '编码';
      const generic_name = '货品';
      const prod_type_text = '类型';
      const from_text = '来源';
      const prod_unit = '单位';
      const stock_num = '库存数量';
      const stock_warning = '预警数量';
      excelJsonList.forEach((item, index) => {
        if (
          item[prod_id] &&
          item[generic_name] &&
          item[prod_type_text] &&
          item[from_text] &&
          item[prod_unit] &&
          Number(item[stock_warning] || 0) > 0
        ) {
          resultArr.push({
            prod_id: item[prod_id],
            generic_name: item[generic_name],
            prod_type_text: item[prod_type_text],
            from_text: item[from_text],
            prod_unit: item[prod_unit],
            stock_num: item[stock_num],
            stock_warning: item[stock_warning]
          });
        }
      });
      return resultArr || [];
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
p {
  margin-bottom: 0px;
}
.buttonBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  .upload-btn {
    padding: 0 20px;
  }
  .had-upload {
    text-align: center;
    width: 130px;
    height: 130px;
    background: #f6f6f6;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    img {
      width: 35px;
      height: 32px;
    }
  }
  .upload-tip {
    margin-top: 10px;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 16px;
  }

  .file-name {
    color: #999999;
    margin-top: 10px;
    display: flex;
    align-items: center;
  }
}
::v-deep .custom-upload-disabled {
  .el-upload {
    cursor: not-allowed !important;
  }
}
</style>
