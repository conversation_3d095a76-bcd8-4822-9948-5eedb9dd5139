<template>
  <div class="dynamics-wrapper">
    <div class="dynamics-content">
      <Form ref="formData" :model="formData" :label-width="0">
        <FormItem>
          <div class="flex flex-item-align">
            <span class="mr10 first-tip">根据诊所过去</span>
            <InputNumber
              class="mr10"
              v-model="formData.count_day"
              :precision="0"
              controls-outside
              placeholder="建议14-21"
              :min="0"
              :max="30"
            ></InputNumber>
            <span>天 的销售数据</span>
          </div>

          <div class="flex flex-item-align mt30">
            <span class="mr10 first-tip">若货品库存无法支持未来</span>
            <InputNumber
              class="mr10"
              v-model="formData.day"
              :precision="0"
              controls-outside
              placeholder="建议7"
              :min="0"
            ></InputNumber>
            <span>天 进行销售，则进行预警</span>
          </div>
        </FormItem>
      </Form>
    </div>

    <div class="footer-tip">
      <div class="tip-header">
        <img src="@/assets/image/warning/tip_icon.png" />
        <p>推荐方案</p>
      </div>
      <div class="tip-content">
        <p>
          建议将库存预警参数设置为：根据过去14-21天的销售数据，支持未来销售7天及以上进行销售。此方案相对精准，可避免因节假日带来的数据波动，且采购-发货-收货周期适宜。
        </p>
        <p class="import-tip">若要暂停/关闭动态库存预警，只需将两个天数设置为0即可。</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dynamicsTab',
  mixins: [],
  components: {},
  props: {},
  data() {
    return {
      formData: {
        count_day: null,
        day: null
      },
      loading: false,
      config_info: {
        is_set_dynamics: '',
        // is_set_regular: '',
        status_text: ''
      }
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.stockGetclinicstockconfig();
  },
  methods: {
    stockGetclinicstockconfig() {
      this.$api.stockGetclinicstockconfig().then(
        res => {
          this.formData.count_day = Number(res.config?.count_day || 0);
          this.formData.day = Number(res.config?.day || 0);

          this.config_info = {
            is_set_dynamics: res.is_set_dynamics,
            // is_set_regular: res.is_set_regular,
            status_text: res.status_text
          };
          this.$emit('dynamicsSuccess', this.config_info);
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    validSuccess() {
      const { count_day, day } = this.formData;
      if (count_day === 0 && day === 0) {
        return true;
      }

      if (count_day === null || count_day === undefined || count_day === 0) {
        this.$Message.error('过去天数必须大于0');
        return false;
      }
      if (day === null || day === undefined || day === 0) {
        this.$Message.error('未来天数必须大于0');
        return false;
      }
      return true;
    },

    // 设置动态库存预警
    setSavestockconfig() {
      if (this.validSuccess()) {
        this.loading = true;
        let params = {
          ...this.formData
        };
        return this.$api
          .setSavestockconfig(params)
          .then(
            res => {
              this.loading = false;
              this.$Message.success('动态库存预警设置成功');
              return new Promise(resolve => {
                resolve('success');
              });
            },
            err => this.$Message.error(err.errmsg)
          )
          .finally(() => (this.loading = false));
      }
    }
  }
};
</script>

<style lang="less" scoped>
.dynamics-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .footer-tip {
    padding: 12px 20px;
    background: #f9fafc;
    border-radius: 4px;
    .tip-header {
      display: flex;
      align-items: center;
      img {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
      font-weight: bold;
      font-size: 14px;
      color: #000000;
      line-height: 16px;
    }

    .tip-content {
      margin-top: 6px;
      font-size: 12px;
      font-weight: 400;
      color: #969aa2;
      line-height: 18px;
      .import-tip {
        color: #e5634b;
        margin-top: 6px;
      }
    }
  }
}

.mt30 {
  margin-top: 30px;
}
p {
  margin: 0px;
}
</style>
