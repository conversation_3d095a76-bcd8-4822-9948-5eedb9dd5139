<template>
  <div class="regular-wrapper">
    <!-- 手动库存预警 -->
    <div class="regular-warning-wrapper" v-if="!isUpload">
      <div class="flex flex-item-align">
        <goods-search
          style="width: 200px; margin-bottom: 10px"
          ref="goods-search"
          :isClearable="true"
          placeholder="请搜索添加预警产品"
          @on-select="selectAddTableChangeHandle"
          v-model="selectAddTable"
        ></goods-search>
        <Poptip confirm width="200" title="将重置当前页面所有货品预警数据，是否继续?" @on-ok="stockRegularstockreset">
          <div type="text" class="reset">
            <Icon type="md-refresh" size="16" color="red" />
            重置
          </div>
        </Poptip>
      </div>
      <Table height="320" :loading="tableLoading" :columns="tableCols" :data="currentPageList" size="small">
        <template slot-scope="{ row, index }" slot="stock_warning">
          <InputNumber
            :min="0"
            v-model="currentPageList[index].stock_warning"
            @on-change="e => changeWarningNum(e, index)"
          ></InputNumber>
        </template>

        <template slot-scope="{ row, index }" slot="action">
          <a @click="deleteProduct(index)">删除</a>
        </template>
      </Table>
      <div style="margin-top: 10px">
        <Page
          :total="totalList.length"
          :page-size="tableFormData.pageSize"
          :current="tableFormData.page"
          :page-size-opts="[2, 4, 6, 8]"
          @on-change="onPageChange"
          @on-page-size-change="onPageSizeChange"
          show-sizer
          show-elevator
          show-total
          transfer
          style="margin-top: 10px; text-align: center"
        >
        </Page>

        <div>预警数量填0表示不预警</div>
      </div>
    </div>
    <!-- 模板设置 -->
    <div class="upload-wrapper" v-else>
      <div class="import-content">
        <div>
          <div class="upload-btn">
            <KExcelUpload
              :isWarn="true"
              :excelUploadLoading="excelUploadLoading"
              btnText="上传文件"
              @excelUpload="excelUpload"
            ></KExcelUpload>
            <p class="upload-tip">点击按钮上传库存批量设置文件</p>
          </div>

          <div class="upload-footer-btn">
            <p class="download" @click="templateDownload">下载模板</p>
            <Button class="next" @click="getStockRegularstocklist()" type="primary">下一步</Button>
          </div>

          <!--          <Progress-->
          <!--            v-if="excelUploadLoading"-->
          <!--            :percent="uploadPercent"-->
          <!--            :stroke-width="20"-->
          <!--            status="active"-->
          <!--            text-inside-->
          <!--            class="mt30"-->
          <!--          />-->
          <!-- 错误报告 -->
          <!--          <div class="error-report flex flex-item-center" v-show="hasHandleExcelList?.length && isImportSuccess">-->
          <!--            <p class="">本次成功导入{{ succ_num }}条记录，{{ fail_num }}条错误记录</p>-->
          <!--            <p class="download-error cursor hover" v-show="Number(fail_num) > 0" @click="seeReport">查看错误报告</p>-->
          <!--          </div>-->
        </div>
        <!--        <div class="spin-container" v-if="excelUploadLoading">-->
        <!--          <Spin fix></Spin>-->
        <!--        </div>-->
      </div>
      <!--      <div slot="footer">-->
      <!--        &lt;!&ndash; btn &ndash;&gt;-->
      <!--        <div class="flex flex-item-center">-->
      <!--          <Button type="primary" class="space6" @click="importConfirm" :disabled="isActiveBtn">-->
      <!--            {{ excelUploadLoading ? '导入中,请耐心等待···' : '导入' }}-->
      <!--          </Button>-->

      <!--          <Button type="primary" class="space6" :disabled="!isImportSuccess" @click="next">下一步</Button>-->
      <!--        </div>-->
      <!--      </div>-->
    </div>

    <!--    查看错误报告-->
    <Modal v-model="reportVisible" :mask-closable="false" width="800" title="错误报告">
      <div class="report-content" v-if="reportVisible">
        <Table :columns="reportColumn" :data="reportList" height="470"></Table>
      </div>
    </Modal>
  </div>
</template>

<script>
import excelBatchUpload from '@/mixins/excelBatchUpload';
import downloadExceL from '@/mixins/downloadExcel';
import KExcelUpload from './excel';
// 搜索产品
import GoodsSearch from './GoodsSearch.vue';
import cloneDeep from 'lodash.clonedeep';
import { $operator } from '@/libs/operation';
export default {
  name: 'regularTab',
  mixins: [excelBatchUpload, downloadExceL],
  components: {
    KExcelUpload,
    GoodsSearch
  },
  props: {},
  data() {
    return {
      loading: false,

      // ========== 表格库存预警 =========
      totalList: [],
      currentPageList: [],
      tableLoading: false,
      tableCols: [
        { title: '编码', key: 'prod_id', width: 80, align: 'center' },
        { title: '货品', key: 'generic_name', minWidth: 100, align: 'center' },
        { title: '类型', key: 'prod_type_text', minWidth: 80, align: 'center' },
        { title: '库存数量', key: 'stock_num', align: 'center' },
        { title: '预警数量', slot: 'stock_warning', width: 90, align: 'center' },
        { title: '单位', key: 'prod_unit', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      tableFormData: {
        page: 1,
        pageSize: 2,
        total: 0
      },
      selectAddTable: '',

      // ========== 模板导入 ============
      reportVisible: false, // 错误报告的modal
      exportLoading: false, // 模板下载loading
      excelUploadLoading: false, // 数据上传的loading
      handleExcelList: [], // 处理后要上传的excel数据
      hasHandleExcelList: [], // 准备导入的数据
      isImportSuccess: false, // 是否导入成功
      fail_num: 0, // 错误数量
      succ_num: 0, // 成功数量
      reportList: [], // 错误报告list
      excelUploadApiName: 'excelStockBatcheditwarning',
      reportColumn: [
        {
          title: '编号',
          key: 'id',
          align: 'center'
        },
        {
          title: '库存预警数量',
          key: 'stock_warning',
          align: 'center'
        },
        {
          title: '错误原因',
          key: 'fail_msg',
          align: 'center'
        }
      ]
    };
  },
  computed: {
    isUpload() {
      return this.totalList.length > 0 ? false : true;
    },
    isActiveBtn() {
      if (this.hasHandleExcelList?.length > 0 && !this.isImportSuccess) {
        return false;
      } else {
        return true;
      }
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getStockRegularstocklist();
  },
  methods: {
    handleParams() {
      let params = {
        batch_params: []
      };
      this.totalList.forEach(item => {
        console.log('-> item', item);
        if (item.stock_warning != item.original_num) {
          params.batch_params.push({
            prod_id: item.prod_id,
            stock_warning: item.stock_warning
          });
        }
      });
      return params;
    },
    // 提交固定库存预警
    productStockBatcheditwarning() {
      if (this.validSuccess()) {
        return this.$api.productStockBatcheditwarning(this.handleParams()).then(
          res => {
            this.$Message.success('接口调用成功');
            return new Promise(resolve => {
              resolve('success');
            });
          },
          err => this.$Message.error(err.errmsg)
        );
      }
    },
    // 重置固定库存预警列表
    stockRegularstockreset() {
      this.$api.stockRegularstockreset().then(
        res => {
          this.totalList = [];
          this.currentPageList = [];
          this.$Message.success('当前页面所有货品预警数据已被清除');
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    // 获取固定库存列表
    getStockRegularstocklist() {
      let params = {
        page: 1,
        pageSize: 2000
      };
      this.$api.getStockRegularstocklist(params).then(
        res => {
          this.totalList = [];
          res.list.forEach(item => {
            this.totalList.push({
              ...item,
              stock_warning: Number(item.stock_warning || 0),
              original_num: this.$lodash.cloneDeep(item.stock_warning)
            });
          });
          this.loadList();
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    // ====== 表格库存预警 =======
    loadList() {
      const { page, pageSize } = this.tableFormData;
      if (this.totalList.length) {
        this.currentPageList = this.totalList.slice(
          $operator.multiply(page - 1, pageSize),
          $operator.multiply(page, pageSize)
        );
      } else {
        this.totalList = [];
      }
    },
    changeWarningNum(e, index) {
      const { page, pageSize } = this.tableFormData;
      this.totalList[$operator.multiply(page - 1, pageSize) + index].stock_warning = e;
    },
    // 产品是否已经添加
    validProductExist(rowItem) {
      let isExist = false;
      this.totalList.some(item => {
        if (item.prod_id === rowItem.prod_id) {
          isExist = true;
          this.$Message.error(`【${rowItem.generic_name}】已经存在列表中`);
          return true;
        }
      });
      return isExist;
    },
    // 校验当前页面数据是否可以提交
    validSuccess() {
      let isSuccess = false;
      isSuccess = this.totalList.some((item, index) => {
        if (item.stock_warning === 0 || item.stock_warning === undefined || item.stock_warning === null) {
          this.$Message.error(`【${item.generic_name}】的预警数量不可为空`);
          return true;
        }
      });
      return !isSuccess;
    },
    // 添加预警产品
    selectAddTableChangeHandle(value, item = {}) {
      if (!value || this.validProductExist(item)) {
        return;
      }
      let currentItem = { ...item, prod_id: item.id, stock_warning: null, original_num: 0 };
      this.totalList.unshift(currentItem);
      this.$nextTick(() => {
        this.$refs['goods-search'].clear();
        this.selectAddTable = '';
        const { page, pageSize } = this.tableFormData;
        this.getCurrentPageList(page, pageSize);
      });
    },
    // 删除表格数据
    deleteProduct(index) {
      const { page, pageSize } = this.tableFormData;
      let previewListNum = $operator.multiply(page - 1, pageSize) + index;
      this.totalList.splice(previewListNum, 1);
      this.getCurrentPageList(page, pageSize);
    },
    tableScrollToTop() {
      this.$nextTick(() => {
        document.getElementsByClassName('ivu-table-body')[0]?.scroll(0, 0);
      });
    },
    // 通过page和pageSize动态计算当前页面的数据
    getCurrentPageList(page, pageSize) {
      const copyList = cloneDeep(this.totalList);
      this.currentPageList = copyList.slice($operator.multiply(page - 1, pageSize), $operator.multiply(page, pageSize));
      // 删除的逻辑
      if (page != 1 && this.currentPageList?.length === 0) {
        this.tableFormData.page = this.tableFormData.page - 1;
        this.currentPageList = copyList.slice(
          $operator.multiply(this.tableFormData.page - 1, pageSize),
          $operator.multiply(this.tableFormData.page, pageSize)
        );
      }
    },
    onPageChange(page, pageSize) {
      this.tableFormData.page = page;
      const curSize = this.tableFormData.pageSize;
      const copyList = cloneDeep(this.totalList);
      this.currentPageList = copyList.slice($operator.multiply(page - 1, curSize), $operator.multiply(page, curSize));
      this.tableScrollToTop();
    },
    onPageSizeChange(pageSize) {
      this.tableFormData.pageSize = pageSize;
      this.tableFormData.page = 1;
      const copyList = cloneDeep(this.totalList);
      this.currentPageList = copyList.slice(0, pageSize);
    },

    // ====== 模板 =======
    // 模板下载
    templateDownload() {
      const { name, prod_type, status } = this.queryFormData;
      this.exportLoading = true;
      let params = {
        name,
        prod_type,
        status,
        type: ' PROD_STOCK_WARNING' // 商品模板标识
      };
      this.$api
        .getClinicTemplateurl(params)
        .then(
          res => {
            this.download(res.url);
          },
          rej => this.$Message.error(rej.errmsg)
        )
        .finally(() => (this.exportLoading = false));
    },
    // 获取获取excel处理后的数据
    excelUpload(hasHandleExcelList) {
      this.hasHandleExcelList = hasHandleExcelList;
      // 处理完直接导入
      this.importConfirm();
    },
    // 查看错误报告
    seeReport() {
      this.importVisible = false;
      this.reportVisible = true;
    },
    // 导入
    importConfirm() {
      // this.saveExcel( this.hasHandleExcelList )
      // 批量轮询
      this._btachUploadPolling(this.hasHandleExcelList, true);
    },
    // 取消导入弹窗
    importCancel() {
      this.importVisible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.regular-wrapper {
  height: 100%;
  .reset {
    margin-left: 10px;
    height: 32px;
    color: red;
    cursor: pointer;
  }
  .upload-wrapper {
    height: 100%;
  }
  .import-content {
    height: 160px;

    .upload-btn {
      height: 400px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .upload-tip {
        margin-top: 10px;
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        line-height: 16px;
      }
    }

    .upload-footer-btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .download {
        color: #155bd4;
        border-bottom: 1px solid #155bd4;
        cursor: pointer;
        height: 20px;
      }

      .next {
        margin-left: 10px;
      }
    }

    p {
      margin-bottom: 0px;
    }

    .choose {
      color: rgba(17, 87, 229, 0.5);
    }

    .error-report {
      margin-top: 80px;

      .download-error {
        margin-left: 15px;
        color: rgba(17, 87, 229, 0.5);
      }
    }
  }
}
</style>
