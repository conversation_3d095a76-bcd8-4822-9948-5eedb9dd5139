<!-- 用于搜索当前产品，不需要做回显，只做添加，所以需要抛出所有数据 -->
<template>
  <div>
    <Select
      ref="goods"
      :value="value"
      :clearable="isClearable"
      filterable
      :placeholder="placeholder"
      @on-select="selectSup"
      @on-query-change="queryChange"
      @on-clear="clearSub"
      transfer
      transfer-class-name="search-sel"
      :disabled="disabled"
    >
      <Option v-for="(item, index) in goods_list" :label="item.generic_name" :value="item.id" :key="item.id">
        <div class="option-item">
          <span class="item-name">{{ item.generic_name }}</span>
          <span class="readonly-desc">{{ item.from_text }}</span>
        </div>
      </Option>
    </Select>
  </div>
</template>

<script>
import util from '@/libs/util';
export default {
  name: 'goods-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请搜索添加相关货品'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchLoading: false,
      goods_list: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchMethod();
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      let params = {
        name: query
      };
      this.searchLoading = true;
      this.$api
        .getInStockProductList(params)
        .then(res => {
          this.goods_list = res.list;
        })
        .finally(() => (this.searchLoading = false));
    }, 300),
    search() {},
    selectSup(val) {
      let selectItem = this.goods_list.find(item => item.id === val.value);
      this.$emit('on-select', val.value, selectItem);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clear() {
      this.$refs.goods.clearSingleSelect();
    },
    clearSub() {
      this.searchMethod('');
      this.$emit('input', '');
    }
  }
};
</script>

<style lang="less" scoped>
.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .item-name {
    width: 180px;
    text-align: justify;
    white-space: break-spaces;
  }
}
.readonly-desc {
  color: #999;
}
</style>
