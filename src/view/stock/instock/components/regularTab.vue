<template>
  <div class="regular-wrapper">
    <!-- 手动库存预警 -->
    <div class="regular-warning-wrapper" v-if="totalList.length">
      <div class="flex flex-item-align">
        <Tooltip
          placement="top"
          content="销售数据计算中，00:00-06:00期间无法重置固定库存预警方案"
          v-if="dynamicsBtnIsDisabled()"
        >
          <div class="reset reset-disabled">
            <Icon type="md-refresh" size="16" color="#ccc" />
            重置
          </div>
        </Tooltip>
        <Poptip
          v-else
          confirm
          width="200"
          title="将重置当前页面所有货品预警数据，是否继续?"
          @on-ok="stockRegularstockreset"
        >
          <div class="reset">
            <Icon type="md-refresh" size="16" color="red" />
            重置
          </div>
        </Poptip>
      </div>
      <Table height="340" :loading="tableLoading" :columns="tableCols" :data="currentPageList" size="small">
        <template slot-scope="{ row, index }" slot="action">
          <span @click="deleteProduct(row, index)" style="color: #ccc" v-if="dynamicsBtnIsDisabled()">删除</span>
          <a @click="deleteProduct(row, index)" style="color: red" v-else>删除</a>
        </template>
      </Table>
      <div style="margin-top: 10px">
        <Page
          :total="totalList.length"
          :page-size="tableFormData.pageSize"
          :current="tableFormData.page"
          @on-change="onPageChange"
          @on-page-size-change="onPageSizeChange"
          show-sizer
          show-elevator
          show-total
          transfer
          style="margin-top: 10px; text-align: center"
        >
        </Page>
      </div>
    </div>
    <!-- 模板设置 -->
    <div class="upload-wrapper" v-else>
      <div class="import-content">
        <div>
          <div class="upload-btn">
            <excelUpload
              ref="excelUpload"
              :excelUploadLoading="excelUploadLoading"
              btnText="上传文件"
              @excelUpload="excelUpload"
              @updateFile="updateFile"
            ></excelUpload>
          </div>

          <div class="upload-footer-btn">
            <p class="download" @click="templateDownload">
              下载模板<Spin v-show="exportLoading" class="ml6"
                ><Icon type="ios-loading" size="14" class="spin-icon-load"></Icon
              ></Spin>
            </p>
            <Button class="next" :disabled="hasHandleExcelList.length === 0" @click="next" type="primary"
              >下一步</Button
            >
          </div>
        </div>
      </div>
    </div>

    <div class="tip-wrapper">
      <div class="tip-block">
        <img src="@/assets/image/warning/warn.png" />
        <div class="title">注意</div>
        <div>预警数量填0表示不预警</div>
      </div>
      <div class="flex flex-item-center" v-show="isImportSuccess">
        <p class="">本次成功导入{{ succ_num }}条记录，{{ fail_num }}条错误记录</p>
        <p class="download-error cursor hover" v-show="Number(fail_num) > 0" @click="seeReport">查看错误报告</p>
      </div>
    </div>

    <!--    查看错误报告-->
    <Modal v-model="reportVisible" :mask-closable="false" width="800" title="错误报告">
      <div class="report-content" v-if="reportVisible">
        <Table :columns="reportColumn" :data="reportList" height="480"></Table>
      </div>
      <div slot="footer">
        <Button type="default" @click="() => (reportVisible = false)">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import downloadExceL from '@/mixins/downloadExcel';
import excelBatchUpload from '@/mixins/excelBatchUpload';
import excelUpload from './excel';
// 搜索产品
import GoodsSearch from './GoodsSearch.vue';
import cloneDeep from 'lodash.clonedeep';
import { $operator } from '@/libs/operation';
export default {
  name: 'regularTab',
  mixins: [excelBatchUpload, downloadExceL],
  components: {
    excelUpload,
    GoodsSearch
  },
  props: {},
  data() {
    return {
      loading: false,

      // ========== 表格库存预警 =========
      totalList: [],
      currentPageList: [],
      // list: [],
      tableLoading: false,
      tableCols: [
        { title: '编码', key: 'prod_id', width: 80, align: 'center' },
        { title: '货品', key: 'generic_name', minWidth: 100, align: 'center' },
        { title: '类型', key: 'prod_type_text', minWidth: 80, align: 'center' },
        { title: '来源', key: 'from_text', minWidth: 80, align: 'center' },
        { title: '库存数量', key: 'stock_num', width: 70, align: 'center' },
        { title: '预警数量', key: 'stock_warning', width: 70, align: 'center' },
        { title: '单位', key: 'prod_unit', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      tableFormData: {
        page: 1,
        pageSize: 10,
        total: 0
      },

      // ========== 模板导入 ============
      reportVisible: false, // 错误报告的modal
      exportLoading: false, // 模板下载loading
      handleExcelList: [], // 处理后要上传的excel数据
      hasHandleExcelList: [], // 准备导入的数据
      isImportSuccess: false, // 是否导入成功
      fail_num: 0, // 错误数量
      succ_num: 0, // 成功数量
      reportList: [], // 错误报告list
      excelUploadApiName: 'excelStockBatcheditwarning',
      reportColumn: [
        {
          title: '编号',
          key: 'id',
          align: 'center'
        },
        {
          title: '库存预警数量',
          key: 'stock_warning',
          align: 'center'
        },
        {
          title: '错误原因',
          key: 'fail_msg',
          align: 'center'
        }
      ]
    };
  },
  computed: {},
  watch: {
    totalList(val) {
      this.$emit('getRegularList', val);
    },
    excelUploadLoading(val) {
      this.$emit('getExcelUploadLoading', val);
    }
  },
  created() {},
  mounted() {
    this.getStockRegularstocklist();
  },
  methods: {
    // 是否处于静止提交方案的时间段
    dynamicsBtnIsDisabled() {
      const date = this.$moment().startOf('day').format('YYYY-MM-DD');
      const start_time = '00:00';
      const end_time = '06:00';
      return this.$moment().isBetween(`${date} ${start_time}`, `${date} ${end_time}`);
    },
    next() {
      this.totalList = this.hasHandleExcelList;
      this.loadList();
    },
    updateFile() {
      this._initUploadData();
      this.hasHandleExcelList = [];
    },
    handleParams() {
      let params = {
        batch_params: []
      };
      this.totalList.forEach(item => {
        params.batch_params.push({
          prod_id: item.prod_id,
          stock_warning: item.stock_warning
        });
      });
      return params;
    },
    // 提交固定库存预警
    productStockBatcheditwarning() {
      if (this.validSuccess()) {
        return this.$api.productStockBatcheditwarning(this.handleParams()).then(
          res => {
            this.$Message.success('提交成功');
            return new Promise(resolve => {
              resolve('success');
            });
          },
          err => this.$Message.error(err.errmsg)
        );
      }
    },
    // 重置固定库存预警列表
    stockRegularstockreset() {
      this.$api.stockRegularstockreset().then(
        res => {
          this.totalList = [];
          this.$Message.success('当前页面所有货品预警数据已被清除');
          this._initUploadData();
          this.$emit('reset');
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    // 删除固定库存预警
    // stockDeleteregularstock(row) {
    //   let params = { id: row.id };
    //   this.$api.stockDeleteregularstock(params).then(
    //     res => {
    //       if (this.tableFormData.page > 1 && this.list.length === 1) {
    //         this.tableFormData.page = this.tableFormData.page - 1;
    //       }
    //       this.$Message.success(`货品【${row.generic_name}】删除成功`);
    //       this.getStockRegularstocklist();
    //     },
    //     err => this.$Message.error(err.errmsg)
    //   );
    // },

    // ====== 表格库存预警 =======
    loadList() {
      this.tableFormData.page = 1;
      this.tableFormData.pageSize = 10;
      const { page, pageSize } = this.tableFormData;
      if (this.totalList.length) {
        this.currentPageList = this.totalList.slice(
          $operator.multiply(page - 1, pageSize),
          $operator.multiply(page, pageSize)
        );
      } else {
        this.totalList = [];
      }
    },
    // 获取固定库存列表
    getStockRegularstocklist() {
      let params = {
        page: 1,
        pageSize: 20000
      };
      this.$api.getStockRegularstocklist(params).then(
        res => {
          this.totalList = [];
          res.list.forEach(item => {
            this.totalList.push({
              ...item,
              stock_warning: Number(item.stock_warning || 0)
            });
          });
          this.hasHandleExcelList = res.list;
          this.loadList();
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    // 通过page和pageSize动态计算当前页面的数据
    getCurrentPageList(page, pageSize) {
      const copyList = cloneDeep(this.totalList);
      this.currentPageList = copyList.slice($operator.multiply(page - 1, pageSize), $operator.multiply(page, pageSize));
      // 删除的逻辑
      if (page != 1 && this.currentPageList?.length === 0) {
        this.tableFormData.page = this.tableFormData.page - 1;
        this.currentPageList = copyList.slice(
          $operator.multiply(this.tableFormData.page - 1, pageSize),
          $operator.multiply(this.tableFormData.page, pageSize)
        );
      }
    },
    onPageChange(page, pageSize) {
      this.tableFormData.page = page;
      const curSize = this.tableFormData.pageSize;
      const copyList = cloneDeep(this.totalList);
      this.currentPageList = copyList.slice($operator.multiply(page - 1, curSize), $operator.multiply(page, curSize));
      this.tableScrollToTop();
    },
    onPageSizeChange(pageSize) {
      this.tableFormData.pageSize = pageSize;
      this.tableFormData.page = 1;
      const copyList = cloneDeep(this.totalList);
      this.currentPageList = copyList.slice(0, pageSize);
    },
    // 删除表格数据
    deleteProduct(row, index) {
      const { page, pageSize } = this.tableFormData;
      let previewListNum = $operator.multiply(page - 1, pageSize) + index;
      this.totalList.splice(previewListNum, 1);
      this.getCurrentPageList(page, pageSize);
      this.$Message.success(`货品【${row.generic_name}】删除成功`);
      this.hasHandleExcelList = this.totalList;
    },

    // 校验当前页面数据是否可以提交
    validSuccess() {
      return this.totalList.length > 0;
    },
    tableScrollToTop() {
      this.$nextTick(() => {
        document.getElementsByClassName('ivu-table-body')[0]?.scroll(0, 0);
      });
    },

    // ====== 模板 =======
    // 模板下载
    templateDownload() {
      this.exportLoading = true;
      let params = {
        type: ' PROD_STOCK_WARNING' // 商品模板标识
      };
      this.$api
        .getClinicTemplateurl(params)
        .then(
          res => {
            this.download(res.url);
          },
          rej => this.$Message.error(rej.errmsg)
        )
        .finally(() => (this.exportLoading = false));
    },
    // 获取获取excel处理后的数据
    excelUpload(hasHandleExcelList) {
      this.hasHandleExcelList = hasHandleExcelList;
    },
    // 查看错误报告
    seeReport() {
      this.importVisible = false;
      this.reportVisible = true;
    },
    // 导入
    importConfirm() {
      // 批量轮询
      this._btachUploadPolling(this.hasHandleExcelList, true, 100, true);
    }
  }
};
</script>

<style lang="less" scoped>
.regular-wrapper {
  height: 100%;
  .reset {
    margin-left: 10px;
    height: 32px;
    color: red;
    cursor: pointer;
  }
  .reset-disabled {
    color: #999 !important;
  }
  .upload-wrapper {
    height: 420px;
  }
  .import-content {
    height: 160px;

    .upload-btn {
      height: 380px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .upload-footer-btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .download {
        color: #155bd4;
        border-bottom: 1px solid #155bd4;
        cursor: pointer;
        height: 20px;
        display: flex;
      }

      .next {
        margin-left: 10px;
      }
    }
  }
  .download-error {
    margin-left: 15px;
    color: #155bd4 !important;
  }
}
.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.tip-wrapper {
  margin-top: 10px;
  background: #f9fafc;
  border-radius: 4px;
  padding: 12px 20px;
  font-size: 12px;
  font-weight: 400;
  color: #969aa2;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tip-block {
    display: flex;
    align-items: center;
    img {
      width: 14px;
      height: 13px;
      margin-right: 4px;
    }
    .title {
      font-size: 12px;
      font-weight: 600;
      color: #000000;
      line-height: 16px;
      margin-right: 10px;
    }
  }
}
p {
  margin: 0px;
}
</style>
