<template>
  <div class="inventory-wrapper">
    <Form ref="inventoryRef" :model="formValidate" :rules="ruleValidate" :label-width="80">
      <Row>
        <!--        <Col span="8">-->
        <!--          <FormItem label="盘点仓库" prop="storehouse">-->
        <!--            <Input type="text" :value="formValidate.storehouse" placeholder="" disabled />-->
        <!--          </FormItem>-->
        <!--        </Col>-->
        <Col span="4">
          <FormItem label="建单人" prop="user">
            <Input type="text" :value="formValidate.user" placeholder="" disabled />
          </FormItem>
        </Col>
        <Col span="4">
          <FormItem label="建单时间">
            <DatePicker
              format="yyyy-MM-dd"
              ref="dateInventoryRef"
              type="date"
              :value="formValidate.stock_create_time"
              disabled
            ></DatePicker>
          </FormItem>
        </Col>
        <Col span="4">
          <FormItem label="盘点时间" prop="create_time">
            <DatePicker
              format="yyyy-MM-dd"
              ref="dateInventoryRef"
              type="date"
              :value="formValidate.create_time"
              :disabled="!(isCanSkipMonth && isMonthInventory) || isExamine || isDisabled"
              :options="dateOptions()"
              @on-change="changeTime"
              :clearable="false"
            ></DatePicker>
          </FormItem>
        </Col>

        <Col span="6">
          <FormItem label="盘点单号" prop="bill_code">
            <Input type="text" :value="formValidate.stocktake_no" placeholder="系统自动生成" disabled />
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="备注" prop="remark">
            <Input
              v-model="formValidate.remark"
              type="text"
              placeholder="请输入盘点备注"
              :disabled="isDisabled"
              show-word-limit
              maxlength="300"
            />
          </FormItem>
        </Col>
      </Row>
    </Form>

    <!-- tab切换 -->
    <Tabs v-model="tabCurrent" @on-click="tabClick">
      <TabPane
        :label="item.key === 'inventoryDetail' ? `${item.label}（${$route.query.prodTypeText}）` : item.label"
        :name="item.key"
        v-for="item in ADD_DETAIL_TAB_LIST"
        :key="item.key"
      ></TabPane>
    </Tabs>
    <!-- 该组件以上逻辑全部放入了 detail_mixin -->
    <detail-table
      ref="detailTable"
      v-show="tabCurrent === 'inventoryDetail'"
      :is-disabled="isDisabled"
      :is-month-inventory="isMonthInventory"
      :prod_type="prod_type"
      :form-validate="formValidate"
      :detail-data="detailData"
    />
    <operate-history v-show="tabCurrent === 'operateHistory'" :audit-records="detailData.operator_list" />

    <div class="fixed-bottom-wrapper">
      <div v-if="isExamine">
        <back-button></back-button>
        <Button v-eleControl="'EpmnwqkXBz'" type="primary" class="ml10" @click="examine">审核</Button>
      </div>
      <div style="display: flex; justify-content: center" v-else>
        <div class="disabled-foot" v-if="isDisabled && (orderStatus === 'REJECT' || orderStatus === 'FORCE_REJECT')">
          <back-button></back-button>
          <Button
            v-eleControl="'EM8BqLvdxN'"
            v-if="tabCurrent === 'inventoryDetail' && !isDetail"
            type="primary"
            @click="editInventory"
            style="margin-left: 12px"
            >修改盘点单</Button
          >
        </div>
        <div v-if="!isDisabled && (orderStatus === 'REJECT' || orderStatus === 'FORCE_REJECT')">
          <Button @click="$router.back()" style="margin-right: 12px">取消</Button>
          <Button
            v-eleControl="'EM8BqLvdxN'"
            type="primary"
            @click="submitForm"
            class="submitBtn"
            :loading="subBtnLoading"
            >提交
          </Button>
        </div>
        <div v-if="orderStatus === 'PASS' || orderStatus === 'WAIT' || orderStatus === 'AUDITING'">
          <back-button></back-button>
        </div>
        <div v-if="isShowCancelStockBtn">
          <Poptip v-eleControl="'EM8BqLvdxN'" confirm title="是否要取消该次盘点?" @on-ok="cancelInventory">
            <Button type="default" style="margin-left: 12px">取消盘点</Button>
          </Poptip>
        </div>
        <div v-if="!$route.query.id && !$route.query.order_status">
          <back-button></back-button>
          <Poptip v-eleControl="'EM8BqLvdxN'" confirm title="是否要清空当前页面全部数据?" @on-ok="clearAll">
            <Button style="margin: 0 12px">清空</Button>
          </Poptip>
          <Button v-eleControl="'EM8BqLvdxN'" type="primary" @click="submitForm" :loading="subBtnLoading">提交</Button>
        </div>
        <div v-if="orderStatus === 'CANCEL'">
          <back-button></back-button>
        </div>
      </div>
    </div>

    <!-- 审核 -->
    <examine-modal
      :examineVisible.sync="modalIsShow"
      :audit_status_info="audit_status_info"
      @examineSuccess="examineSuccess"
    />
  </div>
</template>

<script>
// 该页面表单部分及tab切换的逻辑。 全部在混入文件 detail_mixin。 主要是解偶页面逻辑
// 上述内容较少， 不适合抽离成组件。 如果后续业务量增加，可直接把逻辑抽出去使用
import detail_mixin from './mixin/detail_mixin';
// 当前组件内的js只处理 业务数据 查询， 赋值 和 提交判定。组件逻辑只在对应组件处理。
import { cloneDeep } from 'lodash-es';
import moment from 'moment/moment';
import * as runtime from '@/libs/runtime';
import DetailTable from './components/detailTable.vue';
import OperateHistory from './components/operateHistory.vue';
import examineModal from './components/examineModal.vue';
import { submit_data } from './util/util';
import { isEmpty } from '../../../utils/helper';

export default {
  name: 'InventoryDetail',
  components: {
    examineModal,
    DetailTable,
    OperateHistory,
  },
  mixins: [detail_mixin],
  data() {
    return {
      isDisabled: false, // 是否可编辑
      modalIsShow: false, // 审核弹窗
      audit_status_info: {
        list: [],
        type: '',
        msg: '',
        id: '',
      }, // 审核状态信息
      clone_audit_status_info: {},
      orderStatus: '',
      subBtnLoading: false, // 提交loading
      prod_type: '', // 产品类型
      prodTypeText: '', // 产品类型名称
      userInfo: {}, // 用户信息
      detailData: {}, // 接收详情或暂存额数据用于分发数据
    };
  },
  computed: {
    isShowCancelStockBtn() {
      const { orderStatus, isDisabled } = this;
      const { type } = this.$route.query;
      // 如果是编辑页面， 隐藏
      if (!isDisabled) return false;
      console.log(orderStatus, isDisabled, type, 'orderStatus');
      // 如果不是待审核， 强制驳回 和 普通驳回， 隐藏
      if (!['WAIT', 'FORCE_REJECT', 'REJECT'].includes(orderStatus)) return false;
      // 如果是强制驳回 或者普通驳回页， 并且不是编辑页面，显示
      if (['FORCE_REJECT', 'REJECT'].includes(orderStatus)) return true;
      // 如果是待审核，并且是详情页，显示
      if (orderStatus === 'WAIT' && type === 'detail') return true;
      return false;
    },
  },
  created() {
    this.prod_type = this.$route.query.prod_type;
    this.prodTypeText = this.$route.query.prodTypeText;
    this.userInfo = runtime.getUser();
    this.formValidate.user = runtime.getUser().name;
    this.formValidate.create_time = moment().format('YYYY-MM-DD');
    this.formValidate.stock_create_time = moment().format('YYYY-MM-DD');
    // 如果id存在， 就去获取详情， 否则获取草稿
    if (this.$route.query.id) {
      this.getDetail();
    } else {
      this.getDraft();
    }
  },
  methods: {
    getDraft() {
      const params = {
        prod_type: this.$route.query.prod_type,
        type: this.isMonthInventory ? 'SPAN' : 'NOW',
      };
      this.$api.getInventoryDraft(params).then(res => {
        if (res.has_display === '1') {
          this.detailData = res?.data || {};
          this.formValidate.remark = res.data.remark; // 暂存的备注信息，定义在混入文件
          this.formValidate.create_time = res.data.create_time || res?.data?.take_date || moment().format('YYYY-MM-DD'); // 同上
        }
        this.last_time_stamp = res.last_time; // 上次判断时间戳，定义在混入文件
        // 判断是否是跨月盘点
        if (this.isMonthInventory) {
          // 处于跨月盘点模式，如果当天已经盘过，不存在可以盘点的时间
          // false: 不显示提示框  true: 显示提示框
          if (!this.stopInventoryModal()) {
            // 不显示提示框的逻辑下，草稿的盘点时间是否在可选的盘点时间内
            this.handleDraftInventoryTime(res.data?.create_time);
          }
        }
      });
    },
    // 编辑获取详情
    getDetail() {
      this.tableLoading = true;
      this.isDisabled = true;
      this.$api.getInventoryInfo({ id: this.$route.query.id }).then(
        data => {
          this.orderStatus = data.status;
          this.$router.replace({
            query: {
              ...this.$route.query,
              order_status: data.status,
            },
          });
          data.items = data.items?.map(item => {
            if (data.status === 'PASS') {
              item.stock_num = item.before_stock_num;
            } else if (data.status === 'WAIT') {
              item.stock_num = item.before_stock_num;
              item.current_stock_num = isEmpty(item.new_stock_num)
                ? item.before_stock_num || '0'
                : item.new_stock_num || '0';
            } else {
              item.stock_num = isEmpty(item.new_stock_num) ? item.before_stock_num : item.new_stock_num;
            }
            item.choose_batch_list = item.choose_batch_list?.map(o => {
              const current_stock_num_1 = item?.batch_list?.find(i => i.id === o.id)?.stock_num || '0';
              if (data.status === 'PASS') {
                o.stock_num = o.before_stock_num;
              } else if (data.status === 'WAIT') {
                item.stock_num = o.before_stock_num;
                o.current_stock_num = current_stock_num_1;
              } else {
                o.stock_num = current_stock_num_1;
              }
              o.old_stock_num = o.before_stock_num;
              return o;
            });
            item.old_stock_num = item.before_stock_num;
            return {
              ...item,
              id: item.id || item.prod_id,
            };
          });
          this.clone_audit_status_info = data.audit_status_info;
          this.status = data.status; // 获取接口返回的状态
          this.formValidate.stocktake_no = data.stocktake_no;
          this.formValidate.remark = data.remark;
          if (data.create_info) {
            // this.formValidate.create_time = moment(data.create_info.time * 1000).format('YYYY-MM-DD');
            this.formValidate.stock_create_time = moment(data.stock_create_time * 1000).format('YYYY-MM-DD');
            this.formValidate.create_time = moment(data.stocktake_time * 1000).format('YYYY-MM-DD');
            this.formValidate.user = data.create_info.operator;
          }
          this.auditRecords = data.operator_list;
          this.tableLoading = false;
          this.detailData = data;
          this.last_time_stamp = data.last_time;

          // 如果是审核,详情，则不拦截
          if (!this.isExamine && !this.isDetail) {
            this.stopInventoryModal();
          }
        },
        error => {
          {
          }
        }
      );
    },
    examine() {
      this.audit_status_info = {
        ...cloneDeep(this.clone_audit_status_info),
        id: this.$route.query.id,
      };
      this.modalIsShow = true;
    },
    examineSuccess() {
      this.$router.push('/stock/inventory/list');
    },
    //修改盘点单
    editInventory() {
      // 创建盘点单的时间。5.10改版后，之前的数据不让修改
      const stock_create_time = this.detailData.stock_create_time * 1000;
      const isTime = new Date(stock_create_time).getTime() === stock_create_time;
      if (isTime && moment('2024-05-10').isAfter(stock_create_time)) {
        this.$Message.error('该盘点是历史版本，不支持修改，请重新创建！');
        return;
      }
      let order_create_time = this.formValidate.create_time;
      let type = this.checkDateType(order_create_time);
      if (type !== 'defaultDateRange' && order_create_time && this.isMonthInventory) {
        this.detailEditTipModal(order_create_time);
      } else {
        this.isDisabled = false;
      }
    },

    /**
     * 详情修改盘点单时，存在单子之前提交的日期与 盘点单60天/盘点日期之后 的时间范围有冲突，此处要提示用户处理
     * @params order_create_time 详情里上次单子的盘点时间
     * */
    detailEditTipModal(order_create_time) {
      let type = this.checkDateType(order_create_time);
      let tip_obj = {
        outDateRange: {
          title: '盘点时间不能超过61天',
          content: '系统将盘点时间默认到今天，已选择的所有货品将被清空。',
        },
        innerErrorDateRange: {
          title: '盘点时间不能早于或等于上次盘点时间',
          content: '系统将盘点时间改到今天，已选择的所有货品将被清空。',
        },
      };

      if (tip_obj[type]) {
        this.$Modal.confirm({
          title: tip_obj[type].title,
          content: `<p>${tip_obj[type].content}</p>`,
          okText: '确定',
          onOk: () => {
            // 更新日期
            this.formValidate.create_time = moment().format('YYYY-MM-DD');
            // 清除数据
            this.$refs.detailTable.list = [];
            this.$refs.detailTable.localList = [];
            // 打开禁止修改盘点单的限制
            this.isDisabled = false;
          },
        });
      }
    },

    submitForm() {
      this.$refs['inventoryRef'].validate(valid => {
        if (valid) {
          let values = this.formValidate;
          let params = {
            prod_type: this.$route.query.prod_type,
            remark: values.remark,
            type: this.isMonthInventory ? 'SPAN' : 'NOW',
            take_date: this.formValidate.create_time,
          };

          this.$route.query.id && (params.id = this.$route.query.id);
          const list = submit_data(this.$refs.detailTable.localList, 'submit');
          let isRequire = false;
          let isInput = false;
          for (let i = 0; i < list.length; i++) {
            const item = list[i];
            if (!item.is_batch_list) {
              isRequire = true;
              break;
            }
            if (item.is_batch_list && isEmpty(item.choose_batch_list) && isEmpty(item.after_stock_num)) {
              isInput = true;
              break;
            }
            if (item.is_batch_list && !isEmpty(item.choose_batch_list)) {
              for (let j = 0; j < item.choose_batch_list.length; j++) {
                const item1 = item.choose_batch_list[j].after_stock_num;
                if (isEmpty(item1)) {
                  isInput = true;
                  break;
                }
              }
            }
          }
          if (isRequire) {
            this.$Message.error('无可盘点批次的货品不能参与盘点，请删除后提交！');
            return;
          }
          if (isInput) {
            this.$Message.error('盘后库存必填！');
            return;
          }
          if (isEmpty(list)) {
            this.$Message.error('请选择货品！');
            return;
          }
          params.items = list;
          if (this.id) {
            params.id = this.id;
          }
          if (this.$route.query.order_code) {
            params.order_code = this.$route.query.order_code;
          }
          this.subBtnLoading = true;
          this.$api
            .editInventory(params)
            .then(
              () => {
                this.$Message.success('提交成功');
                this.$router.replace('/stock/inventory/list');
              },
              error => {}
            )
            .finally(() => {
              this.subBtnLoading = false;
            });
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },
    cancelInventory() {
      let params = {
        status: 'CANCEL',
        id: this.$route.query.id,
      };
      this.$api.stocktakeChangestatus(params).then(
        () => {
          this.getDetail();
        },
        err => {}
      );
    },
    clearAll() {
      if (this.isMonthInventory) {
        this.formValidate.create_time = moment().format('YYYY-MM-DD');
      }
      this.$refs.detailTable.list = [];
      this.$refs.detailTable.localList = [];
    },
  },
};
</script>

<style lang="less" scoped>
.search-sel {
  max-height: 300px !important;
}

[v-cloak] {
  display: none;
}

.old-v {
  color: red;
}
</style>
