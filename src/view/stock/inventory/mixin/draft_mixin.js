import { debounce } from 'lodash-es';
import moment from 'moment';
import { submit_data } from '../util/util';

export default {
  data() {
    return {
      saveTimes: 0,
    };
  },
  watch: {
    formValidate: {
      handler(val) {
        this.saveDraft();
      },
      deep: true,
    },
    localList: {
      handler(val) {
        this.saveDraft();
      },
      deep: true,
    },
  },
  methods: {
    saveDraft: debounce(function () {
      if (this.$route.query.id) {
        return;
      }
      if (this.saveTimes === 0) {
        this.saveTimes++;
        return;
      }
      let values = this.formValidate;
      let params = {
        prod_type: this.$route.query.prod_type,
        remark: values.remark,
        type: this.isMonthInventory ? 'SPAN' : 'NOW',
        take_date: this.formValidate.create_time,
      };

      this.$route.query.id && (params.id = this.$route.query.id);
      const list = submit_data(this.localList, 'draft');
      params.items = list;
      if (this.id) {
        params.id = this.id;
      }
      if (this.$route.query.order_code) {
        params.order_code = this.$route.query.order_code;
      }
      if (this.$route.path === '/stock/inventory/detail') {
        this.$api.saveInventoryDraft(params).then(
          res => {
            console.log('-> res', res, '保存草稿啦');
          },
          err => {}
        );
      }
    }, 2000),
  },
};
