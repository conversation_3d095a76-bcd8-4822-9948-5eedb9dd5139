<template>
  <div ref="tableWrapRef" @mouseover="hidePop" style="margin-top: 0">
    <div class="block-header flex flex-item-align flex-item-between">
      <div>
        <el-popover placement="right" width="320" trigger="hover">
          <div class="tip-container" slot="default">
            <div class="title">盘点须知</div>
            <hr style="margin-bottom: 10px" />
            <p>1）盘点需要对应货品的入库批次，否则无法创建</p>
            <p>2）货品下无入库记录，不能参与盘点</p>
            <p>3）货品下的批次库存为0，该批次不能参与盘点</p>
            <p>4）如果盘亏不指定批次，按照先进先出的规则进行出</p>
            <p style="margin-left: 20px">库（如果某批次没有生产时间或过期时间，优先出</p>
            <p style="margin-left: 20px">该批次）</p>
            <p>5）如果盘盈不指定批次，则默认优先入库到最新批次</p>
            <p>6）当供应商处于异常状态时，只能盘亏不能盘盈</p>
            <p v-if="isMonthInventory">7）每种货品类型每天只能盘点一次</p>
          </div>
          <i style="cursor: pointer; color: #2a64a6" slot="reference" class="el-icon-question">
            <span style="color: #000; font-size: 12px; line-height: 14px">盘点须知</span>
          </i>
        </el-popover>
      </div>
      <div class="flex" v-if="!isDisabled">
        <a class="space6" @click="templateDownload" :loading="exportLoading">模板下载</a>
        <KExcelUpload
          :isStock="true"
          :excelUploadLoading="excelUploadLoading"
          btnText="批量导入"
          btnType="link"
          @excelUpload="excelUpload"
          :disabled="isDisabled"
        ></KExcelUpload>
      </div>
    </div>
    <el-table
      :data="list"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      size="small"
      stripe
      :loading="tableLoading"
      :height="$store.state.app.clientHeight - 380"
      :tree-props="{ children: 'choose_batch_list' }"
    >
      <el-table-column prop="parentIndex" label="序号" width="80">
        <template slot-scope="{ row }">
          {{ row.parentIndex || '' }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="item in INVENTORY_DETAIL_TABLE_COLS"
        :key="item.slot"
        :label="item.title"
        :prop="item.slot"
        :width="item.width"
        :align="item.align"
        :fixed="item.fixed"
      >
        <template slot-scope="{ row, column, index }">
          <!-- ppid用来判断是产品还是批次。如果是批次，ppid 等于产品id -->
          <template v-if="column.property === 'id'">
            {{ !row.ppid ? row.id : '' }}
          </template>
          <template v-else-if="column.property === 'type_text'">
            {{ row.type_text || '-' }}
          </template>
          <template v-else-if="column.property === 'container_number'">
            {{ row.container_number || '-' }}
          </template>
          <template v-else-if="column.property === 'supplier_name'">
            {{ row.ppid ? row.supplier_name : '-' }}
            <template v-if="!isEmpty(row.ppid)">
              <span
                v-if="
                  row.is_contain_prod_type === '0' ||
                  row.status === 'INVALID' ||
                  row.status === 'WAIT' ||
                  row.status === 'REJECT'
                "
                style="color: red"
                >(已失效)</span
              >
              <span v-if="row.is_contain_prod_type === '1' && row.status === 'OFF'" style="color: red">(已停用)</span>
            </template>
          </template>
          <template v-else-if="column.property === 'batch_id'">
            {{ row.ppid ? row.id : '-' }}
          </template>
          <template v-else-if="column.property === 'batch'">
            <!-- 如果ppid不存在， 则表示是产品
                 如果batch_list有批次数据，则可以选择，否则提示当前无可盘点批次
                 batchCount： 根据情况， 自定义的数据， 获取缓存/详情/选择批次时要手动处理
                 产品行：
                 可选时， 提示。
                 已选时 已选择批次(${已选批次.length})
                 不可选时： 当前无可盘点批次
                 -->
            <el-input
              v-if="!row.ppid && row.batch_list && row.batch_list.length > 0"
              placeholder="请选择批次"
              :disabled="isDisabled"
              readonly
              size="small"
              v-model="row.batchCount"
              @focus.stop="handleChangeBatchVisible(row)"
            >
              <el-button
                v-if="!isDisabled"
                style="width: 15px; display: flex; justify-content: center"
                :disabled="isDisabled"
                slot="append"
                icon="el-icon-plus"
                @click.stop="handleChangeBatchVisible(row)"
              ></el-button>
            </el-input>
            <template v-else-if="row.batch_list && row.batch_list.length === 0 && !isDisabled">
              <span style="color: red"> 当前无可盘点批次</span>
            </template>
            <template v-else> {{ row.batchCount }}</template>
          </template>
          <template v-else-if="column.property === 'produce_time'">
            {{ formatTime(row.produce_time) }}
          </template>
          <template v-else-if="item.slot === 'expire_time'">
            {{ formatTime(row.expire_time) }}
          </template>
          <template v-else-if="item.slot === 'goodsname'">
            <div class="flex-c" v-if="row.new_generic_name && !isDisabled">
              <product-show
                :generic_name="row.new_generic_name"
                :prod_spec="row.prod_spec"
                :grade_desc="row.grade_desc"
                :manufacturer="row.manufacturer"
                :insure_level_desc="row.insure_level_desc"
                :row="formatSelectedRow(row, { generic_name: row.new_generic_name })"
                @showPop="showPop"
              ></product-show>
              <span class="old-v"> 原商品名：{{ row.generic_name }}</span>
            </div>
            <div v-else>
              <product-show
                v-if="!row.ppid"
                :generic_name="row.generic_name"
                :prod_spec="row.prod_spec"
                :grade_desc="row.grade_desc"
                :manufacturer="row.manufacturer"
                :insure_level_desc="row.insure_level_desc"
                :row="formatSelectedRow(row)"
                @showPop="showPop"
              ></product-show>
            </div>
          </template>
          <!--  库存数量/账面库存，单位为 sales_units 里 checked === 1 的 unit-->
          <template v-else-if="item.slot === 'stock_num'">
            <template v-if="isInventoryRowEdit(row)">
              <div v-if="isShowOldStockNum(row)">
                <span>{{ row.stock_num ? row.stock_num + getUnit(row) : '-' }}</span>
                <span class="old-v"> 原库存：{{ row.old_stock_num ? row.old_stock_num : '-' }}</span>
              </div>
              <div v-else-if="isShowCurrentStockNum(row)">
                <span>{{ row.stock_num ? row.stock_num + getUnit(row) : '-' }}</span>
                <span class="old-v"> 当前库存：{{ row.current_stock_num ? row.current_stock_num : '-' }}</span>
              </div>
              <span v-else>{{ row.stock_num ? row.stock_num + getUnit(row) : '-' }}</span>
            </template>
            <template v-else> - </template>
          </template>
          <template v-else-if="item.slot === 'profit_num'">
            <template
              v-if="isInventoryRowEdit(row) && !isEmpty(row.after_stock_num) && isNumber(row.after_stock_num * 1)"
            >
              <span :style="`color: ${(row.after_stock_num - row.stock_num < 0 && 'red') || 'inherit'}`">
                {{ formatProfitNum(row) }}
              </span>
            </template>
            <template v-else> - </template>
          </template>
          <template v-else-if="item.slot === 'after_stock_num'">
            <!-- 如果是货品层，ppid不存在，则根据批次列表判断是否显示
                如果是批号层， 则直接显示 -->
            <template v-if="isInventoryRowEdit(row)">
              <el-input
                v-model="row.after_stock_num"
                :controls="false"
                class="afterStockNum"
                type="text"
                size="small"
                @change="val => afterStockNumChange(val, row)"
                style="width: 100px"
                :disabled="isDisabled"
                placeholder="盘后库存"
              >
                <span slot="suffix">{{ getUnit(row) }}</span>
              </el-input>
            </template>
            <template v-else>-</template>
          </template>
          <template v-else-if="item.slot === 'operation'">
            <Poptip confirm title="是否删除该条数据" :disabled="isDisabled" @on-ok="delListItemHandle(row, index)">
              <a :disabled="isDisabled">删除</a>
            </Poptip>
          </template>
          <template v-else> {{ row[column.property] }}</template>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex flex-item-between" style="margin-top: 10px">
      <div class="table-search" style="position: relative" v-if="!isDisabled">
        <goods-search
          style="width: 400px"
          ref="goods-search"
          :disabled="isDisabled"
          :isClearable="true"
          :params="{
            prod_type: prod_type,
            is_get_supplier: 1,
            sup_stock_date: isMonthInventory ? formValidate.create_time : '',
          }"
          placeholder="请搜索添加相关货品"
          @on-select="selectAddTableChangeHandle"
          v-model="selectAddTable"
        ></goods-search>
      </div>
      <div class="table-search" v-else></div>
      <Page
        :total="tableFormData.total"
        :page-size="tableFormData.pageSize"
        :current="tableFormData.page"
        :page-size-opts="[10, 20, 50, 80, 100]"
        @on-change="onPageChange"
        @on-page-size-change="onPageSizeChange"
        show-sizer
        show-elevator
        show-total
        transfer
        style="margin-top: 10px"
      >
      </Page>
    </div>
    <download-template
      :visible.sync="downloadVisible"
      :isMonthInventory="isMonthInventory"
      :sup_stock_date="formValidate.create_time"
      @getSupplierLen="
        sup => {
          hasSup = sup;
        }
      "
    />
    <!-- 查看错误报告 -->
    <error-report :visible.sync="reportVisible" :reportList="reportList" />
    <add-batch
      ref="addBatch"
      :visible.sync="detailVisible"
      :selected-row="selectedRow"
      @handleSaveSelected="handleSaveSelected"
    />

    <Tooltip
      ref="custom-tooltip"
      popper-class="generic-name-link-tip"
      transfer-class-name="generic-name-link-tip"
      :reference="tooltipReference"
      :delay="1000"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <generic-name-tip :row="selectedToolTipRow" />
      </template>
    </Tooltip>
  </div>
</template>

<script>
import S from '@/libs/util'; // Some commonly used tools
import KExcelUpload from './excel.vue';
import DownloadTemplate from './downloadTemplate.vue';
import { INVENTORY_DETAIL_TABLE_COLS } from '../util/constant';
import GoodsSearch from './GoodsSearch.vue';
import { isEmpty, isNumber } from '../../../../utils/helper';
import { formatProfitNum, formatTime, getUnit } from '../util/util';
import AddBatch from './addBatch.vue';
import draft_mixin from '../mixin/draft_mixin';
import { isArray  } from 'lodash-es';
import ErrorReport from './errorReport.vue';
import tooltip from '@/view/stock/mixins/tooltip';
import GenericNameTip from '@/view/stock/components/generic-name-tip.vue';

export default {
  components: { GenericNameTip, ErrorReport, AddBatch, GoodsSearch, DownloadTemplate, KExcelUpload },
  mixins: [draft_mixin, tooltip],
  props: {
    isDisabled: {
      type: Boolean,
      default: false,
    },
    isMonthInventory: {
      type: Boolean,
      default: false,
    },
    prod_type: {
      type: String,
    },
    formValidate: {
      type: Object,
      default: () => {},
    },
    detailData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    // 监听 返回的整个 详情数据
    detailData: {
      handler(newVal) {
        // 重新赋值， 保持单项数据流结构
        if (!isEmpty(newVal)) {
          this.products = newVal?.prods || {};
          this.localList =
            newVal?.items?.map((item, i) => {
              let batchCountName = '';
              if (!isEmpty(item.choose_batch_list)) {
                batchCountName = `已选择批次(${item.choose_batch_list.length})`;
              }
              if (
                isEmpty(item.choose_batch_list) &&
                ['PASS', 'WAIT', 'FORCE_REJECT', 'REJECT'].includes(newVal.status)
              ) {
                batchCountName = '未选择批次';
              }
              return {
                ...item,
                batch_list: item?.batch_list,
                batchCount: batchCountName,
                parentIndex: i + 1,
                after_stock_num: !isEmpty(item?.after_stock_num) ? item.after_stock_num : undefined,
                choose_batch_list: item.choose_batch_list?.map(o => ({
                  ...o,
                  ppid: item.id || item.prod_id,
                  batchCount: o.batch_code,
                  stock_num: o.stock_num || o.new_stock_num,
                })),
              };
            }) || [];
        }
      },
      deep: true,
      immediate: true,
    },
    // 监听整个表格的数据变动， 如果有变动，去获取分页的数据
    localList: {
      handler(val) {
        if (isArray(val)) {
          const { page: currentPage, pageSize } = this.tableFormData;
          const leftPageSize = (currentPage - 1) * pageSize;
          const rightPageSize = currentPage * pageSize;
          this.list = val?.filter((o, i) => i >= leftPageSize && i < rightPageSize);
          this.tableFormData.total = val?.length || 0;
        }
      },
      deep: true,
      immediate: true,
    },
    // 监听分页变化， 如果分页改变了， 从localList 重新获取表格数据
    tableFormData: {
      handler(val) {
        if (!isEmpty(val)) {
          const { page: currentPage, pageSize } = val;
          const leftPageSize = (currentPage - 1) * pageSize;
          const rightPageSize = currentPage * pageSize;
          this.list = this.localList?.filter((o, i) => i >= leftPageSize && i < rightPageSize);
        }
      },
      deep: true,
      immediate: true,
    },
    detailVisible: {
      handler(val) {
        !val && (this.selectedRow = {});
      },
    },
  },
  computed: {
    formatSelectedRow() {
      return (row, names = {}) => {
        const prod = Object.values(this.products)?.find(item => item.id === row.id) || {};
        return { ...prod, ...row, ...names };
      };
    },
  },
  data() {
    return {
      isNumber,
      formatTime,
      formatProfitNum,
      isEmpty,
      getUnit,
      INVENTORY_DETAIL_TABLE_COLS, // 表格列
      detailVisible: false, // 选择批次
      exportLoading: false, // 模板下载loading
      excelUploadLoading: false, // 数据上传的loading
      downloadVisible: false, // 模板下载弹窗
      reportVisible: false, // 错误报告弹窗标识
      reportList: [], // 错误报告列表
      hasSup: false, // 是否有供应商
      tableLoading: false, // 表格loading
      tableFormData: {
        page: 1,
        pageSize: 20,
        total: 0,
      },
      localList: [], // 旧的表格数据
      list: [], // 新的表格数据
      selectAddTable: '',
      selectedRow: {}, // 点击批次时被选中行数据
      products: {},
    };
  },
  methods: {
    // 模板下载
    templateDownload() {
      if (!this.hasSup) {
        this.$Message.error(`当前品类（${this.prodTypeText}）商品下没有可用供应商，暂不支持批量盘点`);
        return false;
      }
      this.downloadVisible = true;
      // this.exportLoading = true
    },
    // 获取获取excel处理后的数据
    excelUpload(hasHandleExcelList) {
      // let limitExcelList = hasHandleExcelList
      if (!hasHandleExcelList.length) {
        this.$Message.error('请至少导入一条有效数据');
        return;
      }
      if (S.mathAdd(hasHandleExcelList.length, this.localList.length) > 1000) {
        this.$Message.error('单次盘点总数据不能超过1000条');
        return;
      }
      // 存在重复商品
      let isRepeatGoods = this.localList.filter(item =>
        hasHandleExcelList?.map(o => o.id.toString()).includes(item.id.toString())
      );

      if (!isEmpty(isRepeatGoods)) {
        this.$Modal.warning({
          title: '温馨提示',
          content: `当前导入数据存在重复商品：${isRepeatGoods?.[0]?.generic_name || ''} 。请修改后提交！`,
        });
        return;
      }
      this.saveExcel(hasHandleExcelList);
    },
    // api-上传处理后的excel数据
    saveExcel(hasHandleExcelList) {
      this.excelUploadLoading = true;
      this.$api
        .importInventoryDetection({
          batch_params: hasHandleExcelList,
          prod_type: this.$route.query.prod_type,
          sup_stock_date: this.isMonthInventory ? this.formValidate.create_time : '',
        })
        .then(res => {
          // this.$Message.success('导入成功')
          this.fail_num = res.fail_num;
          this.reportList = res.fail_data;
          if (this.reportList.length) {
            this.reportVisible = true;
          }
          // TODO
          if (res.succ_data.length) {
            // 同一个商品下的批次不能重复
            let totalList = res.succ_data;
            let hasRepeatBatch = false;
            let goodsName = '';
            for (let i = 0; i < totalList.length; i++) {
              const blArr = totalList[i].choose_batch_list;
              hasRepeatBatch = new Set(blArr.map(item => item['id'])).size !== blArr.length;
              goodsName = totalList?.[i].generic_name;
              if (hasRepeatBatch) break;
            }
            if (hasRepeatBatch) {
              this.$Modal.warning({
                title: '温馨提示',
                content: `导入数据中，商品：${goodsName} 下存在重复批次！请修改后提交！`,
              });
              return;
            }
            this.handleUploadSuccessData(res.succ_data);
          }
        })
        .finally(() => {
          this.excelUploadLoading = false;
        });
    },
    // 将导入成功的数据并入
    handleUploadSuccessData(succ_data) {
      const resList = succ_data.map(item => {
        item.retailPrice = item.is_split === '1' ? item.split_price : item.retail_price;
        item.unit = item.unit || item.is_split === '1' ? item.split_prod_unit : item.prod_unit;
        item.profit_num = item.after_stock_num - item.stock_num; // 盈亏数量
        return {
          id: item.id, // 商品编码
          generic_name: item.generic_name, // 商品名称
          prod_spec: item.prod_spec,
          grade_desc: item.grade_desc,
          manufacturer: item.manufacturer,
          unit: item.unit, // 单位
          retailPrice: item.retailPrice, // 零售价
          from_text: item.from_text || '-', // 商品类型
          prod_stock_id: item.prod_stock_id, // 商品库存id
          after_stock_num: item.after_stock_num, // 盘后库存
          profit_num: item.profit_num, // 盈亏数量
          stock_num: Number(item.stock_num), // 库存数量
          profit_price: S.mathMul(Math.abs(item.profit_num), Number(item.retailPrice)), // 盈亏金额
          action_type: Number(item.after_stock_num) > Number(item.stock_num) ? 'ADD' : 'SUB',
          supplier_id: item.supplier_id,
          batch_list: item.batch_list,
          choose_batch_list: item.choose_batch_list?.map(a => {
            const obj = {
              ...item.batch_list?.find(b => b.id === a.id),
              ...a,
              ppid: item.id,
            };
            delete obj.goodsname;
            delete obj.from_text;
            delete obj.generic_name;
            return obj;
          }),
        };
      });

      this.localList = [...resList, ...this.localList]?.map((item, i) => ({
        ...item,
        batch_list: item?.batch_list,
        batchCount: !isEmpty(item.choose_batch_list) ? `已选择批次(${item.choose_batch_list.length})` : '',
        parentIndex: i + 1,
        after_stock_num: !isEmpty(item?.after_stock_num) ? item.after_stock_num : undefined,
        choose_batch_list: item.choose_batch_list?.map(o => ({
          ...o,
          ppid: item.id || item.prod_id,
          batchCount: o.batch_code,
          stock_num: o.stock_num || o.after_stock_num,
        })),
      }));
      this.tableFormData.total = this.localList.length;
    },
    onPageChange(page) {
      this.tableFormData.page = page;
    },
    onPageSizeChange(pageSize) {
      this.tableFormData.pageSize = pageSize;
      this.tableFormData.page = 1;
    },
    // 修改select
    selectAddTableChangeHandle(value, item = {}) {
      if (!value) {
        return;
      }
      if (!isEmpty(this.localList.find(o => o.id === item.id))) {
        this.$Message.error('不能重复选货品');
      } else if (this.localList.length >= 500) {
        this.$Message.error('最多只能添加500条商品');
      } else {
        this.localList.unshift(item);
        this.localList = this.localList.map((item, i) => ({ ...item, parentIndex: i + 1 }));
        this.tableFormData.page = 1;
      }
      this.$nextTick(() => {
        this.$refs['goods-search'].clear();
        this.selectAddTable = '';
        this.inputKey = this.inputKey + 1;
      });
    },
    handleChangeBatchVisible(row) {
      this.detailVisible = true;
      this.selectedRow = row;
    },
    handleSaveSelected(list, row) {
      const childrens = this.list.find(item => item.id === row.id)?.choose_batch_list || [];
      // 判断是新增还是删除， 如果是删除， 需要移出对应数据 拿到移出后的数据
      const removeAfterList = childrens.filter(item => list?.map(o => o.id).includes(item.id));
      // 此时剩下的数据是model框里面选中的， 所以需要把对应的数据赋值给list
      list = list.map(item => {
        const obj = item;
        const oldObj = removeAfterList.find(o => o.id === item.id);
        return {
          ...obj,
          ...oldObj,
          batchCount: item.batch_code,
          ppid: row.id, // 设置一个ppid， 表示是children数据
        };
      });
      this.localList = this.localList.map(item => {
        if (item.id === row.id) {
          return {
            ...item,
            choose_batch_list: list,
            batchCount: list && list.length > 0 ? `已选择批次(${list.length})` : undefined,
          };
        }
        return item;
      });
    },
    delListItemHandle(row) {
      // 表示删除外层数据
      if (isEmpty(row.ppid)) {
        this.localList = this.localList.filter(item => item.id !== row.id);
        return;
      }
      // 删除内层数据
      this.localList = this.localList.map(item => {
        if (item.id === row.ppid) {
          const list = item.choose_batch_list?.filter(o => o.id !== row.id);
          return {
            ...item,
            choose_batch_list: list,
            batchCount: list.length === 0 ? undefined : `已选择批次(${list.length})`,
          };
        }
        return item;
      });
    },

    // 是否是可编辑盘点行，控制编辑和展示库存信息
    isInventoryRowEdit(row) {
      // 如果是产品层，有可选批次，批次未选择， 可以编辑盘点库存
      // 如果是产品层， 无可选批次， 直接不能编辑
      // 如果是产品层，有可选批次，并且批次已选择，不可以编辑
      // 如果是批号行，都可以编辑
      // 以 ppid 是否存在盘点， 如果存在则是批次层， 否则就是产品层
      if (this.isDisabled && isEmpty(row.ppid) && isEmpty(row.choose_batch_list)) return true;
      if (!isEmpty(row.ppid)) return true;
      // 产品层逻辑
      // 如果没有可选批次， 不能编辑，不展示库存信息
      if (this.detailData.status !== 'PASS' && (isEmpty(row.batch_list) || row.batch_list?.length <= 0)) return false;
      if (
        this.detailData.status === 'PASS' &&
        (isEmpty(row.batch_list) || row.batch_list?.length <= 0) &&
        row.choose_batch_list.length <= 0
      )
        return true;
      // 如果没有可选批次， 但是已经选择批次了， 不展示
      if (
        this.detailData.status === 'PASS' &&
        (isEmpty(row.batch_list) || row.batch_list?.length <= 0) &&
        row.choose_batch_list.length > 0
      )
        return false;
      // 如果有可选批次， 但是没有选择批次， 可以编辑
      if (isEmpty(row?.choose_batch_list) || row?.choose_batch_list?.length <= 0) return true;
      return false;
    },

    // 是否显示原库存
    isShowOldStockNum(row) {
      // 原库存只在点击修改时进入的页面展示
      const { old_stock_num, stock_num } = row;
      const old_stock_num_temp = Number(old_stock_num);
      const stock_num_temp = Number(stock_num);
      if (isEmpty(old_stock_num_temp) || !isNumber(old_stock_num_temp)) return false;
      if (stock_num_temp === old_stock_num_temp) return false;
      const { status } = this.detailData;
      if (['REJECT', 'FORCE_REJECT'].some(key => key === status)) return true;
    },
    // 是否显示当前库存
    isShowCurrentStockNum(row) {
      const { status } = this.detailData;
      const { current_stock_num, stock_num } = row;
      if (isEmpty(current_stock_num)) return false;
      const current_stock_num_temp = Number(current_stock_num);
      const stock_num_temp = Number(stock_num);
      if (!isNumber(current_stock_num_temp)) return false;
      if (current_stock_num_temp === stock_num_temp) return false;
      if (['WAIT'].includes(status)) return true;
    },
    // 由于输入框后面要跟单位，所以不能用type = number 框。
    // 所以针对非数字和小数， 范围 手动处理, 只接受大于0的正整数
    // 直接改row.after_stock_num 是利用双向绑定，而且这里是浅拷贝。
    // 所以表格这里的数据，一定要保证能被vue劫持到，否则不会触发视图更新
    afterStockNumChange(val, row) {
      const number = Number(val);
      // 非数字
      if (!isNumber(number)) {
        row.after_stock_num = 0;
        return;
      }
      // 小于0
      if (number < 0) {
        row.after_stock_num = 0;
        return;
      }
      // 浮点数
      if (number % 1 !== 0) {
        row.after_stock_num = Number.parseInt(number);
        return;
      }
      // 大于1亿
      if (number > 99999999) {
        this.$Message.error('盘后库存不能大于1亿！');
        row.after_stock_num = 0;
      }
    },
  },
};
</script>
<style scoped lang="less">
.afterStockNum /deep/ .el-input__suffix {
  margin-top: 4px;
}

.old-v {
  display: block;
  font-size: 12px;
  color: red;
}
.tip-container p {
  visibility: visible;
  font-size: 12px;
  line-height: 1.5;
  color: #333;
  padding: 0;
  margin: 0;
}
.tip-container .title {
  padding-bottom: 5px;
}
</style>
<style lang="less">
.generic-name-link-tip {
  .ivu-tooltip-inner {
    max-width: unset;
    background-color: #fffdec;
  }
}
</style>
