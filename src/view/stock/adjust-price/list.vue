<template>
  <div class="adjust-price" ref="tableWrapRef" @mouseover="hidePop">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      size="small"
      stripe
      class="pl-table"
      ref="sort-table"
      @on-sort-change="sortChanged"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" class="mr10" @click="importEvent">导入零售价</Button>
          <Button type="primary" class="mr10" :loading="editLoading" @click="modifyRetailPurchaseRatio">
            调整零售价异常的货品
          </Button>
          <Button type="primary" class="mr10" @click="modifyRePurRatio">修改调价的偏差区间</Button>
        </div>
        <div class="form-warpper">
          <Form class="form-warpper_left" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem>
              <Input type="text" v-model="queryFormData.name" placeholder="货品名称" />
            </FormItem>
            <FormItem>
              <!-- <Select v-model="queryFormData.status" style="" placeholder="全部状态"> -->
              <!--   <Option value="">全部状态</Option> -->
              <!--   <Option :value="index" v-for="(item, index) in prodStatusDesc" :key="index">{{ item.desc }}</Option> -->
              <!-- </Select> -->

              <Select v-model="queryFormData.price_status" style="" placeholder="全部零售价状态">
                <Option value="">全部零售价状态</Option>
                <Option :value="index" v-for="(item, index) in priceTypes" :key="index">{{ item.kw }}</Option>
              </Select>
            </FormItem>
            <!--        <FormItem>-->
            <!--          <Select v-model="queryFormData.prod_type" placeholder="全部类型">-->
            <!--            <Option value="">全部类型</Option>-->
            <!--            <Option :value="index" v-for="(item,index) in prodTypes" :key="index">{{ item.name }}</Option>-->
            <!--          </Select>-->
            <!--        </FormItem>-->
            <FormItem>
              <Select v-model="queryFormData.has_stock" placeholder="全部库存状态">
                <Option value="0">全部库存状态</Option>
                <Option value="1">仅显示有库存的商品</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.prod_type" placeholder="全部类型">
                <Option value="">全部类型</Option>
                <Option :value="index" v-for="(item, index) in adjustProdTypeDesc" :key="index">{{ item.name }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="initSearch"
                ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
              >
            </FormItem>
          </Form>
        </div>
      </template>

      <template slot-scope="{ row }" slot="prod_id">
        {{ row.id }}
      </template>

      <template slot-scope="{ row }" slot="generic_name">
        <product-show
          :generic_name="row.generic_name"
          :prod_spec="row.prod_spec"
          :grade_desc="row.grade_desc"
          :manufacturer="row.manufacturer"
          :row="row"
          @showPop="showPop"
        ></product-show>
      </template>
      <!--      规格-->
      <template slot-scope="{ row }" slot="prod_spec">
        <Tooltip :content="row.prod_spec" max-width="300">
          {{ row.prod_spec || '-' }}
        </Tooltip>
      </template>
      <!--      库存-->
      <template slot-scope="{ row }" slot="stock_num">
        {{ row.stock_text || '-' }}
      </template>
      <!--      来源-->
      <template slot-scope="{ row }" slot="from_text">
        {{ row.from_text || '-' }}
      </template>
      <!--      总价值-->
      <template slot-scope="{ row }" slot="total_money">
        {{ row.total_money ? '¥ ' + row.total_money : '-' }}
      </template>
      <!--      单位-->
      <template slot-scope="{ row }" slot="prod_unit">
        {{ row.prod_unit || '-' }}
      </template>
      <!--      类型-->
      <template slot-scope="{ row }" slot="prod_type">
        <Tooltip :content="row.prod_type_text" :disabled="row.prod_type_text?.length < 5">
          <span class="ecs" :class="{ cursor: row.prod_type_text?.length > 4 }">{{ row.prod_type_text || '-' }}</span>
        </Tooltip>
      </template>
      <!--      生产厂家-->
      <!-- <template slot-scope="{ row }" slot="zx_update_time"> -->
      <!--   {{ row.zx_update_time | data_format }} -->
      <!-- </template> -->
      <!--      生产厂家-->
      <!-- <template slot-scope="{ row }" slot="zx_pur_price"> -->
      <!--   {{ row.zx_pur_price ? '￥' + row.zx_pur_price_text : '-' }} -->
      <!-- </template> -->
      <!--      状态-->
      <template slot-scope="{ row }" slot="status">
        {{ prodStatusDesc[row.status].desc }}
      </template>
      <!--      零售价-->
      <template slot-scope="{ row, index }" slot="retail_price">
        <InputNumber
          :active-change="false"
          :min="0"
          @on-change="val => changeRetailPrice(val, index)"
          @on-focus="e => removeZero(e, index, 'retail_price')"
          @keyup.enter.native="editItem(row, index)"
          v-if="list[index].isEditMode"
          v-model="list[index].retail_price"
          placeholder="零售价"
          :formatter="e => formatPrice(e, index)"
          :parser="e => parsePrice(e, index)"
        />
        <!-- <span v-else> ¥ {{ row.retail_price }} / {{ row.prod_unit }}</span> -->
        <span v-else>
          <Tooltip>
            <span>¥ {{ getNumFix(row.retail_price) }}</span>
            <div slot="content">
              <div>¥ {{ getNumFix(row.retail_price) }} / {{ row.prod_unit }}</div>
            </div>
          </Tooltip>
        </span>
      </template>
      <template slot-scope="{ row }" slot="insure_level_desc">
        {{ row.insure_level_desc || '-' }}
      </template>
      <!--      拆零比-->
      <template slot-scope="{ row }" slot="split_num">
        {{ row.is_split === '1' && row.split_num && row.split_num !== '0' ? Number(row.split_num) : '-' }}
      </template>
      <!--      拆零单位-->
      <template slot-scope="{ row }" slot="split_unit">
        {{ row.split_prod_unit || '-' }}
      </template>
      <!--              拆零价-->
      <template slot-scope="{ row, index }" slot="split_price">
        <InputNumber
          v-if="list[index].isEditMode && list[index].is_split === '1'"
          v-model="list[index].split_price"
          @on-focus="e => removeZero(e, index, 'split_price')"
          @on-change="val => changeSplitPrice(val, index)"
          placeholder="拆零价"
          @keyup.enter.native="editItem(row, index)"
          :active-change="false"
          :precision="4"
          :min="0"
        />
        <span v-else>
          <span v-if="row.split_price">
            <Tooltip>
              <span>¥ {{ getNumFix(row.split_price, 4) }}</span>
              <div slot="content">
                <div>¥ {{ getNumFix(row.split_price, 4) }} / {{ row.split_prod_unit }}</div>
              </div>
            </Tooltip>
          </span>
          <span v-else>-</span>
          <!-- <span v-if="row.split_price"> -->
          <!--   {{ row.split_price !== '0' ? '¥ ' + row.split_price : '-' }} / -->
          <!--   {{ row.split_prod_unit }} -->
          <!-- </span> -->
          <!-- <span v-else>-</span> -->
        </span>
      </template>
      <!--      上次采购价-->
      <template slot-scope="{ row }" slot="last_purchase_price">
        {{ row.last_purchase_info.price ? '¥ ' + row.last_purchase_info.price : '-' }}
      </template>
      <!--      采购零售比-->
      <template slot-scope="{ row }" slot="zx_pur_price">
        <div v-if="row.zx_pur_price">
          <Tooltip>
            <span style="text-align: center">¥{{ getNumFix(row.zx_pur_price) }}</span>
            <div slot="content">
              <div>¥ {{ getNumFix(row.zx_pur_price) }} / {{ row.prod_unit }}</div>
              <div style="color: #aaaaaa">最近更新：{{ row.zx_update_time | data_format }}</div>
            </div>
          </Tooltip>
        </div>
        <span v-else>-</span>
      </template>
      <!--      采购零售比-->
      <template slot-scope="{ row }" slot="recent_purchase_price">
        <div v-if="row.recent_purchase_price">
          <Tooltip>
            <span style="text-align: center">¥{{ getNumFix(row.recent_purchase_price) }}</span>
            <div slot="content">
              <div>¥ {{ getNumFix(row.recent_purchase_price) }} / {{ row.prod_unit }}</div>
              <div style="color: #aaaaaa">最近更新：{{ row.recent_update_time | data_format }}</div>
            </div>
          </Tooltip>
        </div>
        <span v-else>-</span>
      </template>
      <!--     零售价状态-->
      <template slot-scope="{ row }" slot="retail_price_status">
        <div style="text-align: center">
          <div v-if="typeof row.ratio === 'number'">
            <div
              v-if="Number(row.ratio) < getMinRatio()"
              style="display: flex; justify-content: center; align-items: center"
            >
              <span style="color: green">偏低</span>
              <Icon type="ios-arrow-round-down" style="font-size: 20px; color: green; font-weight: bold" />
            </div>
            <div
              v-else-if="Number(row.ratio) > getMaxRatio()"
              style="display: flex; justify-content: center; align-items: center"
            >
              <span style="color: red">偏高</span>
              <Icon
                type="ios-arrow-round-up"
                style="font-size: 20px; line-height: normal; color: red; font-weight: bold"
              />
            </div>
            <div v-else>正常</div>
          </div>
          <span v-else>-</span>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="operate">
        <Poptip
          popper-class="adjust-price"
          confirm
          :disabled="saveDisabled"
          :title="saveContent"
          @on-ok="editItem(row, index)"
          cancel-text="修改"
          ok-text="继续保存"
          v-if="row.isEditMode"
        >
          <!-- <a style="margin-right: 6px" @click="editItem(row, index)">{{ row.isEditMode ? '保存' : '修改' }}</a> -->
          <!-- <a style="margin-right: 6px" @click="editItem(row, index)">{{ row.isEditMode ? '保存' : '修改' }}</a> -->
          <Button
            size="small"
            type="primary"
            style="margin-right: 6px; line-height: 1.9"
            @click="beforeSaveClick(row, index)"
            >{{ '保存' }}</Button
          >
          <!-- <a>Delete</a> -->
        </Poptip>
        <!-- <a v-if="row.isEditMode" style="margin-right: 6px" @click="editItem(row, index)">{{ '保存' }}</a> -->

        <a v-else style="margin-right: 6px" @click="editItem(row, index)">{{ '修改' }}</a>

        <router-link :to="{ path: '/stock/product/edit', query: { id: row.id } }" class="space6">详情</router-link>
        <a style="margin-right: 12px" @click="priceHistory(row, index)">调价历史</a>
      </template>
    </standard-table>
    <Modal
      v-model="historyVisible"
      :mask-closable="false"
      :title="`调价历史 - ${historyProdName}`"
      @on-ok="historyConfirm"
      @on-cancel="historyCancel"
      width="1200"
      class-name="price-history-modal"
    >
      <Table
        :loading="historyTableLoading"
        :columns="historyColumns"
        :data="historyList"
        size="small"
        stripe
        class="pl-table"
        ref="history"
        height="400"
      >
        <template slot-scope="{ row }" slot="before_retail_price">
          <span v-if="row.before_retail_price">
            <Tooltip>
              <span>¥ {{ row.before_retail_price }}</span>
              <div slot="content">
                <div>{{ row.before_retail_price_text }}</div>
              </div>
            </Tooltip>
          </span>
          <span v-else>-</span>
        </template>
        <template slot-scope="{ row }" slot="after_retail_price">
          <span v-if="row.after_retail_price">
            <Tooltip>
              <span>¥ {{ row.after_retail_price }}</span>
              <div slot="content">
                <div>{{ row.after_retail_price_text }}</div>
              </div>
            </Tooltip>
          </span>
          <span v-else>-</span>
          <!-- <span>{{ row.after_retail_price || '-' }}</span> -->
        </template>
        <template slot-scope="{ row }" slot="before_split_price">
          <span v-if="row.before_split_price">
            <Tooltip>
              <span>¥ {{ row.before_split_price }}</span>
              <div slot="content">
                <div>{{ row.before_split_price_text }}</div>
              </div>
            </Tooltip>
          </span>
          <span v-else>-</span>
          <!-- <span>{{ row.before_split_price || '-' }}</span> -->
        </template>
        <template slot-scope="{ row }" slot="after_split_price">
          <span v-if="row.after_split_price">
            <Tooltip>
              <span>¥ {{ row.after_split_price }}</span>
              <div slot="content">
                <div>{{ row.after_split_price_text }}</div>
              </div>
            </Tooltip>
          </span>
          <span v-else>-</span>
          <!-- <span>{{ row.after_split_price || '-' }}</span> -->
        </template>
        <template slot-scope="{ row }" slot="remark">
          <span>{{ row.remark || '-' }}</span>
        </template>

        <!--      单位-->
        <template slot-scope="{ row }" slot="prod_unit">
          {{ row.prod_unit || '-' }}
        </template>
        <!--      拆零单位-->
        <template slot-scope="{ row }" slot="split_unit">
          {{ row.split_prod_unit || '-' }}
        </template>

        <template slot-scope="{ row }" slot="actionTime">
          {{ row.create_time | data_format }}
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="historyTotal"
        :page-size.sync="historyForm.pageSize"
        :current.sync="historyForm.page"
        @on-change="historyOnPageChange"
        style="text-align: right"
      />
    </Modal>
    <!-- 导入商品销售价-excel -->
    <Modal
      v-model="importVisible"
      :mask-closable="false"
      title="导入零售价"
      @on-ok="importConfirm"
      @on-cancel="importCancel"
    >
      <div class="import-content" v-if="importVisible">
        <div class="flex flex-item-align">
          <p>导入表格：</p>
          <KExcelUpload
            btnType="text"
            :excelUploadLoading="excelUploadLoading"
            btnText="选择表格"
            @excelUpload="excelUpload"
            :isSmart="true"
          ></KExcelUpload>
          <p class="download cursor hover" @click="templateDownload">下载导入模板</p>
        </div>
        <!-- btn -->
        <div class="flex flex-item-center" style="margin-top: 40px">
          <Button type="default" class="space6" @click="importCancel">取消</Button>
          <Button
            type="primary"
            class="spacce6"
            @click="importConfirm"
            :disabled="isActiveBtn"
            :loading="excelUploadLoading"
            >导入
          </Button>
        </div>
        <!-- 错误报告 -->
        <div class="error-report flex flex-item-center" v-show="hasHandleExcelList.length && isImportSuccess">
          <p class="">本次成功导入{{ succ_num }}条记录，{{ fail_num }}条错误记录</p>
          <p class="download-error cursor hover" v-show="Number(fail_num) > 0" @click="seeReport">查看错误报告</p>
        </div>
      </div>
    </Modal>
    <!--     查看错误报告 -->
    <Modal v-model="reportVisible" :mask-closable="false" width="800" title="错误报告">
      <div class="report-content">
        <Table :columns="reportColumn" :data="reportList" height="400"></Table>
      </div>
    </Modal>
    <adjust-modal
      :show-modal.sync="adjustVisible"
      @updateList="onSearch"
      :getMaxRatio="getMaxRatio"
      :getMinRatio="getMinRatio"
      :ratioObj="ratioObj"
      :remind-drugs="exceptionsPriceList"
      :adjustAndSave="adjustAndSave"
    ></adjust-modal>
    <adjust-ratio-modal :visible.sync="modifyRatioVisible" @fresh="refreshList"></adjust-ratio-modal>
    <Tooltip
      ref="custom-tooltip"
      popper-class="generic-name-link-tip"
      transfer-class-name="generic-name-link-tip"
      :reference="tooltipReference"
      :delay="1000"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <generic-name-tip :row="selectedToolTipRow" />
      </template>
    </Tooltip>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import KExcelUpload from '@/components/k-excel-upload/excel';
import AdjustModal from './components/AdjustModal';
/* eslint-disable */
import downloadExceL from '@/mixins/downloadExcel';
import search from '@/mixins/search';
// import AdjustModal from './components/AdjustModal'
import { debounce } from 'lodash-es';
import { $operator } from '@/libs/operation';
import AdjustRatioModal from './components/AdjustRatioModal';
import renderHeader from '@/mixins/renderHeader';
import {getInsureStatus, isRstClinic} from '../../../libs/runtime';
import StandardTable from "@/components/StandardTable/index.vue";
import GenericNameTip from "@/view/stock/components/generic-name-tip.vue";
import tooltip from "@/view/stock/mixins/tooltip";
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status: '',
  // prod_type: '2',
  sort_field: '',
  sort: '',
  is_get_last_ostock: 1,
  prod_source: '9', //来源
  has_stock: '', //是否仅显示有库存的商品
  prod_type: '',
};
let init_history_form_data = {
  page: 1,
  pageSize: 20,
  prod_id: '',
};

export default {
  name: 'list',
  components: {
    GenericNameTip,
    StandardTable,
    KExcelUpload,
    AdjustModal,
    AdjustRatioModal,
  },
  mixins: [downloadExceL, search, renderHeader, tooltip],
  data() {
    const indexMethod = row => {
      let index = (this.historyForm.page - 1) * this.historyForm.pageSize + row._index + 1;
      return this.historyTotal - index + 1;
      // return 1;
    };
    return {
      queryFormData: { ...init_query_form_data },
      historyForm: { ...init_history_form_data },
      historyTotal: 0,
      apiName: 'getInStockProductList',
      historyColumns: [
        { title: '序号', indexMethod: indexMethod, type: 'index', width: 50 },
        { title: '调整前零售价', slot: 'before_retail_price' },
        { title: '调整后零售价', slot: 'after_retail_price' },
        { title: '零售单位', slot: 'prod_unit' },
        { title: '调整前拆零价', slot: 'before_split_price' },
        { title: '调整后拆零价', slot: 'after_split_price' },
        { title: '拆零单位', slot: 'split_unit' },
        { title: '调价方式', slot: 'remark' },
        { title: '操作人', key: 'operator_name' },
        { title: '手机号', key: 'mobile' },
        { title: '操作时间', align: 'center', slot: 'actionTime', minWidth: 40 },
      ],
      tableLoading: true,
      historyTableLoading: false,
      importVisible: false, // 导入商品销售价modal
      reportVisible: false,
      exportLoading: false, // 模板下载loading
      excelUploadLoading: false, // 数据上传的loading
      handleExcelList: [], // 处理后要上传的excel数据
      hasHandleExcelList: [], // 准备导入的数据
      isImportSuccess: false, // 是否导入成功
      fail_num: 0, // 错误数量
      succ_num: 0, // 成功数量
      reportList: [], // 错误报告list
      list: [],
      total: 0,
      prodTypes: {},
      priceTypes: {},
      prodSource: {},
      prodStatusDesc: {},
      adjustProdTypeDesc: {},
      reportColumn: [
        {
          title: '商品编号',
          key: 'id',
          align: 'center',
        },
        {
          title: '零售价',
          key: 'retail_price',
          align: 'center',
        },
        {
          title: '修改零售价',
          key: 'after_retail_price',
          align: 'center',
        },
        {
          title: '错误原因',
          key: 'fail_msg',
          align: 'center',
        },
      ],
      downloadApiName: 'getprodstockurl', // 导出接口名称
      abnormalItems: [], //采购价格与零售价比例异常
      remindVisible: false, //提醒弹窗
      adjustVisible: false, //价格调整弹窗
      editLoading: false,
      exceptionsPriceList: [], //需要调整价格列表
      modifyRatioVisible: false, //修改零售采购比弹窗
      historyVisible: false,
      historyList: [],
      // 偏高：修改后的${{name}} 零售价 ${{high}}, 是否继续保存？
      // 偏低：
      saveContent: 'test1234234234测试',
      saveDisabled: false,
      historyProdName: '',
      ratioObj: {
        ratio: '',
        offset: '',
      },
    };
  },
  created() {
    if (getInsureStatus()) {
      this.tableCols.splice(3, 0, { title: '医保标识', slot: 'insure_level_desc', minWidth: 80, align: 'center' });
    }
    this.getRatio();
    this.getStatusList().then(() => {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      this.submitQueryForm(true);
    });
  },
  computed: {
    tableCols() {
      if (isRstClinic()) {
        return [
          { title: '编码', slot: 'prod_id', width: 80, align: 'center' },
          { title: '货品', slot: 'generic_name', minWidth: 220, align: 'left' },
          { title: '类型', slot: 'prod_type', minWidth: 80, align: 'center' },
          { title: '来源', slot: 'from_text', minWidth: 80, align: 'center' },
          { title: '状态', slot: 'status', minWidth: 60, align: 'center' },
          { title: '单价', slot: 'retail_price', sortable: 'custom', minWidth: 90, align: 'center' },
          { title: '包装单位', slot: 'prod_unit', minWidth: 70, align: 'center' },
          { title: '拆零比', slot: 'split_num', minWidth: 80, sortable: 'custom', align: 'center' },
          { title: '单价（拆零）', slot: 'split_price', sortable: 'custom', minWidth: 120, align: 'center' },
          { title: '最小计价单位', slot: 'split_unit', minWidth: 120, align: 'center' },
          {
            title: '零售价状态',
            slot: 'retail_price_status',
            minWidth: 110,
            align: 'center',
            renderHeader: (h, params) => {
              return h(
                'div',
                {
                  style: {
                    display: 'flex',
                  },
                },
                [
                  h(
                    'Tooltip',
                    {
                      props: {
                        placement: 'bottom',
                        transfer: true,
                        transferClassName: 'table-head-tol',
                        maxWidth: 280,
                        width: 280,
                      },
                    },
                    [
                      h('Icon', {
                        props: {
                          type: 'md-help-circle',
                        },
                        style: {
                          marginRight: '4px',
                          fontSize: '16px',
                          cursor: 'pointer',
                        },
                      }),
                      h(
                        'div',
                        {
                          slot: 'content',
                          style: {
                            lineHeight: 1.5,
                            height: '140px',
                          },
                        },
                        [
                          h(
                            'div',
                            {
                              style: {
                                width: '100%',
                                wordBreak: 'break-all',
                                overflowWrap: 'break-word',
                              },
                            }
                            // '注：零售价状态仅针对【中药饮片】而言'
                          ),
                          h(
                            'div',
                            {
                              style: {
                                width: '100%',
                              },
                            },
                            `注：诊所货品的零售价与市场行情价得到的比例${
                              this.ratioObj.offset == 0
                                ? `=${this.getMinRatio()}时`
                                : `在${this.getMinRatio()}-${this.getMaxRatio()}之间`
                            } ，系统判定零售价为正常状态；`
                          ),
                          h(
                            'div',
                            {
                              style: {
                                width: '100%',
                              },
                            },
                            ` 比例<${this.getMinRatio()}时，系统判定售价偏低，按此价格售卖会有亏损风险；`
                          ),
                          h(
                            'div',
                            {
                              style: {},
                            },
                            `比例>${this.getMaxRatio()}时，系统判定售价偏高，销售溢价偏高会影响用户购买率`
                          ),
                          // h(
                          //   'div',
                          //   {},
                          //   `如果按照系统规则调整，会将问题商品的零售进货比调整为${this.ratioObj.ratio}，门店可在该基础上自行微调。`,
                          //   {
                          //     style: {
                          //       width: '100%'
                          //     }
                          //   }
                          // )
                        ]
                      ),
                    ]
                  ),
                  h('span', '零售价状态'),
                ]
              );
            },
          },
          {
            title: '市场行情价',
            sortable: 'custom',
            align: 'center',
            slot: 'zx_pur_price',
            // className: 'table-tool-h',
            minWidth: 120,
          },
          // {
          //   title: '银联参考采购价',
          //   slot: 'zx_pur_price',
          //   minWidth: 140,
          //   className: 'table-tool-h',
          //   sortable: 'custom',
          //   align: 'center',
          //   renderHeader: (h, params) =>
          //     this._renderHeader(h, params, '当不同供应商同时售卖同一个中药饮片时，会选择售卖价较低的作为参考标的')
          // },
          // { title: '采购价更新时间', slot: 'zx_update_time', minWidth: 135, sortable: 'custom', align: 'center' },
          { title: '当前库存', slot: 'stock_num', sortable: 'custom', minWidth: 100, align: 'center' },
          { title: '操作', slot: 'operate', width: 160, align: 'center', fixed: 'right' },
        ]
      }
      return [
        { title: '编码', slot: 'prod_id', width: 80, align: 'center' },
        { title: '货品', slot: 'generic_name', minWidth: 220, align: 'left' },
        { title: '类型', slot: 'prod_type', minWidth: 80, align: 'center' },
        { title: '来源', slot: 'from_text', minWidth: 80, align: 'center' },
        { title: '状态', slot: 'status', minWidth: 60, align: 'center' },
        { title: '零售价', slot: 'retail_price', sortable: 'custom', minWidth: 90, align: 'center' },
        { title: '零售单位', slot: 'prod_unit', minWidth: 70, align: 'center' },
        { title: '拆零比', slot: 'split_num', minWidth: 80, sortable: 'custom', align: 'center' },
        { title: '拆零价', slot: 'split_price', sortable: 'custom', minWidth: 90, align: 'center' },
        { title: '拆零单位', slot: 'split_unit', minWidth: 70, align: 'center' },
        {
          title: '零售价状态',
          slot: 'retail_price_status',
          minWidth: 110,
          align: 'center',
          renderHeader: (h, params) => {
            return h(
              'div',
              {
                style: {
                  display: 'flex',
                },
              },
              [
                h(
                  'Tooltip',
                  {
                    props: {
                      placement: 'bottom',
                      transfer: true,
                      transferClassName: 'table-head-tol',
                      maxWidth: 280,
                      width: 280,
                    },
                  },
                  [
                    h('Icon', {
                      props: {
                        type: 'md-help-circle',
                      },
                      style: {
                        marginRight: '4px',
                        fontSize: '16px',
                        cursor: 'pointer',
                      },
                    }),
                    h(
                      'div',
                      {
                        slot: 'content',
                        style: {
                          lineHeight: 1.5,
                          height: '140px',
                        },
                      },
                      [
                        h(
                          'div',
                          {
                            style: {
                              width: '100%',
                              wordBreak: 'break-all',
                              overflowWrap: 'break-word',
                            },
                          }
                          // '注：零售价状态仅针对【中药饮片】而言'
                        ),
                        h(
                          'div',
                          {
                            style: {
                              width: '100%',
                            },
                          },
                          `注：诊所货品的零售价与市场行情价得到的比例${
                            this.ratioObj.offset == 0
                              ? `=${this.getMinRatio()}时`
                              : `在${this.getMinRatio()}-${this.getMaxRatio()}之间`
                          } ，系统判定零售价为正常状态；`
                        ),
                        h(
                          'div',
                          {
                            style: {
                              width: '100%',
                            },
                          },
                          ` 比例<${this.getMinRatio()}时，系统判定售价偏低，按此价格售卖会有亏损风险；`
                        ),
                        h(
                          'div',
                          {
                            style: {},
                          },
                          `比例>${this.getMaxRatio()}时，系统判定售价偏高，销售溢价偏高会影响用户购买率`
                        ),
                        // h(
                        //   'div',
                        //   {},
                        //   `如果按照系统规则调整，会将问题商品的零售进货比调整为${this.ratioObj.ratio}，门店可在该基础上自行微调。`,
                        //   {
                        //     style: {
                        //       width: '100%'
                        //     }
                        //   }
                        // )
                      ]
                    ),
                  ]
                ),
                h('span', '零售价状态'),
              ]
            );
          },
        },
        {
          title: '市场行情价',
          sortable: 'custom',
          align: 'center',
          slot: 'zx_pur_price',
          // className: 'table-tool-h',
          minWidth: 120,
        },
        // {
        //   title: '银联参考采购价',
        //   slot: 'zx_pur_price',
        //   minWidth: 140,
        //   className: 'table-tool-h',
        //   sortable: 'custom',
        //   align: 'center',
        //   renderHeader: (h, params) =>
        //     this._renderHeader(h, params, '当不同供应商同时售卖同一个中药饮片时，会选择售卖价较低的作为参考标的')
        // },
        // { title: '采购价更新时间', slot: 'zx_update_time', minWidth: 135, sortable: 'custom', align: 'center' },
        { title: '当前库存', slot: 'stock_num', sortable: 'custom', minWidth: 100, align: 'center' },
        { title: '操作', slot: 'operate', width: 160, align: 'center', fixed: 'right' },
      ]
    },
    isActiveBtn() {
      if (this.hasHandleExcelList.length > 0 && !this.isImportSuccess) {
        return false;
      } else {
        return true;
      }
    },
    getMinRatio() {
      console.log(
        '-> %c $operator.subtract(this.ratioObj.ratio, this.ratioObj.offset)  === %o',
        'font-size: 15px;color: green;',
        $operator.subtract(this.ratioObj.ratio, this.ratioObj.offset)
      );
      return () => $operator.subtract(this.ratioObj.ratio, this.ratioObj.offset);
    },
    getMaxRatio() {
      return () => $operator.add(this.ratioObj.ratio, this.ratioObj.offset);
    },
    getNumFix() {
      return function (num, fixed = 2) {
        return Number(num).toFixed(fixed);
      };
    },
  },
  watch: {
    importVisible(val) {
      if (!val) {
        this.hasHandleExcelList = [];
        this.isImportSuccess = false;
      }
    },
    hasHandleExcelList(val) {
      if (val) {
        // 如果重新选择了表格,将参数置为false,视为重新上传
        this.isImportSuccess = false;
      }
    },
  },
  methods: {
    refreshList() {
      this.getRatio().then(() => {
        this.onSearch();
      });
    },
    getRatio() {
      const res = this.$api.getRetailPurchaseRatio();
      console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
      res.then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.ratioObj.ratio = +res.ratio;
        this.ratioObj.offset = +res.offset;
      });
      return res;
    },
    modifyRePurRatio() {
      this.modifyRatioVisible = true;
    },
    changeRetailPrice(val, index) {
      // console.log(val);
      if (!val || !this.list[index].split_num) {
        this.list[index].split_price = 0;
      } else {
        this.list[index].is_split === '1' &&
          (this.list[index].split_price = $operator.divide(
            this.list[index].retail_price,
            this.list[index].split_num,
            4
          ));
        this.list[index].zx_pur_price &&
          (this.list[index].ratio = $operator.divide(this.list[index].retail_price, this.list[index].zx_pur_price, 8));
        console.log('-> %c this.list[index].ratio  === %o ', 'font-size: 15px;color: green;', this.list[index].ratio);
      }
      console.log(this.list[index].retail_price);
    },
    formatPrice(val, index) {
      let reg = /([0-9]+\.[0-9]{2})[0-9]*/;
      let nums = val.toString().replace(reg, '$1');
      this.list[index].retail_price = Number(nums);
      return val;
    },
    parsePrice(val, index) {
      let reg = /([0-9]+\.[0-9]{2})[0-9]*/;
      let nums = val.toString().replace(reg, '$1');
      this.list[index].retail_price = Number(nums);
      return val;
    },
    changeSplitPrice(val, index) {},
    removeZero(e, index, type) {
      console.log('-> %c e  === %o ', 'font-size: 15px', e.target.value);
      if (!isNaN(Number(e.target.value)) && Number(e.target.value) === 0) {
        this.list[index][type] = null;
      }
    },
    editItem(row, index) {
      console.log('-> %c this.list[index]  === %o ', 'font-size: 15px;color: green;', this.list[index]);
      console.log('-> %c index  === %o ', 'font-size: 15px;color: green;', index);

      if (row.isEditMode) {
        const { retail_price, old_retail_price, old_split_price, split_price } = this.list[index];
        if (retail_price === old_retail_price && split_price === old_split_price) {
          this.list[index].isEditMode = false;
          this.list[index]._highlight = false;
        } else {
          this.editProductPrice(index);
        }
      } else {
        this.list[index].isEditMode = true;
        this.list[index]._highlight = true;
      }

      console.log(this.list[index]);
    },
    editProductPrice(index) {
      console.log(this.list[index]);
      let editItem = this.list[index];
      const adjust_price_list = [
        {
          prod_id: editItem.id,
          retail_price: editItem.retail_price,
          split_price: editItem.split_price,
        },
      ];

      this.$api.editProductPrice({ adjust_price_list }).then(
        res => {
          console.log('-> %c res  === %o ', 'font-size: 15px;color: green;', res);
          this.list[index].isEditMode = false;
          this.list[index]._highlight = false;
          this.$Message.success('调整价格成功');
          this.onSearch();
        },
        err => {}
      );
    },
    modifyRetailPurchaseRatio() {
      this.exceptionsPriceList = [];
      this.editLoading = true;
      this.getRatio().then(res => {
        this.$api
          .getExceptionsPriceList()
          .then(res => {
            console.log('-> %c res  === %o ', 'font-size: 15px', res);
            this.exceptionsPriceList = res.list;
            this.adjustVisible = true;
          })
          .finally(() => (this.editLoading = false));
      });
    },
    adjustAndSave() {
      this.submitForm();
    },
    handleSaveParams() {
      let values = this.formValidate;
      let params = {
        ostock_type: 10,
        prod_type: this.$route.query.prod_type,
        order_code: values.orderCode,
        supplier_id: values.supplier,
        operator_name: values.user,
        arrival_time: moment(values.arrivalTime).format('YYYY-MM-DD'),
        inspection_time: moment(values.inspectionTime).format('YYYY-MM-DD'),
        remark: values.remark,
      };
      let items = this.localList.map((item, i) => {
        return {
          prod_id: item.number,
          prod_stock_id: item.prod_stock_id,
          batch_code: item.batchNumber,
          purchase_stock_num: item.quantity,
          purchase_price: item.purchasePrice,
          produce_time: item.productionDate,
          expire_time: item.termOfValidity,
          invoice_code: item.invoiceNo,
          // manufacturer: item.manufactor,
          warehouse_unit: item.warehouse_unit || item.prod_unit,
        };
      });
      params.items = items;
      if (this.id) {
        params.id = this.id;
      }
      return params;
    },
    submitForm(isAdjust) {
      isAdjust && (this.remindVisible = false);
      this.subBtnLoading = true;
      this.$api
        .updateBatchGoodsPrice(this.handleSaveParams())
        .then(
          data => {
            this.$Message.success({
              content: '提交成功',
              onClose: () => {},
            });
            this.$router.replace('/stock/ostock/list');
          },
          error => {}
        )
        .finally(() => {
          this.subBtnLoading = false;
        });
    },
    sortChanged({ column: { slot }, order }) {
      console.log('-> %c order  === %o ', 'font-size: 15px', order);
      if (order === 'normal') {
        order = '';
      }
      if (slot) {
        this.queryFormData.sort_field = slot;
        this.queryFormData.sort = order;
        this.getsList();
      } else {
        this.$Message.error('无效排序字段');
      }
    },
    // 查看错误报告
    seeReport() {
      this.importVisible = false;
      this.reportVisible = true;
    },
    // 导入商品售价事件
    importEvent() {
      this.importVisible = true;
    },
    // 导入
    importConfirm() {
      this.saveExcel(this.hasHandleExcelList);
    },
    // 取消导入弹窗
    importCancel() {
      this.importVisible = false;
    },
    // 模板下载
    templateDownload() {
      this.exportLoading = true;
      const { name, prod_type, staus } = this.queryFormData;
      let params = {
        name,
        prod_type,
        staus,
        prod_source: 9,
        type: ' PROD', // 商品模板标识
      };
      this.$api
        .getClinicTemplateurl(params)
        .then(res => {
          this.download(res.url);
        })
        .finally(() => (this.exportLoading = false));
    },
    // 获取获取excel处理后的数据
    excelUpload(hasHandleExcelList) {
      this.hasHandleExcelList = hasHandleExcelList;
    },
    // api-上传处理后的excel数据
    saveExcel(hasHandleExcelList) {
      this.excelUploadLoading = true;
      // saveBatcheditprice
      this.$api
        .saveBatcheditprice({ batch_params: hasHandleExcelList, timeout: 60000 })
        .then(res => {
          // this.$Message.success('导入成功')
          this.isImportSuccess = true;
          this.succ_num = res.succ_num;
          this.fail_num = res.fail_num;
          this.reportList = res.fail_data;
          // 重新拉取页面数据
          this.getsList();
        })
        .finally(() => {
          this.excelUploadLoading = false;
        });
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    onChangeStatus: function (id, action) {
      io.post('clinic/product.product.status', { id: id, act: action })
        .then(() => {
          this.$Message.success('操作成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
    },
    getStatusList() {
      let res = io.get('/clinic/product.product.options');
      res
        .then(data => {
          // console.log(data,"data");
          this.prodTypes = data.prodTypes;
          this.prodStatusDesc = data.prodStatusDesc;
          this.prodSource = data.prodSource;
          this.priceTypes = data.priceStatusDesc;
          this.adjustProdTypeDesc = data.adjustProdTypeDesc;
        })
        .catch(error => {
          {
          }
        });
      return res;
    },
    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.total = data.total;
          data.list.map(item => {
            item.isEditMode = false;
            item.retail_price = Number(item.retail_price);
            item.old_retail_price = Number(item.retail_price);
            item.split_price = Number(item.split_price);
            item.old_split_price = Number(item.split_price);
            item.ratio = item.retail_purchase_ratio ? Number(item.retail_purchase_ratio) : '';
          });
          this.list = data.list;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },
    initSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.$refs['sort-table'].handleSort(0, 'normal');
      this.submitQueryForm();
    },
    priceHistory(row, index) {
      this.historyForm.prod_id = row.id;
      this.historyProdName = row.generic_name;
      this.historyVisible = true;
      this.getHistoryList();
    },
    getHistoryList() {
      this.historyTableLoading = true;
      this.$api.getHistoryList({ ...this.historyForm }).then(
        res => {
          this.historyTableLoading = false;
          this.historyList = res.list;
          this.historyTotal = res.total;
        },
        rej => {
          this.historyTableLoading = false;
        }
      );
    },
    historyCancel() {
      this.historyVisible = false;
      this.historyForm = { ...init_history_form_data };
    },
    historyConfirm() {
      console.log(234234);
    },
    historyOnPageChange(page, pageSize) {
      this.historyForm.page = page;
      this.historyForm.pageSize = pageSize;
      this.getHistoryList();
    },
    beforeSaveClick(row, index) {
      let hightText = '';
      let isShowToast = false;
      console.log(row);
      if (typeof row.ratio === 'number') {
        if (Number(row.ratio) < this.getMinRatio()) {
          hightText = '偏低';
          isShowToast = true;
        }
        if (Number(row.ratio) > this.getMaxRatio()) {
          hightText = '偏高';
          isShowToast = true;
        }
      }
      this.saveDisabled = !isShowToast;
      if (isShowToast) {
        let content = `修改后的【${row.prod_name}】零售价${hightText}, 是否继续保存？`;
        this.saveContent = content;
      } else {
        this.editItem(row, index);
      }
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.import-content {
  height: 160px;

  p {
    margin-bottom: 0px;
  }

  .choose {
    color: rgba(17, 87, 229, 0.5);
  }

  .download {
    margin-left: 20px;
    color: rgb(155, 141, 141);
    border-bottom: 1px solid #ccc;
  }

  .error-report {
    margin-top: 20px;

    .download-error {
      margin-left: 15px;
      color: rgba(17, 87, 229, 0.5);
    }
  }
}

.readText {
  color: #ccc;
}

.ml2 {
  margin-left: 2px;
}
</style>

<style lang="less" scoped>
.table-fun {
  margin: -10px 0 10px;
}

// common less
.cursor {
  cursor: pointer;
}

.hover {
  &:hover {
    color: #155bd4 !important;
  }
}

/deep/ .ivu-modal-footer {
  display: none;
}

.generic-name {
  color: #696969;
}

::v-deep .table-tool-h {
  .ivu-table-cell {
    display: flex !important;
    align-items: center;
    justify-content: center;
  }
}

::v-deep .ivu-table-row-highlight {
  td {
    //background-color: rgb(92, 205, 179)!important;
    background-color: #c6ead5 !important;
  }
}
::v-deep .ivu-poptip-footer {
  color: red;
  ::v-deep .ivu-btn {
    background-color: #fff;
  }
}
</style>
<style lang="less">
.ivu-tooltip-inner {
  white-space: normal !important;
}

.adjust-price {
  .ivu-poptip-footer {
    color: red;
    .ivu-btn {
      &:first-child {
        background-color: #155bd4;
        border-color: #155bd4;
        color: #fff;
      }
      &:last-child {
        background-color: #fff;
        border-color: #dcdee2;
        color: #333;
      }
    }
  }
}
.el-input__icon {
  line-height: 34px;
}

.price-history-modal {
  .ivu-modal-body {
    height: 500px;
  }
}
.generic-name-link-tip {
  .ivu-tooltip-inner {
    max-width: unset;
    background-color: #FFFDEC;
  }
}
</style>
