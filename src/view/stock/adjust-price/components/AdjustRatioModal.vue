<template>
  <Modal
    :value="visible"
    title="修改调价的偏差区间"
    @on-visible-change="changeVisibleHandle"
    width="980px"
    top="8vh"
    style="min-width: 720px"
    :mask-closable="false"
    footer-hide
    :loading="saveLoading"
  >
    <div class="content-wrapper">
      <Form :model="formData" label-colon class="flex">
        <FormItem label="调价标的" prop="retail_purchase_ratio" class="flex">
          <!-- <InputNumber -->
          <!--   :precision="2" -->
          <!--   :value="formData.retail_purchase_ratio" -->
          <!--   @on-change="changeRatioHandle" -->
          <!--   :active-change="false" -->
          <!-- ></InputNumber> -->
          <Input value="市场行情价" disabled />
        </FormItem>
        <FormItem label="偏差区间" prop="retail_purchase_offset" class="flex" style="margin-left: 20px">
          <InputNumber
            :precision="2"
            :max="0.5"
            v-model="formData.retail_purchase_offset"
            :min="0"
            @on-change="changeOffsetHandle"
          ></InputNumber>
        </FormItem>
        <FormItem label="" class="flex" style="margin-left: 20px">
          <Button @click="cancelModify">还原</Button>
        </FormItem>
        <FormItem label="" class="flex" style="margin-left: 10px">
          <Button type="primary" @click="confirmModify" :loading="confirmLoading">确认修改</Button>
        </FormItem>
        <span class="text">偏差区间的值只能在0-0.5之间</span>
      </Form>
      <div class="remind-text">
        诊所中药货品的零售价与市场行情价得到的比例<span v-if="formData.retail_purchase_offset != 0"
          >在<span class="ratio-text">{{ getRatioMin }}-{{ getRatioMax }}</span
          >之间</span
        ><span v-else
          ><span class="ratio-text">={{ getRatioMax }}</span
          >时</span
        >
        ，系统判定零售价为正常状态；比例<span class="ratio-text"><{{ getRatioMin }}</span
        >时， 系统判定售价偏低，按此价格售卖会有亏损风险；<br />比例<span class="ratio-text">>{{ getRatioMax }}</span
        >时，系统判定售价偏高，销售溢价偏高会影响用户购买率
      </div>

      <Table :columns="tableCols" :data="list" size="small" border stripe height="410">
        <template slot-scope="{ row, index }" slot="actionTime">
          {{ row.create_time | data_format }}
        </template>

        <template slot-scope="{ row, index }" slot="mobile">
          <span>
            {{ row.mobile || '-' }}
          </span>
        </template>
      </Table>
      <KPage
        :total="total"
        :page-size.sync="tableFormData.pageSize"
        :current.sync="tableFormData.page"
        @on-change="onPageChange"
        :show-sizer="false"
        style="text-align: right; margin-top: 20px"
      />
    </div>
    <div slot="footer"></div>
  </Modal>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import { $operator } from '@/libs/operation';

export default {
  name: 'AdjustRatioModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const indexMethod = row => {
      let index = (this.tableFormData.page - 1) * this.tableFormData.pageSize + row._index + 1;
      return this.total - index + 1;
    };
    return {
      loading: false,
      saveLoading: false,
      tableCols: [
        { title: '序号', type: 'index', indexMethod: indexMethod, width: 50 },
        { title: '调整前标的', key: 'before_standard', width: 120 },
        { title: '调整前系数', key: 'before_ratio', width: 80 },
        { title: '调整前区间', key: 'before_offset', width: 100 },
        { title: '调整后标的', key: 'after_standard', width: 120 },
        { title: '调整后系数', key: 'after_ratio', width: 80 },
        { title: '调整后区间', key: 'after_offset', width: 80 },
        { title: '操作人', key: 'operator_name', minWidth: 50 },
        { title: '手机号', slot: 'mobile', minWidth: 50 },
        { title: '操作时间', align: 'center', slot: 'actionTime', minWidth: 40 }
      ],
      btnLoading: false,
      dataSource: [],
      total: 0,
      submitLoading: false,
      tableFormData: {
        page: 1,
        pageSize: 20
      },
      list: [],
      formData: {
        retail_purchase_offset: 0,
        retail_purchase_ratio: 0
      },
      ratioObj: {
        min: 0,
        max: 0,
        ratio: 0,
        offset: 0
      },
      confirmLoading: false
    };
  },

  computed: {
    getRatioMin() {
      return $operator.subtract(this.formData.retail_purchase_ratio, this.formData.retail_purchase_offset);
    },
    getRatioMax() {
      return $operator.add(this.formData.retail_purchase_ratio, this.formData.retail_purchase_offset);
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initModal();
      }
    }
  },
  methods: {
    changeRatioHandle(val) {
      this.formData.retail_purchase_ratio = val || 0;
    },
    changeOffsetHandle(val) {
      this.formData.retail_purchase_offset = val || 0;
    },
    initModal() {
      this.tableFormData.page = 1;
      this.tableFormData.pageSize = 20;
      this.loadList();
      this.getRatio();
    },
    cancelModify() {
      this.formData.retail_purchase_ratio = this.ratioObj.ratio;
      this.formData.retail_purchase_offset = this.ratioObj.offset;
    },
    //确认修改
    confirmModify() {
      console.log('-> %c this.formData  === %o', 'font-size: 15px;color: green;', this.formData);
      if (this.formData.retail_purchase_ratio < 1) {
        this.$Message.error('零售采购比系数不能小于1');
        return;
      }
      this.confirmLoading = true;
      this.$api
        .editRetailPurchaseRatio(this.formData)
        .then(res => {
          this.$Message.success('修改成功');
          this.$emit('fresh');
          this.closeModal();
        })
        .catch(err => {
          {};
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    closeModal() {
      this.$emit('update:visible', false);
    },
    changeVisibleHandle(visible) {
      console.log('-> %c visible  === %o ', 'font-size: 15px', visible);
      console.log(this.formData.retail_purchase_offset);
      if (this.formData.retail_purchase_offset === 0) {
        this.formData.retail_purchase_offset = 0.01;
      }
      !visible && this.$emit('update:visible', false);
    },
    onPageChange(page, pageSize) {
      console.log('-> %c page,pageSize  === %o ', 'font-size: 15px', page, pageSize);
      this.tableFormData.page = page;
      this.tableFormData.pageSize = pageSize;
      this.loadList();
      this.tableScrollToTop();
    },
    tableScrollToTop() {
      this.$nextTick(() => {
        document.getElementsByClassName('ivu-table-body')[0].scroll(0, 0);
      });
    },
    loadList() {
      this.tableLoading = true;
      this.$api.getRetailPurchaseRatioLog(this.tableFormData).then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.tableLoading = false;
        this.list = res.list;
        this.total = +res.total;
      });
    },
    getRatio() {
      this.$api.getRetailPurchaseRatio().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.formData.retail_purchase_ratio = +res.ratio;
        this.formData.retail_purchase_offset = +res.offset;
        this.ratioObj.ratio = +res.ratio;
        this.ratioObj.offset = +res.offset;
        console.log(this.formData);
      });
    }
  }
};
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  height: 620px;
  overflow-y: auto;
}

::v-deep .ivu-modal-footer {
  display: block !important;
}

.remind-text {
  margin-bottom: 12px;
}

.text {
  margin-left: 10px;
  color: #999;
  margin-top: 17px;
}

.ratio-text {
  color: #e03e2d;
  font-size: 16px;
  font-weight: bold;
}
</style>
