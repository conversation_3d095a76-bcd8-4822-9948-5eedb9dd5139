<template>
  <div>
    <Poptip :disabled="isDisabledPop" style="width: 100%">
      <!--  正常使用录入溯源码    -->
      <div v-if="!isCheckTraceMode" class="trace_code_box" slot="content">
        <div
          v-for="(item, code_index) in row.trace_codes"
          :key="code_index"
          class="trace_item mb10"
          style="line-height: 16px"
        >
          <div>
            <span style="color: #ccc">{{ code_index + 1 }}. </span>
            <span>{{ formatTraceCode(list[index].trace_codes[code_index]) }}</span>
          </div>

          <Icon
            style="cursor: pointer"
            v-if="list[index].trace_codes[code_index] && !pageDisableCondition"
            class="ml10"
            @click="clearTraceCode(index, code_index)"
            type="md-close-circle"
            color="#999"
            size="18"
          />
        </div>
        <div style="height: 1px; width: 100%"></div>
      </div>
      <!--  核码模式下校验    -->
      <div v-else class="trace_code_box" slot="content">
        <div
          v-for="(item, code_index) in row.available_trace_codes"
          :key="code_index"
          class="trace_item mb10"
          style="line-height: 16px"
        >
          <div>
            <span style="color: #ccc">{{ code_index + 1 }}. </span>
            <span>{{ formatTraceCode(item) }}</span>
          </div>

          <Icon class="ml10" type="md-checkmark-circle" :color="isMatch(item) ? 'green' : '#ccc'" size="18" />
        </div>
        <div style="height: 1px; width: 100%"></div>
      </div>

      <Input
        v-if="!isCompleteTraceCode(row.trace_codes) || !Number(row.quantity)"
        v-model="trace_code"
        @on-enter="scanTraceCode(index)"
        @on-blur="trace_code = ''"
        placeholder="必填"
        maxlength="20"
        :disabled="!Number(row.quantity) || pageDisableCondition"
      ></Input>
      <div
        v-else
        class="flex flex-item-align trace_input"
        :class="{
          errorInput: isErrorInput(row.trace_codes),
          disabledInput: pageDisableCondition,
          checkMode: isCheckTraceMode,
        }"
      >
        <div class="ecs">{{ firstTraceCode(row.trace_codes) }}</div>
        <div v-if="effectiveTraceLength(row.trace_codes) > 1" style="min-width: fit-content">
          共{{ effectiveTraceLength(row.trace_codes) }}条
        </div>
      </div>
    </Poptip>
  </div>
</template>

<script>
export default {
  name: 'traceCodePop',

  components: {},
  props: {
    // 列表row
    row: {
      type: Object,
      default: () => {},
    },
    // 列表索引
    index: {
      type: Number,
      default: 0,
    },
    // 外部列表（分页处理后的数据）
    list: {
      type: Array,
      default: () => [],
    },
    // 本地维护的总数据
    localList: {
      type: Array,
      default: () => [],
    },
    // 由页面抛出的禁用条件
    pageDisableCondition: {
      type: Boolean,
      default: false,
    },
    errorTraceCodes: {
      type: Array,
      default: () => [],
    },
    isCheckTraceMode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      scanLoading: false,
      trace_code: '',
      mockData: ['11111', '22223', '3333', '4444'],
    };
  },
  computed: {
    formatTraceCode() {
      return code => {
        return code?.replace(/(.{5})/g, '$1 ').trim();
      };
    },
    // 是否输入了所有溯源码
    isCompleteTraceCode() {
      return codes => {
        return codes?.every(item => item !== '') || false;
      };
    },
    // 是否存在异常（1.未填完所有溯源码）
    isErrorInput() {
      return codes => {
        return this.errorTraceCodes.some(item => codes.includes(item));
        // return codes?.some(item => item === '') || false;
      };
    },
    // 查询按顺序下来的首个溯源码
    firstTraceCode() {
      return codes => {
        return codes?.find(item => item !== '') || '';
      };
    },
    // 已填写溯源码的长度
    effectiveTraceLength() {
      return codes => {
        return codes?.filter(item => item !== '')?.length || 0;
      };
    },
    // 所有药品的溯源码合集（用来校验溯源码唯一）
    allTraceCodes() {
      let allList = [];
      this.localList.map(item => {
        allList = allList.concat(item.trace_codes.filter(trace_code => trace_code !== ''));
      });
      return allList;
    },

    isMatch() {
      return code => {
        return this.list[this.index].trace_codes.includes(code);
      };
    },

    // 以下场景需禁用pop展示
    // 1.手工入库未输入数量时
    // 2.详情未核码场景下，trace_codes未填入
    // 3.常繁导入入库时，订单内的商品已被提交，且已无可用溯源码时
    isDisabledPop() {
      return (
        !Number(this.row.quantity) ||
        this.row.trace_codes?.length === 0 ||
        (this.isCheckTraceMode && this.row.available_trace_codes?.length === 0)
      );
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    scanTraceCode(index) {
      if (this.scanLoading) return;
      let val = this.trace_code;
      console.log('=>(edit.vue:2549) val', val);
      if (val) {
        if (this.isCheckTraceMode) {
          if (!this.row.available_trace_codes.includes(val)) {
            this.$Message.error(`溯源码【${val}】不属于本次入库的产品批次`);
            return;
          }
        }

        if (this.allTraceCodes.includes(val)) {
          this.$Message.error(`溯源码【${val}】存在重复`);
          return;
        }
        // this.list.
        // if()
        this.checkTraceCode(val, index);
      }
    },
    checkTraceCode(val, index) {
      this.scanLoading = true;
      let params = {
        trace_code: val,
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .checkTraceCode(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.$emit('writeTraceCode', val, index);

          this.$nextTick(() => {
            this.trace_code = '';
          });
        })
        .finally(() => (this.scanLoading = false));
    },
    clearTraceCode(index, code_index) {
      this.$emit('clearTraceCode', index, code_index);
    },
  },
};
</script>

<style lang="less" scoped>
.trace_code_box {
  width: 300px;
  padding: 10px 16px 0 16px;
  max-height: 200px;
  .trace_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.errorInput {
  border-color: #ed4014;
}
.trace_input {
  cursor: pointer;
  color: #155bd4;
  border: 1px solid #bcc3d7;
  border-radius: 4px;
  padding: 4px 7px;
  height: 32px;
  background-color: #fff;
  //min-width: 103px;
  width: 100%;
}
.disabledInput {
  background-color: #f3f3f3;
  color: #aaaaaa;
  cursor: not-allowed;
}
.checkMode {
  color: green;
}
</style>

<style lang="less">
.ivu-poptip-rel {
  width: 100%;
}
</style>
