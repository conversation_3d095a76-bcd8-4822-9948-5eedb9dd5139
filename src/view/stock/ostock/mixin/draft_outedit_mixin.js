import { debounce } from 'lodash-es'
import moment from 'moment'

export default {
  data() {
    return {
      saveTimes: 0
    }
  },
  watch: {
    formValidate: {
      handler( val ) {
        this.saveDraft()
      }, deep: true
    },
    List: {
      handler( val ) {
        this.saveDraft()
      }, deep: true
    },
  },
  methods: {
    saveDraft: debounce( function () {
      if ( this.$route.query.id ) {
        return
      }
      if ( this.saveTimes === 0 ) {
        this.saveTimes++
        return
      }
      if ( this.$route.path === '/stock/ostock/edit' || this.$route.path === '/stock/ostock/outedit' ) {
        this.$api.saveStockDraft( this.handleDraftParams() ).then( res => {
          console.log( '-> res', res, '保存草稿啦' )

        }, err => this.$Message.error( err.errmsg ) )
      }
    }, 2000 ),

  },
}
