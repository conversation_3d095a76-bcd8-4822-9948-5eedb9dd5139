<template>
  <div class="ostockEdit" ref="tableWrapRef" @mouseover="hidePop">
    <Form ref="myForm" :model="formValidate" :rules="ruleValidate" :label-width="80">
      <Row>
        <Col span="8">
          <FormItem label="建单人" prop="user">
            <Input type="text" v-model="formValidate.user" placeholder="" :disabled="true"/>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="出库单号" prop="ostock_code" :disabled="true">
            <Input type="text" v-model="formValidate.ostock_code" placeholder="系统自动生成" :disabled="true"/>
          </FormItem>
        </Col>

        <Col span="8">
          <FormItem label="建单时间" prop="create_time">
            <DatePicker
              type="datetime"
              format="yyyy-MM-dd"
              placeholder=""
              v-model="formValidate.create_time"
              :disabled="true"
            ></DatePicker>
          </FormItem>
        </Col>

        <Col span="8">
          <FormItem label="出库类型" prop="out_type">
            <Select
              v-model="formValidate.out_type"
              @on-change="outTypeChange"
              placeholder="出库类型"
              :disabled="isDisatbed"
            >
              <Option v-for="(item, index) in stockTypeOutDesc" :value="index" :key="item.kw">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col v-if="[11, 99].includes(+formValidate.out_type)" span="8">
          <FormItem label="用途" prop="out_purpose">
            <Select
              v-model="formValidate.out_purpose"
              :placeholder="`请选择${+formValidate.out_type === 11 ? '领用' : '其他'}出库的用途`"
              :disabled="isDisatbed"
              @on-change="outPurposeChange"
            >
              <Option v-for="(item, index) in purposeTypeOutDesc" :value="index" :key="item.kw">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col :span="[11, 99].includes(+formValidate.out_type) ? 8 : 16">
          <FormItem
            label="备注"
            :rules="
              isRemarkRequired ? { required: true, message: '请输入备注内容', trigger: 'blur' } : { required: false }
            "
            prop="remark"
          >
            <Input
              ref="remark"
              v-model="formValidate.remark"
              type="textarea"
              placeholder="请输入备注内容"
              maxlength="200"
              show-word-limit
              :autosize="{ minRows: 2, maxRows: 2 }"
              :disabled="isDisatbed"
            />
          </FormItem>
        </Col>
      </Row>

      <Tabs v-model="currentComponent" @on-click="tabClick">
        <TabPane
          :label="
            tab_item.id === 'outStockDetail'
              ? `${tab_item.componentName}（${$route.query.prodTypeText}）`
              : tab_item.componentName
          "
          :name="tab_item.id"
          v-for="tab_item in tabComponents"
          :key="tab_item.id"
          v-if="(tab_item.id === 'operatorHistory' && $route.query.id) || tab_item.id !== 'operatorHistory'"
        ></TabPane>
      </Tabs>

      <div v-if="currentComponent === 'outStockDetail'">
        <div class="block-header">
          出库明细
          <span v-if="prodTypeText">（{{ prodTypeText }}）</span>
        </div>

        <Table
          :columns="tableCols"
          :data="list"
          size="small"
          border
          stripe
          :height="$store.state.app.clientHeight - 500 < 200 ? 200 : $store.state.app.clientHeight - 500"
        >
          <template slot-scope="{ row, index }" slot="number">
            <span v-if="row.parent_stock_detail_id == 0 || id">{{ index + 1 }}</span>
          </template>
          <template slot-scope="{ row }" slot="goodscode">
            <span v-if="row.parent_stock_detail_id == 0 || id">{{ row.number || '-' }}</span>
          </template>
          <template slot-scope="{ row }" slot="supplierName">
            <span>{{ row.supplierName || '-' }}</span>
          </template>
          <template slot-scope="{ row }" slot="goodsname">
            <div v-if="row.parent_stock_detail_id == 0 || id">
              <product-show
                :generic_name="row.goodsname"
                :prod_spec="row.prod_spec"
                :grade_desc="row.grade_desc"
                :manufacturer="row.manufacturer"
                :row="row"
                @showPop="showPop"
              />
            </div>
          </template>
          <template slot-scope="{ row }" slot="from_text">
            <span v-if="row.parent_stock_detail_id == 0 || id">{{ row.from_text || '-' }}</span>
          </template>
          <template slot-scope="{ row }" slot="retail_price">
            <span v-if="row.parent_stock_detail_id == 0 || id">
              ￥ {{ row.retail_price || '-' }}/{{ row.retail_unit }}
            </span>
          </template>
          <template slot-scope="{ row }" slot="split_price">
            <span v-if="row.parent_stock_detail_id == 0 || id">
              {{ (row.is_split == 1 && '￥' + row.split_price + '/' + row.prod_unit) || '-' }}
            </span>
          </template>
          <template slot-scope="{ row }" slot="sales_price">
            <span>{{ '￥' + row.purchase_price + '/' + row.prod_unit }}</span>
          </template>
          <template slot-scope="{ row }" slot="produce_time">
            {{ row.produce_time | data_format('YYYY-MM-DD') }}
          </template>
          <template slot-scope="{ row }" slot="expire_time">
            {{ row.expire_time | data_format('YYYY-MM-DD') }}
          </template>
          <template slot-scope="{ row }" slot="stock_num">
            {{ row.stock_num + row.prod_unit }}
          </template>

          <template slot-scope="{ row, index }" slot="prod_unit">
            {{ row.prod_unit }}
          </template>
          <template slot-scope="{ row, index }" slot="quantity">
            <div class="flex flex-item-align">
              <InputNumber
                v-model="list[index].quantity"
                :min="0"
                :precision="0"
                @on-change="quantity => changeQuantityHandle(quantity, row.parent_stock_detail_id, row.stock_detail_id)"
                :disabled="isDisatbed"
                style="margin-right: 5px"
              />
              {{ row.prod_unit }}
            </div>
          </template>
          <template slot-scope="{ row, index }" slot="batch_id">
            <span>{{ row.curBatchId }}</span>
          </template>
          <template slot-scope="{ row, index }" slot="batchList">
            <Select
              v-if="row.child && row.child.length > 1 && row.is_open !== '1'"
              @on-change="stock_detail_id => changeBatchCode(row.stock_detail_id, stock_detail_id)"
              :value="row.curBatchId"
              :disabled="isDisatbed"
            >
              <Option v-for="(compItem, compIndex) in row.child" :value="compItem.id" :key="compItem.id"
              >{{ compItem.batch_code }}
              </Option>
            </Select>
            <span v-else>{{ row.curBatchNum }}</span>
          </template>
          <template slot-scope="{ row, index }" slot="operation">
            <div class="flex">
              <div v-if="row.parent_stock_detail_id == 0 && row.child.length > 1" style="margin-right: 12px">
                <a v-if="row.is_open !== '1'" type="text" @click="batchCodeOpen(row)">更多批号</a>
                <a v-else type="text" @click="batchCodeClose(row)">收起</a>
              </div>
              <a @click="delListItemHandle(row, index)" :disabled="isDisatbed"
                 v-if="row.parent_stock_detail_id == 0"
              >删除</a
              >
              <div v-else>-</div>
            </div>
          </template>
        </Table>
        <div style="position: relative">
          <el-select
            :disabled="isDisatbed"
            style="width: 300px;margin-top: 10px;"
            ref="goods"
            size="small"
            :value="selectAddTable"
            :loading="searchLoading"
            :remote-method="searchMethod"
            popper-class="customer-option-table"
            clearable
            filterable
            placeholder="请搜索添加相关货品"
            remote
            @focus="searchMethod('')"
            reserve-keyword
            @change="selectAddTableChangeHandle"
            :popper-append-to-body="false"
          >
            <div class="customer-item-box customer-item-header">
              <div
                class="customer-item"
                v-for="(option_item, option_index) in optionsTabCols"
                :key="'header' + option_index"
                :style="{ width: `${option_item.width}px` || '60px', textAlign: option_item.align || 'left' }"
              >
                {{ option_item.title }}
              </div>
            </div>
            <el-option v-for="item in goods_list" :key="item.id" :label="item.generic_name" :value="item.id"
                       :disabled="item.stock_num == 0">
              <div class="customer-item-box">
                <div
                  class="customer-item"
                  :class="{'customer-item--disabled': item.stock_num == 0}"
                  v-for="(option_item, option_index) in optionsTabCols"
                  :key="'data' + option_index"
                  :style="{ width: `${option_item.width}px` || '60px', textAlign: option_item.align || 'left', color: option_item.color || '' }"
                >
                  <span :class="{ 'ecs': option_item.ecs }">{{ item[option_item.key] || '-' }}</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </div>

        <div class="amount-box flex">
          <span>出库（采购）总额: ￥{{ getMaxPurchase }}</span>
          <span style="margin-left: 12px">出库（零售）总额: ￥{{ getMaxRetail }}</span>
          <div style="margin-left: 12px">出库（采购)总量: {{ getTotalWeight() ? `${getTotalWeight()}kg` : '' }}</div>
          <div v-for="(item, index) in getOtherUnitList()" :key="index">
            <div v-show="item.quantity" style="margin-left: 5px">
              <span v-if="index === 0">{{ index === 0 && getTotalWeight() ? '+' : '' }}</span>
              <span v-else>+</span>
              {{ item.quantity }} {{ item.prod_unit }}
            </div>
          </div>
        </div>
      </div>

      <!-- 操作历史 -->
      <div v-if="currentComponent === 'operatorHistory'">
        <Table :columns="historyColumns" :data="historyList" :height="$store.state.app.clientHeight - 370">
          <template slot-scope="{ row, index }" slot="time">
            {{ row.time | data_format }}
          </template>
          <template slot-scope="{ row, index }" slot="operator_name">
            {{ row.operator_name || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="remark">
            {{ row.remark || '-' }}
          </template>
        </Table>
      </div>
    </Form>

    <div class="block20"></div>
    <div class="fixed-bottom-wrapper">
      <div v-if="!isDisatbed && $route.query.examineStatus !== 'WAIT_AUDIT'">
        <div v-if="currentComponent === 'outStockDetail'">
          <Poptip
            v-eleControl="'E58oXxvk2l'"
            confirm
            title="是否要清空当前页面全部数据?"
            @on-ok="clearAll"
            v-if="!$route.query.id"
          >
            <Button>清空</Button>
          </Poptip>
          <back-button style="margin: 0 12px">取消</back-button>
          <Button v-eleControl="'E58oXxvk2l'" type="primary" @click="onSave" class="submitBtn" :loading="subBtnLoading">
            提交
          </Button>
        </div>
        <div v-else>
          <back-button></back-button>
        </div>
      </div>
      <back-button v-else></back-button>
      <Button
        v-eleControl="'EomnPm88db'"
        type="primary"
        v-if="status === '10' && $route.query.examineStatus === 'WAIT_AUDIT'"
        class="ml10"
        @click="examine"
        :loading="examineLoading"
      >
        审核
      </Button>
    </div>

    <ExamineModal ref="examineModal" :currentRow="currentRow" @getsList="examineSuccess" />

    <Tooltip
      ref="custom-tooltip"
      popper-class="generic-name-link-tip"
      transfer-class-name="generic-name-link-tip"
      :reference="tooltipReference"
      :delay="1000"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <generic-name-tip :row="selectedToolTipRow" />
      </template>
    </Tooltip>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import draft_mixin from '@/view/stock/ostock/mixin/draft_mixin';
import { debounce } from 'lodash-es';
import moment from 'moment';
import {$operator} from '@/libs/operation';

import ExamineModal from './components/examineModal';
import tooltip from "@/view/stock/mixins/tooltip";
import GenericNameTip from "@/view/stock/components/generic-name-tip.vue";

let init_query_form_data = {
  keyword: '',
  status: '',
};

export default {
  name: 'edit',
  mixins: [draft_mixin, tooltip],
  components: {
    GenericNameTip,
    ExamineModal,
  },
  data() {
    return {
      queryFormData: {...init_query_form_data},
      tableCols: [
        {title: '序号', slot: 'number', width: 50},
        {title: '编码', slot: 'goodscode', width: 70},
        {title: '货品', slot: 'goodsname', minWidth: 220},
        {title: '来源', slot: 'from_text', width: 80},
        {title: '零售价', slot: 'retail_price', minWidth: 120},
        {title: '拆零价', slot: 'split_price', width: 80},
        {title: '批次编码', slot: 'batch_id', width: 80},
        {title: '生产批号', slot: 'batchList', minWidth: 80},
        {title: '供应商', slot: 'supplierName', minWidth: 120},
        {title: '生产日期', slot: 'produce_time', width: 100},
        {title: '有效期	', slot: 'expire_time', width: 100},
        {title: '采购价', slot: 'sales_price', width: 100},
        // { title: '单位', slot: 'prod_unit', minWidth: 60 },
        {title: '库存', slot: 'stock_num', minWidth: 80},
        {title: '出库数量	', slot: 'quantity', width: 130},
        {title: '操作', slot: 'operation', width: 120, align: 'center', fixed: 'right'},
      ],
      list: [],
      stockTypeOutDesc: [], // 出库类型
      outPurposeTypeDesc: [], // 出库用途类型
      requestRemarkKeys: [], // 选中出库用途后必填备注的字段
      formValidate: {
        user: '', // 建单人
        ostock_code: '', // 出库单号
        create_time: '', // 建单时间
        out_type: '', // 出库类型
        out_purpose: '', // 出库用途
        remark: '', // 备注
      },

      ruleValidate: {
        user: [{required: true, message: '请输入建单人', trigger: 'blur'}],
        create_time: [{required: true, type: 'date', message: '请选择建单时间', trigger: 'blur'}],
        out_type: [{required: true, message: '请选择出库类型', trigger: 'change'}],
        out_purpose: [{required: true, message: '请选择领用出库的用途', trigger: 'change'}]
      },
      selectAddTable: '',
      show: true,
      userInfo: {},
      goods_list: [],
      maxAmount: 0,
      id: null,
      ostock: {}, // 表单数据
      prod_type: '', // 类型，中药西药...
      prodTypeText: '',
      copyGoodsList: [],
      type: 'add',
      copyList: [],
      subBtnLoading: false,
      inputKey: 0,
      canSearch: true,
      isDisatbed: false,
      ostock_type: '',
      suppliers: [],
      stockDetails: {},

      currentComponent: 'outStockDetail',
      tabComponents: [
        {componentName: '出库明细', id: 'outStockDetail'},
        {componentName: '操作历史', id: 'operatorHistory'},
      ],

      // 操作历史
      historyColumns: [
        {title: '操作时间', slot: 'time', align: 'center'},
        {title: '操作人', slot: 'operator_name', align: 'center'},
        {title: '操作详情', slot: 'remark', align: 'center'},
      ],
      historyList: [],

      prodTypes: {},

      currentRow: {},
      examineLoading: false,
      status: '',

      optionsTabCols: [
        {title: '编码', key: 'id', align: 'left', width: 70},
        {title: '货品', key: 'generic_name', align: 'left', width: 140, color: '#333'},
        {title: '品级', key: 'grade_desc', align: 'left', width: 70},
        {title: '规格', key: 'prod_spec', align: 'left', width: 100},
        {title: '厂家', key: 'manufacturer', align: 'left', width: 140, ecs: true},
        {title: '来源', key: 'from_text', align: 'left', width: 70, color: '#333'},
      ],
      searchName: '',
      searchLoading: false,
    };
  },

  created() {
    this.id = this.$route.query.id;
    this.prod_type = this.$route.query.prod_type;
    this.prodTypeText = this.$route.query.prodTypeText;
    this.type = this.$route.query.type;
    this.ostock_type = this.$router.ostock_type;
    this.userInfo = runtime.getUser();
    this.formValidate.user = runtime.getUser().name;
    let nowDate = new Date();
    this.formValidate.create_time = nowDate;
    if (this.id) {
      this.getDetail();
    }
    this.querySupplierList();
    // this.queryGoodsList();
    this.searchMethod('')
  },
  watch: {
    formValidate: {
      handler(val) {
        this.saveDraft();
      },
      deep: true,
    },
    list: {
      handler(val) {
        this.saveDraft();
      },
      deep: true,
    },
  },
  methods: {
    search() {
    },
    examine() {
      this.currentRow = {
        id: this.$route.query.id,
      };
      this.$refs.examineModal.showModal = true;
      this.$refs.examineModal.formValidate.examine = 'PASS';
      this.$refs.examineModal.formValidate.remark = '';
    },
    examineSuccess() {
      this.$router.push({
        path: '/stock/ostock/list',
        query: {ostock_type: '20'},
      });
    },
    // 获取操作历史
    getOstockLogList() {
      let params = {
        id: this.$route.query.id,
      };
      this.$api.getOstockLogList(params).then(
        res => {
          this.historyList = res.list;
        },
        error => {
        }
      );
    },

    tabClick(val) {
      this.currentComponent = val;
      if (val === 'operatorHistory') {
        this.getOstockLogList();
      }
    },

    // 总重量（仅计算单位为g与kg的商品）
    getTotalWeight() {
      return (
        this.list &&
        this.list.length > 0 &&
        this.list.reduce((prev, curr) => {
          if (curr.prod_unit === 'Kg' || curr.prod_unit === 'g') {
            if (curr.prod_unit === 'g') {
              let kilogram = $operator.divide(curr.quantity, 1000, 3);
              return $operator.add(prev, kilogram, 3);
            } else {
              return $operator.add(prev, curr.quantity, 3);
            }
          } else {
            return prev;
          }
        }, 0)
      );
    },
    // 总数量（非克重单位的商品数量）
    getOtherUnitList() {
      // 当前列表非克重单位的剩余商品
      let lastList =
        this.list &&
        this.list.length > 0 &&
        this.list.filter(item => item.prod_unit !== 'Kg' && item.prod_unit !== 'g');
      let unitList = [];
      lastList &&
      lastList.forEach(item => {
        let flag = unitList.findIndex(unit_item => unit_item.prod_unit === item.prod_unit);
        if (flag > -1) {
          unitList[flag].quantity = $operator.add(unitList[flag].quantity, item.quantity);
        } else {
          let currentObj = {prod_unit: item.prod_unit, quantity: item.quantity};
          unitList.push(currentObj);
        }
      });
      return unitList;
    },
    //收起货品批号只显示第一个
    batchCodeClose({stock_detail_id}) {
      console.log('-> %c stock_detail_id  === %o', 'font-size: 15px;color: green;', stock_detail_id);
      let index = 0;
      this.list.forEach(function (item, i) {
        // console.log( '-> %c item  === %o', 'font-size: 15px;color: green;', item )
        if (item.stock_detail_id == stock_detail_id) {
          index = i;
        }
      });
      this.list[index].is_open = '0';
      this.list[index].child.forEach(function (item) {
        item.is_open = '0';
      });
      this.handleList();
    },
    //展开货品批号
    batchCodeOpen({stock_detail_id}) {
      let index = 0;
      this.list.forEach(function (item, i) {
        // console.log( '-> item', item )
        if (item.stock_detail_id == stock_detail_id) {
          index = i;
        }
      });
      this.list[index].is_open = '1';
      this.list[index].child.forEach(function (item) {
        item.is_open = '1';
      });
      this.handleList(this.list);
    },
    // 最终处理成页面显示数据
    handleList() {
      // console.log( this.list )
      let list = [];
      this.list.forEach(function (item) {
        // console.log( '-> %c item  === %o', 'font-size: 15px;color: green;', item )
        let child = [];
        if (item.is_open === '1' && item.child.length > 1) {
          child = item.child;
        }
        if (item.stock_num <= 0) {
          item.noStock = true;
        }
        if (item.child.length || item.parent_stock_detail_id == '0') {
          list.push(item);
        }
        child.length &&
        child.forEach(function (c_item) {
          // console.log( '-> %c c_item  === %o ', 'font-size: 15px', c_item )
          if (c_item.stock_num <= 0) {
            c_item.noStock = true;
          }
          if (item.stock_detail_id != c_item.stock_detail_id) {
            c_item.is_open = '1';
            list.push(c_item);
          }
        });
      });

      this.list = list;
      console.log('-> %c this.list  === %o', 'font-size: 15px;color: green;', this.list);
    },
    //清空全部
    clearAll() {
      this.formValidate = {
        ...this.formValidate,
        ostock_code: '', // 出库单号
        out_type: '', // 出库类型
        out_purpose: '', // 出库用途
        remark: '', // 备注
      };
      this.list = [];
    },
    getDraft(ostock_type) {
      const params = {
        prod_type: this.$route.query.prod_type,
        ostock_type,
      };
      this.$api.getStockDraft(params).then(res => {
        // console.log( '-> res', res )
        this.suppliers = res.data.suppliers;
        if (res.has_display === '1') {
          res.data.items.map(item => {
            item.subtotal = Number(item.subtotal);
            item.quantity = Number(item.quantity);
            item.batchList = item.batch_list;
          });
          // res.data.items.map( item => {
          //   // item.sales_units.map( unitItem => {
          //   //   if ( unitItem.checked === '1' ) {
          //   //     item.warehouse_unit = item.prod_unit = unitItem.unit
          //   //   }
          //   // } )
          //   item.child.map( batchItem => {
          //     // console.log( '-> %c batchItem  === %o ', 'font-size: 15px', batchItem )
          //     if ( item.curBatchId === batchItem.id ) {
          //       item.stock_num = batchItem.stock_num
          //       item.supplierName = this.suppliers[batchItem.supplier_id].name
          //     }
          //   } )
          //
          // } )
          // this.list = res.data.items
          this.initList(res.data.items);

          this.formValidate.user = res.data.operator_name || runtime.getUser().name;
          this.formValidate.ostock_code = res.data.ostock_code;
          this.formValidate.create_time = res.data.create_time || new Date();
          this.formValidate.out_type = res.data.out_type;
          this.formValidate.remark = res.data.remark;
        }
      });
    },
    handleDraftParams() {
      let values = this.formValidate;
      // console.log( '-> values', values )
      let params = {
        ostock_type: 20,
        prod_type: this.prod_type,
        operator_name: values.user, // 建单人
        ostock_code: values.ostock_code, // 出库单号
        create_time: values.create_time, // 建单时间
        out_type: values.out_type, // 出库类型
        remark: values.remark, // 备注
        items: this.list,
      };
      return params;
    },
    // 编辑获取详情
    getDetail() {
      let params = {
        id: this.id,
      };
      io.get('/clinic/ostock.outinfo', {data: params})
        .then(data => {
          // console.log( '🚀 ~ file: outedit.vue ~ line 293 ~ .then ~ data', data )
          this.ostock = data.ostock;
          this.prodTypes = data.prodTypes;
          this.suppliers = data.suppliers;
          // if ( data.ostock.status === '20' || data.ostock.status === '80' ) {
          this.isDisatbed = true;
          // }
          this.status = data.ostock.status;
          this.stockDetails = data.stockDetails;

          this.formValidate.user = data.ostock.operator_name;
          this.formValidate.ostock_code = data.ostock.ostock_code;
          this.formValidate.out_purpose = data.ostock.out_purpose;
          this.formValidate.create_time = data.ostock.create_time
            ? moment(data.ostock.create_time * 1000).format('YYYY-MM-DD')
            : '';
          this.formValidate.out_type = data.ostock.out_type;
          this.formValidate.remark = data.ostock.remark;

          let itemList = data.ostock.items;
          let list = [];
          let products = data.products;
          let stockDetails = data.stockDetails;
          itemList.length &&
          itemList.length > 0 &&
          itemList.map((item, i) => {
            // console.log( '-> %c item  === %o ', 'font-size: 15px', item )
            const prod_id = item.prod_id;
            const stock_detail_id = item.stock_detail_id;
            list.push({
              number: item.prod_id, // 商品id
              goodsname: products[prod_id].prod_name || products[prod_id].generic_name, // 商品名称
              prod_spec: products[prod_id].prod_spec,
              grade_desc: products[prod_id].grade_desc,
              manufacturer: products[prod_id].manufacturer,
              manufactor: products[prod_id].manufacturer,
              retail_price: products[prod_id].retail_price, // 零售价
              sales_price: item.settlement_price.sales_price, //用于计算的各个批次的零售价
              purchase_price: item.settlement_price.price, //采购价
              is_split: products[prod_id].is_split,
              split_price: products[prod_id].split_price, // 拆零价
              from_text: products[prod_id].from_text, //来演
              expire_time:
                stockDetails &&
                stockDetails[prod_id] &&
                stockDetails[prod_id][stock_detail_id] &&
                stockDetails[prod_id][stock_detail_id].expire_time, // 有效期
              stock_num:
                stockDetails && stockDetails[prod_id] && stockDetails[prod_id][stock_detail_id]
                  ? stockDetails[prod_id][stock_detail_id].stock_num
                  : 0, // 库存数量
              quantity: item.purchase_stock_num, //  数量
              curBatchNum:
                stockDetails && stockDetails[prod_id] && stockDetails[prod_id][stock_detail_id]
                  ? stockDetails[prod_id][stock_detail_id].batch_code
                  : '', // 批号
              curBatchId:
                stockDetails && stockDetails[prod_id] && stockDetails[prod_id][stock_detail_id]
                  ? stockDetails[prod_id][stock_detail_id].id
                  : '', // 默认选中批号ID
              supplierName:
                stockDetails && stockDetails[prod_id] && stockDetails[prod_id][stock_detail_id]
                  ? data.suppliers[stockDetails[prod_id][stock_detail_id].supplier_id].name
                  : '', // 供应商
              batchList:
                stockDetails && stockDetails[prod_id] && stockDetails[prod_id][stock_detail_id]
                  ? stockDetails[prod_id]
                  : '', // 批号列表
              prod_stock_id: item.prod_stock_id, // 商品库存id
              stock_detail_id: stock_detail_id, // 明细id
              subtotalDisabled: true, // 小计禁用
              sales_units: products[prod_id].sales_units, // 单位列表
              prod_unit: item.warehouse_unit, // 选中单位
              retail_unit: products[prod_id].prod_unit, // 选中单位
              operation: '操作',
            });
          });
          this.list = list;
          console.log('-> %c this.list  === %o', 'font-size: 15px;color: green;', this.list);
        })
        .catch(error => {
          console.log(error);
          // {}
        });
    },

    // 修改select
    selectAddTableChangeHandle(value) {
      console.log('-> value', value);
      const isExist = this.list.findIndex(item => item.number == value);
      this.$nextTick(() => {
        this.goodsSearchHandle('');
        this.selectAddTable = '';
        this.inputKey = this.inputKey + 1;
        this.canSearch = true;
      });
      if (isExist > -1) {
        this.$Message.error('请勿重复添加');
        return;
      }
      if (!value) {
        return;
      }
      this.canSearch = false;
      let currentItem = {};
      this.goods_list.map((item, i) => {
        if (item.id == value) {
          currentItem = item;
          console.log('-> %c currentItem  === %o ', 'font-size: 15px', currentItem);
        }
      });

      let params = {
        prod_id: value,
      };
      io.get('/clinic/product.stock.num', {data: params})
        .then(data => {
          this.selectAddTable = '';
          console.log('-> data', data);
          this.suppliers = data.suppliers;
          if (!data.list || !data.list.length) {
            this.$Message.error('当前货品没有入库记录，无法操作出库');
            return;
          }
          const obj = {...currentItem, batchList: data.list};
          console.log('objobjobj', obj)
          this.initList([obj]);
        })
        .catch(error => {
          console.log('-> error', error);
          {
          }
        });
    },
    initList(list) {
      console.log('-> %c list  === %o', 'font-size: 15px;color: green;', list);
      if (!Array.isArray(list) || !list.length) {
        return [];
      }
      list.forEach(currentItem => {
        console.log('-> %c currentItem  === %o', 'font-size: 15px;color: green;', currentItem);
        if (currentItem.is_split === '1') {
          currentItem.sales_units.forEach(item => {
            if (item.checked === '1') {
              currentItem.prod_unit = item.unit;
            } else {
              currentItem.retail_unit = item.unit;
            }
          });
        } else {
          currentItem.retail_unit = currentItem.prod_unit;
        }
        const firstItem = currentItem.batchList[0];
        let currentSett = {};
        currentItem.batchList[0].settlement_price.map((citem, ci) => {
          if (citem.unit === currentItem.prod_unit) {
            currentSett = citem;
          }
        });
        currentItem.stock_detail_id = currentItem.batchList[0].id;
        currentItem.batchList.forEach(item => {
          item.settlement_price.map(settlement => {
            if (settlement.unit == currentItem.prod_unit) {
              item.purchase_price = settlement.price;
              item.sales_price = settlement.sales_price;
            }
          });
          item.retail_price = currentItem.retail_price;
          item.stock_detail_id = item.id;
          item.parent_stock_detail_id = currentItem.stock_detail_id;
          item.is_open = '0';
          item.child = [];
          item.prod_unit = currentItem.prod_unit;
          item.curBatchNum = item.batch_code;
          item.curBatchId = item.id;
          item.phonetic_code = this.suppliers[item.supplier_id].phonetic_code; // 商品编码
          item.goodsname = currentItem.generic_name; // 商品名称
          item.prod_spec = currentItem.prod_spec;
          item.grade_desc = currentItem.grade_desc;
          item.manufacturer = currentItem.manufacturer; // 商品名称
          item.from_text = currentItem.from_text;
          item.supplierName = this.suppliers[item.supplier_id].name;
          item.number = currentItem.id; // 商品id
          item.split_price = currentItem.split_price; // 商品id
          item.retail_unit = currentItem.retail_unit;
          item.is_split = currentItem.is_split;
          item.quantity = item.quantity || '';
          item.split_price = currentItem.split_price; // 拆零价
        });
        // console.log( currentItem.batchList )
        let obj = {
          ...currentItem,
          is_open: currentItem.batchList.length > 1 ? '1' : '0',
          number: currentItem.id, // 商品id
          id: currentItem.id, // 商品id
          goodsname: currentItem.generic_name, // 商品名称
          purchase_price: currentSett.price,
          sales_price: currentSett.sales_price,
          from_text: currentItem.from_text, // 商品名称
          retail_price: currentItem.retail_price, // 零售价
          retail_unit: currentItem.retail_unit, // 零售价单位
          split_price: currentItem.split_price, // 拆零价
          is_split: currentItem.is_split, // 是否拆零
          expire_time: firstItem.expire_time, // 有效期
          produce_time: firstItem.produce_time, // 生产日期
          stock_num: firstItem.stock_num, // 库存数量
          supplierName: this.suppliers[firstItem.supplier_id].name, // 供应商
          supplier_id: firstItem.supplier_id, // 供应商
          stock_detail_id: firstItem.id, // 批次id
          parent_stock_detail_id: 0, // 批次id
          prod_unit: currentItem.prod_unit, // 单位
          quantity: currentItem.batchList[0].quantity || '', //  出库数量
          child: currentItem.batchList, // 批号列表
          curBatchNum: firstItem.batch_code, // 默认选中批号
          curBatchId: firstItem.id, // 默认选中批号ID
          prod_stock_id: currentItem.prod_stock_id, // 商品库存id
          subtotalDisabled: true, // 小计禁用
          sales_units: currentItem.sales_units, // 单位列表
          batchList: currentItem.batchList,
          grade_desc: currentItem.grade_desc,
          prod_spec: currentItem.prod_spec,
          manufacturer: currentItem.manufacturer,
          operation: '操作',
        };
        this.list.push(obj);
      });

      console.log('-> %c this.list  === %o', 'font-size: 15px;color: green;', this.list);
      this.handleList();
    },
    // 修改数量
    changeQuantityHandle(new_quantity, parent_stock_detail_id, stock_detail_id) {
      console.log(
        '-> %c new_quantity, parent_stock_detail_id, stock_detail_id  === %o',
        'font-size: 15px;color: green;',
        new_quantity,
        parent_stock_detail_id,
        stock_detail_id
      );
      // console.log("-> %c e, index  === %o", "font-size: 15px;color: green;", e, index)
      // this.list[index].quantity = e
      // console.log(this.list)
      if (!new_quantity) {
        new_quantity = 0;
      }
      parent_stock_detail_id = parent_stock_detail_id == '0' ? stock_detail_id : parent_stock_detail_id;
      this.list.forEach(function (item, i) {
        if (item.stock_detail_id == parent_stock_detail_id) {
          if (item.stock_detail_id == stock_detail_id) {
            item.quantity = new_quantity;
          }
          item.child.forEach(subItem => {
            if (subItem.stock_detail_id == stock_detail_id) {
              subItem.quantity = new_quantity;
            }
          });
        }
      });
      console.log(this.list);
    },
    // onChangeQuantity(new_quantity, parent_stock_detail_id, stock_detail_id) {
    //   console.log("-> new_quantity, parent_stock_detail_id, stock_detail_id", new_quantity, parent_stock_detail_id, stock_detail_id)
    //   parent_stock_detail_id = !parent_stock_detail_id ? stock_detail_id : parent_stock_detail_id
    //   this.drugs.forEach(function (drug, i) {
    //     if (drug.stock_detail_id == parent_stock_detail_id) {
    //       if (drug.stock_detail_id == stock_detail_id) {
    //         drug.quantity = new_quantity
    //       }
    //       drug.child.forEach(item => {
    //         if (item.stock_detail_id == stock_detail_id) {
    //           item.quantity = new_quantity
    //         }
    //       })
    //     }
    //   })
    // },
    // 修改批号
    changeBatchCode(old_stock_detail_id, new_stock_detail_id) {
      this.list.forEach((item, i) => {
        if (item.stock_detail_id == old_stock_detail_id) {
          let _child = this.$lodash.cloneDeep(item.child);
          item.child.forEach(item => {
            console.log('-> %c item  === %o', 'font-size: 15px;color: green;', item);
            if (item.stock_detail_id == new_stock_detail_id) {
              _child.forEach(_c => {
                _c.parent_stock_detail_id = item.stock_detail_id;
              });
              this.list[i] = item;
              this.list[i].parent_stock_detail_id = 0;
              this.list[i].child = _child;
            }
          });
        }
      });

      this.handleList();
    },
    // 删除
    delListItemHandle(row, index) {
      console.log('-> %c row, index  === %o', 'font-size: 15px;color: green;', row, index);
      const len = row.child.length || row.batchList.length;
      console.log('-> %c len  === %o', 'font-size: 15px;color: green;', len);
      const delLen = row.is_open === '1' ? len : 1;
      this.list.splice(index, delLen);
    },
    // 保存
    onSave() {
      if (!this.list.length) {
        this.$Message.error('请至少选择一种货品');
        return;
      }
      this.$refs.myForm.validate(valid => {
        if (valid) {
          for (const listItem of this.list) {
            console.log('-> %c listItem  === %o', 'font-size: 15px;color: green;', listItem);
            if (Number(listItem.quantity) > Number(listItem.stock_num)) {
              this.$Message.error(`货品【${listItem.goodsname}】的出库数量不能大于库存数`);
              return;
            }
            if (listItem.is_open === '0') {
              if (Number(listItem.quantity) <= 0) {
                this.$Message.error(`货品【${listItem.goodsname}】的出库数量不能为0`);
                return;
              }
            } else {
              if (listItem.child.length > 1) {
                let hasQuantity = listItem.child.some(item => {
                  console.log('-> %c item  === %o', 'font-size: 15px;color: green;', item);
                  return Number(item.quantity) > 0;
                });
                console.log('-> %c hasQuantity  === %o', 'font-size: 15px;color: green;', hasQuantity);
                if (!hasQuantity) {
                  this.$Message.error(`货品【${listItem.goodsname}】的各批号出库数量不能全为0`);
                  return;
                }
              }
            }
          }
          this.subBtnLoading = true;
          let values = this.formValidate;
          let params = {
            operator_name: values.user,
            out_type: values.out_type,
            out_purpose: values.out_purpose,
            ostock_type: 20,
            prod_type: this.prod_type,
            remark: values.remark,
          };

          let items = [];

          // this.list.filter( item => Number( item.quantity ) > 0 ).map( ( item, i ) => {
          //   console.log( '-> %c item  === %o', 'font-size: 15px;color: green;', item )
          //   items.push( {
          //     prod_id: item.number,
          //     prod_stock_id: item.prod_stock_id,
          //     purchase_stock_num: item.quantity,
          //     warehouse_unit: item.warehouse_unit || item.prod_unit,
          //     stock_detail_id: item.stock_detail_id,
          //     batch_code: item.curBatchNum,
          //   } )
          // } )

          this.list
            .filter(item => Number(item.quantity) > 0)
            .map((item, i) => {
              console.log('-> %c item  === %o', 'font-size: 15px;color: green;', item);
              items.push({
                prod_id: item.number,
                prod_stock_id: item.prod_stock_id,
                purchase_stock_num: item.quantity,
                warehouse_unit: item.prod_unit,
                stock_detail_id: item.stock_detail_id,
                batch_code: item.curBatchNum,
                // purchase_price: item.purchase_price,
              });
            });
          params.items = items;
          if (this.id) {
            params.id = this.id;
          }
          io.post('/clinic/ostock.out', params)
            .then(
              data => {
                this.$Message.success({
                  content: '提交成功',
                });
                this.$router.replace('/stock/ostock/list?ostock_type=20');
              },
              error => {
                {
                }
              }
            )
            .finally(() => {
              this.subBtnLoading = false;
            });
        } else {
        }
      });
    },
    // 取消
    backHandle() {
      this.$router.back();
    },
    // 获取出库类型
    querySupplierList(query) {
      io.get('/clinic/ostock.options', {data: {}})
        .then(data => {
          this.stockTypeOutDesc = data.stockTypeOutDesc;
          this.outPurposeTypeDesc = data.outPurposeTypeDesc;
          this.requestRemarkKeys = [];
          Object.values(this.outPurposeTypeDesc || {})?.forEach(option => {
            for (let key in option) {
              if (option[key]?.is_remark === '1') {
                this.requestRemarkKeys = [...new Set([...this.requestRemarkKeys, key])]
              }
            }
          });
          if (!this.id) {
            this.getDraft(20);
          }
        })
        .catch(error => {
          {
          }
        });
    },
    // 获取商品列表
    queryGoodsList() {
      let params = {
        page: 1,
        pageSize: 20,
        name: '',
        status: '',
        has_stock: true,
        prod_type: this.prod_type,
      };
      io.get('/clinic/product.product.list', {data: params})
        .then(data => {
          this.goods_list = data.list;
          this.copyGoodsList = data.list;
        })
        .catch(error => {
          {
          }
        });
    },
    searchMethod(query) {
      this.searchName = query
      let params = {
        page: 1,
        pageSize: 20,
        name: query,
        source: 'out_stock',
        prod_type: this.prod_type,
        has_stock: 1
      };
      this.searchLoading = true;
      io.get('/clinic/product.product.list', {data: params})
        .then(data => {
          this.goods_list = data.list;
        })
        .finally(() => this.searchLoading = false)
    },
    goodsSearchHandle(val) {
      this.searchName = val
      this.searchMethod(val)
    },
    // 计算采购总价
    // getMaxAmount() {
    //   let maxAmount = 0;
    //   this.list.length &&
    //   this.list.length > 0 &&
    //   this.list.map((item, i) => {
    //     maxAmount = maxAmount + item.purchasePrice * item.quantity;
    //   });
    //   return S.number_format(maxAmount, 4);
    // },
    // 计算零售总价

    queryGoodsItemInfo(item) {
      let params = {
        prod_id: 1,
      };
      io.get('/clinic/product.stock.num', {data: params})
        .then(data => {
        })
        .catch(error => {
          {
          }
        });
    },
    // 返回默认选中项
    getChecked(arr) {
      let obj = {};
      arr.map((item, i) => {
        if (item.isChecked) {
          obj = item;
        }
      });
      return obj;
    },
    outTypeChange() {
      this.formValidate.out_purpose = ''
    },
    outPurposeChange() {
      this.formValidate.remark = ''
      this.$refs.remark.focus()
      this.$nextTick(() => {
        this.$refs.remark.blur()
      })
    }
  },
  computed: {
    purposeTypeOutDesc() {
      const out_type = this.formValidate.out_type;
      return this.outPurposeTypeDesc?.[out_type] || {}
    },
    isRemarkRequired() {
      return this.requestRemarkKeys.includes(this.formValidate.out_purpose)
    },
    goodsList() {
      if (this.goods_list.length == 0) {
        return [{id: 'header', label: this.searchName}, ...this.goods_list, {id: 'empty', label: this.searchName}]
      } else {
        return [{id: 'header', label: this.searchName}, ...this.goods_list]
      }
    },
    isExamine() {
      return this.$route.query.examineStatus === 'WAIT_AUDIT' && this.status === '10';
    },

    getMaxPurchase() {
      let maxAmount = 0;
      this.list.map((item, i) => {
        console.log('-> %c item  === %o', 'font-size: 15px;color: green;', item);
        // for ( let k in item.child ) {
        //   if ( item.curBatchId === item.child[k].id ) {
        //     item.child[k].settlement_price.map( ( settItem, settI ) => {
        //       if ( settItem.unit === item.prod_unit ) {
        //         maxAmount = maxAmount + settItem.sales_price * item.quantity
        //       }
        //     } )
        //   }
        // }
        const total = $operator.multiply(item.purchase_price, item.quantity, 2);
        maxAmount = $operator.add(maxAmount, total, 2);
      });
      console.log('-> %c maxAmount  === %o', 'font-size: 15px;color: green;', maxAmount);
      return maxAmount;
    },
    getMaxRetail() {
      let maxAmount = 0;
      this.list.map((item, i) => {
        console.log('-> %c item  === %o', 'font-size: 15px;color: green;', item);
        // for ( let k in item.child ) {
        //   if ( item.curBatchId === item.child[k].id ) {
        //     item.child[k].settlement_price.map( ( settItem, settI ) => {
        //       if ( settItem.unit === item.prod_unit ) {
        //         maxAmount = maxAmount + settItem.sales_price * item.quantity
        //       }
        //     } )
        //   }
        // }
        let total = $operator.multiply(item.sales_price, item.quantity);
        maxAmount = $operator.add(maxAmount, total, 2);
      });
      console.log('-> %c maxAmount  === %o', 'font-size: 15px;color: green;', maxAmount);
      return maxAmount;
    },
  },
};
</script>

<style lang="less" scoped>
.amount-box {
  background: #fcfcfc;
  color: #aaaaaa;
  padding: 8px 12px;
  border-radius: 2px;
}
</style>
<style lang="less">
.ostockEdit {
  .ivu-date-picker {
    width: 100%;
  }

  .ivu-table-body {
    // min-height: 120px;
  }

  .ivu-table-wrapper {
    overflow: inherit;
  }

  .submitBtn {
    margin-right: 20px;
  }
}

.option-item {
  display: flex;
  //justify-content: space-between;
  align-items: center;

  .item-name {
    white-space: pre-line;
  }
}

.readonly-desc {
  color: #999;
}

.block20 {
  height: 40px;
  width: 100%;
}
.generic-name-link-tip {
  .ivu-tooltip-inner {
    max-width: unset;
    background-color: #FFFDEC;
  }
}
</style>
