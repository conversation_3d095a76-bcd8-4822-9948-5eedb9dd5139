<template>
  <Modal
    :value="showModal"
    title="调整价格比例"
    @on-visible-change="changeVisibleHandle"
    width="820px"
    top="8vh"
    style="min-width: 720px;"
    :mask-closable="false"
    :loading="saveLoading"
  >
    <div v-if="list.length">
      <Table :columns="tableCols" :data="list" size="small" border stripe height="420">
        <template slot-scope="{row, index}" slot="recentlast_purchase_price">
          ¥ {{ row.last_purchase_price }}
        </template>
        <template slot-scope="{row, index}" slot="retailPrice">
          <InputNumber v-model="list[index].newRetailPrice" @on-change="(e)=>changeRetailPrice(e,index)"
                       placeholder="当前零售价" :precision="4" :min="0" :active-change="false"></InputNumber>
        </template>
        <template slot-scope="{row, index}" slot="retailStatus">
          <div>
            <div v-if="row.ratio<2.8" style="display: flex;justify-content: center;align-items: center">
              偏低
              <Icon type="ios-arrow-round-down" style="font-size: 20px;color: red;font-weight: bold;"/>
            </div>
            <div v-else-if="row.ratio>3.2" style="display: flex;justify-content: center;align-items: center">
              偏高
              <Icon type="ios-arrow-round-up"
                    style="font-size: 20px;line-height: normal;color: green;font-weight: bold;"/>
            </div>
            <div v-else>正常</div>
          </div>

        </template>
      </Table>
      <KPage :total="total"
             :page-size-opts="[20]"
             :page-size.sync="tableFormData.pageSize"
             :current.sync="tableFormData.page"
             @on-change="onPageChange"
             style="text-align:right;margin-top: 20px;"
      />
    </div>
    <div v-else class="no-data flex flex-item-align flex-item-center" style="margin-top: 100px;">
      当前所有商品的零售价都处于正常范围，没有要调整的
    </div>
    <div slot="footer">
      <div v-if="list.length">
        <Button @click="restoreRetailPrice" :disabled="isChanged">还原零售价</Button>
        <Button @click="adjustAll" style="margin: 0 10px;">全部调整</Button>
        <Button @click="adjustLowPrice">只调整售价偏低的商品</Button>
        <Button type="primary" @click="okHandle" style="margin-left: 10px;" :loading="submitLoading" :disabled="isChanged">保存并退出</Button>
      </div>

    </div>
  </Modal>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import { $operator } from '@/libs/operation'

export default {

  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    remindDrugs: {
      type: Array,
      default: () => []
    },
    adjustAndSave: {
      type: Function
    }
  },
  data() {
    return {
      loading: false,
      saveLoading: false,
      tableCols: [
        { title: '序号', type: 'index', width: 50 },
        { title: '商品编码', key: 'id', width: 50 },
        { title: '商品名称', key: 'generic_name', minWidth: 50 },
        { title: '最新进货价', slot: 'recentlast_purchase_price', minWidth: 50 },
        { title: '当前零售价', slot: 'retailPrice', minWidth: 50 },
        {
          title: '零售采购比', key: 'ratio', minWidth: 80, renderHeader: ( h, params ) => {
            return h( 'div', {
              style: {
                display: 'flex'
              }
            }, [
              h( 'span', '零售进货价比' ),
              h( 'Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                    transferClassName: 'table-head-tol',
                    maxWidth: 280,
                    width: 280
                  },
                }, [
                  h( 'Icon', {
                    props: {
                      type: 'md-help-circle'
                    },
                    style: {
                      marginLeft: '0px',
                      fontSize: '16px',
                      cursor: 'pointer'
                    }
                  } ),
                  h( 'div', {
                    slot: 'content',
                    style: {
                      // display: 'flex',
                      // flexDirection: 'column',
                      lineHeight: 1.5,
                      // width: '200px',
                      // flexWrap: 'wrap'
                      height: '200px',

                    }
                  }, [
                    h( 'div', {
                      style: {
                        width: '100%',
                        wordBreak: 'break-all',
                        overflowWrap: 'break-word',
                      }
                    }, '注：当前零售采购比仅针对【中药饮片】而言' ),
                    h( 'div', {
                      style: {
                        width: '100%',
                      }
                    }, '零售进货比在2.8-3.2之间，系统判定零售价为正常状态；', ),
                    h( 'div', {
                      style: {
                        width: '100%',
                      }
                    }, ' 小于2.8时，系统判定售价偏低，按此价格售卖会有亏损风险；', ),
                    h( 'div', {
                      style: {}
                    }, '大于3.2时，系统判定售价偏高，销售溢价偏高会影响用户购买率；' ),
                    h( 'div', {}, '如果按照系统规则调整，会将问题商品的零售进货比调整为3，门店可在该基础上自行微调。', {
                      style: {
                        width: '100%',
                      }
                    } )
                  ], )
                ],
              )
            ] )
          }
        },
        { title: '零售价状态', align: 'center', slot: 'retailStatus', minWidth: 40 },
      ],
      btnLoading: false,
      dataSource: [],
      total: 0,
      submitLoading: false,
      tableFormData: {
        page: 1,
        pageSize: 20
      },
      list: [],
      updateIndex: 0
    }
  },

  computed: {
    remindDrugNames() {
      return this.remindDrugs.map( item => item.goodsname ).join( '、' )
    },
    isChanged(){
      const items = this.dataSource.filter( item => Number(item.retail_price) !== item.newRetailPrice )
      return !Boolean(items.length)
    }
  },
  watch: {
    remindDrugs: {
      handler( val ) {
        if ( val.length ) {
          const newVal = cloneDeep( val )
          newVal.map( item => {
            item.newRetailPrice = Number( item.retail_price )
          } )
          this.total = newVal.length
          this.dataSource = newVal
          console.log("-> %c this.dataSource  === %o ", "font-size: 15px", this.dataSource)
          this.list = newVal.slice( 0, 20 )
        }
      }
    }
  },
  methods: {
    getCurrentIndex( index ) {
      const { page, pageSize } = this.tableFormData
      return $operator.multiply( page - 1, pageSize ) + index
    },
    tableScrollToTop() {
      this.$nextTick( () => {
        document.getElementsByClassName( 'ivu-table-body' )[0].scroll( 0, 0 )
      } )
    },
    onPageChange( page, pageSize ) {
      console.log( '-> %c page,pageSize  === %o ', 'font-size: 15px', page, pageSize )
      this.tableFormData.page = page
      this.list = this.dataSource.slice( $operator.multiply( page - 1, 20 ), $operator.multiply( page, 20 ) )
      console.log( this.dataSource )
      this.tableScrollToTop()
    },
    //还原零售价 1.还原零售价 ---- 还原到出现该弹窗时的初始价格
    restoreRetailPrice() {
      this.list.map( item => {
        console.log( '-> %c item  === %o ', 'font-size: 15px', item )
        item.newRetailPrice = Number( item.retail_price )
        item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.last_purchase_price ) )
      } )
      this.dataSource.map( item => {
        item.newRetailPrice = Number( item.retail_price )
        item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.last_purchase_price ) )
      } )
    },
    //全部调整     * 2.全部调整 ---- 将比例>3.2和<2.8的所有商品，都调整为3
    adjustAll() {
      console.log($operator.divide(200,0,4))
      this.dataSource.map( item => {
        item.newRetailPrice = $operator.multiply( item.last_purchase_price, 3, 4 )
        item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.last_purchase_price ) )
        console.log("-> %c item  === %o ", "font-size: 15px", item)
      } )
      this.list.map( item => {
        item.newRetailPrice = $operator.multiply( item.last_purchase_price, 3, 4 )
        item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.last_purchase_price ) )
      } )
    },
    //只调整售价偏低的商品  3.只调整售价偏低的商品  ---- 将比例<2.8的所有商品，都调整为3
    adjustLowPrice() {
      this.dataSource.map( item => {
        if ( item.ratio < 2.8 ) {
          item.newRetailPrice = $operator.multiply( item.last_purchase_price, 3, 4 )
          item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.last_purchase_price ) )
        }
      } )
      this.list.map( item => {
        if ( item.ratio < 2.8 ) {
          item.newRetailPrice = $operator.multiply( item.last_purchase_price, 3, 4 )
          item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.last_purchase_price ) )
        }
      } )
    },
    //调整零售价
    changeRetailPrice( val, index ) {
      console.log( '-> %c val, index  === %o ', 'font-size: 15px', val, index )
      console.log( this.getCurrentIndex( index ) )
      !val && (val = 0)
      console.log( '-> %c val,index  === %o ', 'font-size: 15px', val, index )
      this.list[index].ratio = $operator.divide( val, Number( this.list[index].last_purchase_price ) )
      this.dataSource[this.getCurrentIndex( index )].ratio = $operator.divide( val, Number( this.dataSource[this.getCurrentIndex( index )].last_purchase_price ) )
    },
    // 保存并退出  4.保存并退出 ---- 系统批量修改这部分商品的零售价，并关闭该弹窗，结束这次的入库流程
    okHandle() {
      const items = this.dataSource.filter( item => Number(item.retail_price) !== item.newRetailPrice )
      console.log("-> %c items  === %o ", "font-size: 15px", items)
      if ( items.length ) {
        this.submitLoading = true
        this.batchUpdateRetail()
      } else {
        this.changeVisibleHandle(false)
      }
    },
    batchUpdateRetail() {
      const items = this.dataSource.filter( item => Number(item.retail_price) !== item.newRetailPrice ).map( item => ({
        prod_id: item.id,
        new_price: item.newRetailPrice,
      }) )
      this.saveLoading = true
      let chunkItems = this.$lodash.chunk( items, 50 )
      console.log("-> %c chunkItems  === %o ", "font-size: 15px", chunkItems)
      let len = chunkItems.length
      if ( len === this.updateIndex) {
        this.saveLoading = false
        this.submitLoading = false
        this.changeVisibleHandle( false )
        this.$Message.success( '批量调整零售价成功' )
      } else {
        this.updateFun( chunkItems[this.updateIndex] )
      }
    },
    updateFun( items ) {
      console.log("-> %c items  === %o ", "font-size: 15px", items)
      this.$api.updateBatchGoodsPrice( { items } ).then( res => {
        console.log( '-> %c res  === %o ', 'font-size: 15px', res )
        this.updateIndex++
        this.batchUpdateRetail()
      }, error => {

        this.submitLoading = false
      } ).finally( () => {

      } )
    },
    changeVisibleHandle( visible ) {
      console.log( '-> %c visible  === %o ', 'font-size: 15px', visible )
      if(!visible){
        this.list = []
        !visible && this.$emit( 'update:showModal', false )
        this.updateIndex = 0
        this.total = 0
        this.tableFormData.page = 1
      }
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  height: 500px;
  overflow-y: auto;
}

::v-deep .ivu-modal-footer {
  display: block !important;
}

</style>
