<template>
  <Modal
    ref="customModal"
    :closable="closable"
    :value="value"
    width="800px"
    :footer-hide="true"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div slot="header">
      <!-- 搜索 -->
      <div class="search-box">
        <Input
          ref="searchInput"
          v-model="searchName"
          @keydown.native="stopPreventDefault"
          @keyup.native.down.stop="keydown"
          @keyup.native.up.stop="keyup"
          @input="handleInput"
          @keyup.native.enter="search"
          placeholder="例：三七"
          class="search-input"
        />
        <Icon type="md-close" size="18" class="close" v-if="searchName" @click="clear" />
        <Button type="primary" class="search-btn" @click="search" :disabled="!searchName" :loading="searchLoading"
          >搜索</Button
        >
      </div>
    </div>
    <div class="default-content" v-if="!searchName">从模板库中搜索相关名称</div>
    <div class="content" v-else>
      <Table
        :loading="searchLoading"
        :columns="defaultTableCols"
        :row-class-name="rowClassName"
        :show-header="false"
        :data="list"
        height="400"
        @keyup.native.enter="keyenter"
        @on-row-click="rowClick"
      >
        <template slot-scope="{ row }" slot="generic_name" tabindex="0">
          <div class="flex flex-item-align" style="margin-left: 10px">
            <img
              v-if="row.is_bind === '1'"
              class="has-add-icon"
              src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1022/182455_31565.png"
            />
            <div>{{ row.generic_name || '-' }}</div>
          </div>
        </template>

        <template slot-scope="{ row }" slot="prod_type_desc">
          <div>{{ row.prod_type_desc || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="grade_desc">
          <div>{{ row.grade_desc || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="prod_spec">
          <div>{{ row.prod_spec || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="manufacturer">
          <div>{{ row.manufacturer || '-' }}</div>
        </template>
      </Table>
    </div>
  </Modal>
</template>

<script>
import { debounce } from 'lodash-es';
export default {
  name: 'templateCreate',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      searchName: '',
      searchLoading: false,
      defaultTableCols: [
        { title: '货品', slot: 'generic_name', minWidth: 150 },
        { title: '类型', slot: 'prod_type_desc', minWidth: 80 },
        { title: '品级', slot: 'grade_desc', minWidth: 100 },
        { title: '规格', slot: 'prod_spec', minWidth: 100 },
        { title: '产地/厂家', slot: 'manufacturer', minWidth: 150 },
      ],
      list: [],
      activeIndex: -1,
    };
  },

  computed: {
    // 是否开启ecs关闭功能
    closable() {
      return !this.searchName;
    },
  },

  watch: {
    searchName(val) {
      if (!val) {
        this.list = [];
      }
    },
  },

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    stopPreventDefault(event) {
      let keyCode = event.keyCode;
      if (keyCode === 38 || keyCode === 40) {
        event.preventDefault();
      }
    },
    keydown(event) {
      event.preventDefault();
      let length = this.list.length;
      if (length == 0) return;
      this.activeIndex = this.directToIndex1(this.activeIndex + 1, 'DOWN');
    },

    keyup(event) {
      event.preventDefault();
      let length = this.list.length;
      if (length == 0) return;
      this.activeIndex = this.directToIndex1(this.activeIndex - 1, 'UP');
    },

    directToIndex1(index, type = 'DOWN') {
      let is_has = this.list.some(item => item.is_bind != '1');
      if (!is_has) return -1;
      let length = this.list.length;
      let nextIndex = 0;
      if (index > length - 1) {
        nextIndex = 0;
      } else if (index < 0) {
        nextIndex = length - 1;
      } else {
        nextIndex = index;
      }
      let is_bind = this.list[nextIndex].is_bind;
      if (is_bind === '1') {
        return this.directToIndex1(type === 'DOWN' ? nextIndex + 1 : nextIndex - 1, type);
      } else {
        return nextIndex;
      }
    },

    enterCheck() {
      if (this.activeIndex === -1) return;
      let row = this.list[this.activeIndex] || {};
      this.$emit('getItem', row);
      this.closeModal();
    },
    clear() {
      this.searchName = '';
    },
    // 给表格行设置样式
    rowClassName(row, index) {
      if (row.is_bind === '1') {
        return 'has-add';
      } else if (index === this.activeIndex) {
        return 'actived';
      } else {
        return '';
      }
    },
    rowClick(row) {
      if (row.is_bind === '1') return;
      this.$emit('getItem', row);
      this.closeModal();
    },
    handleInput(value) {
      // 手动去除空格并更新值
      this.$nextTick(() => {
        this.searchName = value.trim();
        this.search();
      });
    },
    search: debounce(function () {
      if (!this.searchName) return;
      if (this.activeIndex != -1) {
        this.enterCheck();
        return;
      }
      let params = {
        generic_name: this.searchName,
      };
      this.$api.getProductTemplateprod(params).then(res => {
        this.list = res.list;
        this.activeIndex = -1;
      });
    }, 300),

    changeVisible(visible) {
      if (visible) {
        this.$nextTick(() => {
          this.$refs.searchInput.focus();
        });
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.searchName = '';
      this.activeIndex = -1;
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-close {
  display: none;
}
::v-deep .ivu-table-row {
  cursor: pointer;
}
.default-content {
  height: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 14px;
  color: #909399;
}
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 150px;
  padding: 0px;
  overflow-y: auto;
  border-radius: 4px;
  &::-webkit-scrollbar {
    display: none;
  }
}
::v-deep {
  .ivu-modal-header {
    box-sizing: border-box;
    height: 64px;
    padding: 12px 16px;

    .search-box {
      width: 100%;
      display: flex;
      align-items: center;
      .search-input {
        flex: 1;
        max-width: none;
        .ivu-input {
          height: 40px;
          border: none;
          box-shadow: none;
          font-weight: 400;
          font-size: 16px;
          color: #303133;
          line-height: 22px;
          padding-right: 30px;
        }
      }
      .close {
        color: #dcbcbc;
        margin-left: 60px;
        padding: 2px;
        border-radius: 50%;
        cursor: pointer;
        &:hover {
          color: #514c4c;
          background: #ece4e4;
        }
      }
      .search-btn {
        margin-left: 20px;
        width: 92px;
        height: 40px;
        background: #3088ff;
        border-radius: 4px;
        border: none;
        background-image: none;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 22px;
      }
    }
  }
}
.has-add-icon {
  width: 16px;
  min-width: 16px;
  height: 16px;
  margin-right: 12px;
}
::v-deep .ivu-btn-primary[disabled] {
  background: #f7f7f7 !important;
  color: #c5c8ce !important;
}

//::v-deep .ivu-input-icon {
//  width: 24px;
//  height: 24px;
//  line-height: 20px;
//  margin-top: 10px;
//}
//::v-deep .ivu-icon-ios-close-circle:before {
//  content: "\f178";
//  font-size: 26px;
//  display: block;
//  cursor: pointer;
//}
// 禁用的表格行置灰样式
::v-deep .ivu-table .has-add {
  &:hover {
    td {
      background: transparent !important;
    }
  }
  td {
    color: #c0c4cc;
    cursor: not-allowed;
    a,
    p,
    div,
    span {
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}

// 选中的行样式
::v-deep .ivu-table .actived {
  td {
    background: #f1f6fe;
  }
}
</style>
