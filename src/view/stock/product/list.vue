<template>
  <div ref="tableWrapRef" class="global-list-box" @mouseover="hidePop">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      size="small"
      stripe
      :row-class-name="rowClassName"
      class="pl-table"
      ref="pl-table"
      @on-sort-change="sortChanged"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template slot="header">
        <!-- 搜索区域 -->
        <div
          class="global-list-search"
          ref="searchRefs"
          :style="{ height: expandStatus === 'expand' ? 'auto' : '52px' }"
        >
          <!-- 搜索条件区域 -->
          <div class="global-list-search-condition">
            <Form
              id="searchForm"
              class="form-warpper_left"
              inline
              :label-width="0"
              @submit.native.prevent
              @keyup.enter.native="onSearch"
            >
              <FormItem>
                <Input type="text" v-model="queryFormData.name" placeholder="货品名称" />
              </FormItem>

              <FormItem>
                <Select
                  style="width: 240px"
                  v-model="queryFormData.prod_type"
                  placeholder="全部类型"
                  clearable
                  max-tag-count="2"
                  :multiple="is_rst"
                >
                  <Option v-if="!is_rst" value="">全部类型</Option>
                  <Option :value="item.id" v-for="item in prodTypes" :key="item.id">{{ item.name }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.prod_source" placeholder="全部来源">
                  <Option value="">全部来源</Option>
                  <Option :value="index" v-for="(item, index) in prodSource" :key="index">{{ item.desc }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.status" style="" placeholder="全部状态">
                  <Option value="">全部状态</Option>
                  <Option :value="index" v-for="(item, index) in prodStatusDesc" :key="index">{{ item.desc }}</Option>
                </Select>
              </FormItem>
            </Form>
          </div>
          <!-- 搜索条件功能区域 -->
          <div class="global-list-search-operate">
            <Button class="search-btn" type="primary" @click="onSearch">筛选</Button>
            <span class="list-reset-btn" @click="initSearch"
              ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
            >
            <div class="expand-or-collapse" v-if="isShowExpandDom" @click="expandCollapseEvent">
              <span>{{ expandStatus === 'expand' ? '收起筛选' : '更多筛选' }}</span>
              <img v-if="expandStatus === 'collapse'" src="@/assets/image/list/expand.png" />
              <img v-else src="@/assets/image/list/collapse.png" />
            </div>
          </div>
        </div>

        <!-- 功能区域 -->
        <div class="global-list-function flex flex-item-align" ref="functionRefs">
          <Button type="primary" class="mr10" v-if="!is_rst_opc" :to="{ path: '/stock/product/edit' }">+ 新建货品</Button>
          <Button type="default" :loading="downloadLoading" @click="downloadFile">
            <img class="export-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1024/101143_96325.png" />
            导出
          </Button>
        </div>
      </template>

      <template slot-scope="{ row }" slot="id">
        {{ row.id }}
      </template>

      <template slot-scope="{ row }" slot="prod_name">
        <!--  数字化非榕树堂和普通诊所弹窗-->
        <template v-if="!is_rst_opc">
          <div v-if="row.generic_name">
            <Tooltip :content="row.generic_name" width="100">
              <div class="ecs a-plus">
                <k-link :to="{ path: '/stock/product/edit', query: { id: row.id } }" target="_blank">
                  {{ row.generic_name }}
                </k-link>
              </div>
            </Tooltip>
          </div>
          <div v-else>-</div>
        </template>
        <!--  数字化榕树堂特定弹窗-->
        <template v-else>
          <div class="custom-k-lint" v-if="row.generic_name">
            <k-link :to="{ path: '/stock/product/edit', query: { id: row.id } }" target="_blank">
              <div @mouseenter="e => showPop('custom-tooltip', e, row)" class="link-text">{{ row.generic_name }}</div>
            </k-link>
          </div>
          <div v-else>-</div>
        </template>
      </template>

      <!--      库存-->
      <template slot-scope="{ row }" slot="stock_num">
        {{ row.stock_text || '-' }}
      </template>

      <!--      来源-->
      <template slot-scope="{ row }" slot="from_text">
        {{ row.from_text || '-' }}
      </template>

      <!--      总价值-->
      <!--        <template slot-scope="{ row }" slot="total_money">-->
      <!--          {{ row.total_money ? '¥ ' + row.total_money : '-' }}-->
      <!--        </template>-->
      <!--      单位-->
      <!--        <template slot-scope="{ row }" slot="prod_unit">-->
      <!--          {{ row.prod_unit || '-' }}-->
      <!--        </template>-->
      <!--      类型-->

      <template slot-scope="{ row }" slot="prod_type">
        <Tooltip :content="row.prod_type_text" :disabled="row.prod_type_text?.length < 5">
          <span class="ecs" :class="{ cursor: row.prod_type_text?.length > 4 }">{{ row.prod_type_text || '-' }}</span>
        </Tooltip>
      </template>

      <template slot-scope="{ row }" slot="prod_spec">
        {{ row.prod_spec || '-' }}
      </template>

      <template slot-scope="{ row }" slot="grade_desc">
        {{ row.grade_desc || '-' }}
      </template>

      <!-- 厂家-->
      <template slot-scope="{ row }" slot="manufacturer">
        <Tooltip :content="row.manufacturer" v-if="row.manufacturer" max-width="300">
          <span class="cursor ecs">{{ row.manufacturer }}</span>
        </Tooltip>
        <span v-else>-</span>
      </template>

      <!-- 厂家-->
      <template slot-scope="{ row }" slot="container_number">
        {{ row.container_number || '-' }}
      </template>

      <template slot-scope="{ row }" slot="insure_level_desc">
        {{ row.insure_level_desc || '-' }}
      </template>

      <!--      状态-->
      <template slot-scope="{ row }" slot="status">
        <mark-status :type="getStatusTextColor(row.status)">
          {{ prodStatusDesc[row.status] ? prodStatusDesc[row.status].desc : '' }}
        </mark-status>
      </template>

      <!--零售价-->
      <template slot-scope="{ row }" slot="retail_price">
        <span v-if="row.retail_price">
          <Tooltip>
            <span>¥ {{ row.retail_price }}</span>
            <div slot="content">
              <div>¥{{ row.retail_price }} / {{ row.prod_unit }}</div>
            </div>
          </Tooltip>
        </span>
        <span v-else>-</span>
      </template>

      <!--      拆零比-->
      <template slot-scope="{ row }" slot="split_ratio">
        {{ row.is_split === '1' && row.split_num && row.split_num !== '0' ? Number(row.split_num) : '-' }}
      </template>

      <!--      拆零价-->
      <template slot-scope="{ row }" slot="split_price">
        <span v-if="row.is_split == 1">
          <Tooltip>
            <span>¥ {{ row.split_price }}</span>
            <div slot="content">
              <div>¥{{ row.split_price }} / {{ row.split_prod_unit }}</div>
            </div>
          </Tooltip>
        </span>
        <span v-else>-</span>
      </template>

      <!--      拆零单位-->
      <template slot-scope="{ row }" slot="split_prod_unit">
        <span v-if="row.is_split == 1"> {{ row.prod_unit }} / {{ row.split_prod_unit }} </span>
        <span v-else>-</span>
      </template>

      <!--      上次采购价-->
      <template slot-scope="{ row }" slot="last_purchase_price">
        {{ row.last_purchase_info.price ? '¥ ' + row.last_purchase_info.price : '-' }}
      </template>

      <!--      采购零售比-->
      <template slot-scope="{ row }" slot="retail_purchase_ratio">
        {{
          row.prod_type == 2 && row.last_purchase_info.retail_purchase_ratio
            ? row.last_purchase_info.retail_purchase_ratio
            : '-'
        }}
      </template>

      <template slot-scope="{ row }" slot="operate">
        <Poptip v-if="row.status == 7" confirm transfer title="确定启用吗?" @on-ok="onChangeStatus(row.id, 'ENABLE')">
          <a href="javascript:;">启用</a>
        </Poptip>
        <template v-else>
          <Poptip confirm transfer title="确定停用吗?" @on-ok="onChangeStatus(row.id, 'DISABLE')">
            <a href="javascript:;" v-if="row.source != 9">停用</a>
          </Poptip>
          <span class="readText ml2" v-if="row.source == 9">停用</span>
        </template>
        <router-link :to="{ path: '/stock/product/edit', query: { id: row.id } }" class="ml6">详情</router-link>
      </template>
    </standard-table>

    <Tooltip
      ref="custom-tooltip"
      popper-class="generic-name-link-tip"
      transfer-class-name="generic-name-link-tip"
      :reference="tooltipReference"
      :delay="1000"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <generic-name-tip :row="selectedToolTipRow" />
      </template>
    </Tooltip>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import KExcelUpload from '@/components/k-excel-upload/excel';
/* eslint-disable */
import downloadExceL from '@/mixins/downloadExcel';
import search from '@/mixins/search';
import tooltip from '@/view/stock/mixins/tooltip';
import {getInsureStatus, isRstClinic, isRstOpcClinic} from '../../../libs/runtime';
import { debounce } from 'lodash-es';
import StandardTable from '@/components/StandardTable/index.vue';
import GenericNameTip from '@/view/stock/components/generic-name-tip.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status: '',
  prod_type: '',
  sort_field: '',
  sort: '',
  is_get_last_ostock: 1,
  prod_source: '', //来源
};

export default {
  name: 'list',
  components: {
    GenericNameTip,
    StandardTable,
    KExcelUpload,
  },
  mixins: [downloadExceL, search, tooltip],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getInStockProductList',
      tableLoading: true,

      downloadApiName: 'getprodstockurl', // 导出接口名称
      abnormalItems: [], //采购价格与零售价比例异常
      remindVisible: false, //提醒弹窗
      adjustVisible: false, //价格调整弹窗
      editLoading: false,
      exceptionsPriceList: [], //需要调整价格列表

      importVisible: false, // 导入商品销售价modal
      reportVisible: false,
      exportLoading: false, // 模板下载loading
      excelUploadLoading: false, // 数据上传的loading
      handleExcelList: [], // 处理后要上传的excel数据
      hasHandleExcelList: [], // 准备导入的数据
      isImportSuccess: false, // 是否导入成功

      list: [],
      total: 0,
      prodTypes: [],
      prodSource: {},
      prodStatusDesc: {},
    };
  },
  created() {
    this.getStatusList().then(() => {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      this.submitQueryForm(true);
    });
    if (getInsureStatus()) {
      this.tableCols.splice(6, 0, { title: '医保标识', slot: 'insure_level_desc', minWidth: 80, align: 'center' });
    }
  },
  computed: {
    is_rst() {
      return isRstClinic();
    },
    is_rst_opc() {
      return isRstOpcClinic();
    },
    tableCols() {
      return [
        { title: '编码', slot: 'id', minWidth: 80, align: 'center' },
        { title: '货品', slot: 'prod_name', minWidth: 140, align: 'center' },
        { title: '类型', slot: 'prod_type', minWidth: 120, align: 'center' },
        { title: '品级', slot: 'grade_desc', minWidth: 80, align: 'center' },
        { title: '规格', slot: 'prod_spec', minWidth: 120, align: 'center' },
        { title: this.is_rst_opc ? '生产厂家' : '厂家', slot: 'manufacturer', minWidth: 120, align: 'center' },
        { title: '柜号', slot: 'container_number', minWidth: 100, align: 'center' },
        { title: '来源', slot: 'from_text', minWidth: 100, align: 'center' },
        { title: '库存', slot: 'stock_num', sortable: 'custom', minWidth: 100, align: 'center' },
        // { title: '库存价值', slot: 'total_money', sortable: 'custom',  minWidth: 100, },
        {
          title: this.is_rst_opc ? '单价' : '零售价',
          slot: 'retail_price',
          sortable: 'custom',
          minWidth: 100,
          align: 'center',
        },
        {
          title: this.is_rst_opc ? '单价（拆零）' : '拆零价',
          slot: 'split_price',
          sortable: 'custom',
          minWidth: this.is_rst_opc ? 120 : 100,
          align: 'center',
        },
        { title: '拆零比', slot: 'split_ratio', minWidth: 100, align: 'center' },
        {
          title: this.is_rst_opc ? '包装单位/最小计价单位' : '零售/拆零单位',
          slot: 'split_prod_unit',
          minWidth: 100,
          align: 'center',
        },
        { title: '状态', slot: 'status', minWidth: 80 },
        { title: '操作', slot: 'operate', width: 100, align: 'center', fixed: 'right' },
      ];
    },
    getStatusTextColor() {
      return status => {
        switch (status) {
          case '1':
            return 'success';
          case '7':
            return 'gray';
        }
      };
    },
    isActiveBtn() {
      if (this.hasHandleExcelList.length > 0 && !this.isImportSuccess) {
        return false;
      } else {
        return true;
      }
    },
  },
  watch: {
    importVisible(val) {
      if (!val) {
        this.hasHandleExcelList = [];
        this.isImportSuccess = false;
      }
    },
    hasHandleExcelList(val) {
      if (val) {
        // 如果重新选择了表格,将参数置为false,视为重新上传
        this.isImportSuccess = false;
      }
    },
  },
  methods: {
    // 给表格行设置样式
    rowClassName(row, index) {
      return row.status == 7 ? 'del-cell' : '';
    },
    downloadFile() {
      this.downloadExcel(this.queryFormData);
    },
    modifyRetailPurchaseRatio() {
      this.exceptionsPriceList = [];
      this.editLoading = true;
      this.$api
        .getExceptionsPriceList()
        .then(res => {
          console.log('-> %c res  === %o ', 'font-size: 15px', res);
          this.exceptionsPriceList = res.list;
          this.adjustVisible = true;
        })
        .finally(() => (this.editLoading = false));
    },
    adjustAndSave() {
      this.submitForm();
    },
    handleSaveParams() {
      let values = this.formValidate;
      let params = {
        ostock_type: 10,
        prod_type: this.$route.query.prod_type,
        order_code: values.orderCode,
        supplier_id: values.supplier,
        operator_name: values.user,
        arrival_time: moment(values.arrivalTime).format('YYYY-MM-DD'),
        inspection_time: moment(values.inspectionTime).format('YYYY-MM-DD'),
        remark: values.remark,
      };
      let items = this.localList.map((item, i) => {
        return {
          prod_id: item.number,
          prod_stock_id: item.prod_stock_id,
          batch_code: item.batchNumber,
          purchase_stock_num: item.quantity,
          purchase_price: item.purchasePrice,
          produce_time: item.productionDate,
          expire_time: item.termOfValidity,
          invoice_code: item.invoiceNo,
          // manufacturer: item.manufactor,
          warehouse_unit: item.warehouse_unit || item.prod_unit,
        };
      });
      params.items = items;
      if (this.id) {
        params.id = this.id;
      }
      return params;
    },
    submitForm(isAdjust) {
      isAdjust && (this.remindVisible = false);
      this.subBtnLoading = true;
      this.$api
        .updateBatchGoodsPrice(this.handleSaveParams())
        .then(
          data => {
            this.$Message.success({
              content: '提交成功',
              onClose: () => {},
            });
            this.$router.replace('/stock/ostock/list');
          },
          error => {}
        )
        .finally(() => {
          this.subBtnLoading = false;
        });
    },
    sortChanged({ column: { slot }, order }) {
      console.log('-> %c order  === %o ', 'font-size: 15px', order);
      if (order === 'normal') {
        order = '';
      }
      if (slot) {
        this.queryFormData.sort_field = slot;
        this.queryFormData.sort = order;
        this.getsList();
      } else {
        this.$Message.error('无效排序字段');
      }
    },

    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    onChangeStatus: function (id, action) {
      io.post('clinic/product.product.status', { id: id, act: action })
        .then(() => {
          this.$Message.success('操作成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
    },
    getStatusList() {
      let res = io.get('/clinic/product.product.options');
      res
        .then(data => {
          // console.log(data,"data");
          this.prodTypes = S.descToArrHandle(data.prodTypes).sort((a, b) => a.sort - b.sort);
          this.prodStatusDesc = data.prodStatusDesc;
          this.prodSource = data.prodSource;
        })
        .catch(error => {
          {
          }
        });
      return res;
    },
    handleList: function (list) {
      return list;
    },
    initSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.$refs['pl-table'].handleSort(0, 'normal');
      this.submitQueryForm();
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.import-content {
  height: 160px;

  p {
    margin-bottom: 0px;
  }

  .choose {
    color: rgba(17, 87, 229, 0.5);
  }

  .download {
    margin-left: 20px;
    color: rgb(155, 141, 141);
    border-bottom: 1px solid #ccc;
  }

  .error-report {
    margin-top: 20px;

    .download-error {
      margin-left: 15px;
      color: rgba(17, 87, 229, 0.5);
    }
  }
}

.readText {
  color: #ccc;
}

.ml2 {
  margin-left: 2px;
}

.ml6 {
  margin-left: 6px;
}
</style>

<style lang="less" scoped>
.table-fun {
  margin: -10px 0 10px;
}

// common less
.cursor {
  cursor: pointer;
}

.hover {
  &:hover {
    color: #155bd4 !important;
  }
}

/deep/ .ivu-modal-footer {
  display: none;
}

.generic-name {
  color: #696969;
}

.custom-k-lint {
  overflow: unset;
  text-overflow: unset;
  white-space: pre-wrap;
  word-break: break-all;
  :deep(a) {
    overflow: unset;
    text-overflow: unset;
    white-space: pre-wrap;
    word-break: break-all;
  }
  :deep(.link-text) {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.ecs {
  white-space: normal !important;
}
</style>
<style lang="less">
.ivu-tooltip-inner {
  white-space: normal !important;
  max-width: 250px;
}

.el-input__icon {
  line-height: 34px;
}

.generic-name-link-tip {
  .ivu-tooltip-inner {
    max-width: unset;
    background-color: #fffdec;
  }
}
</style>
