<template>
  <div v-loading="pageLoading" class="app-page-wrapper">
    <!-- Header -->

    <div v-show="!pageLoading">
      <div class="header flex gap-20">
        <div class="flex flex-item-align">
          <div>统计时间：</div>
          <customDatePicker
            v-model="formDate"
            defaultType="date"
            :type-options="['date', 'week', 'month']"
            @getDateType="getDateType"
          ></customDatePicker>
        </div>

        <div>
          <Button size="small" type="primary" style="width: 106px; height: 32px" @click="onReport">
            <img class="export-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0702/154428_97382.png" />
            导出数据
          </Button>
        </div>
      </div>

      <div>
        <div class="table-item" v-for="(item, key) in tableList" :key="key">
          <div class="title">
            <div class="line"></div>
            {{ item.title }}
            <!-- always -->
            <Tooltip max-width="410" theme="light" placement="bottom" class="custom-tooltip">
              <div slot="content">
                <div class="tip-content" v-html="item.tip"></div>
              </div>
              <!-- <img src="@/assets/printLogo.svg" /> -->
              <div class="tip-icon">
                <img style="width: 14px; height: 14px" src="../../assets/image/help_icon.png" />
              </div>
              <!-- <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon> -->
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(item.columns.length) }"
            :loading="loading"
            :columns="item.columns"
            :data="item.data"
          ></Table>
        </div>
      </div>
    </div>

    <ReportExport v-model="reportModalVisible" title="经营报表" :api-name="apiName"></ReportExport>
  </div>
</template>

<script>
import tip from './data/tip';
import ReportExport from './components/ReportExport';
import customDatePicker from '@/view/statistics/components/CustomDatePicker/CustomDatePicker.vue';

export default {
  name: 'Physio',
  components: {
    customDatePicker,
    ReportExport,
  },
  data() {
    return {
      reportModalVisible: false,
      loading: false,
      pageLoading: false,
      apiName: 'getBizReport',

      formDate: [],
      month: '',
      date_type: 'month',
      typeOpts: [
        {
          id: 'day',
          desc: '日报',
        },
        {
          id: 'week',
          desc: '周报',
        },
        {
          id: 'month',
          desc: '月报',
        },
      ],

      date: '',

      type: 'month',
      format: '',

      st: '',
      et: '',

      //表格基本数据
      colMap: {
        overview: {
          title: '概况',
          tip: tip.generalTip,
        },
        revenueFlow: {
          title: '业绩分析',
          tip: tip.flowTip,
        },
        transaction: {
          title: '交易额分析',
          tip: tip.sumTip,
        },
        revenue: {
          title: ' 营收(实耗)分析',
          tip: tip.revenTip,
        },
        storeValue: {
          title: '储值分析',
          tip: tip.storeTip,
        },
        liHeader: {
          title: '理疗分析',
          tip: tip.treatTip,
        },
        customerGroupHeader: {
          title: '客群分析',
          tip: tip.cusTip,
        },
        newMemberHeader: {
          title: '新客来源',
          tip: tip.treatTip,
        },
        reservationHeader: {
          title: '预约分析',
          tip: tip.appTip,
        },
      },

      tableList: [],
    };
  },
  watch: {
    formDate: {
      handler(val) {
        console.log('🚀 ~ manage.vue:139 ~ handler ~ val---->', val);
        if (val && val[0]) {
          this.st = val[0];
          this.et = val[1];
          this.getList();
        }
      },
    },
  },
  mounted() {
    this.scrollTop();
  },
  activated() {
    this.scrollTop();
  },
  methods: {
    tableWidth(length, minWidth = 150) {
      // 根据 colums 的数量和每列的最小宽度来计算
      return `${length * minWidth}px`;
    },
    scrollTop() {
      this.$nextTick(() => {
        const wrapper = document.querySelector('.app-wrapper');
        if (wrapper) wrapper.scrollTop = 0;
      });
    },
    getDateType(getDateType) {
      this.date_type = getDateType;
    },

    generateTable(data) {
      // 生成表头
      const columns = data.map(item => ({
        title: item.title,
        key: item.title,
        align: 'center',
        width: '150',
        minWidth: '150',
        maxWidth: '150',
        render: (h, params) => h('span', params.row[item.title]),
      }));

      // 生成表格数据（只有一行）
      const row = {};
      data.forEach(item => {
        row[item.title] = item.value;
      });

      return {
        columns: columns,
        data: [row],
      };
    },
    getList() {
      this.loading = true;
      this.pageLoading = true;
      this.$api
        .getBizData({
          st: this.st,
          et: this.et,
          date_type: this.date_type,
        })
        .then(res => {
          const list = [];

          list.push({
            ...this.colMap['overview'],
            ...this.generateTable(res.overview),
          });

          list.push({
            ...this.colMap['revenueFlow'],
            ...this.generateTable(res.revenueFlow),
          });

          list.push({
            ...this.colMap['revenue'],
            ...this.generateTable(res.revenue),
          });

          list.push({
            ...this.colMap['transaction'],
            ...this.generateTable(res.transaction),
          });

          list.push({
            ...this.colMap['storeValue'],
            ...this.generateTable(res.storeValue),
          });

          list.push({
            ...this.colMap['liHeader'],
            ...this.generateTable(res.liHeader),
          });
          list.push({
            ...this.colMap['customerGroupHeader'],
            ...this.generateTable(res.customerGroupHeader),
          });
          list.push({
            ...this.colMap['newMemberHeader'],
            ...this.generateTable(res.newMemberHeader),
          });
          list.push({
            ...this.colMap['reservationHeader'],
            ...this.generateTable(res.reservationHeader),
          });

          // TODO 对象便利会出现顺序问题
          // for (const key in res) {
          //   if (Object.prototype.hasOwnProperty.call(res, key)) {
          //     if (key !== 'data') {
          //       const item = res[key];
          //       list.push({
          //         ...this.colMap[key],
          //         ...this.generateTable(item),
          //       });
          //     }
          //   }
          // }

          this.tableList = list;
        })
        .finally(() => {
          this.loading = false;
          this.pageLoading = false;
        });
    },

    onReport() {
      this.reportModalVisible = true;
    },
  },
};
</script>

<style scoped lang="less">
.el-select {
  border-radius: 4px;
}
.gap-20 {
  gap: 20px;
}

.gap-10 {
  gap: 10px;
}

.export-icon {
  width: 14px;
  height: 13px;
  margin-right: 6px;
  // background-color: #fff;
}

.header {
  // gap: 16px;
  // position: sticky;
  // top: -24px;
  // z-index: 9;
  // padding: 20px 0px 16px 0px;
  // background-color: #fff;
  // justify-content: space-between;
}

.app-page-wrapper {
  position: relative;
  background-color: #fff;
  min-height: 90vh;
  padding: 24px;
  margin-top: 10px;
  z-index: 2;

  .table-item {
    margin-top: 36px;

    // max-width: 1000px;

    .title {
      display: flex;
      gap: 8px;

      .line {
        width: 4px;
        height: 18px;
        background: linear-gradient(90deg, #155bd4 0%, #4988fd 100%);
        border-radius: 2px;
      }

      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin-top: 14px;
      margin-bottom: 18px;
    }
  }
}

.tip-content {
  line-height: 20px;
  font-size: 13px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  span {
    font-size: 15px;
  }
}

:deep(.el-input__inner) {
  border-radius: 4px !important;
}

.tip-icon {
  display: flex;
  flex: 1;
  align-items: center;
}
</style>
