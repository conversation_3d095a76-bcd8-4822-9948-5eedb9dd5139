<template>
  <div class="wrapper">
    <!-- Header -->
    <header class="head-block ceiling">
      <div class="flex flex-item-align">
        <p>选择月份：</p>
        <customDatePicker v-model="formDate" :type-options="['month']" @getDateType="getDateType"></customDatePicker>
      </div>
    </header>

    <!-- 交易额分析 -->
    <template>
      <div class="light-title">交易额分析</div>
      <div class="flex mt16" style="min-height: 260px">
        <div class="block block-bg flex-1" :style="{ backgroundImage: `url(${money_bg})` }">
          <h4 class="block-title flex flex-align-center">总交易额</h4>
          <div class="block-content-desc">¥{{ total.turnover || 0 }}</div>
        </div>

        <div class="block ml16 flex-2">
          <h4 class="block-title flex flex-align-center">新老客交易额</h4>
          <div class="width100">
            <chart-view
              height="260px"
              :chart-option="turnover_new_old_customer"
              v-if="isShowLineEcharts(turnover_new_old_customer, 'pie')"
            ></chart-view>
            <div class="empty" v-else>暂无数据</div>
          </div>
        </div>

        <div class="block ml16 flex-2">
          <h4 class="block-title flex flex-align-center">会员和非会员交易额</h4>
          <div class="width100">
            <chart-view
              height="260px"
              :chart-option="turnover_vip_or_none"
              v-if="isShowLineEcharts(turnover_vip_or_none, 'pie')"
            ></chart-view>
            <div class="empty" v-else>暂无数据</div>
          </div>
        </div>
      </div>
    </template>

    <!-- 交易额趋势 -->
    <div class="block mt16" style="min-height: 300px">
      <h4 class="block-title flex flex-align-center">交易额趋势</h4>
      <div class="width100">
        <chart-view
          height="300px"
          :chart-option="turnover_options"
          v-if="isShowLineEcharts(turnover_options, 'line')"
        ></chart-view>
        <div class="empty" v-else>暂无数据</div>
      </div>
    </div>

    <!-- 交易分析 -->
    <template>
      <div class="light-title mt32">交易分析</div>

      <div class="flex mt16" style="min-height: 260px">
        <div class="block block-bg flex-1" :style="{ backgroundImage: `url(${personal_bg})` }">
          <h4 class="block-title flex flex-align-center">总成交人数</h4>
          <div class="block-content-desc">{{ total.paid_cv || 0 }}人</div>
        </div>

        <div class="flex flex-4 ml16">
          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">新老客成交</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="paid_cv_new_old_customer"
                v-if="isShowLineEcharts(paid_cv_new_old_customer, 'pie')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>

          <div class="block ml16 flex-1">
            <h4 class="block-title flex flex-align-center">会员和非会员成交</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="paid_cv_vip_or_none"
                v-if="isShowLineEcharts(paid_cv_vip_or_none, 'pie')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 成交转化率 -->
    <template>
      <div class="flex mt16" style="min-height: 260px">
        <div class="block block-bg flex-1" :style="{ backgroundImage: `url(${pie_bg})` }">
          <h4 class="block-title flex flex-align-center">成交转化率</h4>
          <div class="block-content-desc">{{ total.arrival_pay_convert_ratio || 0 }}%</div>
        </div>

        <div class="ml16 flex-4">
          <div class="block">
            <h4 class="block-title flex flex-align-center">新老客成交转化率趋势</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="customer_ratio_options"
                v-if="isShowLineEcharts(customer_ratio_options, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 客单价 -->
    <template>
      <div class="flex mt16" style="min-height: 260px">
        <div class="block block-bg flex-1" :style="{ backgroundImage: `url(${money_bg})` }">
          <h4 class="block-title flex flex-align-center">客单价</h4>
          <div class="block-content-desc">¥{{ total.per_cv_amount || 0 }}</div>
        </div>

        <div class="ml16 flex-4">
          <div class="block">
            <h4 class="block-title flex flex-align-center">当月客单价每日环比趋势</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="per_cv_amount_options"
                v-if="isShowLineEcharts(per_cv_amount_options, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 30日复购 -->
    <template>
      <div class="flex mt16" style="min-height: 260px">
        <div class="block block-bg flex-1" :style="{ backgroundImage: `url(${personal_bg})` }">
          <h4 class="block-title flex flex-align-center">本月复购</h4>
          <div class="block-content-desc">{{ total.repurchase_cv || 0 }} 人</div>
        </div>

        <div class="ml16 flex-4 flex">
          <div class="block block-bg flex-1" :style="{ backgroundImage: `url(${pie_bg})` }">
            <h4 class="block-title flex flex-align-center">本月复购率</h4>
            <div class="block-content-desc">{{ total.repurchase_ratio || 0 }}%</div>
          </div>

          <div class="block block-bg ml16 flex-1" :style="{ backgroundImage: `url(${personal_bg})` }">
            <h4 class="block-title flex flex-align-center">双月复购</h4>
            <div class="block-content-desc">{{ total['60day_repurchase_cv'] || 0 }}人</div>
          </div>

          <div class="block block-bg ml16 flex-1" :style="{ backgroundImage: `url(${pie_bg})` }">
            <h4 class="block-title flex flex-align-center">双月复购率</h4>
            <div class="block-content-desc">{{ total['60day_repurchase_ratio'] || 0 }}%</div>
          </div>
        </div>
      </div>
    </template>

    <!-- 客源分析 -->
    <template>
      <div class="light-title mt32">客源分析</div>

      <div class="flex mt16" style="min-height: 260px">
        <div class="flex-1">
          <div class="block block-bg" style="min-height: 260px" :style="{ backgroundImage: `url(${personal_bg})` }">
            <h4 class="block-title flex flex-align-center">新获客人数</h4>
            <div class="block-content-desc">{{ total.new_customers || 0 }}人</div>
          </div>

          <div class="block mt16">
            <h4 class="block-title flex flex-align-center">获客渠道占比</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="new_customers_channel"
                v-if="isShowLineEcharts(new_customers_channel, 'pie')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>

        <div class="block ml16 flex-1 flex flex-c">
          <h4 class="block-title flex flex-align-center">收入来源构成</h4>
          <div class="width100 flex-1 flex flex-item-center">
            <chart-view
              height="260px"
              :chart-option="kind_turnover"
              v-if="isShowLineEcharts(kind_turnover, 'pie')"
            ></chart-view>
            <div class="empty" v-else>暂无数据</div>
          </div>
        </div>

        <div class="block ml16 flex-1 flex flex-c">
          <h4 class="block-title flex flex-align-center">门店医生业绩</h4>
          <div class="width100 flex-1 flex flex-item-center">
            <chart-view
              height="260px"
              :chart-option="doctor_sale"
              v-if="isShowLineEcharts(doctor_sale, 'pie')"
            ></chart-view>
            <div class="empty" v-else>暂无数据</div>
          </div>
        </div>
      </div>
    </template>

    <!-- 利润分析 -->
    <template>
      <div class="light-title mt32">利润分析</div>

      <div class="flex mt16" style="min-height: 260px">
        <div class="block block-bg flex-1" :style="{ backgroundImage: `url(${money_bg})` }">
          <h4 class="block-title flex flex-align-center">总利润</h4>
          <div class="block-content-desc">¥{{ total.profit || 0 }}</div>
          <!-- <div>
            <div>营收:{{ total.confirm_revenue }}</div>
            <div>固定成本:{{ total.base_cost_amount }}</div>
            <div>订单关联物料成本:{{ total.order_stock_cost_amount }}</div>
            <div>运营费用成本:{{ total.opex_cost_amount }}</div>
          </div> -->
        </div>

        <div class="ml16 flex-4">
          <div class="block">
            <h4 class="block-title flex flex-align-center">每日利润</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="profit_options"
                v-if="isShowLineEcharts(profit_options, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { color_enum } from '@/view/statistics/data/color';
import dateChange from './mixins/dateChange';
export default {
  name: 'business-board',
  components: {},
  mixins: [dateChange],
  props: {},
  data() {
    return {
      color_table_enum: color_enum, //色值表
      sale_bar_options: {}, // 销售数据饼图options

      surplus_data: {}, // 柱状图
      money_data: {}, // 柱状图
      money_bg: 'https://static.rsjxx.com/image/2025/0711/141649_8756.png',
      personal_bg: 'https://static.rsjxx.com/image/2025/0711/152754_79225.png',
      pie_bg: 'https://static.rsjxx.com/image/2025/0711/152754_15559.png',

      total: {},
      turnover_new_old_customer: {}, // 新老客交易额数据饼图options
      turnover_vip_or_none: {}, // 会员非会员交易额数据饼图options
      paid_cv_new_old_customer: {}, // 新老客成交人数数据饼图options
      paid_cv_vip_or_none: {}, // 会员非会员成交人数数据饼图options
      new_customers_channel: {}, // 获客渠道占比数据饼图options
      kind_turnover: {}, // 收入来源构成数据饼图options
      doctor_sale: {}, // 门店医生业绩数据饼图options
      turnover_options: {}, // 交易额趋势柱状折线图
      customer_ratio_options: {}, // 新老客成交转化率趋势
      per_cv_amount_options: {}, // 当月客单价每日环比趋势
      profit_options: {}, // 每日利润
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 当时间发生变化，进行得操作
    dateChange() {
      this.getdateReportClinicreport();
    },

    getdateReportClinicreport() {
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        date_type: this.date_type,
      };
      this.$api
        .getdateReportClinicreport(params)
        .then(res => {
          this.total = res.total || {};

          // 新老客交易额
          this.handleTurnoverCustomer(res.turnover_new_old_customer);

          // 会员非会员交易额
          this.handleTurnoverVipCustomer(res.turnover_vip_or_none);

          // 交易额趋势
          this.handleTurnoverLine(res.date_list);

          // 新老客成交人数
          this.handleCustomerCv(res.paid_cv_new_old_customer);

          // 会员和非会员成交人数
          this.handleVipCustomerCv(res.paid_cv_vip_or_none);

          // 新老客成交转化率趋势
          this.handleCustomerRatioLine(res.date_list);

          // 当月客单价每日环比趋势
          this.handlePerCvAmountLine(res.date_list);

          // 获客渠道占比
          this.handleNewCustomersChannel(res.new_customers_channel);

          // 收入来源构成
          this.handleKindTurnover(res.kind_turnover);

          // 门店医生业绩
          this.handleDoctor(res.doctor_sale);

          // 每日利润
          this.handleProfitLine(res.date_list);
        })
        .catch(error => {});
    },

    // 新老客交易额
    handleTurnoverCustomer(list) {
      let resultList = [];
      let defaultTooltipList = [];
      list &&
        list.forEach(item => {
          defaultTooltipList = [
            { key: '', value: item.name },
            { key: '金额', value: item.count },
            { key: '占比', value: `${item.rate}%` },
          ];
          resultList.push({
            ...item,
            value: item.count,
            tootipList: defaultTooltipList,
          });
        });
      this.setPieConfigure_guide('turnover_new_old_customer', resultList);
    },

    // 会员非会员交易额
    handleTurnoverVipCustomer(list) {
      let resultList = [];
      let defaultTooltipList = [];
      list &&
        list.forEach(item => {
          defaultTooltipList = [
            { key: '', value: item.name },
            { key: '金额', value: item.count },
            { key: '占比', value: `${item.rate}%` },
          ];
          resultList.push({
            ...item,
            value: item.count,
            tootipList: defaultTooltipList,
          });
        });
      this.setPieConfigure_guide('turnover_vip_or_none', resultList);
    },

    // 交易额趋势
    handleTurnoverLine(source = []) {
      let resultList = {
        date: source.map(item => item.date),
        xData: source.map(item => item.last_turnover),
        yData: source.map(item => item.turnover),
        zData: source.map(item => {
          return {
            value: item.turnover_incr,
            tooltip_text: `${item.turnover_incr}%`,
          };
        }),
        legend: ['上期交易额', '当期交易额', '交易额环比'],
      };
      this.handleBarLine('turnover_options', resultList);
    },

    // 新老客成交转化率趋势
    handleCustomerRatioLine(source = []) {
      let resultList = {
        date: source.map(item => item.date),
        xData: source.map(item => {
          return {
            value: item.old_cv_arrival_pay_convert_ratio,
            value_text: `${item.old_cv_arrival_pay_convert_ratio}%`,
            tooltip_text: `${item.old_cv_arrival_pay_convert_ratio}%`,
          };
        }),
        yData: source.map(item => {
          return {
            value: item.new_cv_pay_convert_ratio,
            value_text: `${item.new_cv_pay_convert_ratio}%`,
            tooltip_text: `${item.new_cv_pay_convert_ratio}%`,
          };
        }),
        legend: ['老客', '新客'],
        leftSymbol: '%',
      };
      this.handleBarLine('customer_ratio_options', resultList);
    },

    // 当月客单价每日环比趋势
    handlePerCvAmountLine(source = []) {
      let resultList = {
        date: source.map(item => item.date),
        xData: source.map(item => item.last_per_cv_amount),
        yData: source.map(item => item.per_cv_amount),
        zData: source.map(item => {
          return {
            value: item.per_cv_amount_incr,
            tooltip_text: `${item.per_cv_amount_incr}%`,
          };
        }),
        legend: ['上期客单价', '本期客单价', '客单价环比'],
      };
      this.handleBarLine('per_cv_amount_options', resultList);
    },

    // 新老客成交人数
    handleCustomerCv(list) {
      let resultList = [];
      let defaultTooltipList = [];
      list &&
        list.forEach(item => {
          defaultTooltipList = [
            { key: '', value: item.name },
            { key: '人数', value: item.count },
            { key: '占比', value: `${item.rate}%` },
          ];
          resultList.push({
            ...item,
            value: item.count,
            tootipList: defaultTooltipList,
          });
        });
      this.setPieConfigure_guide('paid_cv_new_old_customer', resultList);
    },

    // 会员和非会员成交人数
    handleVipCustomerCv(list) {
      let resultList = [];
      let defaultTooltipList = [];
      list &&
        list.forEach(item => {
          defaultTooltipList = [
            { key: '', value: item.name },
            { key: '人数', value: item.count },
            { key: '占比', value: `${item.rate}%` },
          ];
          resultList.push({
            ...item,
            value: item.count,
            tootipList: defaultTooltipList,
          });
        });
      this.setPieConfigure_guide('paid_cv_vip_or_none', resultList);
    },

    // 获客渠道占比
    handleNewCustomersChannel(list) {
      let resultList = [];
      let defaultTooltipList = [];
      list &&
        list.forEach(item => {
          defaultTooltipList = [
            { key: '', value: item.name },
            { key: '人数', value: item.count },
            { key: '占比', value: `${item.rate}%` },
          ];
          resultList.push({
            ...item,
            value: item.count,
            tootipList: defaultTooltipList,
          });
        });
      this.setPieConfigure_guide('new_customers_channel', resultList);
    },

    // 收入来源构成
    handleKindTurnover(list) {
      let resultList = [];
      let defaultTooltipList = [];
      list &&
        list.forEach(item => {
          defaultTooltipList = [
            { key: '', value: item.name },
            { key: '金额', value: item.count },
            { key: '占比', value: `${item.rate}%` },
          ];
          resultList.push({
            ...item,
            value: item.count,
            tootipList: defaultTooltipList,
          });
        });
      this.setPieConfigure_guide('kind_turnover', resultList);
    },

    // 门店医生业绩
    handleDoctor(list) {
      let resultList = [];
      let defaultTooltipList = [];
      list &&
        list.forEach(item => {
          defaultTooltipList = [
            { key: '', value: item.name },
            { key: '金额', value: item.count },
            { key: '占比', value: `${item.rate}%` },
          ];
          resultList.push({
            ...item,
            value: item.count,
            tootipList: defaultTooltipList,
          });
        });
      this.setPieConfigure_guide('doctor_sale', resultList);
    },

    // 每日利润
    handleProfitLine(source = []) {
      let resultList = {
        date: source.map(item => item.date),
        xData: source.map(item => ({
          value: item.profit,
          // confirm_revenue: item.confirm_revenue,
          // base_cost_amount: item.base_cost_amount,
          // order_stock_cost_amount: item.order_stock_cost_amount,
          // opex_cost_amount: item.opex_cost_amount,
        })),
        legend: ['利润'],
      };
      this.handleBar('profit_options', resultList);
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
@import url('./style/common.less');
.block-content-desc {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  font-size: 20px;
  color: #333333;
  line-height: 45px;
}
</style>
