<template>
  <div class="daily-meeting-container">
    <!-- 共用的顶部区域 -->
    <div class="shared-header">
      <div class="date-section">
        <DatePicker
          type="date"
          placeholder="选择日期"
          :value="selectedDate"
          @on-change="changeDate"
          format="yyyy-MM-dd"
          style="width: 200px"
          :options="options"
        />
        <span class="date-display">{{ formatDate(selectedDate) }}</span>
      </div>
      <div class="action-buttons">
        <Button :type="activeTab === 'morning' ? 'primary' : 'default'" @click="switchTab('morning')">晨会</Button>
        <Button :type="activeTab === 'evening' ? 'primary' : 'default'" @click="switchTab('evening')">夕会</Button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="meeting-content">
      <MorningMeeting v-if="activeTab === 'morning'" :selected-date="selectedDate" />
      <EveningMeeting v-if="activeTab === 'evening'" :selected-date="selectedDate" />
    </div>
  </div>
</template>

<script>
import MorningMeeting from './components/MorningMeeting.vue';
import EveningMeeting from './components/EveningMeeting.vue';
import moment from 'moment';
export default {
  name: 'DailyMeeting',
  components: {
    MorningMeeting,
    EveningMeeting,
  },
  data() {
    return {
      activeTab: 'morning',
      selectedDate: moment().format('YYYY-MM-DD'),
      options: {
        disabledDate: date => {
          // 不能大于今天
          return date && date.valueOf() > Date.now();
        },
      },
    };
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab;
    },
    formatDate(date) {
      return moment(date).format('YYYY-MM-DD dddd');
    },
    changeDate(date) {
      this.selectedDate = date;
    },
  },
};
</script>

<style lang="less" scoped>
.daily-meeting-container {
  padding: 0;
  background: #f5f6f8;
}

.shared-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  position: sticky;
  top: 0;
}

.date-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.date-display {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 12px;
}
.meeting-content {
  height: ~'calc(100vh - 160px)';
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
