<template>
  <div class="dynamic-tooltip-wrapper">
    <span>{{ title }}</span>
    <div class="tip-icon" ref="triggerRef">
      <img style="width: 14px; height: 14px" src="../../../assets/image/help_icon.png" />
    </div>
  </div>
</template>

<script>
import tippy from 'tippy.js';
import 'tippy.js/dist/tippy.css';

export default {
  name: 'DynamicTooltip',
  props: {
    title: {
      type: String,
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      tippyInstance: null,
    };
  },
  mounted() {
    this.initTippy();
  },
  beforeDestroy() {
    if (this.tippyInstance) {
      this.tippyInstance.destroy();
    }
  },
  methods: {
    initTippy() {
      this.tippyInstance = tippy(this.$refs.triggerRef, {
        content: this.getStyledContent(),
        allowHTML: true,
        placement: 'bottom',
        maxWidth: 410,
        theme: 'light',
        interactive: true,
        delay: [200, 100], // [show delay, hide delay]
        duration: [200, 150], // [show duration, hide duration]
        arrow: true,
        trigger: 'mouseenter focus',
        onShow: instance => {
          // 只在需要显示时才创建内容
          instance.setContent(this.getStyledContent());
          // 动态设置 tooltip 样式
          const box = instance.popper.querySelector('.tippy-box');

          if (box) {
            // 添加自定义类名用于样式控制
            box.classList.add('custom-tooltip-box');
            Object.assign(box.style, {
              backgroundColor: '#fff',
              color: '#333',
              // border: '1px solid #e4e7ed',
              borderRadius: '4px',
              fontWeight: 'normal',
              boxShadow: '0 1px 6px rgba(0, 0, 0, 0.2)',
            });
          }
        },
      });
    },
    getStyledContent() {
      return `
        <div style="
          line-height: 20px;
          font-size: 13px;
          color: #333;
          padding: 3px;
        ">${this.content}</div>
      `;
    },
  },
  watch: {
    content() {
      // 当内容变化时更新 tooltip
      if (this.tippyInstance) {
        this.tippyInstance.setContent(this.getStyledContent());
      }
    },
  },
};
</script>

<style lang="less" scoped>
.dynamic-tooltip-wrapper {
  display: flex;
  align-items: center;

  .tip-icon {
    display: flex;
    align-items: center;
    margin-left: 8px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>

<style lang="less">
// 自定义 tooltip 箭头样式
.custom-tooltip-box {
  .tippy-arrow {
    color: #fff !important;

    &::before {
      border-top-color: #e4e7ed !important;
    }
  }
}
</style>
