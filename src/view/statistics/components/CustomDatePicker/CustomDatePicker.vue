<template>
  <div class="test-yy-wrapper flex">
    <el-select
      size="small"
      v-if="!typeOptions.length || typeOptions.length != 1"
      style="width: 90px"
      v-model="dateType"
      @change="changeDateType"
    >
      <el-option v-for="item in dateTypeOptions" :value="item.value" :label="item.label" :key="item.value"></el-option>
    </el-select>
    <el-date-picker
      popper-class="date-picker"
      :picker-options="pickerOptions"
      class="ml10"
      size="small"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :placeholder="datePlaceholder"
      :key="dateType"
      :type="dateType"
      :value-format="curFormat"
      :clearable="false"
      :format="curFormat || `${this.weekDate_st}到${this.weekDate_et}  第WW周`"
      v-model="curDate"
      style="min-width: 255px; max-width: 300px"
      @change="changeDate"
    ></el-date-picker>
  </div>
</template>

<script>
import { getEnv } from '@/libs/runtime';
export default {
  name: 'CustomDatePicker',
  mixins: [],

  components: {},

  props: {
    propDateType: {
      type: String,
      default: 'week',
    },
    propCurDate: {
      type: Array,
      default: () => [],
    },
    // 外部约定类型，默认见 dateTypeOptions, 当只有一种类型的时候，下拉框不展示
    typeOptions: {
      type: Array,
      default: () => [],
    },
    // 外部约定的默认类型，需要搭配 typeOptions 一起用
    defaultType: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      pickerOptions: {
        firstDayOfWeek: 1,
        showWeekNumber: true,
        disabledDate: date => {
          if (this.dateType === 'week') {
            return date && date.valueOf() >= new Date();
          } else {
            // return date && date.valueOf() >= new Date()
            if (getEnv() === 'production') {
              return date && date.valueOf() >= new Date() - 86400000;
            } else {
              // 当非生产环境下，月份放开至下一个月
              if (this.dateType === 'month') {
                return date && date.valueOf() >= new Date().valueOf() + 86400000 * 31;
              } else {
                return date && date.valueOf() >= new Date();
              }
            }
          }
        },
      },
      dateType: '',
      dateTypeOptions: [],
      default_type_options: [
        { value: 'date', label: '日', format: 'yyyy-MM-dd' },
        { value: 'week', label: '周', format: '' },
        { value: 'month', label: '月', format: 'yyyy-MM' },
        { value: 'daterange', label: '自定义', format: 'yyyy-MM-dd' },
      ],
      curDate: null,
      curFormat: '',
      outputDate: null,
      weekDate_st: '',
      weekDate_et: '',
      datePlaceholder: '请选择日期',
    };
  },

  computed: {},

  watch: {
    outputDate: {
      immediate: true,
      handler(val) {
        if (val instanceof Array) {
          this.$emit('input', val);
        } else {
          this.$emit('input', [val, '']);
        }
      },
    },

    dateType: {
      immediate: true,
      handler(val) {
        let current_date_type = val;
        if (val == 'date') {
          current_date_type = 'day';
        }
        this.$emit('getDateType', current_date_type);
      },
    },
    // propDateType: {
    //   immediate: true,
    //   handler (val) {
    //     this.dateType = val
    //   }
    // },
    // propCurDate: {
    //   handler (val) {
    //     console.log("-> %c val  === %o ", "font-size: 15px", val)
    //
    //     this.curDate = val
    //   }
    // },
  },

  created() {
    this.initType();
  },

  mounted() {},

  destroyed() {},
  methods: {
    // 初始化默认的类型数据
    initType() {
      if (this.typeOptions.length) {
        // 默认走外部设定值，如果没有，则默认外部传入类型的第一个
        this.dateType = this.defaultType || this.typeOptions[0];
        this.dateTypeOptions = this.default_type_options?.filter(item => this.typeOptions.includes(item.value));
      } else {
        // 默认全部，周
        this.dateType = 'week';
        this.dateTypeOptions = this.default_type_options;
      }
      this.initDate(this.dateType);
      this.changeDateType(this.dateType);
    },
    initDate(dateType) {
      switch (dateType) {
        case 'date':
          this.initDate_date();
          break;
        case 'week':
          this.initWeekDate();
          break;
        case 'month':
          this.initMonthDate();
          break;
        case 'daterange':
          break;
      }
    },
    initDate_date() {
      let yesterday = this.$moment().add(-1, 'days').format('YYYY-MM-DD');
      console.log('-> %c yesterday  === %o ', 'font-size: 15px', yesterday);
      this.curDate = yesterday;
      this.outputDate = [yesterday, yesterday];
    },
    initWeekDate() {
      const weekDay = this.$moment().day();
      let initDate = [];
      if (weekDay == 1) {
        initDate = this.getLastWeek(1);
      } else {
        initDate = this.getCurrentWeek();
      }
      this.outputDate = initDate;
      this.weekDate_st = initDate[0];
      this.weekDate_et = initDate[1];
      this.curDate = new Date(initDate[0]);
    },
    initMonthDate() {
      console.log(this.curDate);
      const dateDay = this.$moment().date();
      console.log('=>(CustomDatePicker.vue:195) dateDay', dateDay);
      if (dateDay === 1) {
        const startOfMonth = this.$moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
        const endOfMonth = this.$moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD');
        this.outputDate = [startOfMonth, endOfMonth];
        this.curDate = this.$moment().subtract(1, 'months').format('YYYY-MM-DD');
      } else {
        const startOfMonth = this.$moment().startOf('month').format('YYYY-MM-DD');
        const endOfMonth = this.$moment().endOf('month').format('YYYY-MM-DD');
        this.outputDate = [startOfMonth, endOfMonth];
        this.curDate = this.$moment().format('YYYY-MM');
      }
      console.log('-> %c dateDay  === %o ', 'font-size: 15px', dateDay);
    },
    getLastWeek(i) {
      let weekOfDay = parseInt(this.$moment().format('E')); //计算今天是这周第几天
      let last_monday = this.$moment()
        .subtract(weekOfDay + 7 * i - 1, 'days')
        .format('YYYY-MM-DD'); //周一日期
      let last_sunday = this.$moment()
        .subtract(weekOfDay + 7 * (i - 1), 'days')
        .format('YYYY-MM-DD'); //周日日期
      return [last_monday, last_sunday];
    },
    getCurrentWeek() {
      const start = this.$moment().isoWeekday(1).format('YYYY-MM-DD'); //本周一
      const end = this.$moment().isoWeekday(7).format('YYYY-MM-DD'); //本周日
      return [start, end];
    },
    getNextWeek(i) {
      let weekOfDay = parseInt(this.$moment().format('E')); //计算今天是这周第几天
      let next_monday = this.$moment()
        .add(7 - weekOfDay + 7 * (i - 1) + 1, 'days')
        .format('YYYY-MM-DD'); //周一日期
      let next_sunday = this.$moment()
        .add(7 - weekOfDay + 7 * i, 'days')
        .format('YYYY-MM-DD'); //周日日期
      return [next_monday, next_sunday];
    },
    changeDate(val) {
      if (this.dateType === 'week') {
        console.log('-> %c this.curDate  === %o ', 'font-size: 15px', this.curDate);
        this.weekDate_st = this.$moment(this.curDate).startOf('week').format('YYYY-MM-DD');
        this.weekDate_et = this.$moment(this.curDate).endOf('week').format('YYYY-MM-DD');
        this.outputDate = [this.weekDate_st, this.weekDate_et];
      } else if (this.dateType === 'month') {
        const startOfMonth = this.$moment(this.curDate).startOf('month').format('YYYY-MM-DD');
        const endOfMonth = this.$moment(this.curDate).endOf('month').format('YYYY-MM-DD');
        this.outputDate = [startOfMonth, endOfMonth];
      } else if (this.dateType === 'daterange') {
        this.outputDate = this.curDate;
      } else if (this.dateType === 'date') {
        this.outputDate = [this.curDate, this.curDate];
      }
    },
    changeDateType(val) {
      console.log('-> %c val  === %o ', 'font-size: 15px', val);
      this.curDate = '';
      const selectedType = this.dateTypeOptions.filter(item => item.value === val);
      if (selectedType.length) {
        this.curFormat = selectedType[0].format;
      } else {
        this.curFormat = 'yyyy-MM-DD';
      }
      if (val === 'date') {
        this.$emit('getDateType', 'day');
      } else {
        this.$emit('getDateType', val);
      }
      this.initDate(val);
    },
  },
};
</script>

<style scoped lang="less"></style>
