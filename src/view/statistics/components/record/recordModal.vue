<template>
  <Modal
    ref="customModal"
    :value="value"
    width="600px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="tab-box">
        <div
          class="tab-item"
          @click="tabChange(item.id)"
          :class="{ 'tab-item--active': tab_active == item.id }"
          v-for="item in tab_list"
          :key="item.id"
        >
          {{ item.label }}
        </div>
      </div>

      <div class="form-content">
        <div class="tip">
          <div v-if="tab_active === '1'">
            当前{{ current_item.desc }}为 {{ Number(current_item.long_fee || 0).toFixed(2) }}，需修改为:
          </div>
        </div>
        <Form
          ref="formData"
          class="form-box"
          :model="formData"
          :label-width="100"
          :label-colon="true"
          :rules="formDataRules"
        >
          <FormItem :label="current_item.desc" prop="rent">
            <div class="rent-box">
              <InputNumber
                class="rent-input"
                :min="0"
                v-model="formData.rent"
                :precision="2"
                :active-change="false"
                placeholder="请输入"
              />
              <Checkbox v-if="tab_active === '2'" class="rent-checkbox" v-model="formData.minus"
                >减值（勾选则表示负数）</Checkbox
              >
            </div>
          </FormItem>

          <FormItem label="分" prop="month" style="width: 346px" v-if="tab_active === '2'">
            <div class="flex flex-item-center">
              <Select v-model="formData.month" clearable placeholder="请选择" style="flex: 1">
                <Option :value="item.id" v-for="(item, index) in month_options" :key="index">{{ item.label }}</Option>
              </Select>
              <div style="width: fit-content; margin-left: 20px">月平摊</div>
            </div>
          </FormItem>

          <FormItem label="备注" prop="remark">
            <Input
              v-model.trim="formData.remark"
              maxlength="50"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 4 }"
              placeholder="请输入备注"
              show-word-limit
            ></Input>
          </FormItem>
        </Form>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确认</Button>
    </div>
  </Modal>
</template>

<script>
let init_form_data = {
  rent: null,
  minus: false,
  month: '',
  remark: '',
};
export default {
  name: 'recordModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '费用记录',
    },
    current_item: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    const rentValid = (rule, value, callback) => {
      if (Number(value || 0) === 0) {
        callback(new Error('请输入大于0的金额'));
      } else {
        callback();
      }
    };
    return {
      confirmLoading: false,
      tab_active: '1',
      formData: { ...init_form_data },
      formDataRules: {
        rent: [{ required: true, validator: rentValid, trigger: 'change' }],
        month: [{ required: true, message: '请选择月份', trigger: 'change' }],
      },
      tab_list: [
        { id: '1', label: '修改为长期费用' },
        { id: '2', label: '记一笔一次性费用' },
      ],
      month_options: [
        { id: '1', label: '1' },
        { id: '2', label: '2' },
        { id: '3', label: '3' },
        { id: '4', label: '4' },
        { id: '5', label: '5' },
        { id: '6', label: '6' },
        { id: '7', label: '7' },
        { id: '8', label: '8' },
        { id: '9', label: '9' },
        { id: '10', label: '10' },
        { id: '11', label: '11' },
        { id: '12', label: '12' },
      ],
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    tabChange(val) {
      console.log('=>(recordModal.vue:64) val', val);
      this.tab_active = val;
    },

    changeVisible(visible) {
      if (visible) {
        // todo
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.tab_active = '1';
      this.formData = { ...init_form_data };
      this.$refs.formData.resetFields();
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    /**
     * @description: 确定事件
     * */
    confirm() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.getFeeCreate();
        }
      });
    },

    getFeeCreate() {
      let params = {
        fee_type: this.current_item.id,
        amount: this.formData.rent,
        type: this.tab_active,
        sign: this.formData.minus ? '1' : '0',
        divide_month: this.formData.month,
        remark: this.formData.remark,
      };
      this.$api
        .getFeeCreate(params)
        .then(res => {
          this.confirmLoading = true;
          this.$emit('success');
          this.confirmLoading = false;
          this.closeModal();
        })
        .finally(() => (this.confirmLoading = false));
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 150px;
  overflow-y: auto;
}
.content {
  min-height: 300px;
  padding: 0 50px;
  .tab-box {
    display: flex;
    justify-content: center;
    .tab-item {
      background: #ffffff;
      border: 1px solid #155bd4;
      padding: 7px 16px;
      font-weight: 400;
      font-size: 13px;
      color: #155bd4;
      line-height: 18px;
      cursor: pointer;
      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    .tab-item--active {
      background: #155bd4;
      color: #ffffff;
    }
  }
  .form-content {
    .tip {
      font-weight: 400;
      font-size: 13px;
      color: #333333;
      line-height: 20px;
      margin: 20px 0px 20px 40px;
      height: 16px;
    }
    .rent-box {
      display: flex;
      align-items: center;
      .rent-input {
        flex: 1;
      }
      .rent-checkbox {
        width: fit-content;
        margin-left: 20px;
      }
    }
  }
}
</style>
