<template>
  <div v-loading="pageLoading" class="app-page-wrapper">
    <div v-show="!pageLoading">
      <!-- Header -->
      <header class="header">
        <Form inline label-position="left" :label-width="70" :label-colon="true">
          <div class="flex gap-20">
            <Form-item label="统计范围">
              <Select v-model="queryForm.date_type" @on-change="onType">
                <Option v-for="item in typeOpts" :key="item.id" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </Form-item>
            <Form-item label="统计时间">
              <Select v-model="queryForm.year" @on-change="onYear">
                <Option v-for="item in yearOpts" :key="item.id" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </Form-item>
          </div>
        </Form>

        <div v-if="queryForm.date_type != 3" class="flex card_box">
          <div
            v-for="item in statistics_scope"
            :key="item.value"
            class="card_item"
            :class="{
              unset: item.status == '2',
              disabled: item.status == '1',
              succes: item.status == '3',
              active: activeIndex == item.value,
            }"
            @click="onCardClick(item)"
          >
            <div class="tip" v-if="item.desc">
              {{ item.desc }}
            </div>
            <div>{{ item.title || '-' }}</div>
          </div>
        </div>
      </header>
      <div v-if="is_target != 0" class="chart-box">
        <div class="chart-title">
          <div class="line"></div>
          目标完成情况
        </div>

        <div class="flex">
          <div class="flex-1 chart-box-i">
            <!-- <chart-view :chart-option="sale_bar_options" class="echarts" /> -->
            <div class="chart-title">
              业绩
              <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
                <div style="white-space: pre-wrap" slot="content">{{ tips.flow }}</div>
                <p class="flex flex-item-center cursor">
                  <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
                </p>
              </Tooltip>
            </div>
            <el-progress
              type="circle"
              stroke-linecap="square"
              :show-text="true"
              :stroke-width="28"
              :percentage="sale_percent"
              :width="200"
            ></el-progress>

            <div class="chart-detail">
              <div class="row target">
                <span class="label">目标</span>
                <span class="value">￥{{ target_achieve_data[0]?.target || 0 }}</span>
              </div>
              <div class="row">
                <span class="label">已完成</span>
                <span class="value">￥{{ target_achieve_data[0]?.achieve || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="flex-1 chart-box-i">
            <div class="chart-title">
              营收
              <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
                <div style="white-space: pre-wrap" slot="content">{{ tips.used }}</div>
                <p class="flex flex-item-center cursor">
                  <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
                </p>
              </Tooltip>
            </div>

            <el-progress
              type="circle"
              stroke-linecap="square"
              :show-text="true"
              :stroke-width="28"
              :percentage="consume_percent"
              :width="200"
            ></el-progress>

            <div class="chart-detail">
              <div class="row target">
                <span class="label">目标</span>
                <span class="value">¥ {{ target_achieve_data[1]?.target || 0 }}</span>
              </div>
              <div class="row">
                <span class="label">已完成</span>
                <span class="value">¥ {{ target_achieve_data[1]?.achieve || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="flex-1 chart-box-i">
            <!-- <chart-view :chart-option="person_bar_options" class="echarts" /> -->
            <div class="chart-title">
              到店人次
              <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
                <div style="white-space: pre-wrap" slot="content">{{ tips.num }}</div>
                <p class="flex flex-item-center cursor">
                  <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
                </p>
              </Tooltip>
            </div>
            <el-progress
              type="circle"
              stroke-linecap="square"
              :show-text="true"
              :stroke-width="28"
              :percentage="person_percent"
              :width="200"
            ></el-progress>
            <div class="chart-detail">
              <div class="row target">
                <span class="label">目标</span>
                <span class="value">{{ target_achieve_data[2]?.target || 0 }}人次</span>
              </div>
              <div class="row">
                <span class="label">已完成</span>
                <span class="value">{{ target_achieve_data[2]?.achieve || 0 }}人次</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="is_target != 0 && queryForm.date_type != 1" class="achieve-box">
        <div class="achieve-title">
          <div class="line"></div>
          详细达成数据
        </div>

        <Table :data="achieveData" :columns="achieveCols" border> </Table>
      </div>
      <!--  -->
      <div v-if="is_target == 0" class="not-box">
        <div class="not-title">
          <img style="width: 280px; height: 246px" src="../../assets/image/s_not.png" alt="" srcset="" />
          <div>暂未设置目标</div>
        </div>
        <div class="not-btn">
          <Button type="primary" size="large" @click="onAddTarget">设置目标</Button>
        </div>
      </div>
    </div>

    <Modal
      v-model="visible"
      title="设置目标"
      width="800px"
      :closable="false"
      :mask-closable="false"
      class-name="confirm-modal"
      transfer
    >
      <!-- :mask-closable="maskClosable" -->
      <div class="modal-box">
        <div class="tip">目标管理在诊所端仅支持设置目标数值，且只有一次设置机会，如后续修改请联系直营中心修改</div>
        <!-- <div class="title">同开诊所2025年7月目标</div> -->
        <div>
          <!-- :span-method="mergeMonthRows"  -->
          <Table :data="targetData" :columns="targetCols" border height="430">
            <template v-slot:dimensionality="{ row }">
              <div class="cell">业绩（元）</div>
              <div class="cell">营收（元）</div>
              <div class="cell">到店人次</div>
            </template>
            <template v-slot:target="{ row, index }">
              <div class="cell">
                <InputNumber
                  style="width: 140px"
                  :active-change="false"
                  :precision="2"
                  :min="0"
                  v-model="targetData[index].order_cash_turnover_target"
                />
              </div>
              <div class="cell">
                <InputNumber
                  style="width: 140px"
                  :precision="2"
                  :active-change="false"
                  :min="0"
                  v-model="targetData[index].serv_used_money_target"
                />
              </div>
              <div class="cell">
                <InputNumber style="width: 140px" :min="0" v-model="targetData[index].customers_target" />
              </div>
            </template>
          </Table>
        </div>
      </div>

      <div slot="footer" style="text-align: center">
        <Button @click="() => (visible = false)">取消</Button>
        <Button type="primary" :loading="saveLoading" @click="onOk">保存</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import tip from './data/tip';
import S from 'libs/util';
import dateChange from './mixins/dateChange';
export default {
  name: 'Goals',
  components: {},
  mixins: [dateChange],
  data() {
    return {
      visible: false,
      pageLoading: false,
      saveLoading: false,

      queryForm: {
        date_type: '1', //1 月度 2 季度 3 年度
        year: '',
        month: '',
        quarter: '',
      },

      achieve_detail_data: [],
      statistics_scope: [],
      target_achieve_data: [],
      typeOpts: [],
      yearOpts: [],
      activeIndex: '', // 当前选中

      is_target: '0', //0-无数据，

      // 详细达成数据表
      achieveCols: [],
      achieveData: [],

      tips: {
        flow: tip.goals_flow_tip,
        used: tip.goals_used_tip,
        num: tip.goals_num_tip,
      },

      targetCols: [
        {
          key: 'month',
          title: '月份',
          render(h, params) {
            return h('span', params.row.month + '月');
          },
        },
        {
          slot: 'dimensionality',
          className: 'cell-parent',
          title: '目标维度',
        },
        {
          slot: 'target',
          className: 'cell-parent',
          title: '目标数值',
        },
      ],

      targetData: [],

      sale_percent: 0,
      consume_percent: 0,
      person_percent: 0,
    };
  },
  created() {},
  mounted() {
    this.scrollTop();
    this.initData();
  },
  activated() {
    this.scrollTop();
  },
  methods: {
    scrollTop() {
      this.$nextTick(() => {
        const wrapper = document.querySelector('.app-wrapper');
        if (wrapper) wrapper.scrollTop = 0;
      });
    },
    initData() {
      const now = new Date();
      const currentQuarter = Math.floor(now.getMonth() / 3) + 1;
      this.queryForm.year = now.getFullYear() + '';
      this.queryForm.month = now.getMonth() + 1;
      this.queryForm.quarter = currentQuarter;

      this.activeIndex = this.queryForm.month;

      this.pageLoading = true;

      this.getTargets();
      this.$api
        .getBusinessOpts()
        .then(res => {
          this.typeOpts = S.descToArrHandle(res.date_type);
          this.yearOpts = S.descToArrHandle(res.year_desc);
        })
        .finally(res => {
          console.log('🚀 ~ goals.vue:265 ~ initData ~ res---->', res);
          this.pageLoading = false;
        });
    },

    initChart(data) {
      // 流水
      this.sale_percent = Number(data[0].rate);
      console.log('🚀 ~ goals.vue:368 ~ initChart ~ this.sale_percent---->', this.sale_percent);

      // 实耗目标
      this.consume_percent = Number(data[1].rate);
      console.log('🚀 ~ goals.vue:372 ~ initChart ~ this.consume_percent---->', this.consume_percent);

      // 人次目标
      this.person_percent = Number(data[2].rate);
      console.log('🚀 ~ goals.vue:376 ~ initChart ~  this.person_percent---->', this.person_percent);
    },

    initTable(data) {
      // 人次目标 order_cash_turnover_target
      // 交易额目标（元 order_cash_turnover
      // 实耗目标 serv_used_money_target

      // 1. 生成表头
      const monthCols = data.map(item => ({
        title: item.title,
        key: item.title,
        minWidth: 100,
        align: 'center',
      }));
      this.achieveCols = [{ title: '目标纬度', key: 'target', minWidth: 150, align: 'center' }, ...monthCols];

      // 2. 生成表体
      const rowMap = [
        { key: 'order_cash_turnover_target', label: '业绩（元）', minWidth: 150 },
        { key: 'serv_used_money_target', label: '营收（元）', minWidth: 150 },
        { key: 'customers_target', label: '到店人次', minWidth: 150 },

        // { key: 'order_cash_turnover_target', label: '交易额目标（元）' },
        // { key: 'serv_used_money_target', label: '实耗目标（元）' },
        // { key: 'customers_target', label: '人次目标' },
      ];
      this.achieveData = rowMap.map(row => {
        const rowData = { target: row.label };
        data.forEach(item => {
          rowData[item.title] = item[row.key] || '-';
        });
        return rowData;
      });
    },
    onType() {
      if (this.queryForm.date_type == 1) {
        this.activeIndex = this.queryForm.month;
      } else if (this.queryForm.date_type == 2) {
        this.activeIndex = this.queryForm.quarter;
      } else {
        this.activeIndex = '';
      }
      this.getTargets();
    },
    onYear() {
      this.getTargets();
    },
    onCardClick(item) {
      if (item.desc == '已超时') return;
      const value = item.value;
      this.activeIndex = value;

      if (this.queryForm.date_type == 1) {
        this.queryForm.month = value;
      }

      if (this.queryForm.date_type == 2) {
        this.queryForm.quarter = value;
      }

      this.getTargets();
    },

    getTargets() {
      this.$api
        .getBusinessTargets({
          date_type: this.queryForm.date_type,
          year: this.queryForm.year,
          month: this.queryForm.month,
          quarter: this.queryForm.quarter,
        })
        .then(res => {
          this.statistics_scope = res.statistics_scope;
          this.target_achieve_data = res.target_achieve_data;
          this.achieve_detail_data = res.achieve_detail_data;

          this.is_target = res.is_target;

          this.initChart(res.target_achieve_data);
          this.initTable(res.achieve_detail_data || []);

          // 示例：假设第2个超时，第3个未设置
          this.timeoutIndex = 1;
          this.unsetIndex = 2;
        });
    },

    onAddTarget() {
      this.visible = true;
      const now = new Date();
      const currentMonth = now.getMonth() + 1; // 1~12
      const targetData = [];
      for (let i = currentMonth; i <= 12; i++) {
        targetData.push({
          month: i,
          order_cash_turnover_target: 0, // 交易
          serv_used_money_target: 0, //实耗
          customers_target: 0, //人
        });
      }

      this.targetData = targetData;
    },

    // postBusinessSave

    onOk() {
      // 校验所有目标值必须填写
      const isValid = this.targetData.every(
        item => item.order_cash_turnover_target > 0 && item.serv_used_money_target > 0 && item.customers_target > 0
      );
      if (!isValid) {
        this.$Message.warning('请将所有目标数值填写完整');
        return;
      }

      this.saveLoading = true;
      this.$api
        .postBusinessSave({ target: this.targetData })
        .then(res => {
          this.initData();
        })
        .finally(() => {
          this.visible = false;
          this.saveLoading = false;
        });
    },
  },
};
</script>
<style scoped lang="less">
.export-icon {
  width: 14px;
  height: 13px;
  margin-right: 6px;
  // background-color: #fff;
}
.app-page-wrapper {
  position: relative;
  background-color: #fff;
  margin: 10px 0;
  padding: 26px;
  min-height: 90vh;
  border-radius: 2px;
  z-index: 2;
}

.gap-20 {
  gap: 20px;
}

.card_box {
  gap: 20px;
  .card_item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 70px;
    height: 70px;
    min-width: 50px;
    min-height: 50px;
    border-radius: 8px;
    border: 1.5px solid #e0e0e0;
    cursor: pointer;
    background: #fafbfc;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
    font-size: 13px;
    color: #676767;

    .tip {
      position: absolute;
      top: 0px;
      left: 0px;
      padding: 2px 4px;
      border-top-left-radius: 8px;
      border-bottom-right-radius: 8px;
      font-size: 10px;
      background: #f0f0f0;
      color: #888;
      z-index: 1;
      letter-spacing: 1px;
    }

    &.disabled {
      background: #f5f5f5;
      color: #bbb;
      border: 1px dashed #bbb;
      cursor: not-allowed;

      .tip {
        background: #f5f5f5;
        color: #bbb;
      }
    }

    &.unset {
      background: #ffffff;
      color: #5c5c5c;
      border: 1px solid #c1c1c1;
      .tip {
        background: #fff;
        color: #5c5c5c;
      }
    }

    &.succes {
      border: 1px solid #c1c1c1;
      background: #ffffff;
      color: #5c5c5c;
      .tip {
        background: #fff;
        color: #5c5c5c;
      }
    }

    &.active {
      border: 1px solid #1890ff;
      background: #e6f7ff;
      color: #1890ff;
      .tip {
        background: #fff;
        color: #1890ff;
      }
    }
  }
}

.chart-box {
  margin: 30px 0;
  .chart-title {
    display: flex;
    gap: 8px;
    .line {
      width: 4px;
      height: 18px;
      background: linear-gradient(90deg, #155bd4 0%, #4988fd 100%);
      border-radius: 2px;
    }
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 18px;
    color: #222;
  }
  .flex {
    gap: 24px;
  }
  .chart-box-i {
    background: #fff;
    border: 1px solid #e5e6eb;
    border-radius: 8px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
    padding: 24px 16px 18px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    // justify-content: space-between;
    min-width: 260px;
    min-height: 300px;
    position: relative;
    transition: box-shadow 0.2s;

    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin-bottom: 20px;
      text-align: center;
    }
    .chart-detail {
      width: 100%;
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      font-size: 14px;
      color: #666;
      .row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .label {
          color: #8a8a8a;
          min-width: 48px;
        }
        .value {
          font-weight: 500;
          color: #222;
        }
      }
      .row.target .label {
        color: #1890ff;
        font-weight: 500;
      }
      .row.target .value {
        color: #1890ff;
      }
    }
  }
}

.achieve-box {
  margin: 30px 0;
  .achieve-title {
    display: flex;
    gap: 8px;
    .line {
      width: 4px;
      height: 18px;
      background: linear-gradient(90deg, #155bd4 0%, #4988fd 100%);
      border-radius: 2px;
    }
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 18px;
    color: #222;
  }
}

.not-box {
  text-align: center;
  margin: 140px 0;
  .not-title {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0;
    color: #606060;
  }
}

.modal-box {
  // height: 7vh;
  // overflow: auto;

  .tip {
    font-size: 14px;
    margin-bottom: 10px;
    color: #8a8a8a;
  }

  .title {
    font-size: 16px;
    margin: 10px 0;
    color: #676767;
  }

  .cell {
    padding: 8px 8px;
    height: 50px;
    display: flex;
    align-items: center;
    // text-align: center;
    // line-height: 50px;
    border-bottom: 1px solid #e5e6eb;

    &:last-child {
      border-bottom: none;
    }
  }
}

:deep(.cell-parent .ivu-table-cell) {
  padding: 0;
}
:deep(.ivu-table-header .cell-parent) {
  padding: 0 8px;
}
</style>
