<template>
  <div v-loading="pageLoading" class="app-page-wrapper">
    <!-- Header -->

    <div v-show="!pageLoading">
      <div class="header flex gap-20">
        <div class="flex flex-item-align">
          <div>统计时间：</div>
          <customDatePicker
            v-model="formDate"
            defaultType="date"
            :type-options="['date', 'week', 'month']"
            @getDateType="getDateType"
          ></customDatePicker>
        </div>

        <div>
          <Button size="small" type="primary" style="width: 106px; height: 32px" @click="onReport">
            <img class="export-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0702/154428_97382.png" />
            导出数据
          </Button>
        </div>
      </div>

      <div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            服务客群
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_kq }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(service_customer_groupCols.length) }"
            style="max-width: 1171px; min-width: 900px"
            :loading="loading"
            :columns="service_customer_groupCols"
            :data="service_customer_group"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }} </template>
          </Table>
        </div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            评价
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_pj }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(appraiseCols.length) }"
            :loading="loading"
            :columns="appraiseCols"
            :data="appraise"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }} </template>
          </Table>
        </div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            服务明细
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_mx }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(service_detailsCols.length) }"
            :loading="loading"
            :columns="service_detailsCols"
            :data="service_details"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }} </template>
          </Table>
        </div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            复购
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_fg }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(repurchaseCols.length) }"
            :loading="loading"
            :columns="repurchaseCols"
            :data="repurchase"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }} </template>
          </Table>
        </div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            收入分析
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_fx }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(revenue_analysisCols.length) }"
            :loading="loading"
            :columns="revenue_analysisCols"
            :data="revenue_analysis"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }} </template>
          </Table>
        </div>
      </div>
    </div>

    <ReportExport v-model="reportModalVisible" title="理疗师统计表" :api-name="apiName"></ReportExport>
  </div>
</template>

<script>
import tip from './data/tip';
import ReportExport from './components/ReportExport';
import customDatePicker from '@/view/statistics/components/CustomDatePicker/CustomDatePicker.vue';

export default {
  name: 'Physio',
  components: {
    customDatePicker,
    ReportExport,
  },
  data() {
    return {
      reportModalVisible: false,
      pageLoading: false,
      loading: false,
      apiName: 'getPhysioReport',
      formDate: [],

      month: '',
      date_type: 'month',
      typeOpts: [],

      date: '',

      type: 'month',
      format: '',

      st: '',
      et: '',

      tips: {
        ...tip.phyTip,
      },

      // 服务客群
      service_customer_group: [],
      service_customer_groupCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '服务客户总数',
          key: 'total_customer',
          align: 'center',
          width: 150,
        },
        {
          title: '老客数',
          key: 'old_customer',
          align: 'center',
          width: 150,
        },
        {
          title: '新客数',
          key: 'new_customer',
          align: 'center',
          width: 150,
        },
        {
          title: '会员客数',
          key: 'member_customer',
          align: 'center',
          width: 150,
        },
        {
          title: '非会员客数',
          key: 'no_member_customer',
          align: 'center',
          width: 150,
        },
        {
          title: '预约客户数',
          key: 'reservation_customer',
          align: 'center',
          width: 150,
        },
        {
          title: '非预约客户数',
          key: 'no_reservation_customer',
          align: 'center',
          width: 150,
        },
      ],
      //评价
      appraise: [],
      appraiseCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '评价数',
          key: 'total',
          align: 'center',
          width: 150,
        },
        {
          title: '带文字评价数',
          key: 'have_text',
          align: 'center',
          width: 150,
        },
      ],

      // 服务详情
      service_details: [],
      service_detailsCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '服务项目数',
          key: 'service_items',
          align: 'center',
          width: 150,
        },
      ],

      // 复购
      repurchase: [],
      repurchaseCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '复购人数',
          key: 'repurchase_customer',
          align: 'center',
          width: 150,
        },
        {
          title: '复购率',
          key: 'repurchase_rate',
          align: 'center',
          width: 150,
        },
      ],
      // 收入分析
      revenue_analysis: [],
      revenue_analysisCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '实耗服务数量',
          key: 'serv_used_num',
          align: 'center',
          width: 150,
        },
        {
          title: '实耗业绩',
          key: 'serv_used_money',
          align: 'center',
          width: 150,
        },
        {
          title: '销售商品数量',
          key: 'product_quantity',
          align: 'center',
          width: 150,
        },
        {
          title: '销售业绩',
          key: 'product_revenue',
          align: 'center',
          width: 150,
        },
      ],
    };
  },
  watch: {
    formDate: {
      handler(val) {
        if (val && val[0]) {
          this.st = val[0];
          this.et = val[1];
          this.getList();
        }
      },
    },
  },
  mounted() {
    this.scrollTop();
  },
  activated() {
    this.scrollTop();
  },
  methods: {
    tableWidth(length, minWidth = 150) {
      // 根据 colums 的数量和每列的最小宽度来计算
      return `${length * minWidth}px`;
    },
    scrollTop() {
      this.$nextTick(() => {
        const wrapper = document.querySelector('.app-wrapper');
        if (wrapper) wrapper.scrollTop = 0;
      });
    },
    getDateType(getDateType) {
      this.date_type = getDateType;
    },

    getList() {
      this.loading = true;
      this.pageLoading = true;
      this.$api
        .getPhysioList({
          st: this.st,
          et: this.et,
          date_type: this.date_type,
        })
        .then(res => {
          this.appraise = res.appraise || [];
          this.repurchase = res.repurchase || [];
          this.revenue_analysis = res.revenue_analysis || [];
          this.service_customer_group = res.service_customer_group || [];
          this.service_details = res.service_details || [];
        })
        .finally(() => {
          this.loading = false;
          this.pageLoading = false;
        });
    },

    onReport() {
      this.reportModalVisible = true;
    },
  },
};
</script>
<style scoped lang="less">
.export-icon {
  width: 14px;
  height: 13px;
  margin-right: 6px;
  // background-color: #fff;
}
.gap-20 {
  gap: 20px;
}

.gap-10 {
  gap: 10px;
}

.header {
  // position: sticky;
  // top: -20px;
  // z-index: 9;
  // padding: 20px 0px 16px 0px;
  // background-color: #fff;
  // justify-content: space-between;
}

.app-page-wrapper {
  position: relative;
  background-color: #fff;
  min-height: 90vh;
  // height: 91vh;
  // overflow: auto;
  padding: 24px;
  margin-top: 10px;
  z-index: 2;

  .table-item {
    margin-top: 36px;

    .title {
      display: flex;
      gap: 8px;

      .line {
        width: 4px;
        height: 18px;
        background: linear-gradient(90deg, #155bd4 0%, #4988fd 100%);
        border-radius: 2px;
      }

      font-size: 16px;
      font-weight: 500;
      color: #222;
      margin-top: 14px;
      margin-bottom: 18px;
    }
  }
}
</style>
