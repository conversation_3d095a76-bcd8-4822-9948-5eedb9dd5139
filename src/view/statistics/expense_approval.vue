<template>
  <div class="expense-wrapper">
    <standard-table
      :columns="tableCols"
      :data="list"
      :loading="tableLoading"
      :current.sync="queryFormData.page"
      :page-size.sync="queryFormData.pageSize"
      :total="total"
      @on-change="onPageChange"
    >
      <template slot="header">
        <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
          <FormItem>
            <DatePicker
              v-model="times"
              class="time-range"
              clearable
              format="yyyy-MM-dd"
              placeholder="请选择日期"
              type="daterange"
              @on-change="onTimesChange"
            ></DatePicker>
          </FormItem>
          <FormItem>
            <Select v-model="queryFormData.status" placeholder="审批状态" clearable>
              <Option v-for="item in statusOpt" :value="item.id" :key="item.value">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          </FormItem>
        </Form>
      </template>
    </standard-table>
  </div>
</template>

<script>
import S from 'libs/util';
import search from '@/mixins/search';
import renderHeader from '@/mixins/renderHeader';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  st: '',
  ent: '',
  status: '',
  r: '',
};
export default {
  name: 'list',
  mixins: [search, renderHeader],
  components: { StandardTable },
  data() {
    return {
      apiName: 'getCheckList', // 获取明细列表
      queryFormData: { ...init_query_form_data },

      times: [],
      status_remark: '',
      status: '',
      rowData: {},

      statusOpt: [],
      tableCols: [
        {
          title: '序号',
          type: 'index',
          width: 60,
          align: 'center',
        },
        { title: '费用类型', key: 'fee_type_text', align: 'center', width: 90 },
        { title: '费用金额', key: 'amount', align: 'center', width: 150 },
        { title: '平摊时间', key: 'divide_month_text', align: 'center', width: 80 },
        { title: '记录时间', key: 'record_time', align: 'center', width: 160 },
        {
          title: '提交人',
          key: 'operator',
          align: 'center',
        },
        {
          title: '状态',
          key: 'status_text',
          align: 'center',
        },
        {
          title: '审核备注',
          slot: 'status_remark',
          automate: 'string',
          align: 'center',
        },
      ],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getFeeOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    onTimesChange(times) {
      this.handleTimeChange(times);
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    getFeeOptions() {
      this.$api.getFeeOpt().then(res => {
        this.statusOpt = S.descToArrHandle(res.status_desc);
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.getsList();
    next();
  },
};
</script>

<style scoped lang="less">
.expense-wrapper {
  background-color: #fff;
  padding: 12px;
  margin-top: 12px;
}
:deep(.ivu-form-inline .ivu-input-wrapper) {
  width: 200px;
}
</style>
