// 概况
const generalTip = `<div><b>1、总业绩：</b>现金收入（诊所当日进账的所有钱总和），即： 订单交易额- 订单交易额里储值支付的部分 + 储值金额。</div>
<div><b>2、总营收：</b></div>
   <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:15px">①</span>、订单里的实物商品，订单已完成后，T+3算营收</div>
   <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:15px">②</span>、订单里的虚拟商品，核销后，T+1算营收、过期后立即确认收入。</div>
   <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:15px">③</span>、即今日营收为 T-3日的实物商品收入和T-1天的虚拟商品收入。</div>
   <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:15px">④</span>、包含聚合支付和标记支付</div>
<div><b>3、订单交易额：</b>当日店铺所有订单的实际支付部分之和减去退款部分之和。</div>
<div><b>4、储值金额：</b>店铺当日储值订单金额总和，减去退款的部分。</div>
<div><b>5、消费人数：</b>当日进店产生（商城、HIS）订单的人数。</div>
<div><b>6、储值人数：</b>当日有过储值行为的客户，去重。</div>
<div><b>7、新增会员：</b>当日新开会员数。</div>
<div><b>8、新增理疗数：</b>当日订单里，新增的理疗总次数。</div>`;

const flowTip = `<div><b>1、订单业绩：</b>订单交易额- 订单交易额里储值付的部分。</div>
<div><b>2、储值业绩：</b>店铺当日储值订单金额总和，减去退款的部分。</div>`;

// 交易额分析
const sumTip = `<div><b>1、订单交易额：</b>当日店铺所有订单的实际支付部分之和减去退款部分之和;</div>
<div><b>2、新客交易额：</b>当日总交易额中的首次到店客户贡献的收入。</div>
<div><b>3、老客交易额：</b>当日总交易额中的非首次到店客户贡献的收入。</div>
<div><b>4、会员交易额：</b>当日总交易额中，属于会员客户的部分贡献的收入。</div>
<div><b>5、非会员交易额：</b>当日总交易额中，属于非会员客户的部分贡献的收入。</div>
<div><b>6、聚合支付：</b>即当日通过聚合支付的订单金额。</div>
<div><b>7、云储值支付：</b>即当日云直通储值消耗。</div>
<div><b>8、原门店储值支付：</b>即当日原门店储值消耗。</div>
<div><b>9、三方平台支付：</b>美团/抖音支付订单。</div>
<div><b>10、商保支付：</b>商保支付。</div>
<div><b>11、医保支付：</b>医保/线上医保。</div>
<div><b>12、其他支付：</b>月结等其他支付。</div>`;

// 营收（实耗）分析
const revenTip = `<div><b>1、总营收：</b></div>
   <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:15px">①</span>、订单里的实物商品，订单已完成后，T+3 算营收</div>
   <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:15px">②</span>、订单里的虚拟商品，核销后，T+1算营收；过期后立即确认收入。</div>
   <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:15px">③</span>、即今日营收为 T-3日的实物商品收入和T-1天的虚拟商品收入。</div>
   <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size:15px">④</span>、含聚合支付和标记支付</div>
<div><b>2、新客营收：</b>总营收中，客户属性为新客的营收。</div>
<div><b>3、老客营收：</b>总营收中，客户属性为新客的营收。</div>
<div><b>4、会员营收：</b>总营收中，客户属性为会员的营收。</div>
<div><b>5、非会员营收：</b>总营收中，客户属性为非会员的营收。</div>
<div><b>6、理疗营收：</b>总营收中，商品属性或服务属性为理疗的部分。</div>
<div><b>7、医疗营收：</b>总营收中，商品属性或服务属性为医疗的部分。</div>
<div><b>8、养疗营收：</b>总营收中，商品属性或服务属性为养疗的部分。</div>`;

// 储值分析
const storeTip = `<div><b>1、储值金额：</b>店铺当日储值订单金额总和，减去退款的部分。</div>
<div><b>2、储值人数：</b>店铺当日有过储值充值行为的人数。</div>
<div><b>3、储值退款人数：</b>店铺当日有过储值退款的人数。</div>
<div><b>4、储值消费：</b>当日交易额中，支付方式为储值支付的订单。</div>
<div><b>5、储值剩余金额：</b>店铺剩余储值总额。</div>
<div><b>6、储值消耗率：</b>反映储值消耗情况，大概多少日消耗完。</div>
<div><b>7、7日滚动消耗率：</b>反映最近储值平均消耗情况。</div>`;

// 理疗分析
const treatTip = `
<div><b>1、核销理疗数：</b>当日核销明细里，理疗的总次数。</div>
<div><b>2、理疗核销率：</b>当日核销理疗 / 剩余可用理疗次数。</div>
<div><b>3、核销理疗价值：</b>当日核销明细里，理疗的总价值。</div>
<div><b>4、理疗核销价值比：</b>当日核销价值 / 剩余可核销理疗价值。</div>
<div><b>5、理疗新增数：</b>当日订单里，新增的理疗总次数。</div>
<div><b>6、理疗新增价值：</b>当日订单里，新增的理疗总价值。</div>
<div><b>7、剩余理疗可用次数：</b>店铺剩余在有效期内理疗总次数。</div>
<div><b>8、剩余理疗总价值：</b>店铺剩余在有效期内理疗总价值。</div>
<div><b>9、本月到期理疗价值：</b>有效期在当前月的理疗价值总数。</div>
<div><b>10、本月到期理疗次数：</b>有效期在当前月的理疗总次数。</div>
<div><b>11、7日理疗滚动核销率：</b>7日平均理疗核销率。</div>
<div><b>12、7日理疗滚动核销价值比：</b>7日平均核销价值比。</div>`;

//客群分析
const cusTip = `
<div><b>1、到店人次：</b>未退款的 HIS订单、商城订单、储值订单和核销记录任一条记录，一个自然日算一人次，多个自然日累加，不去重。</div>
<div><b>2、消费用户数：</b>当日进店产生（商城、HIS）订单的人数。</div>
<div><b>3、储值人数：</b>当日有过储值行为的客户，去重</div>
<div><b>4、核销人数：</b>当日进行卡券核销的用户数。</div>
<div><b>5、新增会员人数：</b>当日新开会员数。</div>
<div><b>6、消费新客数：</b>当日消费用户数中，属于当日首次到店用户数。</div>
<div><b>7、消费老客数：</b>当日消费用户数中，不属于当日首次到店用户数。</div>
<div><b>8、客单价：</b>当日总交易额/ 当日消费用户数。</div>
<div><b>9、消费客户新客占比：</b>消费新客数 / 当日消费用户数。</div>
<div><b>10、消费客户老客占比：</b>消费老客数 / 到店老客数。</div>
`;

//新客来源
const newTip = `<div><b>1、新客总数：</b>当日在店铺新注册用户数。</div>
<div><b>2、转介绍：</b>当日新客中，用户来源为 亲友推荐的用户数。</div>
<div><b>3、媒体报道：</b>当日新客中，用户来源为 媒体报道的用户数。</div>
<div><b>4、活动推广：</b>当日新客中，用户来源为 活动推广的用户数。</div>
<div><b>5、免费义诊：</b>当日新客中，用户来源为 免费义诊断的用户数。</div>
<div><b>6、自然流量：</b>当日新客中，用户来源为 自然流量的用户数。</div>
<div><b>7、抖音：</b>当日新客中，用户来源为 抖音的用户数。</div>
<div><b>8、美团：</b>当日新客中，用户来源为 美团的用户数。</div>
<div><b>9、服务预约：</b>当日新客中，用户来源为 服务预约的用户数。</div>
<div><b>10、渠道合作：</b>当日新客中，用户来源为 渠道合作的用户数。</div>
<div><b>11、其他：</b>当日新客中，用户来源为 其他的用户数。</div>`;

//预约分析
const appTip = `<div><b>1、当日预约：</b>预约当日到店用户数量。</div>
<div><b>2、实际到店：</b>预约单中实际点击了“已到店”的数量。</div>
<div><b>3、实际消费：</b>预约并当日产生消费订单的用户数。</div>
<div><b>4、实际到店率：</b>预约当日到店用户当日到店比例。</div>
<div><b>5、预约消费率：</b>预约当日到店用户当日消费比例。</div>`;

/**营业计划 */
const goals_flow_tip = `（订单实收金额（包括储值充值）- 订单退款） -  储值消耗金额`;
const goals_used_tip = `完成核销且已D+1后确认结算。`;
const goals_num_tip = `1、统计服务消耗，shop订单(不包括购买树家商品)，his订单，储值充值+消耗；
2、同一天同一人做去重；
3、从以支付成功的订单（包括支付成功后退款）。`;

/**医生人效 */

const doctorTip = {
  doctor_labar: `见诊人数：该医生his中只要创建过问诊计一人，当天做去重。
初诊人数：该医生当日his中首次创建问诊的用户。
初诊成交人数：该医生当日his中首次创建问诊的用户是否有支付成功的行为(发生退款不影响成交人数)。
初诊转化率：该医生当日成交初诊人数(退款行为不计算)/当日总初诊人数。
复诊人数：该医生接诊的老患者人数（排除首次就诊）。
复诊转化率：该医生统计周期内复诊人数/该医生历史患者总数。`,

  doctor_fg: `复购率：该医生患者再次(在his)购买的人数/有过(在his)购买的人数。`,
  doctor_ys: `成交业绩：该医生当日his中患者支付总额 - 退款金额。
初诊成交业绩：该医生当日his首次就诊的患者支付总额 - 退款金额。
复诊成交业绩：该医生接诊的老患者支付总额（排除首次支付金额）- 退款金额。`,

  doctor_zc: `中药饮片：该医生his中饮片开单收费金额 - 退款金额。
中成药：该医生his中成药开单收费金额 - 退款金额。
理疗：该医生his中理疗类开单收费金额 - 退款金额。
养疗：该医生his中养疗开单收费金额 - 退款金额。
挂号费：该医生his挂号费开单收费金额 - 退款金额。
问诊费：该医生his问诊费开单收费金额 - 退款金额。
加工费：该医生his加工费开单收费金额 - 退款金额。`,

  doctor_fx: `实耗服务数量：服务确认核销的数量 - 撤销核销数量。
实耗业绩：核销完成d+1结算的金额。
销售商品数量：实物确认签收的商品数量 - 退款数量。
销售业绩：签收完成d+7结算的金额。`,
};

/**理疗师人效 */

const phyTip = {
  phy_kq: `服务客户数：提供服务核销完成的数量，包含撤回核销和退款，同日做去重。
老客数：老用户完成核销。
新客数：新用户完成核销。
会员客数：会员完成核销。
非会员客数：非会员完成核销。
预约客户数：通过预约单生成的预约且完成核销，包括美团、抖音等三方平台。
非预约客户数：未通过系统发起预约，线下到店直接服务核销。`,
  phy_pj: `评价数：通过小程序或客户端，用户实际提交评价的数量，包括带文字。
带文字评价数：通过榕树堂或客户端，用户实际提交带文字评价的数量。`,
  phy_mx: `服务项目数：核销完成的服务次数。`,
  phy_fg: `复购人数：统计周期内消费≥2次的客户数。
复购率：统计周期内消费≥2次的客户数/总消费客户数。`,
  phy_fx: `实耗服务数量：服务确认核销的数量 - 撤销核销数量。
实耗业绩：核销完成d+1结算的金额。
销售商品数量：实物确认签收的商品数量 - 退款数量。
销售业绩：签收完成d+7结算的金额。`,
};

export default {
  generalTip,
  sumTip,
  storeTip,
  appTip,
  newTip,
  cusTip,
  treatTip,
  revenTip,
  flowTip,
  goals_flow_tip,
  goals_used_tip,
  goals_num_tip,
  // doctor_labar,
  // doctor_fg,
  // doctor_fx,
  // doctor_ys,
  // doctor_zc,
  doctorTip,

  phyTip,

  // phy_fg,
  // phy_fx,
  // phy_kq,
  // phy_mx,
  // phy_pj,
};
