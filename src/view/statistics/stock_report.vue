<template>
  <div :style="{ margin: '10px', padding: '16px', background: '#FFFFFF' }">
    <standard-table
      :loading="tableLoading"
      ref="user-table"
      class="user-table"
      border
      :columns="calcTableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form class="table-form" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <FormItem label="查询时间" :label-width="60">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              :picker-options="pickerOptions"
              size="small"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="min-width: 255px; max-width: 300px"
              @change="handleTimeChange"
            >
            </el-date-picker>
          </FormItem>
          <FormItem>
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="downloadExcel(queryFormData)">导出</Button>
            <span class="list-reset-btn" @click="onResetSearch">
              <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
              <span>清除条件</span>
            </span>
          </FormItem>
        </Form>
      </template>
      <template #generic_name="{ row }">
        <div
          v-if="row.generic_name && rootPageCheck('/stock/product/edit')"
          class="link text-ellipsis"
          v-overflow-tooltip="{ placement: 'top' }"
          @click="toProductDetail(row)"
        >
          {{ row.generic_name }}
        </div>
        <div
          class="text-ellipsis"
          v-else-if="row.generic_name && !rootPageCheck('/stock/product/edit')"
          v-overflow-tooltip="{ placement: 'top' }"
        >
          {{ row.generic_name }}
        </div>
        <div v-else>-</div>
      </template>
      <template #expire_time="{ row }">
        <div v-if="+row.expire_time">{{ row.expire_time }}</div>
        <div v-else>-</div>
      </template>
      <template #operate="{ row }">
        <a @click="toProductCostDetail(row)">单价明细</a>
      </template>
    </standard-table>
  </div>
</template>

<script>
import StandardTable from '@/components/StandardTable/index.vue';
import search from '@/mixins/search';
import downloadExcel from '@/mixins/downloadExcel';
import renderHeader from '@/mixins/renderHeader';
import S from '@/libs/util';
import { cloneDeep  } from 'lodash-es';
import { openNewPage } from '@/utils/helper';
import moment from 'moment';

const anomaliesNumber =
  '' +
  '统计在选择查询的时间段内，出库类型为不合格库存和报损出库的出库数量和金额(元)\n' +
  '\n' +
  '金额=出库数量X出库当时的单价\n' +
  '\n' +
  '例如今天是2025年8月1日，某货品单价为10，选择的查询日期是2025年5月1日-2025年5月31日，此货品在5月2日不合格出库的出库数量为1，当时货品单价为2；在5月2日报损出库的出库数量为3，当时货品单价为4\n' +
  '\n' +
  '那么本期异常数量为4=1+3，本期异常金额为14=1X2+3X4';
const end_time_html =
  '' +
  '是指截止到今天，到期时间小于六个月的货品，其中最近的到期时间。\n' +
  '\n' +
  '例如今天是2025年8月1日，某货品的库存里有三个批次，到期时间分别为：批次1：2025年8月12日，批次2：2025年8月10日，批次3：2027年1月1日。那么最近到期时间按照批次2来计算，是9天。';

const end_amount_html =
  '' +
  '是指截止到今天，到期时间小于六个月的货品，其中最近的到期时间货品数量。\n' +
  '\n' +
  '例如今天是2025年8月1日，某货品的库存里有三个批次，到期时间及数量分为：批次1：2025年8月12日，货品数量为30；批次2：2025年8月10日，货品数量为10；批次3：2027年1月1日，货品数量为100；那么最近到期数量按照批次2来计算，数量取10。';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  st: '',
  et: '',
};
export default {
  name: 'stock_report',
  components: { StandardTable },
  mixins: [search, renderHeader, downloadExcel],
  data() {
    return {
      apiName: 'getStockReportList',
      downloadApiName: 'stockReportExportUrl',
      pickerOptions: {
        disabledDate(date) {
          return moment(moment(date).format('YYYY-MM-DD')).isAfter(moment(moment().format('YYYY-MM-DD')));
        },
      },
      dateRange: [],
      queryFormData: cloneDeep(init_query_form_data),
      tableCols: [
        { title: '货品编号', key: 'prod_id', width: 100, align: 'center' },
        { title: '货品名称', slot: 'generic_name', width: 120, align: 'center' },
        { title: '生产厂家', slot: 'manufacturer', automate: 'string', width: 120, align: 'center' },
        {
          title: '期初',
          key: 'begin',
          align: 'center',
          children: [
            { title: '期初数量', slot: 'begin_num', automate: 'string', width: 100, align: 'center' },
            { title: '期初单价', slot: 'begin_price', automate: 'string', width: 100, align: 'center' },
            { title: '期初金额', slot: 'begin_amount', automate: 'string', width: 140, align: 'center' },
          ],
        },
        {
          title: '本期入库',
          slot: 'in',
          align: 'center',
          children: [
            { title: '本期入库数量', slot: 'in_num', automate: 'string', width: 100, align: 'center' },
            { title: '本期入库单价', slot: 'in_price', automate: 'string', width: 120, align: 'center' },
            { title: '本期入库金额', slot: 'in_amount', automate: 'string', width: 140, align: 'center' },
          ],
        },
        {
          title: '本期出库',
          slot: 'out',
          align: 'center',
          children: [
            { title: '本期出库数量', slot: 'out_num', automate: 'string', width: 100, align: 'center' },
            { title: '本期出库单价', slot: 'out_price', automate: 'string', width: 120, align: 'center' },
            { title: '本期出库金额', slot: 'out_amount', automate: 'string', width: 140, align: 'center' },
          ],
        },
        {
          title: '期末数量',
          slot: 'end',
          align: 'center',
          children: [
            { title: '期末数量', slot: 'end_num', automate: 'string', width: 100, align: 'center' },
            { title: '期末单价', slot: 'end_price', automate: 'string', width: 120, align: 'center' },
            { title: '期末金额', slot: 'end_amount', automate: 'string', width: 140, align: 'center' },
          ],
        },
        {
          title: '本期异常数量',
          slot: 'abnormal_num',
          automate: 'string',
          width: 120,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, anomaliesNumber, 400, 'custom_white_space'),
        },
        {
          title: '本期异常金额',
          slot: 'abnormal_amount',
          automate: 'string',
          width: 120,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, anomaliesNumber, 400, 'custom_white_space'),
        },
        {
          title: '最后到期时间(截止今天)/天',
          slot: 'expire_time',
          width: 180,
          align: 'center',
          renderHeader: (h, params) =>
            this._renderHeader(h, params, end_time_html, 400, 'custom_white_space', {
              paddingLeft: '20px',
              paddingRight: '30px',
            }),
        },
        {
          title: '最后到期数量(截止今天)',
          slot: 'expire_num',
          automate: 'string',
          width: 120,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, end_amount_html, 400, 'custom_white_space'),
        },
        { title: '操作', slot: 'operate', width: 120, align: 'center', fixed: 'right' },
      ],
    };
  },
  computed: {
    calcTableCols() {
      const ProductCostDetail = this.rootPageCheck('/stock/product-cost/ProductCost/detail');
      if (!ProductCostDetail) return this.tableCols?.filter(item => item.slot !== 'operate');
      return this.tableCols;
    },
  },
  created() {
    this.queryFormData = S.merge(init_query_form_data, this.$route.query);
    this.submitQueryForm(true);
    // console.log(this.rootPageCheck('/stock/product/edit'), 'this.rootPageCheck(\'/stock/product/edit\')')
  },
  methods: {
    onResetSearch() {
      this.queryFormData = cloneDeep(init_query_form_data);
      this.dateRange = [];
      this.submitQueryForm();
    },
    toProductDetail(row) {
      if (!row?.prod_id) {
        this.$Message.error('无该货品信息');
        return;
      }
      openNewPage('/stock/product/edit', {
        id: row?.prod_id,
      });
    },
    toProductCostDetail(row) {
      if (!row?.cost_id) {
        this.$Message.error('该货品无单价明细');
        return;
      }
      openNewPage('/stock/product-cost/ProductCost/detail', {
        id: row?.cost_id,
      });
    },
    rootPageCheck(path = '') {
      if (!path) return false;
      let routes = this.$store.state.router.routes;
      // 递归检查路径是否存在于router数组中
      const hasPath = (routes, targetPath) => {
        if (!Array.isArray(routes)) return false;

        for (let route of routes) {
          // 检查当前路由的path
          if (route.path === targetPath) {
            return true;
          }

          // 递归检查children
          if (route.children && hasPath(route.children, targetPath)) {
            return true;
          }
        }

        return false;
      };
      return hasPath(routes, path);
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    if (to.query.st && to.query.et) {
      this.dateRange = [to.query.st, to.query.et];
    } else {
      this.dateRange = [];
    }
    this.getsList();
    next();
  },
};
</script>

<style scoped lang="less">
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.link {
  color: #115bd4;
  cursor: pointer;
}
</style>
