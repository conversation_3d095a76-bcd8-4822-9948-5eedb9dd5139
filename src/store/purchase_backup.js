// import { Message } from 'view-design';
// import request from 'libs/io';
// import { cloneDeep } from 'lodash-es';
// import { $operator } from '@/libs/operation';
// import { getClinicid } from '@/libs/runtime';
// import select from 'view-design/src/components/select';
//
// const state = {
//   showPurchaseModal: false, // 显示采购单弹窗
//   selectGoodsList: [],
//   settleVisible: false, //是否显示提示弹窗
//   mergeGoodsList_copy: [],
//   showMultipleSpecsModal: false, // 显示一个spu对应多个sku的弹窗
//   multipleSkuList: [], // 弹窗里面的skuList
//   covidListof: {}, // 新冠listof
//   ysHomologyListof: {}, // 药食同源listof
//   selectGoodsListMap: {},
// };
//
// const mutations = {
//   // 采购订单进行重新加入采购单的功能
//   REJOIN: (state, { list, router, dispatch }) => {
//     let mergeGoodsList = cloneDeep(list);
//     // state.mergeGoodsList_copy = cloneDeep(list);
//     let merge_ids = [];
//     let merge_taocan_goods_ids = [];
//
//     // 统一数据格式
//     mergeGoodsList.map(item => {
//       // 判读是否是套餐
//       if (item.is_taocan == 1) {
//         item.id = item.goods_id;
//         item.quantity = Number(item.num);
//         item.children = item.attrs;
//         item._showChildren = false;
//         item.is_taocan_goods = item.is_taocan;
//         merge_taocan_goods_ids.push(item.goods_id);
//       } else {
//         item.id = item.goods_sku_id;
//         item.quantity = Number(item.num);
//         merge_ids.push(item.goods_sku_id);
//       }
//     });
//
//     // 如果选中的数据中没有该商品，则直接追加
//     state.selectGoodsList.map(goods => {
//       if (goods.is_taocan_goods == 1) {
//         if (merge_taocan_goods_ids.indexOf(goods.id) == -1) {
//           mergeGoodsList.push(goods);
//         }
//       } else {
//         if (merge_ids.indexOf(goods.id) == -1) {
//           mergeGoodsList.push(goods);
//         }
//       }
//     });
//
//     // 处理新加入的数据，更新已选中的最新数据
//     state.selectGoodsList = mergeGoodsList;
//
//     router.push({
//       path: '/purchase/stock/list',
//     });
//     dispatch('purchase/getCartList', { isOpenModal: true, isMerge: true });
//   },
//   SHOW_PURCHASE_MODAL: (state, showHelpWrapper) => {
//     console.log('-> showHelpWrapper', showHelpWrapper);
//     state.showPurchaseModal = showHelpWrapper;
//   }, // 显示选择规格选择的弹窗
//   SHOW_MUTIPLE_SPECS_MODAL: (state, payload) => {
//     console.log('SHOW_MUTIPLE_SPECS_MODAL', payload);
//     state.showMultipleSpecsModal = payload;
//   }, // 设置弹窗里的sku数据
//   SET_MODAL_SKULIST: (state, payload) => {
//     state.multipleSkuList = payload;
//   },
//   CHANGE_SELECT_GOODS(state, payload) {
//     console.log('-> payload', payload);
//     /**
//      * 原先的goods_id,用id进行替换
//      * generic_name由name进行替换
//      * 2023 0613 套餐的商品ID可能和sku的id重复，两者不是一个维度
//      * */
//
//     const existIndex = state.selectGoodsList.findIndex(item => {
//       if (payload.is_taocan_goods == 1) {
//         return item.id === payload.id && item.is_taocan_goods == payload.is_taocan_goods;
//       } else {
//         return item.id === payload.id;
//       }
//     });
//     if (existIndex > -1) {
//       state.selectGoodsList[existIndex].quantity =
//         Number(state.selectGoodsList[existIndex].quantity) + Number(payload.quantity);
//       Message.info(`商品${payload.name}数量加${payload.quantity}`);
//     } else {
//       state.selectGoodsList.push(payload);
//       Message.success(`商品${payload.name}加入购物车成功`);
//     }
//   },
//   CHANGE_SELECT_GOODS_UNIT(state, { index, quantity }) {
//     state.selectGoodsList[index].quantity = Number(quantity);
//   },
//   REMOVE_SELECT_GOODS(state, index) {
//     state.selectGoodsList.splice(index, 1);
//     state.selectGoodsList.forEach((item, index) => {
//       if (item.is_taocan_goods == 1) {
//         item.children.forEach(attrs_item => {
//           attrs_item.parentIndex = index;
//         });
//       }
//     });
//     console.log('state', state.selectGoodsList);
//   },
//   CHANGE_GOODS_ALL(state, payload) {
//     state.selectGoodsList = payload;
//   },
//   CLEAR_GOODS_LIST(state, isClose) {
//     console.log('-> %c isClose  === %o ', 'font-size: 15px', isClose);
//     state.selectGoodsList = [];
//     // isClose && (state.showPurchaseModal = false)
//   },
//   CHANGE_GOODS_UNIT(state, { index, quantity }) {
//     state.selectGoodsList[index].quantity = quantity;
//   },
//   CHANGE_SETTLE_VISIBLE(state, payload) {
//     state.settleVisible = payload;
//   },
//   SET_COVID_LISTOF(state, payload) {
//     state.covidListof = payload;
//   },
//   SET_YS_HOMOLOGY_LISTOF(state, payload) {
//     console.log('-> %c payload  ===    %o', 'font-size: 15px;color: #fa8c16 ;', payload);
//     state.ysHomologyListof = payload;
//   },
// };
//
// const actions = {
//   async getCartList({ state, commit }, { isOpenModal, isMerge }) {
//     console.log('-> state', state);
//     console.log('-> 0612state', state.selectGoodsList);
//     // sku id
//     let ids = [];
//     // 套餐商品id
//     let taocan_goods_ids = [];
//     let state_selectGoodsList_copy = cloneDeep(state.selectGoodsList);
//     console.log('-> state_selectGoodsList_copy', state_selectGoodsList_copy);
//     // return
//     if (state_selectGoodsList_copy.length > 0) {
//       state_selectGoodsList_copy.forEach(item => {
//         if (item.is_taocan_goods == 1) {
//           taocan_goods_ids.push(item.id);
//         } else {
//           ids.push(item.id);
//         }
//       });
//       return await request
//         .post('/clinic/pms.goodssku.cartgoods', { ids, taocan_goods_ids })
//         .then(({ goods_map, tc_goods_map }) => {
//           for (let key in tc_goods_map) {
//             tc_goods_map[key].children = tc_goods_map[key].skus;
//             tc_goods_map[key]._showChildren = false;
//             tc_goods_map[key].id = tc_goods_map[key].goods_id;
//             tc_goods_map[key].skus?.forEach(item => {
//               // 确保子集
//               item.isChild = true;
//             });
//           }
//           console.log('-> tc_goods_maptc_goods_maptc_goods_map', tc_goods_map);
//
//           let goods_map_collection = { ...goods_map, ...tc_goods_map };
//           console.log('goods_map_collection', goods_map_collection);
//
//           state_selectGoodsList_copy.map((item, index) => {
//             console.log(item);
//             // 如果sku的id与套餐商品id相同，两个is_taocan_goods也要相同，确保相同id的sku和套餐商品互相拿错
//             if (
//               goods_map_collection[item.id] &&
//               item.is_taocan_goods == goods_map_collection[item.id].is_taocan_goods
//             ) {
//               goods_map_collection[item.id].quantity = item.quantity;
//               state_selectGoodsList_copy[index] = goods_map_collection[item.id];
//
//               if (goods_map_collection[item.id].is_taocan_goods == 1) {
//                 state_selectGoodsList_copy[index]?.skus?.forEach(tc_item => {
//                   tc_item.parentIndex = index;
//                   tc_item.generic_name = tc_item.name;
//                 });
//               }
//             } else {
//               item.is_effective = '0';
//             }
//           });
//           console.log('debug', state_selectGoodsList_copy);
//           state_selectGoodsList_copy.map(item => {
//             console.log('-> %c item  === %o ', 'font-size: 15px', item);
//             item.isUpStock = false;
//             if (Number(item.stock_num) < Number(item.quantity) && item.stock_mod === '1') {
//               item.isUpStock = true;
//             }
//           });
//           console.log('state_selectGoodsList_copy', state_selectGoodsList_copy);
//           commit('CHANGE_GOODS_ALL', state_selectGoodsList_copy);
//           if (isOpenModal) {
//             setTimeout(
//               () => {
//                 commit('SHOW_PURCHASE_MODAL', true);
//               },
//               isMerge ? 600 : 100
//             );
//           }
//           return Promise.resolve(true);
//         });
//     }
//   },
// };
// const getters = {
//   total_num: state => {
//     let num = 0;
//     state.selectGoodsList.map(item => {
//       num += Number(item.quantity);
//     });
//     return num;
//   },
//   total_price: state => {
//     let sum = 0;
//     state.selectGoodsList.map(item => {
//       console.log('total_price', item);
//       sum += item.quantity * Number(item.clinic_price);
//     });
//     return sum.toFixed(2);
//   },
//   total_type: state => {
//     return state.selectGoodsList.length;
//   },
// };
// export default {
//   namespaced: true,
//   state,
//   mutations,
//   actions,
//   getters,
// };
