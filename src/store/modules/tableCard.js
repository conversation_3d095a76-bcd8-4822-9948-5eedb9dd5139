// 表格卡片数据模块
import axios from 'axios';

const state = {
  tableDataMap: {}, // 存储不同token对应的表格数据 {token: {headers, items, total}}
  pageInfoMap: {}, // 存储不同token对应的分页信息 {token: {currentPage, pageSize}}
  loadingMap: {}, // 存储不同token对应的加载状态 {token: boolean}
  errorMap: {}, // 存储不同token对应的错误信息 {token: string}
  searchResultsMap: {}, // 存储搜索结果数据，按消息ID存储 {messageId: [results]}
};

const mutations = {
  // 设置表格数据
  SET_TABLE_DATA(state, { token, data }) {
    state.tableDataMap = {
      ...state.tableDataMap,
      [token]: data,
    };
  },
  // 设置分页信息
  SET_PAGE_INFO(state, { token, pageInfo }) {
    state.pageInfoMap = {
      ...state.pageInfoMap,
      [token]: pageInfo,
    };
  },
  // 设置加载状态
  SET_LOADING(state, { token, loading }) {
    state.loadingMap = {
      ...state.loadingMap,
      [token]: loading,
    };
  },
  // 设置错误信息
  SET_ERROR(state, { token, error }) {
    state.errorMap = {
      ...state.errorMap,
      [token]: error,
    };
  },
  // 设置搜索结果
  SET_SEARCH_RESULTS(state, { messageId, results }) {
    state.searchResultsMap = {
      ...state.searchResultsMap,
      [messageId]: results,
    };
  },
};

const actions = {
  // 获取表格数据
  async fetchTableData({ commit, state }, { token, shouldScrollToBottom = true }) {
    if (!token) return;

    // 设置加载状态
    commit('SET_LOADING', { token, loading: true });
    commit('SET_ERROR', { token, error: null });

    try {
      const [ticketToken, propTotal] = token.split('&');

      // 获取或初始化分页信息
      const pageInfo = state.pageInfoMap[token] || { currentPage: 1, pageSize: 10 };

      if (shouldScrollToBottom && propTotal) {
        pageInfo.total = Number(propTotal || 0);
        commit('SET_PAGE_INFO', { token, pageInfo });
      }

      // 发起请求
      const res = await axios.get('https://clinic.rsjxx.com/api-server/service/llm_chat/basic.list', {
        params: {
          ticket: ticketToken,
          page: pageInfo.currentPage,
          size: pageInfo.pageSize,
        },
      });

      // 更新数据
      const tableData = {
        headers: res.data.data.header,
        items: res.data.data.items,
        total: res.data.data.total,
      };

      // 更新分页信息
      const newPageInfo = {
        ...pageInfo,
        total: res.data.data.total,
      };

      // 提交更新
      commit('SET_TABLE_DATA', { token, data: tableData });
      commit('SET_PAGE_INFO', { token, pageInfo: newPageInfo });

      if (token === 'invalid') {
        throw new Error('无效的数据标识');
      }
    } catch (err) {
      console.error('获取表格数据失败:', err);
      const errorMsg = err instanceof Error ? err.message : '未知错误';
      commit('SET_ERROR', { token, error: errorMsg });
    } finally {
      commit('SET_LOADING', { token, loading: false });
    }
  },

  // 更新分页信息并重新获取数据
  async changePage({ commit, dispatch, state }, { token, page }) {
    const pageInfo = state.pageInfoMap[token] || { currentPage: 1, pageSize: 10 };
    const newPageInfo = { ...pageInfo, currentPage: page };

    // 更新分页信息
    commit('SET_PAGE_INFO', { token, pageInfo: newPageInfo });

    // 重新获取数据
    dispatch('fetchTableData', { token, shouldScrollToBottom: false });
  },

  // 更新每页显示数量并重新获取数据
  async changePageSize({ commit, dispatch, state }, { token, size }) {
    const pageInfo = state.pageInfoMap[token] || { currentPage: 1, pageSize: 10 };
    const newPageInfo = { ...pageInfo, pageSize: size, currentPage: 1 };

    // 更新分页信息
    commit('SET_PAGE_INFO', { token, pageInfo: newPageInfo });

    // 重新获取数据
    dispatch('fetchTableData', { token, shouldScrollToBottom: false });
  },

  // 保存搜索结果
  setSearchResults({ commit }, { messageId, results }) {
    if (!messageId) {
      console.error('设置搜索结果时缺少messageId');
      return;
    }
    commit('SET_SEARCH_RESULTS', { messageId, results });
  },
};

const getters = {
  // 获取表格数据
  getTableData: state => token => {
    return state.tableDataMap[token] || { headers: [], items: [], total: 0 };
  },

  // 获取分页信息
  getPageInfo: state => token => {
    return state.pageInfoMap[token] || { currentPage: 1, pageSize: 10, total: 0 };
  },

  // 获取加载状态
  getLoading: state => token => {
    return state.loadingMap[token] === undefined ? true : state.loadingMap[token];
  },

  // 获取错误信息
  getError: state => token => {
    return state.errorMap[token] || null;
  },

  // 获取搜索结果
  getSearchResults: state => messageId => {
    return state.searchResultsMap[messageId] || [];
  },

  // 按索引获取特定搜索结果
  getSearchResultByIndex: state => (messageId, index) => {
    const results = state.searchResultsMap[messageId] || [];
    return results.find(item => item.index == index);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
