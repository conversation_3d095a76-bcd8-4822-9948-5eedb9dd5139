// Echarts卡片数据模块
import { assistantService } from '@/libs/SSE/SSEService';

const state = {
  chartDataMap: {}, // 存储不同token对应的图表数据 {token: {chartOptions, title}}
  loadingMap: {}, // 存储不同token对应的加载状态 {token: boolean}
  errorMap: {}, // 存储不同token对应的错误信息 {token: string}
  fetchingMap: {}, // 存储不同token对应的请求状态 {token: boolean}
};

const mutations = {
  // 设置图表数据
  SET_CHART_DATA(state, { token, data }) {
    state.chartDataMap = {
      ...state.chartDataMap,
      [token]: data,
    };
  },
  // 设置加载状态
  SET_LOADING(state, { token, loading }) {
    state.loadingMap = {
      ...state.loadingMap,
      [token]: loading,
    };
  },
  // 设置错误信息
  SET_ERROR(state, { token, error }) {
    state.errorMap = {
      ...state.errorMap,
      [token]: error,
    };
  },
  // 设置请求状态
  SET_FETCHING(state, { token, fetching }) {
    state.fetchingMap = {
      ...state.fetchingMap,
      [token]: fetching,
    };
  },
};

const actions = {
  // 获取图表数据
  async fetchChartData({ commit, state }, { content }) {
    if (!content) return;

    // 使用encodeURIComponent(content)作为唯一token
    const token = encodeURIComponent(content);

    // 检查是否正在请求中
    if (state.fetchingMap[token]) {
      // console.log('Already fetching chart data, skipping. Token:', token);
      return;
    }

    // 检查是否已有缓存数据
    if (state.chartDataMap[token]) {
      console.log('Found cached chart data, skipping request. Token:', token);
      return;
    }

    // 设置请求状态
    commit('SET_FETCHING', { token, fetching: true });
    commit('SET_LOADING', { token, loading: true });
    commit('SET_ERROR', { token, error: null });

    try {
      // console.log('Fetching chart data for token:', token);

      const params = {
        mermaid: token, // 已经是encodeURIComponent处理过的
      };

      const response = await assistantService.mermaidToEcharts(params);
      console.log('Chart data response:', response);

      if (!response || !response.echarts_option) {
        throw new Error('Invalid response data');
      }

      // 存储图表数据
      const chartData = {
        chartOptions: response.echarts_option,
        title: response.title || null,
        timestamp: Date.now(),
      };

      commit('SET_CHART_DATA', { token, data: chartData });
      // console.log('Cached chart data for token:', token);

    } catch (err) {
      console.error('获取图表数据失败:', err);
      const errorMsg = err instanceof Error ? err.message : '未知错误';
      commit('SET_ERROR', { token, error: errorMsg });
    } finally {
      commit('SET_LOADING', { token, loading: false });
      commit('SET_FETCHING', { token, fetching: false });
    }
  },

  // 清除特定图表数据
  clearChartData({ commit }, { content }) {
    if (!content) return;
    const token = encodeURIComponent(content);
    commit('SET_CHART_DATA', { token, data: null });
    commit('SET_LOADING', { token, loading: false });
    commit('SET_ERROR', { token, error: null });
    commit('SET_FETCHING', { token, fetching: false });
  },

  // 清除所有图表数据
  clearAllChartData({ commit, state }) {
    Object.keys(state.chartDataMap).forEach(token => {
      commit('SET_CHART_DATA', { token, data: null });
      commit('SET_LOADING', { token, loading: false });
      commit('SET_ERROR', { token, error: null });
      commit('SET_FETCHING', { token, fetching: false });
    });
  },
};

const getters = {
  // 获取图表数据
  getChartData: state => token => {
    return state.chartDataMap[token] || null;
  },

  // 获取加载状态
  getLoading: state => token => {
    return state.loadingMap[token] === undefined ? false : state.loadingMap[token];
  },

  // 获取错误信息
  getError: state => token => {
    return state.errorMap[token] || null;
  },

  // 获取请求状态
  getFetching: state => token => {
    return state.fetchingMap[token] === undefined ? false : state.fetchingMap[token];
  },

  // 检查是否有缓存数据
  hasChartData: state => token => {
    return !!state.chartDataMap[token];
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
