import Dexie from 'dexie';

let env = process.env.VUE_APP_NODE_ENV;
let db_name = '_rsj_clinic_db';
let db_VERSION = '002';
let LAST_DB_VERSION = '001';

// 创建广播频道
const broadcastChannel = new BroadcastChannel('database_operations');

// 创建诊所数据库
export const db = new Dexie(`${env}_${db_name}_${db_VERSION}`);

// 添加数据库重置状态标志
export let isResetting = false;

// 监听其他标签页的数据库操作
broadcastChannel.onmessage = async (event) => {
  if (event.data.type === 'DATABASE_RESET') {
    // 收到重置信号，关闭当前连接
    try {
      await db.close();
      console.log('Database connection closed due to reset signal');

      // 等待一段时间后尝试重新连接
      setTimeout(async () => {
        try {
          if (!db.isOpen()) {
            await db.open();
            console.log('Database reconnected after reset');
          }
        } catch (error) {
          console.error('Error reconnecting to database:', error);
        }
      }, 2000); // 给予足够的时间让重置操作完成

    } catch (error) {
      console.error('Error closing database:', error);
    }
  }
};

// 清理旧版本数据库
try {
  Dexie.delete(`${env}_${db_name}_${LAST_DB_VERSION}`);
} catch (e) {
  console.log(e);
}

// 修改重置数据库的方法
export const resetDatabase = async () => {
  const dbName = `${env}_${db_name}_${db_VERSION}`;
  try {
    isResetting = true;

    // 1. 获取所有现有数据
    const existingData = await db.rrweb_events.toArray();

    // 2. 通知其他标签页
    broadcastChannel.postMessage({ type: 'DATABASE_RESET' });

    // 3. 等待其他标签页响应
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 4. 关闭当前连接
    await db.close();

    // 5. 删除数据库
    await Dexie.delete(dbName);
    console.log('删除数据库', dbName)
    // 6. 重新初始化数据库
    db.version(1).stores({
      rrweb_events: '++id,session_id,data,created_at',
    });

    // 7. 重新打开数据库
    await db.open();

    return {
      success: true,
      previousData: existingData
    };
  } catch (error) {
    console.error('Failed to reset database:', error);
    return {
      success: false,
      previousData: []
    };
  } finally {
    isResetting = false;
  }
};

// 数据库结构定义
db.version(1).stores({
  rrweb_events: '++id,session_id,data,created_at',
});

// 添加数据库关闭时的清理
window.addEventListener('unload', () => {
  broadcastChannel.close();
});
