import { Base64 } from 'js-base64';
import pako from 'pako';
import S from '../util';

export const zip = function (data) {
  if (!data) return data;
  // 判断数据是否需要转为JSON
  const dataJson = typeof data !== 'string' && typeof data !== 'number' ? JSON.stringify(data) : data;
  // 使用Base64.encode处理字符编码，兼容中文
  const str = Base64.encode(dataJson);
  let binaryString = pako.gzip(str);
  let arr = Array.from(binaryString);
  let s = '';
  arr.forEach(item => {
    s += String.fromCharCode(item);
  });
  return Base64.btoa(s);
};
export const unzip = function (b64Data) {
  let strData = Base64.atob(b64Data);
  let charData = strData.split('').map(function (x) {
    return x.charCodeAt(0);
  });
  let binData = new Uint8Array(charData);
  let data = pako.ungzip(binData);
  // ↓切片处理数据，防止内存溢出报错↓
  let str = '';
  const chunk = 8 * 1024;
  let i;
  for (i = 0; i < data.length / chunk; i++) {
    str += String.fromCharCode.apply(null, data.slice(i * chunk, (i + 1) * chunk));
  }
  str += String.fromCharCode.apply(null, data.slice(i * chunk));
  // ↑切片处理数据，防止内存溢出报错↑
  const unzipStr = Base64.decode(str);
  let result = '';
  // 对象或数组进行JSON转换
  try {
    result = JSON.parse(unzipStr);
  } catch (error) {
    if (/Unexpected token o in JSON at position 0/.test(error)) {
      // 如果没有转换成功，代表值为基本数据，直接赋值
      result = unzipStr;
    }
  }
  return result;
};

export const sizeof = function (str, charset) {
  var total = 0;
  var charCode, i, len;

  charset = charset ? charset.toLowerCase() : '';

  if (charset === 'utf-16' || charset === 'utf16') {
    for (i = 0, len = str.length; i < len; i++) {
      charCode = str.charCodeAt(i);

      if (charCode <= 0xffff) {
        total += 2;
      } else {
        total += 4;
      }
    }
  } else {
    for (i = 0, len = str.length; i < len; i++) {
      charCode = str.charCodeAt(i);

      if (charCode <= 0x007f) {
        total += 1;
      } else if (charCode <= 0x07ff) {
        total += 2;
      } else if (charCode <= 0xffff) {
        total += 3;
      } else {
        total += 4;
      }
    }
  }

  return total;
};


/**
 * @description localStorage 储存
 * */
export const setLocalStorage = function (key, value) {
  localStorage.setItem(key, JSON.stringify(value));
};

/**
 * @description localStorage 获取
 * */
export const getLocalStorage = function (key) {
  return JSON.parse(localStorage.getItem(key));
};

// 生成 sid
export const createSessionId = function () {
  let sid = S.Cookies.get('_sj_ulid') + S.random(5) + parseInt(new Date().getTime() / 1000);
  return sid.toLowerCase()
}

// 生成 rid
export const createReplayId = function () {
  return 'rid-' + parseInt(new Date().getTime() / 1000);
}

export const chunkArr = function (arr, size) {
  //判断如果不是数组(就没有length)，或者size没有传值，size小于1，就返回空数组
  if (!arr?.length || !size || size < 1) return []
  let [start, end, result] = [null, null, []]
  for (let i = 0; i < Math.ceil(arr.length / size); i++) {
    start = i * size
    end = start + size
    result.push(arr.slice(start, end))
  }
  return result
}

/**
 * @description 将字符串切割成数组
 * { str } 源字符串
 * { maxBytes } 以多少字节切割 默认 4M
 * */
export const splitStringBySize = function (str, maxBytes = 1024 * 1024 * 4) {
  const encoder = new TextEncoder(); // 用于计算字节大小
  const data = encoder.encode(str); // 将字符串转为 Uint8Array
  const result = [];

  let start = 0;
  while (start < data.length) {
    const end = Math.min(start + maxBytes, data.length); // 确保不超过最大字节数
    const slice = data.slice(start, end);
    result.push(new TextDecoder().decode(slice)); // 将字节切片转换回字符串
    start = end;
  }

  return result;
}

/**
 * @description 提供快捷复制
 * */
export const copyText = function (val) {
  const textareaC = document.createElement('textarea');
  textareaC.setAttribute('readonly', 'readonly'); // 设置只读属性防止手机上弹出软键盘
  textareaC.value = val;
  document.body.appendChild(textareaC);  // 将textarea添加为body子元素
  textareaC.select();
  const result = document.execCommand('copy');
  document.body.removeChild(textareaC); // 移除DOM元素
  return result;
}
