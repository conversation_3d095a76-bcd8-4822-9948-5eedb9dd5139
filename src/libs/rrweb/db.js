import { db, resetDatabase, isResetting } from './dexie';
import RRwebEvents from './data.json'
// 添加重连状态追踪
let isReconnecting = false;
let reconnectPromise = null;

// 添加写入队列来保证顺序
const writeQueue = {
  queue: [],
  isProcessing: false,
};

// 改进重连机制
const reconnectDatabase = async (retries = 3, delay = 1000) => {
  // 如果已经在重连中，返回当前的重连 Promise
  if (isReconnecting) {
    return reconnectPromise;
  }

  isReconnecting = true;
  reconnectPromise = (async () => {
    for (let i = 0; i < retries; i++) {
      try {
        if (!db.isOpen()) {
          await new Promise(resolve => setTimeout(resolve, delay));
          await db.open();
          console.log('Database reconnected successfully');
        }
        return true;
      } catch (error) {
        console.log(`Reconnection attempt ${i + 1} failed:`, error);
        if (i === retries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
    return false;
  })();

  try {
    await reconnectPromise;
  } finally {
    isReconnecting = false;
    reconnectPromise = null;
  }

  return true;
};

/**
 * @description 处理写入失败的重试逻辑
 */
export const handleDataWrite = async (session_id, data, retries = 3) => {
  // 将数据加入队列
  writeQueue.queue.push({ session_id, data });

  // 如果已经在处理队列，直接返回
  if (writeQueue.isProcessing) {
    return;
  }

  // 开始处理队列
  writeQueue.isProcessing = true;
  try {
    // 按顺序处理队列中的所有写入
    while (writeQueue.queue.length > 0) {
      const item = writeQueue.queue[0]; // 只查看队列头部，不移除
      try {
        await processWrite(item.session_id, item.data, retries);
        writeQueue.queue.shift(); // 写入成功后才移除
      } catch (error) {
        console.error(`Failed to write ${item.session_id}:`, error);
        // 如果是数据库关闭错误，等待后重试
        if (error.name === 'DatabaseClosedError') {
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }
        // 其他错误则移除该项并继续处理队列
        writeQueue.queue.shift();
      }
    }
  } finally {
    writeQueue.isProcessing = false;
  }
};

// 单个写入处理函数
const processWrite = async (session_id, data, retries) => {
  while (isResetting) {
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  let lastError = null;
  for (let i = 0; i < retries; i++) {
    try {
      if (!db.isOpen()) {
        await reconnectDatabase();
      }
      await addSessionData(session_id, data);
      return true;
    } catch (error) {
      lastError = error;
      if (error.name === 'DatabaseClosedError') {
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        try {
          await reconnectDatabase();
          continue;
        } catch (reconnectError) {
          console.error('Reconnection failed:', reconnectError);
        }
      }
      if (i === retries - 1) {
        throw lastError;
      }
    }
  }
  return false;
};

// 改进数据库操作包装器
const wrapDbOperation = async (operation, retries = 3) => {
  let lastError = null;

  for (let i = 0; i < retries; i++) {
    try {
      if (!db.isOpen()) {
        await reconnectDatabase();
      }
      return await operation();
    } catch (error) {
      lastError = error;
      console.log(`Operation attempt ${i + 1} failed:`, error);

      if (error.name === 'DatabaseClosedError') {
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        try {
          await reconnectDatabase();
          continue;
        } catch (reconnectError) {
          console.error('Reconnection failed:', reconnectError);
        }
      }

      if (i === retries - 1) {
        throw lastError;
      }
    }
  }
};

/**
 * @description 添加事件数据
 */
export const addSessionData = async function (session_id, data) {
  return await wrapDbOperation(async () => {
    return await db.rrweb_events.add({
      session_id: session_id,
      data: JSON.stringify(data || []),
      created_at: new Date().getTime(),
    });
  });
};

/**
 * @description 获取指定 session_id 的所有数据
 */
export const getSessionData = async function (session_id) {
  return await wrapDbOperation(async () => {
    return await db.rrweb_events.where('session_id').equals(session_id).toArray();
  });
};

/**
 * @description 获取最新的记录
 */
export const getLatestRecord = async function (session_id) {
  return await db.rrweb_events.where('session_id').equals(session_id).last();
};

/**
 * @description 更新指定记录
 */
export const updateSessionData = async function (id, session_id, data) {
  await db.rrweb_events.put({
    id,
    session_id,
    data: JSON.stringify(data),
    created_at: new Date().getTime(),
  });
};

/**
 * @description 获取所有数据
 */
export const getAllData = async function () {
  return await wrapDbOperation(async () => {
    return await db.rrweb_events.toArray();
  });
};

/**
 * @description 批量删除事件
 */
export const deleteEvents = async function (ids) {
  return await wrapDbOperation(async () => {
    return await db.rrweb_events.bulkDelete(ids);
  });
};

/**
 * @description 测试用：持续写入数据
 * @returns {Function} 停止写入的函数
 */
export const testContinuousWrite = () => {
  let counter = 0;
  let isRunning = true;
  const stats = {
    totalAttempts: 0,
    successfulWrites: 0,
    failedWrites: 0,
    waitingForReset: 0,
    startTime: Date.now(),
  };

  const writeInterval = setInterval(async () => {
    if (!isRunning) return;

    try {
      stats.totalAttempts++;
      counter++;

      // 如果数据库正在重置，记录等待次数
      if (isResetting) {
        stats.waitingForReset++;
      }

      const testData = RRwebEvents;

      await handleDataWrite(`test-${counter.toString().padStart(6, '0')}`, testData);
      stats.successfulWrites++;

      // 打印状态，包含等待信息
      if (counter % 10 === 0) {
        const runningTime = (Date.now() - stats.startTime) / 1000;
        const successRate = ((stats.successfulWrites / stats.totalAttempts) * 100).toFixed(2);
        const writeSpeed = (stats.successfulWrites / runningTime).toFixed(2);

        console.log(`
=== Write Test Status (Current Sequence: ${counter}) ===
Total Attempts: ${stats.totalAttempts}
Successful Writes: ${stats.successfulWrites}
Failed Writes: ${stats.failedWrites}
Times Waited for Reset: ${stats.waitingForReset}
Success Rate: ${successRate}%
Write Speed: ${writeSpeed} records/s
Current Count: ${await db.rrweb_events.count()} records
Last Written ID: test-${counter.toString().padStart(6, '0')}
Database Reset Status: ${isResetting ? 'Resetting' : 'Normal'}
=====================
        `);
      } else {
        // 每次写入的简短状态
        const status = isResetting ? '⌛ Waiting for reset' : '✓ Success';
        console.log(`${status} #${counter.toString().padStart(6, '0')}`);
      }
    } catch (error) {
      stats.failedWrites++;
      console.error(`✗ Write #${counter.toString().padStart(6, '0')} failed:`, error.message);
    }
  }, 100);

  return () => {
    isRunning = false;
    clearInterval(writeInterval);
    console.log(`
=== Final Write Test Status ===
Total Sequence Numbers: 1 to ${counter}
Total Attempts: ${stats.totalAttempts}
Successful Writes: ${stats.successfulWrites}
Failed Writes: ${stats.failedWrites}
Times Waited for Reset: ${stats.waitingForReset}
Success Rate: ${((stats.successfulWrites / stats.totalAttempts) * 100).toFixed(2)}%
=====================
    `);
  };
};

// 添加一个全局的测试控制变量
let stopTest = null;

/**
 * @description 测试用：周期性重置数据库
 * @returns {Function} 停止重置的函数
 */
export const testPeriodicReset = () => {
  let isRunning = true;
  const stats = {
    totalResets: 0,
    successfulResets: 0,
    failedResets: 0,
    startTime: Date.now(),
  };

  const resetLoop = async () => {
    while (isRunning) {
      try {
        stats.totalResets++;
        console.log('\nStarting database reset...');

        // 如果即将进行第二次重置，先停止所有测试
        if (stats.successfulResets === 1) {
          console.log('\n=== Preparing for second reset, stopping all tests... ===\n');
          if (stopTest) {
            stopTest();
          }
          return;
        }

        // 获取重置前的记录数
        const beforeCount = await db.rrweb_events.count();
        console.log(`Records before reset: ${beforeCount}`);

        // 执行重置
        const { success, previousData } = await resetDatabase();

        if (success) {
          stats.successfulResets++;
          console.log(`
=== Reset Test Status ===
Total Resets: ${stats.totalResets}
Successful Resets: ${stats.successfulResets}
Failed Resets: ${stats.failedResets}
Backed up records: ${previousData.length}
Records after reset: ${await db.rrweb_events.count()}
=====================
          `);
        } else {
          stats.failedResets++;
          console.error('Reset failed');
        }

        // 随机等待5-15秒
        const delay = Math.floor(Math.random() * (15000 - 5000 + 1) + 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
      } catch (error) {
        stats.failedResets++;
        console.error('Reset error:', error);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  };

  // 启动重置循环
  resetLoop();

  return () => {
    isRunning = false;
    console.log('Reset test stopped');
  };
};

// 修改测试启动方式
const startTests = () => {
  const stopReset = testPeriodicReset();
  const stopWrite = testContinuousWrite();

  // 保存停止函数以便在需要时调用
  stopTest = () => {
    stopReset();
    stopWrite();
    console.log('All tests stopped');
  };
};

// 启动测试
// startTests();
