/**
 * rrweb相关的基础功能函数库
 * */

import io from '../io';
import S from '../util';
import * as rrweb from 'rrweb';
import { getUser } from '../runtime';
import { sizeof, createSessionId, createReplayId, zip, splitStringBySize } from './tools';
import cloneDeep from 'lodash.clonedeep';

export const record = rrweb.record;
export let isRecording = false;
let trace_id = S.Cookies.get('_sj_ulid') || '';
export let closeRecord = null;
let SOURCE_NAME = 'CLINIC';
let session_id = '';
let replay_id = '';
let events_num = 200; // 修改为200条
let split_symbol = 'EOF;';

// 内存存储
const memoryStore = {
  events: [],
  lastFullSnapshotIndex: -1,
};

// 初始化数据结构
let _init_data = {
  events: [],
  trace_id: getUser().uid,
  session_id: '',
  replay_id: '',
  start_time: '',
  end_time: '',
  uid: getUser().uid,
  operator: getUser().name,
  mobile: getUser().mobile,
  source: SOURCE_NAME,
  source_name: getUser().clinic_name,
};

let init_params_data = cloneDeep(_init_data);

// 上报数据
const replaySave = async function (info) {
  console.log('[RRWeb] Start uploading data:', {
    replayId: info.replay_id,
    eventsCount: info.events.length,
    startTime: new Date(info.start_time * 1000).toLocaleString(),
    endTime: new Date(info.end_time * 1000).toLocaleString(),
  });

  let event_zip = zip(info.events);
  console.log('[RRWeb] Data size before compression:', sizeof(JSON.stringify(info.events)) / 1024 / 1024, 'MB');
  console.log('[RRWeb] Data size after compression:', sizeof(event_zip) / 1024 / 1024, 'MB');

  let list = splitStringBySize(event_zip);
  console.log('[RRWeb] Split into chunks:', list.length);

  for (let index in list) {
    let params = {
      ...info,
      events: index == list.length - 1 ? `${list[index]}${split_symbol}` : list[index],
    };
    try {
      console.log(`[RRWeb] Uploading chunk ${parseInt(index) + 1}/${list.length}`);
      const res = await io.post('/services/behavior.replay.save', params);
      if (res.switch_status !== 'ON') {
        console.log('[RRWeb] Recording switched OFF by server');
        closeRecord && closeRecord();
      }
    } catch (error) {
      console.error('[RRWeb] Error uploading chunk:', error);
    }
  }
  console.log('[RRWeb] Upload completed');
};

// 初始化数据
const initData = function () {
  session_id = createSessionId();
};

// 获取初始化状态
const initStatus = function () {
  return getInitSwitchstatus();
};

const getInitSwitchstatus = function () {
  let params = { source: SOURCE_NAME };
  return io.get('/services/behavior.replay.switchstatus', { params });
};

// 处理数据上报
const handleDataUpload = async data => {
  try {
    await replaySave(data);
    return true;
  } catch (error) {
    console.error('Failed to upload data:', error);
    return false;
  }
};

// 更新时间戳
const handleParamsData = function () {
  let events = init_params_data.events;
  if (events.length == 0) return;
  if (!init_params_data.start_time) {
    init_params_data.start_time = parseInt(events[0].timestamp / 1000);
  }
  init_params_data.end_time = parseInt(events[events.length - 1].timestamp / 1000);
};

export const initRrweb = () => {
  initStatus().then(res => {
    if (res.status === 'ON') {
      initData();
      startRecord();
    } else {
      closeRecord && closeRecord();
      isRecording = false;
    }
  });
};

// 开启录制
export const startRecord = async function () {
  closeRecord = record({
    emit: async event => {
      // 全量快照处理
      if (event.type === 4) {
        console.log('%c=>(index.js:182) 生成全量啦', 'color: #ECA233;font-size: 36px;', '生成全量啦');

        console.log('\n[RRWeb] Full snapshot generated');

        // 如果有未上报的增量数据，先上报
        if (init_params_data.events.length) {
          console.log('[RRWeb] Uploading pending incremental data:', init_params_data.events.length, 'events');
          const cloneData = cloneDeep(init_params_data);
          // 去除紧跟全量生成的增量，解决生成的视频时间计算问题
          let last_length = cloneData.events.length - 1;
          let last_event = cloneData.events.length && cloneData.events[last_length];
          if (last_event.type === 3) {
            cloneData.events = cloneData.events.slice(0, last_length);
          }
          handleDataUpload(cloneData);
          // 立即清除数据，避免重复上报
        }
        init_params_data = cloneDeep(_init_data);
        // 重置数据并准备新的全量快照
        replay_id = createReplayId();

        // 确保 init_params_data 是全新的
        init_params_data.events.push(event);
        init_params_data.trace_id = trace_id;
        init_params_data.session_id = session_id;
        init_params_data.replay_id = replay_id;
      } else {
        // 增量数据处理
        init_params_data.events.push(event);
        init_params_data.trace_id = trace_id;
        init_params_data.session_id = session_id;
        init_params_data.replay_id = replay_id;
      }
      handleParamsData();

      // 达到阈值时上报，并立即清除数据
      if (init_params_data.events.length >= events_num) {
        console.log(`[RRWeb] Events threshold reached (${events_num}), uploading data`);
        const dataToUpload = cloneDeep(init_params_data);
        // 立即清除数据，避免重复上报
        init_params_data = cloneDeep(_init_data);
        await handleDataUpload(dataToUpload);
      }
      isRecording = true;
    },
    checkoutEveryNms: 10 * 60 * 1000,
    recordCanvas: false,
    inlineStylesheet: true,
    sampling: {
      mousemove: 80,
      scroll: 80,
      input: 'last',
    },
    blockClass: 'rr-block',
    hooks: {
      mouseInteraction: data => {
        return data.type === 2;
      },
    },
  });
  console.log('[RRWeb] Recording started');
};

// 定时上报
let timer = setInterval(async () => {
  if (!isRecording || closeRecord === null) {
    console.log('[RRWeb] Timer stopped due to recording status');
    clearInterval(timer);
    timer = null;
    return;
  }

  if (init_params_data.events.length > 0) {
    console.log('[RRWeb] Timer triggered upload:', init_params_data.events.length, 'events');
    await handleDataUpload(cloneDeep(init_params_data));
    init_params_data = cloneDeep(_init_data);
  }
}, 1000 * 30);

export const stopRecord = function () {
  isRecording = false;
  closeRecord && closeRecord();
};

// 请求或响应太大时上报请求数据
const uploadRequestData = params => {
  io.post('/services/behavior.replay.saverequest', params).then(() => {});
};

const getRequestKbSizeOverload = (config, response) => {
  const requestData = config.data || config.params;
  const requestSize = encodeURIComponent(JSON.stringify(requestData)).length / 1024;
  const responseSize = encodeURIComponent(JSON.stringify(response.data)).length / 1024;
  return Math.max(requestSize, responseSize) > 500;
};

export const recordRequestEvent = response => {
  const blockUrl = [
    '/services/behavior.trace.create',
    '/services/behavior.replay.save',
    '/services/behavior.replay.details',
  ];

  const config = response.config;
  if (isRecording && !blockUrl.includes(config.url) && !config.url.startsWith('/services/behavior')) {
    const payload = {
      data: {
        methods: config.method,
        request: {
          headers: config.headers,
          data: config.data,
          params: config.params,
          url: config.url,
        },
        response: response.data,
        status_code: response.status,
      },
      endTimestamp: Date.now(),
      startTimestamp: config.startTimestamp,
      op: 'resources.xhr',
      description: config.baseURL + config.url,
    };
    if (getRequestKbSizeOverload(response.config, response)) {
      const randomStr = S.random(16);
      uploadRequestData({
        ...payload.data,
        key: randomStr,
      });
      record.addCustomEvent('xhr', {
        key: randomStr,
      });
    } else {
      record.addCustomEvent('xhr', payload);
    }
  }
};

export const recordNavigationEvent = (to, from) => {
  if (isRecording) {
    record.addCustomEvent('navigation', {
      category: 'navigation',
      data: {
        from: from.fullPath,
        to: to.fullPath,
      },
      timestamp: Date.now(),
    });
  }
};
