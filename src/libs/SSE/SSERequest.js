import { fetchEventSource } from '@microsoft/fetch-event-source';
import config from '@/config';
import { throttle  } from 'lodash-es';
const MAX_TIMEOUT = 120000; // 2分钟
class RetriableError extends Error {}
import { Message } from 'view-design';
class FatalError extends Error {}

class SSEClient {
  constructor() {
    this.controller = null;
    this.timeoutId = null;
    this.retryCount = 0;
    this.isConnecting = false;
    this.connectionStatus = 'idle';
    this.baseUrl = config.AssistantApiDomain;
    this.defaultOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'text/event-stream',
        'Cache-Control': 'no-cache',
      },
    };

    // 网络状态监听相关
    this.isOnline = navigator.onLine;
    this.networkListeners = [];
    this.currentOptions = null; // 保存当前连接的选项，用于断网重连

    // 初始化网络状态监听
    this.initNetworkListeners();
  }

  // 初始化网络状态监听
  initNetworkListeners() {
    const handleOnline = () => {
      console.log('网络已连接');
      this.isOnline = true;
      // 网络恢复时，如果有保存的连接选项，可以考虑自动重连
      // 这里不自动重连，让用户手动重试，体验更可控
    };

    const handleOffline = () => {
      console.log('网络已断开');
      this.isOnline = false;
      // 断网时，如果有活跃连接，触发错误处理
      if (this.controller && !this.controller.signal.aborted && this.currentOptions) {
        console.warn('检测到网络断开，终止SSE连接');
        this.currentOptions.onError?.(new Error('网络连接已断开'));
        this.disconnect();
      }
    };

    // 添加事件监听
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 保存监听器引用，用于清理
    this.networkListeners = [
      { event: 'online', handler: handleOnline },
      { event: 'offline', handler: handleOffline },
    ];
  }

  // 清理网络状态监听
  cleanupNetworkListeners() {
    this.networkListeners.forEach(({ event, handler }) => {
      window.removeEventListener(event, handler);
    });
    this.networkListeners = [];
  }

  async connect(options) {
    if (this.isConnecting) {
      console.warn('Connection already in progress');
      return;
    }

    if (this.controller && !this.controller.signal.aborted) {
      console.warn('Active connection exists');
      return;
    }

    // 移除网络检查，统一在错误回调中处理

    const mergedOptions = { ...this.defaultOptions, ...options };
    this.currentOptions = mergedOptions; // 保存当前连接选项
    this.isConnecting = true;
    this.connectionStatus = 'connecting';

    try {
      await this.establishConnection(mergedOptions);
    } catch (error) {
      Message.error({
        content: error,
        duration: 30,
      });
      await this.handleError(error, mergedOptions);
    } finally {
      this.isConnecting = false;
    }
  }

  async establishConnection(options) {
    // console.log('%c [ options ]-50-「SSERequest.js」', 'font-size:13px; background:#4c80c6; color:#90c4ff;', options);
    this.controller = new AbortController();

    const resetTimeout = () => {
      // console.log('this.timeoutId: ', this.timeoutId);
      if (this.timeoutId) {
        // console.log('清除之前的定时器:', this.timeoutId);
        clearTimeout(this.timeoutId);
      }
      this.timeoutId = setTimeout(() => {
        console.warn('SSE 120秒无响应，主动断开连接');
        console.log('🚀 ~ SSEClient ~ this.timeoutId=setTimeout ~ this.timeoutId:', this.timeoutId);
        this.disconnect();
        options.onError?.(new Error('SSE 120秒无响应，已断开连接'));
      }, MAX_TIMEOUT);
      // console.log('创建新的定时器:', this.timeoutId);
    };

    // 使用 lodash throttle 创建节流版本的 resetTimeout
    // 1秒内最多执行一次，leading: true 表示第一次调用立即执行
    const throttledResetTimeout = throttle(resetTimeout, 1000, { leading: true, trailing: false });

    // 初始化第一个定时器
    resetTimeout();

    try {
      await fetchEventSource(`${this.baseUrl}${options.url}`, {
        method: options.method,
        headers: options.headers || {},
        body: options.data ? JSON.stringify(options.data) : null,
        signal: this.controller.signal,
        openWhenHidden: true,
        async onopen(response) {
          // console.log('%c [ response ]-90', 'font-size:13px; background:pink; color:#bf2c9f;', response);
          if (response.ok && response.status === 200) {
            return; // everything's good
          } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            // client-side errors are usually non-retriable:
            throw new FatalError();
          } else {
            throw new RetriableError();
          }
        },
        onmessage: event => {
          throttledResetTimeout();
          if (event.data === 'FatalError') {
            throw new FatalError(event.data);
          }
          if (!event.data) return;
          try {
            const data = JSON.parse(event.data);
            options.onMessage?.(data);
            if (data?.choices?.[0]?.delta?.type === 'content_end') {
              this.handleComplete(options);
            }
          } catch (error) {
            console.error('Error parsing SSE message:', error);
          }
        },
        onclose: () => {
          console.log('on SSE Close');
          if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
          }
          console.warn('SSE连接意外关闭');
          // 连接意外关闭时触发错误回调
          // options.onError?.(new Error('SSE连接意外断开'));
          options.onComplete?.();
          throw new RetriableError();
        },
        onerror: error => {
          console.warn('SSE connection error:', error);
          if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
          }
          // 触发错误回调
          options.onError?.(error);
          throw error;
        },
      });
    } catch (error) {
      this.handleError(error, options);
    }
  }

  async handleError(error, options) {
    this.connectionStatus = 'error';

    options.onError?.(error);
  }

  handleComplete(options) {
    options.onComplete?.();
  }

  disconnect() {
    this.isConnecting = false;
    this.connectionStatus = 'idle';
    console.log('this.timeoutId: ', this.timeoutId);
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    if (this.controller) {
      this.controller.abort();
      this.controller = null;
    }
    this.currentOptions = null; // 清理当前连接选项
  }

  // 析构函数，清理资源
  destroy() {
    this.disconnect();
    this.cleanupNetworkListeners();
  }

  getConnectionStatus() {
    return this.connectionStatus;
  }
}

export class SSEManager {
  clients = new Map();

  constructor() {
    this.clients = new Map();
  }

  async connect(key, options) {
    this.disconnect(key);

    const client = new SSEClient();
    this.clients.set(key, client);

    try {
      await client.connect(options);
    } catch (error) {
      this.clients.delete(key);
      throw error;
    }
  }

  disconnect(key) {
    const client = this.clients.get(key);
    if (client) {
      client.disconnect();
      this.clients.delete(key);
    }
  }

  disconnectAll() {
    this.clients.forEach(client => {
      client.destroy(); // 使用destroy方法清理所有资源
    });
    this.clients.clear();
  }

  getclientstatus(key) {
    return this.clients.get(key)?.getConnectionStatus() || 'idle';
  }
}

export class MessageService {
  /**
   * 构造函数
   * @param {string} sessionId
   * @param {SSEManager} manager
   */
  constructor(sessionId, manager) {
    this.sessionId = sessionId;
    this.clientKey = `chat_${sessionId}`;
    this.manager = manager;
    this.messageCallback = undefined;
    this.errorCallback = undefined;
    this.completeCallback = undefined;
  }

  /**
   * 发送 SSE 消息请求
   * @param {object} data
   * @param {string} url
   * @returns {Promise<void>}
   */
  async sendMessage(data, url) {
    // 触发 streaming start 回调
    this.messageCallback?.(null, true);

    try {
      await this.manager.connect(this.clientKey, {
        url,
        method: 'POST',
        data,
        onMessage: data => {
          // if (!data.choices && data.trace_id) {
          //   this.messageCallback?.(data, false);
          //   manager.disconnect(this.clientKey);
          // } else {
          this.messageCallback?.(data, true);
          // }
        },
        onComplete: () => {
          this.completeCallback?.();
          manager.disconnect(this.clientKey);
        },
        onError: err => {
          console.error('SSE连接错误:', err);
          // 不显示确认对话框，直接触发错误回调
          this.errorCallback?.(err);
          manager.disconnect(this.clientKey);
        },
      });
    } catch (err) {
      Message.error('SSE Error:', err);
      this.errorCallback?.(err);
      throw err;
    }
  }

  /**
   * 注册消息回调函数
   * @param {(data: object|null, streaming: boolean) => void} cb
   */
  onMessage(cb) {
    this.messageCallback = cb;
  }

  onError(cb) {
    this.errorCallback = cb;
  }

  onComplete(cb) {
    this.completeCallback = cb;
  }

  /**
   * 清除消息回调
   */
  clearMessageCallback() {
    this.messageCallback = undefined;
    this.errorCallback = undefined;
    this.completeCallback = undefined;
  }

  /**
   * 主动停止消息流
   */
  stop() {
    // Message.info('SSE Error:', '主动断开');
    this.manager.disconnect(this.clientKey);
    this.messageCallback?.(null, false);
  }

  /**
   * 获取当前连接状态
   */
  getStatus() {
    return this.manager.getStatus(this.clientKey);
  }
}

const manager = new SSEManager();
export function createMessageService(sessionId) {
  return new MessageService(sessionId, manager);
}
// 导出消息服务实例
