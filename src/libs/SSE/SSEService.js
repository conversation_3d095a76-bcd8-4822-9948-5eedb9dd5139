import { assistantInstance } from '../io';
// 使用闭包来实现私有变量
// const createMessageService = (sessionId) => {
//   // 私有变量
//   let messageCallback;
//   const key = `chat_${sessionId}`;
//   return {
//     /**
//      * 发送消息并获取流式响应
//      * @param data 会话参数
//      * @param url 接口地址
//      */
//     async sendMessage(data, url) {
//       return new Promise((resolve, reject) => {
//         const sseKey = `chat_${data.session_id}`;

//         // 开始响应时通知
//         messageCallback?.(null, true);

//         manager.connect(sseKey, {
//           url: url,
//           method: 'POST',
//           data: data,
//           onMessage: data => {
//             // console.log('%c [ data ]-48', 'font-size:13px; background:#8e4f24; color:#d29368;', data)
//             // 调用回调函数

//             // 检查是否是最后一条消息
//             if (data.choices && data.choices?.[0]?.delta?.type === 'content_end') {
//               messageCallback?.(data, false);
//               manager.disconnect(sseKey);
//               resolve();
//             } else {
//               messageCallback?.(data, true);
//             }
//           },
//           onError: error => {
//             messageCallback?.(null, false);
//             manager.disconnect(sseKey);
//             reject(error);
//           },
//           onComplete: () => {
//             messageCallback?.(null, false);
//             manager.disconnect(sseKey);
//             resolve();
//           },
//         });
//       });
//     },

//     /**
//      * 设置消息回调
//      * @param callback 回调函数
//      */
//     onMessage(callback) {
//       messageCallback = callback;
//     },

//     /**
//      * 清除消息回调
//      */
//     clearMessageCallback() {
//       messageCallback = undefined;
//     },

//     /**
//      * 停止消息生成
//      */
//     stopGeneration(sessionId) {
//       const key = `chat_${sessionId}`;
//       manager.disconnect(key);
//       messageCallback?.(null, false);
//     },
//   };
// };
// export function createMessageService(sessionId) {
//   return new MessageService(sessionId, manager);
// }
// 导出消息服务实例

export const assistantService = {
  // 对话列表

  /**
   *
   * @param clinic_id String
   * @param user_id String
   * @returns
   */
  getChatList(params) {
    return assistantInstance.get('/clinic_chat/history/get', { params });
  },
  // 添加新对话
  /**
   *
   * @param {user_id String} params
   * @param {clinic_id String} params
   * @param {clinic_name String} params
   * @param {chat_name rs_jia | rs_jia } params
   * @returns
   */
  addNewChat(params) {
    return assistantInstance.post('/clinic_chat/history/add', params);
  },
  // 设置对话
  /**
   * @param {session_id String} params
   * @param {title String} params
   * @param {clinic_id String} params
   * @param {clinic_name String} params
   * @param {chat_name rs_jia | rs_jia } params
   * @returns
   */
  setChat(params) {
    return assistantInstance.post('/clinic_chat/history/edit', params);
  },
  // 生成对话标题
  /**
   * @param {session_id String} params
   * @returns {title String}
   */
  generateChatTitle(params) {
    return assistantInstance.get('/clinic_chat/title/set', { params });
  },
  // 删除对话
  /**
   * @param {session_id String} params
   * @returns
   */
  deleteChat(params) {
    return assistantInstance.get('/clinic_chat/chat/delete', { params });
  },
  // 获取session_id
  getSessionId(params) {
    return assistantInstance.post('/clinic_chat/session_id', params);
  },
  // 对话历史详情
  getChatHistoryDetail(params) {
    return assistantInstance.get('/clinic_chat/detail', { params });
  },
  // 标记对话
  markChat(params) {
    return assistantInstance.post('/clinic_chat/detail/mark', params);
  },
  // 对话-修改诊所卡片状态
  updateChatCardStatus(params) {
    return assistantInstance.post('/clinic_chat/content/mode_card_deal/sse', params);
  },
  // 修改诊所卡片状态
  updateCardStatus(params) {
    return assistantInstance.post('/clinic_chat/content/mode_card_deal', params);
  },
  // 终止生成
  stopGeneration(params) {
    return assistantInstance.post('/clinic_chat/content/stop', params);
  },
  // 获取推荐问题
  getRecommendQuestions(params) {
    return assistantInstance.get('/clinic_chat/suggest/list', { params });
  },
  // Mermaid转Echarts
  mermaidToEcharts(params) {
    return assistantInstance.get('/clinic_chat/mermaid/to/echarts_html', { params });
  },
};
