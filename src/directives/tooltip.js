import tippy from 'tippy.js';
import 'tippy.js/dist/tippy.css';
import 'tippy.js/animations/scale.css';
// 添加自定义样式
const customStyles = `
  .tippy-box[data-theme~='custom-dark'] {
    background-color: rgba(0, 0, 0, 0.85);
    color: #fff;
    font-size: 12px;
    padding: 6px 8px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .tippy-box[data-theme~='custom-dark'] .tippy-arrow {
    color: rgba(0, 0, 0, 0.85);
  }

  .tippy-box[data-theme~='custom-dark'] .tippy-content {
    padding: 0;
    font-weight: normal;
    line-height: 1.4;
  }

  /* 兼容原有的 tread-tooltip 样式 */
  .tippy-box.tread-tooltip-style {
    background-color: rgba(0, 0, 0, 0.85);
    color: #fff;
    font-size: 12px;
    padding: 6px 8px;
    border-radius: 4px;
    max-width: 200px;
  }

  .tippy-box.tread-tooltip-style .tippy-arrow {
    color: rgba(0, 0, 0, 0.85);
  }
`;

// 注入样式
if (!document.getElementById('custom-tooltip-styles')) {
  const style = document.createElement('style');
  style.id = 'custom-tooltip-styles';
  style.textContent = customStyles;
  document.head.appendChild(style);
}

/**
 * v-tooltip 指令 - 基于 tippy.js
 * 使用方式：
 * 1. 简单文本：v-tooltip="'提示文本'"
 * 2. 配置对象：v-tooltip="{ content: '提示文本', placement: 'top' }"
 * 3. 动态内容：v-tooltip="tooltipConfig"
 *
 * 配置选项：
 * - content: 提示内容（必需）
 * - placement: 位置 'top' | 'bottom' | 'left' | 'right'，默认 'top'
 * - theme: 主题，默认使用自定义样式
 * - disabled: 是否禁用，默认 false
 * - delay: [显示延迟, 隐藏延迟]，默认 [0, 0]
 */

const TOOLTIP_INSTANCE = '@@tippyInstance';

/**
 * 创建 tooltip 实例
 */
function createTooltip(el, binding) {
  const config = parseConfig(binding.value);

  if (!config.content) {
    return;
  }

  // 创建 tippy 实例
  const instance = tippy(el, {
    content: config.content,
    placement: config.placement || 'top',
    theme: 'custom-dark', // 使用自定义主题
    delay: config.delay || [0, 0],
    duration: [150, 100], // [显示动画, 隐藏动画]
    arrow: true,
    hideOnClick: false,
    trigger: 'mouseenter focus',
    interactive: false,
    appendTo: () => document.body,
    zIndex: 9999,
    // animation: 'scale',
    // 自定义样式
    onShow(instance) {
      // 添加自定义样式类
      if (config.class) {
        instance.popper.classList.add(config.class);
      }
    },
    onHide(instance) {
      // 清理自定义样式类
      if (config.class) {
        instance.popper.classList.remove(config.class);
      }
    }
  });

  // 存储实例到元素上
  el[TOOLTIP_INSTANCE] = instance;
}

/**
 * 更新 tooltip 配置
 */
function updateTooltip(el, binding) {
  const instance = el[TOOLTIP_INSTANCE];
  if (!instance) {
    createTooltip(el, binding);
    return;
  }

  const config = parseConfig(binding.value);

  if (!config.content) {
    destroyTooltip(el);
    return;
  }

  // 更新配置
  instance.setContent(config.content);
  instance.setProps({
    placement: config.placement || 'top',
    delay: config.delay || [0, 0]
  });
}

/**
 * 销毁 tooltip 实例
 */
function destroyTooltip(el) {
  const instance = el[TOOLTIP_INSTANCE];
  if (instance) {
    instance.destroy();
    delete el[TOOLTIP_INSTANCE];
  }
}

/**
 * 解析配置参数
 */
function parseConfig(value) {
  if (typeof value === 'string') {
    return { content: value };
  } else if (typeof value === 'object' && value !== null) {
    return {
      content: value.content || '',
      placement: value.placement || 'top',
      delay: value.delay || [0, 0],
      class: value.class || ''
    };
  }
  return { content: '' };
}

export default {
  name: 'tooltip',

  bind(el, binding) {
    createTooltip(el, binding);
  },

  update(el, binding) {
    updateTooltip(el, binding);
  },

  unbind(el) {
    destroyTooltip(el);
  }
};

/**
 * 工具函数：创建 tooltip 配置
 * 可以在组件中使用，方便创建复杂的 tooltip 配置
 */
export function createTooltipConfig(content, options = {}) {
  return {
    content,
    placement: options.placement || 'top',
    delay: options.delay || [0, 0],
    class: options.class || ''
  };
}


