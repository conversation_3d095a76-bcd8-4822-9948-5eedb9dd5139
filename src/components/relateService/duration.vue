<template>
  <Modal
    ref="customModal"
    :value="value"
    width="800px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <Table :columns="columns" :data="list" :height="460" no-data-text="没有更多数据了">
        <template slot-scope="{ row }" slot="name">
          {{ row.name }}
        </template>

        <template slot-scope="{ row }" slot="serv_type_text">{{ row.serv_type_text || '-' }}</template>

        <template slot-scope="{ row, index }" slot="duration">
          <div class="suffix-box" style="margin-left: 10px">
            <InputNumber
              :max="1000"
              :min="15"
              :active-change="false"
              v-model="list[index].duration"
              placeholder="请输入服务时长"
              style="width: 100%"
            ></InputNumber>
            <div class="suffix">分钟</div>
          </div>
        </template>
      </Table>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { cloneDeep } from 'lodash-es';
export default {
  name: 'duration',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '补充服务提供时长',
    },
    need_list: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      confirmLoading: false,
      columns: [
        { title: '服务名称', slot: 'name', align: 'center' },
        { title: '服务类型', slot: 'serv_type_text', align: 'center' },
        { title: '*服务时长', slot: 'duration', align: 'center' },
      ],
      list: [],
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
        this.list = cloneDeep(this.need_list);
        this.list.forEach(item => (item.duration = null));
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.list = [];
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      if (this.validData()) {
        this.getR2SPhysioSetDuration();
      }
    },
    validData() {
      for (let item of this.list) {
        console.log('=>(duration.vue:113) item', item);
        if (!item.duration) {
          this.$Message.error(`【${item.name}】的服务时长不能为空`);
          return false;
        }
      }
      return true;
    },

    getR2SPhysioSetDuration() {
      let params = {
        setting_list: this.handlerParamsList(),
      };
      this.$api.getR2SPhysioSetDuration(params).then(res => {
        this.$Message.success('设置成功');
        this.$emit('success');
        this.closeModal();
      });
    },

    handlerParamsList() {
      let list = [];
      this.list.forEach(item => {
        list.push({
          id: item.id,
          duration: item.duration,
        });
      });
      return list || [];
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
}
// suffix
.suffix-box {
  display: flex;
  align-items: center;
  position: relative;
  width: 200px;

  .suffix {
    width: fit-content;
    padding: 0 10px;
    height: 30px;
    background: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 1px;
    top: 1px;
    z-index: 1;
    border-left: 1px solid #dcdee2;
  }
}
</style>
