<template>
  <div>
    <Modal
      :value="value"
      width="800px"
      :title="title"
      :footer-hide="false"
      :mask-closable="false"
      :lock-scroll="true"
      @on-visible-change="changeVisible"
    >
      <div class="content">
        <div class="tip-box">
          <Icon type="ios-information-circle" color="#8558fa" size="20" />
          <span class="ml10"
            >选择“所有服务”或“一级服务分类”，后续该分类下新增的项目将自动关联，若不希望按分类整体关联可选择具体项目进行关联</span
          >
        </div>
        <div class="tree-content">
          <div class="content-left">
            <Tree
              v-if="service_list.length"
              :data="service_list"
              :check-strictly="true"
              ref="tree"
              show-checkbox
              multiple
              @on-check-change="checkChange"
            ></Tree>
            <div v-else class="empty">暂无数据</div>
          </div>
          <div class="content-right">
            <Table :columns="columns" :data="list" :height="400" no-data-text="没有更多数据了">
              <template slot-scope="{ row }" slot="name">
                <div class="flex flex-item-align">
                  <svg-icon v-if="row.isHasChildren" name="folder" size="20" style="margin-right: 4px"></svg-icon>
                  <div>{{ row.name }}</div>
                </div>
              </template>

              <template slot-scope="{ row }" slot="action">
                <a @click="deleteLevel(row)">删除</a>
              </template>
            </Table>
          </div>
        </div>
      </div>
      <div slot="footer">
        <Button @click="closeModal">取消</Button>
        <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
      </div>
    </Modal>

    <duration v-model="durationVisible" :need_list="need_list"></duration>
  </div>
</template>

<script>
import duration from './duration.vue';
import { cloneDeep } from 'lodash-es';
export default {
  name: 'relateService',
  mixins: [],

  components: { duration },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '关联服务',
    },
    row: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      confirmLoading: false,
      columns: [
        { title: '已选', slot: 'name', align: 'left' },
        { title: '操作', slot: 'action', align: 'right' },
      ],
      list: [],
      service_list: [],
      durationVisible: false,
      need_list: [],
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    deleteId(arr, id) {
      for (let item of arr) {
        if (item.id === id) {
          item.checked = false;
          return;
        }
        // 如果当前项有 children，递归调用
        if (Array.isArray(item.children)) {
          this.deleteId(item.children, id);
        }
      }
    },
    deleteLevel(row) {
      this.deleteId(this.service_list, row.id);
      this.calc_table_list();
    },

    flattenArray(arr) {
      let result = [];

      arr.forEach(item => {
        let clone_item = cloneDeep(item);
        if (Array.isArray(item.children)) {
          clone_item.isHasChildren = true;
        }
        delete clone_item.children;
        result.push(clone_item);
        if (Array.isArray(item.children)) {
          result = result.concat(this.flattenArray(item.children));
        }
      });

      return result;
    },

    calc_table_list() {
      let list = [];
      let falt_list = this.flattenArray(this.service_list);
      falt_list?.forEach((item, index) => {
        if (item.checked) {
          list.push(item);
        }
      });
      this.list = list || [];
    },
    checkChange() {
      this.calc_table_list();
    },

    changeVisible(visible) {
      if (visible) {
        this.getPhysicalTServiceTree();
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.list = [];
      this.service_list = [];
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      this.getPhysicalTSetServiceScope();
    },

    getPhysicalTServiceTree() {
      let params = {
        id: this.row.id,
      };
      this.$api.getPhysicalTServiceTree(params).then(res => {
        this.handlerData(res.tree);
      });
    },
    handlerData(data) {
      let list = data || [];
      this.setCheckedTrue(list);
      this.service_list = list;
      this.calc_table_list();
    },

    setCheckedTrue(arr) {
      arr.forEach(item => {
        item.checked = Boolean(Number(item.checked || 0));
        item.title = item.name;
        item.expand = true;

        // 如果当前项有 children，递归调用
        if (Array.isArray(item.children)) {
          this.setCheckedTrue(item.children);
        }
      });
    },

    // 保存
    getPhysicalTSetServiceScope() {
      this.confirmLoading = true;
      let params = {
        id: this.row.id,
        checked_node: this.handlerNode(),
      };
      this.$api
        .getPhysicalTSetServiceScope(params)
        .then(res => {
          if (res.need_setting === '1') {
            this.need_list = res.list;
            this.durationVisible = true;
          } else {
            this.$Message.success('关联成功');
            this.$emit('success');
            this.closeModal();
          }
        })
        .finally(() => (this.confirmLoading = false));
    },

    handlerNode() {
      let list = [];
      getNode(this.service_list);

      function getNode(arr) {
        arr.forEach(item => {
          if (item.checked) {
            list.push({
              id: item.id,
              parent: item.parent,
            });
          }
          // 如果当前项有 children，递归调用
          if (Array.isArray(item.children)) {
            getNode(item.children);
          }
        });
      }

      return list;
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
  padding: 13px;
}

.tip-box {
  margin-bottom: 20px;
  background: #f2edff;
  border-radius: 4px;
  border: 1px solid #b39af7;
  padding: 15px;
  display: flex;
  align-items: center;
}

.tree-content {
  display: flex;

  .content-left {
    width: 300px;
    overflow-x: visible;
    height: 400px;
    overflow-y: auto;
  }

  .content-right {
    flex: 1;
    margin-left: 20px;
  }
}

::v-deep .ivu-table-tip {
  color: #333;
  margin-top: 0px;
}
::v-deep .ivu-tree-title {
  width: 200px;
  white-space: pre-line;
}
::v-deep .ivu-tree-title-selected,
.ivu-tree-title-selected:hover {
  background: unset;
}
::v-deep .ivu-tree-title:hover {
  background: unset;
  cursor: default;
}
.empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #333;
  font-size: 13px;
}
</style>
