<template>
  <div class="wrapper">
    <Modal
      :value="visible"
      title="创建用户"
      :mask-closable="false"
      :before-close="closeModal"
      @on-cancel="closeModal"
      @on-visible-change="visibleChange"
    >
      <div class="content">
        <Form ref="formData" :model="formData" :label-width="80" :label-colon="true" :rules="ruleValidate">
          <FormItem v-if="is_temporary">
            <div slot="label" class="custom-label">
              <Tooltip content="临时号由系统生成且在本店唯一，后续可修改成自己的真实手机号">
                <Icon type="ios-alert-outline" size="16" class="tip-icon cursor" />
              </Tooltip>
              <span>临时号:</span>
            </div>
            <div class="flex">
              <Input v-model="formData.mobile" placeholder="请生成临时号" disabled> </Input>
              <a @click="rebuild" class="rebuild cursor">
                <span class="text">重新生成</span>
                <Spin>
                  <Icon type="ios-loading" v-show="xnLoading" size="16" class="spin-icon-load"></Icon>
                </Spin>
              </a>
            </div>
          </FormItem>

          <FormItem label="暂存号" v-if="is_temporary" style="margin-bottom: 0px">
            <div>
              <Input v-model="formData.staging_mobile" maxlength="11" placeholder="非必填"> </Input>
              <p class="staging-number-tip">
                如果用户愿意提供手机号却不愿意提供验证码，可将用户手机号暂存于此，用于日常联系、随访等；等用户信任度建立后，再将临时号修改成用户真实手机号
              </p>
            </div>
          </FormItem>

          <FormItem label="用户手机" :prop="is_temporary ? '' : 'mobile'" v-show="!is_temporary">
            <div class="flex">
              <Input v-model="formData.mobile" maxlength="11" placeholder="请输入用户手机号"> </Input>
            </div>
          </FormItem>
          <FormItem label="" v-if="vip_info.vip_type_desc?.length > 0">
            <div class="vip-wrapper">
              <div class="vip-box" v-for="item in vip_info.list" :key="item.user_type_text">
                <img class="tip-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0927/123915_94370.png" />
                <div class="item-box flex">
                  <div class="label">会员名称:</div>
                  <div class="value">{{ item.user_type_text || '-' }}</div>
                </div>
                <div class="item-box flex">
                  <div class="label">会员开通渠道:</div>
                  <div class="value">{{ item.vip_source_text || '-' }}</div>
                </div>
              </div>
            </div>
          </FormItem>
          <FormItem label="验证码" :prop="is_temporary ? '' : 'authcode'" v-show="!is_temporary">
            <div class="flex flex-item-align">
              <Input v-model="formData.authcode" :maxlength="6" placeholder="请输入验证码"> </Input>
              <vac
                ref="vac"
                :auto-start="false"
                :left-time="60000"
                @finish="onCountDownFinish"
                style="width: 92px; text-align: center; margin-top: -2px"
              >
                <Button disabled slot="process" slot-scope="{ timeObj }" style="width: 100%">
                  {{ timeObj.ceil.s }} 重新获取
                </Button>
                <Button slot="before" type="primary" :disabled="!formData.mobile" @click="sendAuthCode"
                  >获取验证码
                </Button>
                <Button slot="finish" type="primary" :disabled="!formData.mobile" @click="sendAuthCode"
                  >获取验证码
                </Button>
              </vac>
            </div>
            <div class="unable-receive">
              <a @click="receiveTipModal">没办法提供验证码?</a>
            </div>
          </FormItem>
          <FormItem label="用户姓名" prop="real_name" class="pt20">
            <Input v-model="formData.real_name" placeholder="请输入用户姓名" maxlength="16" show-word-limit></Input>
          </FormItem>
          <FormItem label="用户性别" prop="sex">
            <Select v-model="formData.sex" placeholder="请选择用户性别">
              <Option :value="sex_item.value" v-for="(sex_item, sex_index) in sexList" :key="sex_index + 'sex'"
                >{{ sex_item.label }}
              </Option>
            </Select>
          </FormItem>

          <FormItem label="出生日期" prop="birthday">
            <DatePicker
              :options="disabledTime"
              :start-date="!formData.birthday ? new Date('1972-01-01') : null"
              type="date"
              placeholder="请选择出生日期"
              @on-change="changeDate"
              :value="formData.birthday"
            ></DatePicker>
          </FormItem>
          <FormItem label="用户年龄">
            <Input disabled v-model="userAge" placeholder="根据出生日期自动生成"></Input>
          </FormItem>

          <FormItem label="用户来源">
            <Select v-model="formData.source" placeholder="请选择用户来源">
              <Option
                :value="source_item.id"
                v-for="(source_item, source_index) in sourceListOptions"
                :key="source_index + 'source'"
                >{{ source_item.desc }}
              </Option>
            </Select>
          </FormItem>

          <FormItem label="用户等级">
            <Select v-model="formData.offline_level" placeholder="请选择用户等级">
              <Option :value="level" v-for="(level, source_index) in levelList" :key="source_index + 'level'"
                >{{ level }}
              </Option>
            </Select>
          </FormItem>
        </Form>
      </div>
      <div class="footer" slot="footer">
        <Button @click="closeModal">取消</Button>
        <Button type="primary" :loading="loading || extraLoading" @click="createNewConsumer">创建新用户</Button>
      </div>
    </Modal>

    <unable-receive-tip-modal
      v-model="receiveTipModalVisible"
      @success="success"
      :tipType="receive_tip_type"
    ></unable-receive-tip-modal>

    <syn-account-modal
      v-model="synAccountVisible"
      :levelList="levelList"
      :sourceList="sourceListOptions"
      :sexLis="sexList"
      :info="syn_user_info"
      :is_exist="is_exist"
      @success="showCreateInfo"
    ></syn-account-modal>
  </div>
</template>

<script>
import S from '@/libs/util';
import unableReceiveTipModal from './UnableReceiveTipModal.vue';
import SynAccountModal from './SynAccountModal.vue';
import { debounce } from 'lodash-es';
import { cloneDeep  } from 'lodash-es';

let init_formData = {
  real_name: '',
  mobile: '',
  authcode: '',
  birthday: '',
  sex: '',
  source: '',
  offline_level: '',
  staging_mobile: '', // 暂存号
};
export default {
  name: 'CreateUserModal',
  components: { unableReceiveTipModal, SynAccountModal },
  mixins: [],
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    name: {
      type: String,
      default: () => '',
    },
    levelList: {
      type: Array,
      default: () => [],
    },
    sourceList: {
      type: Array,
      default: () => [],
    },
    showCreateInfo: {
      type: Function,
    },
    defaultSource: {
      type: String,
      default: '',
    },
    quickCreate: {
      type: Boolean,
      default: () => false,
    },
    extraLoading: {
      type: Boolean,
      default: false,
    },
    quickCreateInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      formData: { ...init_formData }, // 创建用户的数据
      loading: false,
      sexList: [
        { label: '男', value: '1' },
        { label: '女', value: '2' },
      ],
      ruleValidate: {
        real_name: [{ required: true, message: '请输入用户姓名', trigger: 'change' }],
        sex: [{ required: true, message: '请选择用户性别', trigger: 'change' }],
        birthday: [{ required: true, message: '请选择用户出生日期', trigger: 'change' }],
        mobile: [
          { required: true, message: '请输入用户手机号', trigger: 'change' },
          {
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!this.regRole(value)) {
                this.vip_info = {};
                callback(new Error('请输入正确的手机号'));
              } else {
                this.checkIsVip(value);
                callback();
              }
            },
          },
        ],
        authcode: [{ required: true, message: '请输入验证码', trigger: 'change' }],
      },
      countdowning: false,
      userAge: '',

      receiveTipModalVisible: false,
      xnLoading: false,
      is_exist: '', // 1: 表示存在
      tip_type: '',
      receive_tip_type: '',
      synAccountVisible: false,
      syn_user_info: {},
      sourceListOptions: [],
      vip_info: {
        list: [],
        vip_type_desc: [],
      }, // 检测的会员信息
    };
  },
  computed: {
    is_temporary() {
      return this.tip_type === 'temporary';
    },
  },
  watch: {
    visible(val) {
      if (!val) {
        this.clearData();
      } else {
        // 手机号最新正则
        const phoneRegex = /^1[3-9]\d{9}$/;
        console.log(phoneRegex.test(this.name));
        if (phoneRegex.test(this.name)) {
          this.formData.mobile = this.name;
        } else {
          this.formData.real_name = this.name;
        }
        // 默认来源
        if (this.defaultSource) {
          this.formData.source = this.defaultSource;
        }
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    visibleChange(val) {
      console.log('=>(CreateUserModal.vue:288) val???');
      if (val) {
        // 预约快速创建导入部分信息
        console.log('=>(CreateUserModal.vue:275) this.quickCreate', this.quickCreateInfo);
        if (this.quickCreate) {
          this.formData.mobile = this.quickCreateInfo.mobile;
          this.formData.real_name = this.quickCreateInfo.name;
          this.formData.sex = this.quickCreateInfo.gender;
          this.formData.source = this.quickCreateInfo.user_source;
        }
        if (this.sourceList.length <= 0) {
          // api-获取options
          this.$api.getArrivalOptions().then(res => {
            // 用户来源
            this.sourceListOptions = S.descToArrHandle(res.userFromDesc);
          });
        } else {
          this.sourceListOptions = this.sourceList;
        }
      }
    },
    // 检测是否是vip
    checkIsVip: debounce(function (mobile) {
      let params = {
        mobile,
      };
      console.log('test');
      this.$api.getuservipbymobile(params).then(res => {
        console.log('res', res);
        this.vip_info = res;
      });
    }, 200),
    success(type) {
      // 使用临时号
      if (type === 'temporary') {
        this.tip_type = 'temporary';
        let freeze_mobile = cloneDeep(this.formData.mobile);
        this.formData.mobile = '';
        if (this.regRole(freeze_mobile)) {
          this.$set(this.formData, 'staging_mobile', freeze_mobile);
        }
        this.userGetxnmobile();
      }

      // 同步账号
      if (type === 'exist') {
        this.tip_type === 'exist';
        this.synAccountVisible = true;
        this.closeModal();
      }
    },
    // 获取手机号是否存在于用户中心
    userExistmobileuc() {
      let params = {
        mobile: this.formData.mobile,
      };
      this.receive_tip_type = '';
      return this.$api.userExistmobileuc(params).then(
        res => {
          return new Promise(
            resolve => {
              this.is_exist = res.is_exist;
              this.syn_user_info = res.info;
              if (this.is_exist === '1') {
                this.receive_tip_type = 'exist';
                this.receiveTipModalVisible = true;
              }
              resolve(res);
            },
            rej => rej('error')
          );
        },
        err => {}
      );
    },
    // 获取虚拟号
    userGetxnmobile() {
      this.xnLoading = true;
      this.$api
        .userGetxnmobile()
        .then(
          res => {
            this.formData.mobile = res.mobile;
            this.xnLoading = false;
          },
          err => {}
        )
        .finally(() => (this.xnLoading = false));
    },
    // 重新生成
    rebuild() {
      this.userGetxnmobile();
    },
    receiveTipModal() {
      this.receive_tip_type = 'temporary';
      this.receiveTipModalVisible = true;
    },
    changeDate(date) {
      console.log('-> %cdate %o', 'font-size: 15px', date);
      this.formData.birthday = date;
      let age = S.formatDuration(date);
      let index = age?.indexOf('岁');
      this.userAge = age?.substring(0, index + 1);
    },
    // 手机号校验
    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },
    closeModal() {
      this.$emit('update:visible', false);
    },
    clearData() {
      this.tip_type = '';
      this.receive_tip_type = '';
      this.formData = { ...init_formData };
      this.$refs.formData.resetFields();
      this.$refs.vac.finishCountdown();
      this.userAge = '';
      this.vip_info = {
        list: [],
        vip_type_desc: [],
      };
    },
    // 创建新用户（创建用户时，同样需要对手机号做检测）
    createNewConsumer() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          // if (  this.formData.staging_mobile !== '' && !this.regRole(this.formData.staging_mobile)) {
          //   this.$Message.error('请输入正确的暂存号')
          //   return
          // }

          this.userExistmobileuc().then(res => {
            if (res?.is_exist === '0') {
              this.loading = true;
              let params = {
                ...this.formData,
                user_type: this.tip_type === 'temporary' ? '2' : '1',
              };
              this.$api
                .createUser(params)
                .then(
                  res => {
                    this.$Message.success('创建用户成功');
                    this.closeModal();
                    this.$emit('success', res?.user);
                    this.showCreateInfo(res.user);
                  },
                  err => {}
                )
                .finally(() => {
                  this.loading = false;
                });
            }
          });
        }
      });
    },
    sendAuthCode() {
      if (!this.regRole(this.formData.mobile)) {
        this.$Message.error('请输入正确的手机号');
        return;
      }
      this.userExistmobileuc().then(res => {
        if (res?.is_exist === '0') {
          let params = {
            mobile: this.formData.mobile,
          };
          this.$api
            .sendAuthCode(params)
            .then(res => {
              this.$Message.success('发送成功');
              this.$refs.vac.startCountdown(true);
            })
            .catch(err => {});
        }
      });
    },
    onCountDownFinish() {
      this.countdowning = false;
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.custom-label {
  margin-top: -2px;

  .tip-icon {
    margin-right: 4px;
  }
}

.rebuild {
  position: relative;
  width: 90px;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .text {
    margin-right: 4px;
    min-width: fit-content;
  }
}

.unable-receive {
  height: 4px;
  text-align: right;
}

.ivu-date-picker {
  width: 100%;
}

::v-deep .ivu-modal-body {
  padding-right: 28px;
}

.cursor {
  cursor: pointer;
}

.spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

.pt20 {
  padding-top: 20px;
}

.staging-number-tip {
  height: 30px;
  font-size: 12px;
  color: red;
  line-height: 18px;
  margin-top: 10px;
  text-align: justify;
}

.vip-box {
  background: rgba(48, 136, 255, 0.08);
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;

  .tip-icon {
    width: 16px;
    min-width: 16px;
    height: 16px;
    margin-right: 12px;
  }

  .item-box {
    font-weight: 400;
    font-size: 12px;
    color: #909399;
    line-height: 16px;

    .label {
    }

    .value {
      color: #3088ff;
      margin-left: 10px;
      margin-right: 10px;
    }
  }
}

.vip-wrapper {
  .vip-box {
    margin-bottom: 10px;
  }

  .vip-box:last-child {
    margin-bottom: 0;
  }
}
</style>
