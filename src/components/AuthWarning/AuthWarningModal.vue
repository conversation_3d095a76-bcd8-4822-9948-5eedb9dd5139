<template>
  <Modal
    ref="customModal"
    v-model="visible"
    width="400px"
    :footer-hide="true"
    :mask-closable="false"
    :lock-scroll="true"
  >
    <!--    @on-visible-change="changeVisible"-->
    <div class="content">
      <Icon type="ios-alert" class="tip-icon" size="26" color="#faad14" />
      <div class="tip-text">主体认证未成功，暂时无法使用线上收款功能</div>
    </div>
    <div class="footer">
      <Button type="primary" @click="onLink">去开通</Button>
      <Button @click="closeModal">取消</Button>
    </div>
  </Modal>
</template>
<script>
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'AuthWarningModal',
  data() {
    return {
      visible: false,
    };
  },
  computed: {
    isRst() {
      return isRstClinic();
    },
  },
  created() {},

  mounted() {},

  methods: {
    async verify() {
      if (this.isRst) {
        const res = await this.$api.getyztauditstatus();
        if (res?.audit_status !== 'succeed') {
          this.visible = true;
          return Promise.reject(res);
        } else {
          return Promise.resolve(res);
        }
      } else {
        // 非数字化不进校验直接通过
        return Promise.resolve({
          audit_status: '',
          msg: '非数字化不进校验！',
        });
      }
    },

    onLink() {
      this.$router
        .push({
          path: '/setting/subject-auth/index',
        })
        .then(() => {
          // 防止弹窗没有关闭
          this.visible = false;
          console.log('🚀 ~ AuthWarningModal.vue:67 ~ .then ~ this.visible =>', this.visible);
        });
    },

    closeModal() {
      this.visible = false;
    },
  },
};
</script>

<style scoped lang="less">
.content {
  display: flex;
  align-items: center;
  gap: 10px;

  .tip-text {
    color: #333;
  }
}

.footer {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin: 24px 0 0 0;
}
</style>
