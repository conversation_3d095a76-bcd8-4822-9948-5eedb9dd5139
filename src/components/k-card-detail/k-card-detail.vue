<template>
  <!-- 核销弹窗 -->
  <Modal
    :value="value"
    width="1080"
    title="服务卡券详情"
    :mask-closable="false"
    @on-visible-change="visibleChange"
    footer-hide
  >
    <div class="content">
      <Table :loading="tableLoading" :columns="cardColumns" :data="cardData" height="400">
        <!-- 预约单号 -->
        <template slot-scope="{ row }" slot="reserve_code">
          <k-link
            v-if="row.reserve_code"
            :to="{ path: '/reserve/listing/list', query: { code: row.reserve_code } }"
            target="_blank"
            >{{ row.reserve_code }}</k-link
          >
          <div v-else>-</div>
        </template>

        <!-- 附注 -->
        <template slot-scope="{ row }" slot="notes">
          {{ row.notes || '-' }}
        </template>

        <!-- 生效时间 -->
        <template slot-scope="{ row }" slot="create_time">
          {{ row.create_time | data_format }}
        </template>
        <!-- 售后情况 -->
        <!--          <template slot-scope="{ row }" slot="refund_status">-->
        <!--            <span>{{ row.refund_status_text || '-' }}</span> <br />-->
        <!--            <span v-if="row.closed_type_text" style="color: #999999;">({{ row.refund_use_text }})</span>-->
        <!--          </template>-->
        <!-- 截止时间 -->
        <template slot-scope="{ row }" slot="expire_time">
          {{ row.expire_time | data_format }}
        </template>

        <!-- 核销时间 -->
        <template slot-scope="{ row }" slot="used_time">
          {{ row.used_time | data_format }}
        </template>

        <!-- 核销人 -->
        <template slot-scope="{ row }" slot="operator_name">
          {{ row.operator_name || '-' }}
        </template>

        <!-- 服务理疗师 -->
        <template slot-scope="{ row }" slot="artificer_name">
          <p v-if="isRstClinic">
            <span v-if="row.divide_info?.physio_name">
              {{ row.divide_info?.physio_name
              }}<span v-if="row.divide_info?.mdp_permission_name"
                >({{ row.divide_info?.mdp_permission_name }} | {{ row.divide_info?.mdp_level_text }})</span
              >
            </span>
            <span v-else>-</span>
          </p>
          <p v-else>{{ row.artificer_name || '-' }}</p>
        </template>

        <!-- 分佣金额 -->
        <template slot-scope="{ row }" slot="artificer_amount">
          <p v-if="isRstClinic">
            <span v-if="row.divide_info?.money">￥{{ row.divide_info?.money }}</span>
            <span v-else>-</span>
          </p>
          <p v-else>
            <span v-if="row.artificer_amount">￥{{ row.artificer_amount }}</span>
            <span v-else>-</span>
          </p>
        </template>

        <template slot-scope="{ row, index }" slot="action">
          <a v-if="row.status == '10'" @click="jumpVerification(row)">核销</a>
          <!--  榕树堂诊所    -->
          <div v-else-if="isRstClinic && row.status == '20'">
            <!--   区分新老订单，跳转详情     -->
            <a v-if="row.is_convert === '1'" @click="jumpVerification(row)">核销详情</a>
            <a v-else @click="jumpVerificationDetail(row)">核销详情</a>
          </div>
          <!--  普通诊所    -->
          <div v-else-if="!isRstClinic && row.status == '20'">
            <a @click="jumpVerification(row)">核销详情</a>
          </div>
          <p v-else>-</p>
        </template>
      </Table>
    </div>
  </Modal>
</template>

<script>
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'cardDetail',
  components: {},
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false,
    },
    batch_id: {
      type: String,
      default: () => '',
    },
  },
  data() {
    return {
      cardColumns: [
        { title: '卡券编号', key: 'card_no' },
        { title: '预约单号', slot: 'reserve_code' },
        { title: '服务项目', key: 'card_name' },
        { title: '附注', slot: 'notes' },
        { title: '生效时间', slot: 'create_time' },
        { title: '截止时间', slot: 'expire_time' },
        { title: '状态', key: 'status_text' },
        // {title: '售后情况', slot: 'refund_status'},
        { title: '核销时间', slot: 'used_time' },
        { title: '核销人', slot: 'operator_name' },
        {
          title: '服务理疗师',
          slot: 'artificer_name',
          renderHeader: (h, params) => {
            return h('span', {}, `${this.isRstClinic ? '服务人' : '服务理疗师'}`);
          },
        },
        {
          title: '分佣金额',
          slot: 'artificer_amount',
          renderHeader: (h, params) => {
            return h('span', {}, `${this.isRstClinic ? '业绩提成' : '分佣金额'}`);
          },
        },
        { title: '操作', slot: 'action' },
      ], // 卡券核销columns

      cardData: [], // 卡券核销数据
      tableLoading: false,
    };
  },
  computed: {
    isRstClinic() {
      return isRstClinic();
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    visibleChange(val) {
      if (!val) {
        this.$emit('input', false);
        this.cardData = [];
      } else {
        this.getCarddetail();
      }
    },
    // 跳转到核销页面
    jumpVerification({ card_no }) {
      this.$emit('input', false);
      this.$router.push({
        path: '/service/card/verification',
        query: { card: card_no },
      });
    },
    jumpVerificationDetail(row) {
      this.$emit('input', false);
      this.$router.push({
        path: '/service/writeDetail/detail',
        query: { id: row.id },
      });
    },

    // 获取详情
    getCarddetail() {
      this.tableLoading = true;
      const { batch_id } = this;
      let params = {
        batch_id,
      };
      this.$api
        .getCarddetail(params)
        .then(res => {
          this.cardData = res.cards;
        })
        .catch(err => {})
        .finally(() => {
          this.tableLoading = false;
        });
    },

    // 获取核销的卡券编号
    getCardno() {
      const { batch_id } = this;
      let params = {
        batch_id,
      };
      this.$api
        .getCardno(params)
        .then(res => {
          this.jumpVerification(res.card_no);
        })
        .catch(err => {});
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
p {
  margin: 0px;
}
</style>
