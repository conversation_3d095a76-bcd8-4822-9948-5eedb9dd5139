<template>
  <div class="diagnose-wrapper">
    <div class="block-header flex flex-item-between search-header">
      <div class="search-title">选择综合的症状</div>
      <div class="select-box">
        <el-select v-model="searchName" style="width:200px" @change="comitSelect" filterable placeholder="输入关键词搜索症状" popper-class='curpopper'>
          <el-option
            v-for="(item,index) in optionList"
            :key="item.code"
            :label="item.name"
            :value="index">
          </el-option>
        </el-select>
        </div>
    </div>
    <Row style="border-bottom: 1px solid #dddddd;">
      <Col span="8">
        <Menu ref="menus" mode="vertical" width="auto" :active-name="activeName" :open-names="openNames" accordion style="height: 100%" @on-open-change="subClick" @on-select="menuItemClick">
          <Submenu :name="index" v-for="(item,index) in diaList" :key="index">
            <template slot="title">{{item.name}}</template>
            <MenuItem :name="index+ '-' + idx" v-for="(e,idx) in item.children" :key="idx" >{{e.name}}</MenuItem>
          </Submenu>
        </Menu>
      </Col>
      <Col span="16">
        <div class="zz-warpper" >
          <div v-for="(item,index) in subItemList" :key="index" @click="checkMainWrap(item,index)">
            <div class="zz-title"  >{{`${index + 1}.${item.name}`}}</div>
            <div class="tags" >
              <span :class="resultList.includes(e.name)?'active':needFilterList.includes(e.code)?'disSpan':''" v-for="(e,idx) in item.children" :key="idx" @click="checkSubWrap($event,e,idx)">{{e.name}}</span>
            </div>
          </div>
        </div>
      </Col>
    </Row>
    <div class="block-header">已选症状({{options4.length}}) <span>至少选择4项，点击已选症状可设为主症</span></div>
    <div class="xz-tags" v-if="options4.length>0">
      <div :class="(activeTag === index) || (mainCode == item.id)?'mainTag':item.can_main == '0'?'disTag':''" v-for="(item,index) in options4" :key="index" @click="changeTag(item,index)">{{item.label}}
        <i @click.stop="delOptions(item,index)">×</i>
        <p class="posiDiv" v-if="(activeTag === index) || (mainCode == item.id)">主</p>
      </div>
    </div>
    <div class="block-header">辨证结果</div>
    <div>
      <Tabs :value="name1" style="min-height: 400px">
        <TabPane :label="item.name" v-for="(item,index) in delTabList" :key="index">
          <div class="confidenceLevel">
            <p class="p1"></p>
            <p class="p2">{{`置信度：${item.score}%`}}</p>
          </div>
          <div v-for="(e,idx) in item.explain" :key="idx" class="resuBox">
            <div class="resuTitle">
              <div class="circle"></div>
              <div class="text">{{e.title}}</div>
            </div>
            <div class="gay_text">
              {{e.desc}}
            </div>
          </div>
        </TabPane>
      </Tabs>
    </div>
    <div class="block_40"></div>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util' // Some commonly used tools
import io from "@/libs/io" // Http request
import * as runtime from '@/libs/runtime' // Runtime information
/* eslint-disable */

export default {
  props:["diaList","options4","resultList","tabsList","medicineList","needFilterList","mainCode"],
  name: "diagnose",

  data() {
    return {
      openNames:[0],
      subList:[0],
      itemList:0,
      subItemList:[],
      activeMainIndex:"",
      activeSubIndex:"",
      name1:0,
      activeTag:"",
      rejectList:[],
      activeName:"0-0",
      searchName:'', //搜索词
      optionList:[
      ]
    }
  },
  computed:{
    //置信度精确两位小数
    delTabList(){
      let tabsList = [];
      this.tabsList.map((item)=>{
        tabsList.push({
          ...item,
          score:S.number_format(item.score,2,".","")
        })
      })
      return tabsList
    }
  },
  watch: {
    diaList: function (val) {
      this.$nextTick(() => {
        this.$refs.menus.updateActiveName();
        this.$refs.menus.updateOpened()
      });
    },
  },
  created() {
    this.subItemList = this.diaList[0].children[0].children;
    this.getSelectList()
  },
  methods: {
    subClick(name){
      if(!S.isEmptyObject(name)){
        this.activeName = name[0] + "-0";
        this.subList = name;
        this.subItemList = this.diaList[this.subList[0]].children[0].children;
      }
    },
    menuItemClick(name){
      console.log(name,'sada')
      let nameIndex = name.split("-")[1];
      this.subItemList = this.diaList[this.subList[0]].children[nameIndex].children;
    },
    checkMainWrap(item,index){
      this.activeMainIndex = index;
    },
    checkSubWrap(event,e,idx){
    console.log(e)
      if(this.needFilterList.includes(e.code)){
        return
      };
      if (event.target.className.indexOf("active") == -1) {
        event.target.className = "active"; //选中div样式
      } else {
        event.target.className = "";//未选中div样式
      }
      let symptomObj = {
        name:e.name,
        id:e.code,
        type:e.type,
        can_main:e.can_main
      }
      this.$emit("sendSymptom",symptomObj)
    },
    delOptions(item,index){
      if(index == this.activeTag){
        this.activeTag = "";
        this.$emit("delSymptom",item,true)
      }else{
        this.$emit("delSymptom",item,false)
      }
    },
    //点击切换主症或者取消选择主症
    changeTag(item,index){
      //先判断能否选为主症，再进行取消判断的逻辑
      if(item.can_main == '1'){
        if(this.activeTag == index || this.mainCode == item.id){
          this.activeTag = -1;
          this.$emit('clearMainCode');
        }else{
          this.activeTag = index;
          this.$emit("handleTag",{code:item.id});
        }
      }else{
        this.$Message.warning("非主症无法添加")
        return;
      }
    },
    // 获取select选项
    getSelectList(){
      if(this.diaList && this.diaList.length>0){
        this.deepChildren(this.diaList)
      }
    },
    deepChildren(arr){
      arr.map(val=>{
        if(val.children && val.children instanceof Array){
          this.deepChildren(val.children)
        }else{
          this.optionList.push(val)
        }
      })
    },
    // 下拉框选择症状
    comitSelect(index){
      if(index == undefined){
        return
      }
      let val = this.optionList[index]
      if(this.needFilterList.includes(val.code)){
        return
      };
      console.log(val)
      let symptomObj = {
        name:val.name,
        id:val.code,
        type:val.type,
        can_main:val.can_main
      }
      this.$emit("sendSymptom",symptomObj)
    },
    // closeScroll(state){
    //   if(state){
    //     document.body.style.overflow = 'hidden'
    //     document.querySelectorAll(".app-help-container .help-container-body")[0].style.overflow = 'hidden'
    //   }else{
    //     document.body.style.overflow = 'auto'
    //     document.querySelectorAll(".app-help-container .help-container-body")[0].style.overflow = 'auto'
    //   }
    // }
  }
}
</script>

<style lang="less">
.diagnose-wrapper {
  font-size: 14px;
  padding-top: 60px;
  .block-header {
    background-color: #F8F8F8;
    padding-left: 14px;
    margin: 0px;
    span {
      font-size: 12px;
      color: #bbbbbb;
      font-weight: 300;
    }
  }
  .block-header:nth-of-type(n+2) {
    margin: 15px 0 10px 0;
  }

  .ivu-menu-submenu-title {
    font-size: 14px;
  }
  .ivu-menu-opened {
    .ivu-menu-submenu-title {
      color: #155BD4;
      font-weight: bold;
    }
  }
  .ivu-menu-vertical .ivu-menu-submenu-title-icon {
    right: 3px;
  }
  .ivu-menu-vertical .ivu-menu-item {
    padding: 4px 14px;
    margin: 6px 0;
    font-size: 14px;
  }
  .ivu-menu-vertical .ivu-menu-submenu-title {
    padding: 14px 14px;
  }

  .zz-warpper {
    max-height: 560px;
    overflow-y: auto;

    padding: 5px;

    .zz-title {
      padding: 5px 0;
    }
    .tags {
      padding-bottom: 5px;

      span {
        padding: 5px;
        border: 1px solid #E2E4E6;
        border-radius: 2px;
        display: inline-block;
        margin-top: 5px;
        margin-right: 4px;
        color: #969799;
        cursor: pointer;

        &:hover {
          border: 1px solid #cbcccd;
          color: #77787a;
        }
        &.disSpan{
          background: #F7F8FA;
          color:#C8C9CC;
          border:#E2E4E6;
          cursor: auto;
        }
        &.active {
          border: 1px solid #155BD4;
          color: #155BD4;
        }
      }
    }
  }

  .xz-tags {
    padding: 14px 0 0 14px;
    border: 1px solid #E2E4E6;
    border-radius: 2px;
    div {
      display: inline-block;
      margin: 0 14px 14px 0;
      padding: 8px 25px;
      border: 1px solid #155BD4;
      border-radius: 2px;
      color: #155BD4;
      position: relative;
      cursor: pointer;
      .posiDiv{
        position: absolute;
        left: -6px;
        top:-6px;
        width: 19px;
        height: 14px;
        border-radius: 7px;
        display: flex;
        align-items: center;
        justify-content: center;
        color:#fff;
        font-size: 10px;
        background: #FF8E38;
        border: none;
      }
    }
    .mainTag{
      background: #155BD4;
      color:#fff;
      position: relative;
      .posiDiv{
        position: absolute;
        left: -6px;
        top:-6px;
        width: 19px;
        height: 14px;
        border-radius: 7px;
        display: flex;
        align-items: center;
        justify-content: center;
        color:#fff;
        font-size: 10px;
        background: #FF8E38;
      }
    }
    .disTag{
      background: #F7F8FA;
      color:#C8C9CC;
      position: relative;
      border:#E2E4E6;
    }
    i {
      position: absolute;
      top: -2px;
      right: 3px;
      font-style: normal;
      cursor: pointer;
    }
  }
}
.confidenceLevel{
  display: flex;
  align-items: center;
  .p1{
    width: 6px;
    height: 6px;
    background: #323233;
    border-radius: 50%;
    opacity: 1;
  }
  .p2{
    margin-left: 10px;
    font-size: 14px;
    line-height: 20px;
    color:#323233;
    font-weight: 600;
  }
}
.resuBox{
  margin-bottom: 12px;
  .resuTitle{
    display: flex;
    align-items: center;
    .circle{
      width: 6px;
      height: 6px;
      background: #323233;
      border-radius: 50%;
      opacity: 1;
    }
    .text{
      margin-left: 10px;
      font-size: 14px;
      line-height: 20px;
      color:#323233;
      font-weight: 600;
    }
  }
  .gay_text{
    font-size: 14px;
    line-height: 20px;
    color:#969799;
  }
}

.select-box {
  width: 200px;
}
.search-title{
  line-height: 40px;
}
.curpopper{
  width: 200px;
  z-index: 300!important;
	.selected{
		color:#155BD4;
	}
}
.search-header{
  width: 419px;
  padding: 0 20px;
  line-height: 40px;
  position: fixed!important;
  top: 56px;
  right: 20px;
  background-color: #efefef;
  z-index: 999;
}
</style>

