<template>
  <div class="prescription-warpper">
    <div class="block-header">推荐处方</div>
    <div class="re-pre">
      <RadioGroup @on-change="changeHandle" v-model="recm">
        <Row v-for="(item, index) in recommendList" :key="index">
          <Radio :label="index"
            ><span>{{ `${item.name}` }}</span
            ><span class="grey_text">{{
              `(${item.type_text} ${item.from})`
            }}</span></Radio
          >
          <span>置信度：{{ `${item.score}%` }}</span>
        </Row>
      </RadioGroup>
    </div>
    <Divider />
    <div class="block-header">
      药方组成
      <Select class="pre-unit" v-model="initDose" @on-select="selectDose">
        <Option :value="index" v-for="(item, index) in doseDesc" :key="index">{{
          item.desc
        }}</Option>
      </Select>
    </div>
    <Alert
      type="warning"
      style="color: #d47a32"
      v-for="(item, index) in hasDescCautionsList"
      :key="index"
    >
      {{ `${item.title}：${item.desc}` }}
    </Alert>
    <div class="re-pre">
      <table class="table" style="text-align: center">
        <thead>
          <tr>
            <th>名称</th>
            <th>{{ currentDoseText }}</th>
            <th>可替换</th>
            <th>备注</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in medicineList" :key="index">
            <td>{{ item.name }}</td>
            <td v-if="currentDose == 'dose'">{{ item.dose }}g</td>
            <td v-if="currentDose == 'dose_medium'">{{ item.dose_medium }}g</td>
            <td v-if="currentDose == 'dose_big'">{{ item.dose_big }}g</td>
            <td class="point" @click="replaceMain(index)" v-if="item.replace">
              {{ item.replace.name }}
            </td>
            <td>{{ medicine_remarks_map_list[item.code] }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <Divider />

    <div class="block-header">
      药方微调 <span>(勾选相关症状，微调药方组成)</span>
    </div>
    <div class="re-pre">
      <div v-for="(item, index) in adjustList" :key="index">
        <CheckboxGroup @on-change="checkChange" v-model="adjust">
          <Checkbox :label="index">{{ index + 1 }}.{{ item.title }}</Checkbox>
        </CheckboxGroup>
        <table class="table" style="text-align: center">
          <thead>
            <tr>
              <th>名称</th>
              <th>{{ currentDoseText }}</th>
              <th v-if="adjust.includes(index)">可替换</th>
              <th>备注</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(e, idx) in item.medicine" :key="idx">
              <td>{{ e.name }}</td>
              <td v-if="currentDose == 'dose'">{{ e.dose }}g</td>
              <td v-if="currentDose == 'dose_medium'">{{ e.dose_medium }}g</td>
              <td v-if="currentDose == 'dose_big'">{{ e.dose_big }}g</td>
              <td
                class="point"
                v-if="adjust.includes(index)"
                @click="replaceName(index, idx)"
              >
                {{ e.replace.length > 0 ? e.replace[0].name : "" }}
              </td>
              <td>{{ medicine_remarks_map_list[e.code] }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="block-header">药方性状</div>
    <div id="myChart" :style="{ width: '100%', height: '300px' }"></div>
  </div>
</template>

<script>
/* eslint-disable */
// import * as echarts from "echarts";
// import S from "@/libs/util"; // Some commonly used tools
// import io from "@/libs/io"; // Http request
// import * as runtime from "@/libs/runtime"; // Runtime information
/* eslint-disable */

export default {
  name: "prescription",
  props: [
    "recommendList",
    "medicineList",
    "adjustList",
    "attributesNameList",
    "attributesDataList",
    "doseDesc",
    "cautionsList",
    "medicine_remarks_map_list",
  ],
  data() {
    return {
      recm: 0,
      initDose: "dose",
      codeList: [],
      currentDose: "dose",
      currentDoseText: "标准剂量",
      adjust: [],
    };
  },
  computed: {
    hasDescCautionsList() {
      //只显示有desc的数据
      return this.cautionsList.filter((item) => {
        return item.desc;
      });
    },
  },
  watch: {
    attributesDataList: {
      handler: function () {
        this.drawLine();
      },
    },
  },
  mounted() {
    this.drawLine();
  },
  methods: {
    changeHandle(value) {
      this.codeList = this.recommendList[value].codes;
      this.$emit("chooseRecm", this.recommendList[value].codes);
    },
    selectDose(value) {
      this.currentDose = value.value;
      this.currentDoseText = value.label;
      this.$emit("changeDose");
    },
    checkChange(value) {
      console.log(this.adjust);
      let adjust_idx = this.adjust;
      let dose = this.currentDose;
      this.$emit("handleCheck", adjust_idx, dose);
    },
    drawLine() {
      var myChart = this.$echarts.init(document.getElementById("myChart"));
      // 指定图表的配置项和数据
      var option = {
        title: {},
        tooltip: {},
        legend: {},
        xAxis: {
          data: this.attributesNameList,
        },
        yAxis: {},
        series: [
          {
            barWidth: 12,
            color: "#155BD4",
            type: "bar",
            data: this.attributesDataList,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    },
    replaceMain(index) {
      let tcode = this.medicineList[index].code;

      let query = {
        mr_id: this.$route.query.mr_id,
        main:tcode
      };
      this.$api
        .getmedicinereplace(query)
        .then((res) => {
          if (res && res.errcode !== "4000") {
            this.medicineList.map((val, ind) => {
              if (index == ind) {
                let temp = val.name;
                val.name = val.replace.name;
                val.replace.name = temp;
                let tempcode = val.code;
                val.code = val.replace.code;
                val.replace.code = tempcode;
              }
            });
          } else {
          }
        })
        .catch((e) => {});
    },
    replaceName(index, ind) {
      let postCode = this.adjustList[index].medicine[ind].code;

      let query = {
        mr_id: this.$route.query.mr_id,
        adjust: { [index]: postCode },
      };
      this.$api
        .getmedicinereplace(query)
        .then((res) => {
          if (res && res.errcode !== "4000") {
            this.adjustList.forEach((val, idx) => {
              if (index == idx) {
                val.medicine.forEach((v, i) => {
                  if (ind == i && v.replace.length > 0) {
                    let temp2 = v.name;
                    v.name = v.replace[0].name;
                    v.replace[0].name = temp2;
                    let tempCode = v.code;
                    v.code = v.replace[0].code;
                    v.replace[0].code = tempCode;
                  }
                });
              }
            });
          }
        })
        .catch((e) => {});
    },
    postReplaceInfo(code, ind) {
      let query;
      if (ind) {
        query = {
          mr_id: this.$route.query.mr_id,
          adjust: { [ind]: code },
        };
      } else {
        query = {
          mr_id: this.$route.query.mr_id,
          main: code,
        };
      }
      this.$api
        .getmedicinereplace(query)
        .then((res) => {
          console.log(res);
        })
        .catch((e) => {});
    },
  },
};
</script>

<style lang="less">
.prescription-warpper {
  font-size: 14px;

  .block-header {
    background-color: #f8f8f8;
    padding-left: 14px;
    margin: 0;
    span {
      font-size: 12px;
      color: #bbbbbb;
      font-weight: 300;
    }
  }
  .block-header:nth-of-type(n + 2) {
    margin: 15px 0 10px 0;
  }

  .pre-unit {
    width: 90px;
    height: 26px;
    position: absolute;
    right: 7px;
    top: 7px;
    .ivu-select-selection {
      height: 26px;
    }
    .ivu-select-selected-value {
      height: 26px;
      line-height: 26px;
      color: #333;
    }
  }

  .re-pre {
    padding: 10px;
    label {
      padding-bottom: 10px;
    }
  }

  .table {
    color: #323233;
    th {
      text-align: center;
      font-weight: normal;
    }
  }
}
.grey_text {
  color: #999;
}
.point {
  cursor: pointer;
}
</style>
