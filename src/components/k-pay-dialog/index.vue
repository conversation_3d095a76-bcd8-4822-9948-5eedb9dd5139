<template>
  <div>
    <Modal
      :value="visible"
      width="840"
      :mask-closable="false"
      class-name="vertical-center-modal"
      :title="title"
      @on-visible-change="changeVisible"
    >
      <div class="content flex" v-if="visible">
        <div class="content-left flex-1">
          <div class="padding-block">
            <div>
              <div class="title-text">订单金额</div>
              <div class="order-moeny-block flex flex-item-align flex-item-between">
                <div class="label-text">应付金额</div>
                <div class="flex flex-item-align">
                  <span
                    class="underline-money"
                    v-show="pay_activeId == '3' && is_recharge_buy === 'yes' && getDiscountFee"
                    v-text-format.number="receivable_fee"
                  ></span>
                  <span class="pay-money" v-text-format.number="ori_payment"></span>
                </div>
              </div>
            </div>

            <div class="mt-30">
              <div class="flex flex-item-align flex-item-between">
                <div class="title-text">优惠活动</div>
              </div>
              <Select
                :disabled="disabledDiscount || is_ry_order === '1'"
                class="control_width"
                v-model="formData.sp_id"
                @on-change="changeVoucher"
                clearable
                placeholder="请选择优惠活动"
                size="large"
              >
                <Option v-for="item in activityList" :value="item.id" :key="item.id">
                  {{ item.name }}
                </Option>
              </Select>
            </div>
            <div class="discount-type-box mt-10 mb-10" v-if="discountRules.length > 0">
              <RadioGroup
                v-model="formData.sp_item_id"
                button-style="solid"
                type="button"
                @on-change="changeDiscountType"
              >
                <Radio v-for="item in discountRules" style="margin-bottom: 6px" :label="item.id" :key="item.id">
                  {{ item.name }}
                </Radio>
              </RadioGroup>
            </div>
            <div v-if="showDiscount" class="discount-input-box">
              <span class="discount-label-text flex-1">{{ getDiscountInfo.label }}</span>
              <span class="unit-text" v-if="formData.sp_item_id !== '1'">￥</span>
              <el-input-number
                :class="`control_width custom-number-input ${
                  formData.sp_item_id === '1' ? 'discount-input-box-percent' : ''
                }`"
                ref="discountInput"
                :max="Number(getDiscountInfo.max)"
                :min="Number(getDiscountInfo.min) || 0"
                :precision="getDiscountInfo.precision"
                :active-change="false"
                :placeholder="getDiscountInfo.placeholder"
                style="width: 88px"
                :controls="false"
                v-model="formData.sp_item_value"
                @change="calculateAmount"
                @focus="e => handleFocus(e)"
                @blur="handleLimitValue"
              />
              <span class="unit-text" v-if="formData.sp_item_id === '1'">折</span>
            </div>
            <div class="reduction-money">-<span v-text-format.number="discountAmount"></span></div>
          </div>

          <div class="actual-payment-block">
            <div style="color: #999">
              <span>结算明细</span>
              <el-tooltip effect="dark" placement="top-start" max-width="240">
                <Icon type="ios-arrow-dropright" class="ml-2 cursor" />
                <div class="pay-tooltip-content" slot="content">
                  <div class="pay-item">
                    <span class="pay-label">订单金额</span>
                    <span class="pay-amount" v-text-format.number="ori_payment"></span>
                  </div>
                  <div class="pay-item">
                    <span class="pay-label">优惠活动</span>
                    <span class="pay-amount">{{ getActivityName }}</span>
                  </div>
                  <div class="pay-item" v-for="(item, index) in discountList" :key="'discount' + index">
                    <span class="pay-label">{{ item.name }}</span>
                    <span> -<span class="pay-amount" v-text-format.number="item.discount_fee"></span> </span>
                  </div>

                  <div class="pay-item actual-pay-item">
                    <span class="pay-label">实际支付金额</span>
                    <span class="pay-amount" v-text-format.number="paymentFee"></span>
                  </div>
                </div>
              </el-tooltip>
            </div>
            <div>
              <span class="actual-pay-label">实际支付</span>
              <span class="actual-pay-amount"
                ><span class="pay-symbol">￥</span>{{ Number(paymentFee).toFixed(2) }}</span
              >
            </div>
          </div>
        </div>

        <div class="content-right flex-1">
          <div>
            <div class="title-text">收款方式</div>
            <div
              class="payment-methods-block"
              :class="{
                'user-recharge-special-block': pay_activeId === '3',
                'payment-methods-block-style': pay_activeId !== '2',
              }"
            >
              <div class="flex flex-item-between methods-block-item-wrapper">
                <div
                  class="payment-methods-block-item"
                  :class="{
                    'payment-methods-block-item--active': pay_activeId === item.type,
                    'item-balance': item.type === '3',
                  }"
                  @click="paymentMethodsChange(item.type)"
                  v-for="(item, index) in cPaymentMethods"
                  :key="index"
                >
                  <div>{{ item.label }}</div>
                  <div style="width: 100%; text-align: center" v-if="ap_switch === 'ENABLE' && item.type === '2'">
                    微信/支付宝
                  </div>

                  <div
                    class="blance-block"
                    v-show="item.type === '3' && pay_activeId !== '3'"
                    v-text-format.number="balance"
                  ></div>

                  <div class="triangle" v-if="pay_activeId === item.type">
                    <svg-icon iconClass="check-mark"></svg-icon>
                  </div>

                  <div
                    class="pay-discount"
                    :class="{ 'pay-discount--active': pay_activeId === item.type }"
                    v-if="getDiscountFee != 0 && item.type === '3'"
                  >
                    <span class="discount-text">减<span v-text-format.number="getDiscountFee"></span></span>
                  </div>
                </div>
              </div>
              <div class="pay-methods-change-block">
                <div class="scan-pay" v-if="pay_activeId === '2' && ap_switch === 'ENABLE'">
                  <RadioGroup v-model="checkoutPayMethod" @on-change="changeCheckoutType">
                    <Radio :label="item.type" v-for="item in scanPayments" :value="item.type" :key="'scan' + item.type">
                      <svg-icon :name="item.type" size="40" />
                      <div class="pay-content">
                        <div class="pay-name">{{ item.name }}</div>
                        <div class="pay-desc">{{ item.desc }}</div>
                      </div>
                    </Radio>
                  </RadioGroup>
                </div>
                <div v-if="pay_activeId === '1'" class="cash-pay">
                  <RadioGroup v-model="offline_collection" @on-change="changePayType">
                    <Radio :label="item.id" v-for="item in OfflinePaymentMethod" :value="item.id" :key="item.id">
                      <img :src="item.imageUrl" />
                      <span>{{ item.pay_name }}</span>
                    </Radio>
                  </RadioGroup>
                </div>

                <div v-if="pay_activeId === '3'">
                  <div class="select-wallet" v-if="showWalletSelect">
                    <Select v-model="trade_uid" style="width: 220px" placeholder="请选择储值金额钱包">
                      <Option
                        v-for="wallet in walletList"
                        :key="wallet.uid"
                        :value="wallet.uid"
                        :label="wallet.real_name + '（¥' + wallet.balance + '）'"
                      >
                        <div class="wallet-info">
                          <span class="name">{{ wallet.real_name }}</span>
                          <span class="balance">（¥{{ wallet.balance }}）</span>
                        </div>
                      </Option>
                    </Select>
                  </div>
                  <div
                    class="user-recharge-pay"
                    :style="{ backgroundImage: `url('${isMyWallet ? mineWalletImg : familyWalletImg}')` }"
                  >
                    <span class="balance">
                      <span class="unit">¥</span>
                      <span class="money" v-text-format.money="getWalletBalance"></span>
                    </span>
                    <span class="cardholder">持卡人：{{ walletInfo.real_name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="title-text mt25">收款备注</div>
            <Input
              class="control_width"
              v-model="remark"
              maxlength="100"
              show-word-limit
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 4 }"
              placeholder="请输入内容"
            />
          </div>
        </div>
      </div>
      <div slot="footer">
        <div class="flex flex-item-align flex-item-between">
          <div class="active-tip">
            <span v-if="coupon_id && pay_activeId === '3'"
              >注：卡券仅限原价购买商品时使用，不与优惠和储值等一系列活动合并使用</span
            >
            <span v-else-if="+activity_id"
              >注：该订单参与了"购买赠券"活动，只有原价购买（不包含储值支付）时才能正常发放优惠券</span
            >
          </div>
          <div>
            <Button @click="cancel">取消</Button>
            <Button type="primary" :disabled="true" v-if="isInsufficientBalance && pay_activeId === '3'"
              >余额不足
            </Button>
            <Tooltip
              v-else-if="notRechargeBuy && pay_activeId === '3'"
              content="商品购买清单中包含不支持使用储值余额支付的商品"
              placement="top"
              max-width="400"
            >
              <Button type="primary" class="ml10" :disabled="true">无法支付</Button>
            </Tooltip>
            <Button v-else :loading="payLoading || blurLoading || calculateLoading" type="primary" @click="confirmPay"
              >{{ getBtnText }}
            </Button>
          </div>
        </div>
      </div>
    </Modal>

    <!--    &lt;!&ndash; 微信付款 &ndash;&gt;-->
    <!--    <wx-collection-code-->
    <!--      v-model="codeVisible"-->
    <!--      :wxpayment_fee="wxpayment_fee"-->
    <!--      :codeUrl="codeUrl"-->
    <!--      :order_id="order_id"-->
    <!--      :trade_record_id="trade_record_id"-->
    <!--    ></wx-collection-code>-->

    <!-- 支付成功 -->
    <success-pay-dialog
      v-model="successVisible"
      :successData="successData"
      v-if="successVisible"
      :order_id="order_id"
      :isLocalClose="isLocalClose"
      @input="successClose"
    ></success-pay-dialog>

    <!-- 包含了【赠券商品】且检测到【实付金额】<【订单金额】 -->
    <pay-tip-modal v-model="payTipVisible" @success="orderPayShop"></pay-tip-modal>

    <!-- 无法送券提示窗 -->
    <confirm-tip-modal v-model="confirmTipVisible" :list="checkCouponList" @success="orderPayShop"></confirm-tip-modal>
    <pay-status-dialog
      v-model="payStatusVisible"
      :is-scan-pay="isScanPay"
      :payment_fee="wxpayment_fee"
      :codeUrl="codeUrl"
      :order_id="order_id"
      :trade_record_id="trade_record_id"
      :scanParams="scanParams"
      :closeModal="cancel"
      :isLocalClose="isLocalClose"
      @on-success="handlePaySuccess"
      @changeVisible="handlePayStatusVisibleChange"
    ></pay-status-dialog>
  </div>
</template>

<script>
import { $operator } from '@/libs/operation';
// 付款成功
// import SuccessPayDialog from './SuccessPayDialog.vue';
// 微信二维码
// 包含赠券逻辑提示弹窗
import PayTipModal from './PayTipModal.vue';
// 无法送券提示窗
import ConfirmTipModal from './ConfirmTipModal.vue';
import SuccessPayDialog from './pay-status/SuccessPayDialog.vue';
import PayStatusDialog from './pay-status-dialog.vue';
import { isEmpty } from '../../utils/helper';
// import WxCollectionCode from '../../view/trade/order/components/WxCollectionCode.vue';

export default {
  name: 'KPayDialog',
  mixins: [],
  components: {
    // WxCollectionCode,
    PayTipModal,
    ConfirmTipModal,
    SuccessPayDialog,
    PayStatusDialog,
  },
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '收款',
    },
    order_id: {
      type: String,
      default: '',
    },
    is_rst: {
      type: Boolean,
      default: false,
    },
    isLocalClose: {
      type: Boolean,
      default: false,
    },
    disabledDiscount: {
      type: Boolean,
      default: false,
    },
    completeAfterPay: {
      type: Boolean,
      default: false,
    },
    is_ry_order: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      // ************************************
      payLoading: false, // 确认支付的loading
      paymentMethods: [
        { label: '扫码支付', type: '2', sort: 3 },
        { label: '储值余额支付', type: '3', sort: 2 },
        { label: '线下收款', type: '1', sort: 1 },
      ], // 收款方式枚举

      OfflinePaymentMethod: [], // 线下支付方式枚举
      OfflinePaymentIcon: {
        CASH: require('@/assets/image/pay/collection/cash.png'), // 现金收款
        OFFLINE_WXCODE: require('@/assets/image/pay/collection/weixin.png'), // 微信收款
        OFFLINE_ALIPAY: require('@/assets/image/pay/collection/zhifubao.png'), // 支付宝收款
        OFFLINE_LAKALA: require('@/assets/image/pay/collection/lakala.png'), // 拉卡拉收款
        OFFLINE_UNION_PAY: require('@/assets/image/pay/collection/union.png'), // 银联收款
        OFFLINE_MEITUAN: require('@/assets/image/pay/collection/meituan.png'), // 美团团购
        OFFLINE_DOUYIN: require('@/assets/image/pay/collection/douyin.png'), // 抖音团购
        OFFLINE_YB: require('@/assets/image/pay/collection/yibao.png'), // 医保
        OFFLINE_HJK: require('@/assets/image/pay/collection/hjk.png'), // 好聚客
        OFFLINE_CLINIC_SK: require('@/assets/image/pay/collection/gonghu.png'), // 公户收款
        OFFLINE_JD: require('@/assets/image/pay/collection/jd.png'),
      },

      couponList: [], // 抵扣券枚举

      ori_payment: '', // 订单金额
      coupon_id: '', // 抵扣券
      extra_discount: null, // 优惠金额
      pay_activeId: '2', // 收款方式
      offline_collection: '', // 线下支付方式
      remark: '', // 备注,

      receivable_fee: '', // 应收金额
      balance: '', // 储值余额
      stored_total_fee: '', // 储值金额应当支付的金额
      is_recharge_buy: 'no', // 是否是充值购买
      promptCopy: ['储值余额不足', '商品购买清单中包含不支持使用储值余额支付的商品'],
      promptCopyText: '',
      isWXPayProgress: false, // 微信支付是否正在进行中

      // 微信支付
      codeUrl: '', // 二维码收款地址
      wxpayment_fee: '', // 微信扫码待收款金额,正常情况下与payment_fee数值一样
      codeVisible: false, // 收款码弹窗显示标识

      // 支付成功的弹窗数据
      successVisible: false, // 支付成功弹窗显示标识
      successData: {}, // 支付成功返回的数据

      // 包含赠券逻辑提示弹窗
      payTipVisible: false,
      confirmTipVisible: false,

      coupon_pay: '', // 1:支付优惠券支付
      activity_id: '', // 该订单是否包含赠券商品
      checkCouponList: [], // 检测商品
      walletList: [], // 储值钱包列表
      trade_uid: '', // 交易用户钱包id
      mineWalletImg: require('@/assets/image/pay/collection/user_recharge_mine.png'),
      familyWalletImg: require('@/assets/image/pay/collection/user_recharge_family.png'),
      ap_switch: '',
      trade_record_id: '',
      scanPayments: [
        { name: '扫码枪收款', desc: '使用扫码枪扫描客户付款码', type: 'scan-pay' },
        { name: '收款码收款', desc: '客户扫我的收款码向我付款', type: 'collection-pay' },
      ],
      checkoutPayMethod: 'scan-pay', // scan-pay 扫码枪支付   collection-pay   收款码支付
      payStatusVisible: false, // 支付状态弹窗显示标识
      isScanPay: false,
      scanParams: {},
      discountAmount: 0,
      activityList: [],
      paymentFee: '',
      formData: {
        order_id: '',
        sp_id: '',
        sp_item_id: '',
        sp_item_value: '',
      },
      discountList: [],
      canSubmit: true,
      calculateLoading: false,
      blurLoading: false,
      canCalculate: true,
    };
  },

  computed: {
    getBtnText() {
      return this.pay_activeId === '2' ? '下一步' : '确认支付';
    },

    getWalletBalance() {
      return this.walletInfo?.balance || 0;
    },
    walletInfo() {
      return this.walletList.find(item => item.uid === this.trade_uid);
    },
    isMyWallet() {
      return this.trade_uid && this.trade_uid === this.walletList[0].uid;
    },
    // 是否显示储值余额群组钱包选择
    showWalletSelect() {
      if (this.walletList.length < 2) {
        return false;
      }
      return true;
    },
    // 储值支付是否余额不足, true,表示余额不足
    isInsufficientBalance() {
      // return this.balanceAmount > Number(this.balance);
      return Number(this.paymentFee) > Number(this.getWalletBalance);
    },
    // 是否可以储值购买
    notRechargeBuy() {
      return this.is_recharge_buy == 'no';
    },
    getDiscountFee() {
      if (this.is_recharge_buy === 'no') {
        return 0;
      } else {
        return $operator.subtract(this.receivable_fee, this.stored_total_fee);
      }
    },
    cPaymentMethods() {
      console.log(this.paymentMethods, 'this.paymentMethods');
      return this.paymentMethods.filter(item => {
        if (this.is_ry_order === '1') {
          return item.type !== '3';
        } else {
          return item;
        }
      });
    },
    getActivityName() {
      return this.activityList.find(item => item.id === this.formData.sp_id)?.name || '';
    },
    discountRules() {
      if (!this.formData.sp_id) return [];
      return this.activityList.find(item => item.id === this.formData.sp_id)?.rule?.items || [];
    },
    getDiscountInfo() {
      // const halfPrice = $operator.divide(this.ori_payment, 2);
      const discountMap = {
        1: {
          placeholder: '请输入折扣',
          label: '折扣',
          precision: 1,
          max: 9.9,
          min: 0,
        },
        2: {
          placeholder: '请输入金额',
          label: '立减',
          precision: 2,
          max: this.ori_payment,
          min: 0,
        },
        3: {
          placeholder: '请输入金额',
          label: '一口价',
          precision: 2,
          max: this.ori_payment,
          min: 0,
        },
      };
      return discountMap[this.formData.sp_item_id];
    },
    showDiscount() {
      return this.formData.sp_id === '3';
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    successClose() {
      if (this.isLocalClose) {
        this.$emit('localClose');
      }
    },
    // 处理扫码支付成功
    handlePaySuccess(data) {
      // 扫码支付成功后立即触发localClose事件刷新看板
      this.successClose();
    },
    // 处理支付状态弹窗关闭
    handlePayStatusVisibleChange(visible) {
      if (!visible && this.isLocalClose) {
        // 支付状态弹窗关闭时也触发localClose事件刷新看板
        this.$emit('localClose');
      }
    },
    // 切换机器支付
    changeCheckoutType() {},
    changePayType(paytype) {
      console.log(paytype);
      if (paytype.toUpperCase() === 'OFFLINE_YB') {
        // todo 医保支付 清空抵扣券
        this.coupon_id = '';
        this.formData.sp_id = '';
        this.formData.sp_item_id = '';
        this.formData.sp_item_value = '';
        this.resetDiscount();
        // this.extra_discount = null;
      }
    },
    // 获取支付枚举
    getOrderPayOptions() {
      let params = {
        order_id: this.order_id,
      };
      this.$api.getOrderPayOptions(params).then(res => {
        this.OfflinePaymentMethod = [];
        for (let pay_key in res.offline_pay_desc) {
          this.OfflinePaymentMethod.push({
            id: pay_key,
            pay_name: res.offline_pay_desc[pay_key].desc,
            imageUrl: this.OfflinePaymentIcon[pay_key.toUpperCase()],
          });
        }
        this.offline_collection = this.OfflinePaymentMethod[0] && this.OfflinePaymentMethod[0].id;
      });
    },
    // 收款方式切换
    paymentMethodsChange(type) {
      console.log(type);
      this.pay_activeId = type;
      if (type === '1') {
        this.ori_payment = this.receivable_fee;
        if (this.offline_collection === 'offline_yb') {
          //todo   医保支付 清空抵扣券
          // this.extra_discount = null;
          this.coupon_id = '';
          this.formData.sp_id = '';
          this.formData.sp_item_id = '';
          this.formData.sp_item_value = '';
          this.resetDiscount();
        }
      }
      if (type === '2') {
        this.ori_payment = this.receivable_fee;
      }
      if (type === '3') {
        if (this.is_recharge_buy !== 'no') {
          this.ori_payment = this.stored_total_fee;
          if (Number(this.ori_payment) <= Number(this.discountAmount || 0)) {
            this.discountAmount = Number(this.ori_payment);
            // this.formData.sp_item_value = this.discountAmount;
          }
        }
      }
      this.paymentFee = $operator.subtract(this.ori_payment, this.discountAmount);
      if (this.formData.sp_item_id && this.formData.sp_item_value) {
        this.calculateAmount(true);
      }
    },

    paySubmit() {
      this.payTipVisible = true;
    },

    // 体验券，在进行扫码支付时校验
    checked() {
      const coupon_id = this.activityList.find(item => item.id === this.formData.sp_id).origin_id;
      let params = {
        coupon_id: coupon_id,
        order_id: this.order_id,
        coupon_type: '4',
      };
      this.payLoading = true;
      return this.$api.getOrderPayCheckorderusecoupon(params).finally(() => (this.payLoading = false));
    },

    // 确认支付
    async confirmPay() {
      console.log('[ this.loading ] >', this.loading);
      if (this.payLoading) return;

      if (this.paymentFee <= 0 && isEmpty(this.remark) && this.pay_activeId == '1') {
        this.$Message.error('支付金额为0时，请输入收款备注');
        return;
      }
      if (this.paymentFee <= 0 && this.pay_activeId != '1') {
        this.$Message.error('实际支付不能小于0.01元');
        return;
      }

      if (this.pay_activeId === '2') {
        // 扫码支付下，origin_type：4  需要校验是否可以下单
        let is_type4 = this.activityList.findIndex(item => item.id === this.formData.sp_id && item.origin_type === '4');
        if (is_type4 !== -1) {
          const checked_res = await this.checked();
          if (checked_res.status === '0') {
            this.$Message.error(checked_res.msg);
            return;
          }
        }
      }

      if (this.formData.sp_id && this.formData.sp_id !== '3') {
        console.log(this.activity_id, this.paymentFee, Number(this.ori_payment), 'this.activity_id');
        // 订单中包含了【赠券商品】且检测到【实付金额】<【订单金额】
        if (this.activity_id && this.paymentFee < Number(this.ori_payment)) {
          this.payTipVisible = true;
          return;
        }

        // 如果是按照原价支付，符合赠券的情况。但是订单中的赠券商品已经无法再正常送券
        if (this.activity_id && this.paymentFee == Number(this.ori_payment)) {
          this.getOrderPayCheckcoupon().then(res => {
            if (res) {
              this.confirmTipVisible = true;
            } else {
              this.orderPayShop();
            }
          });
          return;
        }
      }

      this.orderPayShop();
    },

    // 检测优惠券
    getOrderPayCheckcoupon() {
      let params = {
        order_id: this.order_id,
      };
      return this.$api.getOrderPayCheckcoupon(params).then(res => {
        this.checkCouponList = res;
        return new Promise(resolve => resolve(Boolean(this.checkCouponList.length)));
      });
    },

    // 提交预付款
    orderPayShop() {
      this.isWXPayProgress = false;
      const sales_promotion = {
        sp_id: this.formData.sp_id,
        sp_item_id: this.formData.sp_item_id,
        sp_item_value: this.formData.sp_item_value,
      };
      if (!this.formData.sp_item_value && this.formData.sp_item_value !== 0) {
        if (sales_promotion.sp_id === '3') {
          sales_promotion.sp_id = '';
        }
        sales_promotion.sp_item_id = '';
        sales_promotion.sp_item_value = '';
      }
      let params = {
        order_id: this.order_id,
        coupon_id: this.coupon_id, // 抵扣券
        remark: this.remark, // 备注
        pay_platform: '',
        // extra_discount: this.extra_discount, // 优惠
        use_coupon_activity: this.checkCouponList.length > 0 ? 0 : 1, // 1: 使用， 0： 不适用 （是否使用优惠券）
        sales_promotion,
      };

      // 如果需要支付后直接完成服务
      if (this.completeAfterPay) {
        params.complete_reserve = 1;
      }
      if (this.pay_activeId === '1') {
        params.pay_platform = this.offline_collection;
      } else if (this.pay_activeId === '2') {
        params.pay_platform = this.ap_switch === 'ENABLE' ? 'ap' : 'wxcode';
        if (this.ap_switch === 'ENABLE') {
          this.isScanPay = this.checkoutPayMethod === 'scan-pay';
          this.payStatusVisible = true;
          if (this.isScanPay) {
            this.wxpayment_fee = this.paymentFee;
            this.scanParams = params;
            // this.cancel();
            this.payLoading = false;
            return;
          }
        } else {
          params.pay_platform = 'wxcode';
        }
        this.isWXPayProgress = true;
      } else if (this.pay_activeId === '3') {
        params.pay_platform = 'user_recharge';
      }
      if (this.pay_activeId === '3') {
        params.trade_uid = this.trade_uid;
      }
      this.payLoading = true;
      this.$api
        .orderPayShop(params)
        .then(res => {
          this.codeUrl = res.pay_params.code_url;
          this.wxpayment_fee = res.payment_fee;
          if (this.pay_activeId == '1' || this.pay_activeId == '3') {
            // 现金支付
            this.orderPayConfirm();
          }
          if (this.pay_activeId == '2') {
            this.trade_record_id = res.trade_record_id;
            this.payStatusVisible = true;
          }
        })
        .finally(() => {
          this.payLoading = false;
        });
    },

    // 确认支付获取支付成功信息
    orderPayConfirm() {
      let params = {
        order_id: this.order_id,
      };
      this.$api.orderPayConfirm(params).then(res => {
        this.closeCollectionModal();
        this.successVisible = true;
        this.successData = res;
        // 支付成功后触发localClose事件
        this.successClose();
      });
    },

    // 关闭收款弹窗
    closeCollectionModal() {
      this.cancel();
    },

    // checked：1 默认选中
    handlerActivity(list = []) {
      this.activityList = list;
      this.activityList.some(item => {
        if (item.checked === '1') {
          this.formData.sp_id = item.id;
          // 此处调用，衔接切换活动的逻辑
          this.changeVoucher(this.formData.sp_id);
          return true;
        }
      });
    },

    // 获取订单信息
    getOrderInfo() {
      this.$api.getOrderInfo({ order_id: this.order_id }).then(res => {
        this.ori_payment = res.order.ori_payment;
        this.stored_total_fee = res.order.stored_total_fee;
        this.receivable_fee = res.order.ori_payment;
        this.balance = res.user_wallet.total_money;
        // this.activityList = res.sales_promotion;
        this.handlerActivity(res.sales_promotion);
        this.extra_discount =
          Number(res.order.extra_discount || 0) === 0 ? null : Number(res.order.extra_discount || 0);
        this.is_recharge_buy = res.order.is_recharge_buy;

        this.couponList = res.coupon;
        this.coupon_pay = res.coupon_pay;
        this.activity_id = res.activity_id;
        this.walletList = res.user_wallet_list;
        this.handleWalletSelect();
        if (this.is_recharge_buy === 'no') {
          this.promptCopyText = this.promptCopy[1];
        } else {
          this.promptCopyText = this.promptCopy[0];
        }
        this.ap_switch = res.ap_switch;
        if (this.ap_switch === 'ENABLE') {
          this.paymentMethods.find(item => item.type === '2').label = '扫码支付';
        } else {
          this.pay_activeId = '1';
          if (this.is_rst) {
            this.pay_activeId = '2';
          }
          this.paymentMethods.find(item => item.type === '2').label = '微信支付';
          this.paymentMethods = JSON.parse(JSON.stringify(this.paymentMethods)).toSorted((a, b) => a.sort - b.sort);
        }
        if (this.formData.sp_id) {
          this.calculateAmount(true);
        }
        this.resetDiscount();
      });
    },
    // 选择储值余额钱包逻辑
    handleWalletSelect() {
      // 自己余额足够
      if (Number(this.balance) >= Number(this.paymentFee) || this.walletList.length < 2) {
        this.trade_uid = this.walletList[0].uid;
        return;
      }
      // 选择储值钱包 余额balance最多的

      let max_balance = 0;
      let max_wallet_id = '';
      for (let i = 0; i < this.walletList.length; i++) {
        const wallet = this.walletList[i];
        if (Number(wallet.balance) > Number(max_balance) && Number(wallet.balance) >= Number(this.paymentFee)) {
          max_balance = wallet.balance;
          max_wallet_id = wallet.uid;
        }
      }
      if (max_wallet_id) {
        this.trade_uid = max_wallet_id;
      } else {
        this.trade_uid = this.walletList[0].uid;
      }
    },
    // ****弹窗事件****
    cancel() {
      this.$emit('changeVisible', false);
    },
    changeVisible(visible) {
      if (visible) {
        this.checkoutPayMethod = 'scan-pay';
        if (this.order_id) {
          this.getOrderPayOptions();
          this.getOrderInfo();
        }
        this.scanParams = {};
      } else {
        this.cancel();
        this.clearData();
      }
    },
    // 清空数据
    clearData() {
      this.coupon_pay = '';
      // this.remark = '';
      this.pay_activeId = '2';
      // this.extra_discount = null;
      this.offline_collection = this.OfflinePaymentMethod[0] && this.OfflinePaymentMethod[0].id;
      this.coupon_pay = '';
      this.remark = '';
      this.extra_discount = undefined;
      this.formData = {
        order_id: '',
        sp_id: '',
        sp_item_id: '',
        sp_item_value: undefined,
      };
      this.scanParams = {};
    },
    handleLimitValue() {
      this.canSubmit = true;
      this.blurLoading = true;
      setTimeout(() => {
        this.blurLoading = false;
      }, 300);
    },
    handleFocus(e) {
      e.target.select();
      this.canSubmit = false;
    },
    calculateAmount(v) {
      setTimeout(() => {
        if (!this.canCalculate) {
          this.discountList = [];
          this.discountAmount = 0;
          this.paymentFee = this.ori_payment;
          return;
        }
        const params = {
          ...this.formData,
          order_id: this.order_id,
        };
        if (this.pay_activeId === '1') {
          params.pay_platform = this.offline_collection;
        } else if (this.pay_activeId === '2') {
          params.pay_platform = this.ap_switch === 'ENABLE' ? 'ap' : 'wxcode';
        } else if (this.pay_activeId === '3') {
          params.pay_platform = 'user_recharge';
        }
        this.calculateLoading = true;
        this.$api
          .getRstOrderAmount(params)
          .then(res => {
            this.paymentFee = res.payment_fee;
            this.discountAmount = $operator.subtract(res.order_total_fee, res.payment_fee);
            this.discountList = res.discount_list;
          })
          .catch(() => {
            this.formData.sp_item_value = undefined;
            this.resetDiscount();
          })
          .finally(() => {
            this.calculateLoading = false;
          });
      }, 200);
    },
    changeDiscountType() {
      this.canCalculate = false;
      this.discountList = [];
      this.formData.sp_item_value = undefined;
      this.resetDiscount();
      this.focusDiscountInput();
      setTimeout(() => {
        this.canCalculate = true;
      }, 300);
    },
    resetDiscount() {
      this.discountAmount = 0;
      this.paymentFee = this.ori_payment;
    },
    focusDiscountInput() {
      this.$nextTick(() => {
        this.$refs.discountInput.select();
      });
    },
    changeVoucher(v) {
      console.log('changeVoucher', v);
      const item = this.activityList.find(item => item.id === v);
      console.log('=>(index.vue:927) item', item);
      this.discountList = [];
      if (!v) {
        this.formData.sp_item_id = '';
        this.formData.sp_item_value = undefined;
        this.resetDiscount();
        this.discountList = [];
        this.coupon_id = '';
        return;
      }
      this.formData.sp_id = v;
      if (v === '3') {
        this.formData.sp_item_id = '';
        this.formData.sp_item_value = undefined;
        this.coupon_id = '';
        this.resetDiscount();
        if (this.discountRules.length) {
          this.formData.sp_item_id = this.discountRules[0].id;
          this.focusDiscountInput();
        }
        return;
      }
      if (item.type === '30') {
        this.coupon_id = v;
      }
      // if (v === '1') {
      //   this.formData.sp_item_id = '';
      // }
      this.calculateAmount(true);
    },
  },
};
</script>
<style lang="less" scoped>
.content-right .scan-pay {
  padding: 16px;
  border-radius: 4px;
  background: #fcfdfe;
  border: 1px solid #f9f9f9;

  ::v-deep .ivu-radio-group {
    display: flex;
    flex-direction: column;

    .ivu-radio-group-item {
      width: 100%;
      max-width: 100%;
      padding: 12px;

      .pay-content {
        margin-left: 12px;
        font-size: 14px;

        .pay-name {
          font-weight: 500;
          line-height: 20px;
        }

        .pay-desc {
          font-size: 12px;
          line-height: 16px;
        }
      }
    }
  }
}

.content {
  height: 490px;

  .title-text {
    height: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 18px;
    margin-bottom: 10px;
  }

  .content-left {
    position: relative;
    height: 100% !important;

    .discount-type-box {
      ::v-deep .ivu-radio-group-item {
        max-width: unset;
        min-width: unset;
      }
    }

    &::after {
      content: ' ';
      background-image: url('~@/assets/image/pay/collection/lace_base.png');
      display: inline-block;
      width: 100%;
      height: 20px;
      bottom: -16px;
      position: absolute;
      background-repeat: round;
    }

    .padding-block {
      padding: 20px;
    }

    background: #fafbfc;
    box-shadow: 0px 2px 4px 0px rgba(236, 236, 236, 0.5);

    .order-moeny-block {
      width: 100%;
      padding: 20px 16px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #f3f3f5;

      .label-text {
        font-size: 14px;
        font-weight: 400;
        color: #bbbbbb;
        line-height: 18px;
      }

      .underline-money {
        font-size: 16px;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;
        text-decoration: line-through;
      }

      .pay-money {
        font-size: 22px;
        font-weight: 500;
        color: #333333;
        line-height: 28px;
        margin-left: 12px;
      }
    }

    .reduction-money {
      font-size: 16px;
      font-weight: 400;
      color: #f74441;
      line-height: 18px;
      margin-right: 16px;
      text-align: right;
      margin-top: 4px;
    }

    // 实际支付
    .actual-payment-block {
      margin-right: 16px;
      border-top: 1px solid #f4f4f4;
      display: flex;
      align-items: center;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .actual-pay-label {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        margin-right: 8px;
      }

      .actual-pay-amount {
        font-size: 22px;
        font-weight: 500;
        color: #f74441;
        line-height: 32px;

        .pay-symbol {
          font-size: 16px;
        }
      }
    }
  }

  .content-right {
    overflow-y: auto;
    padding: 20px 20px 0px 20px;

    .user-recharge-special-block {
      border: none !important;
      background: #fff !important;
    }

    .payment-methods-block-style {
      min-height: 240px;
      //border: 1px solid #f3f3f3;
      //border-top: unset;
    }

    .payment-methods-block {
      position: relative;
      background: rgba(21, 91, 212, 0.01);
      border-radius: 4px;

      .pay-methods-change-block {
        height: inherit;
        margin-top: 8px;

        .cash-pay {
          padding: 26px 16px 10px 16px;
          border: 1px solid #f3f3f3;
          border-radius: 4px;

          img {
            width: 28px;
            height: 28px;
            margin-right: 8px;
          }
        }

        .user-recharge-pay {
          margin-top: 20px;
          height: 187px;

          background-repeat: no-repeat;
          background-size: cover;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;

          .balance {
            display: flex;
            align-items: center;

            .unit {
              font-size: 20px;
              font-weight: normal;
              color: #ffffff;
              line-height: 24px;
              font-size: 20px;
              margin-right: 5px;
            }

            .money {
              font-size: 40px;
              color: #ffffff;
              line-height: 48px;
            }
          }

          .cardholder {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.75);
            line-height: 18px;
            text-align: right;
            position: absolute;
            right: 16px;
            bottom: 16px;
          }
        }
      }

      .methods-block-item-wrapper {
        //padding: 0 8px;
        gap: 12px;
        .payment-methods-block-item {
          position: relative;
          cursor: pointer;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          //width: 110px;
          flex: 1;

          height: 54px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #dfe1e6;
          color: #666666;
          font-size: 14px;
          //margin-right: 12px;

          //&:last-child {
          //  margin-right: 0px;
          //}

          &:hover {
            border: 1px solid #1157e5;
            color: #1157e5;
          }

          .blance-block {
            position: relative;
            bottom: -4px;
            min-height: 23px;
            display: flex;
            justify-content: center;
            align-items: center;
            word-break: break-all;
            width: 100%;
            background: #f0f4f9;
            border-radius: 0px 0px 3px 3px;
            font-size: 12px;
            font-weight: 400;
            color: #afb6be;
            line-height: 16px;
          }

          .pay-discount {
            position: absolute;
            top: -20px;
            right: 0px;
            background: #c3cad7;
            padding: 1px 4px;
            height: 20px;
            border-radius: 4px 4px 0px 0px;
            color: #fff;
            font-size: 14px;
            font-weight: 400;

            .discount-text {
              display: flex;
              transform: scale(0.9);
            }
          }

          .pay-discount--active {
            background: #1157e5;
          }
        }

        .item-balance {
          display: flex;
          flex-direction: column !important;
          //justify-content: space-around !important;
        }

        .payment-methods-block-item--active {
          position: relative;
          border: 1px solid #1157e5;
          color: #1157e5;

          .triangle {
            width: 0;
            height: 0;
            border-bottom: 25px solid #1157e5;
            border-left: 25px solid transparent;
            position: absolute;
            right: 0px;
            bottom: 0px;

            .svg-icon {
              position: absolute;
              font-size: 14px;
              left: -15px;
              bottom: -25px;
              color: #fff;
            }
          }
        }
      }
    }
  }
}

.active-tip {
  color: red;
}

::v-deep .custom-number-input {
  //height: 57px;
  background: #ffffff;
  border-radius: 4px;
  position: relative;

  .ivu-input-number-input-wrap::before {
    content: '¥';
    font-size: 14px;
    position: absolute;
    left: 10px;
    top: -1px;
    color: #333333;
  }

  .ivu-input-number-input-wrap {
    //  display: flex;
    //  align-items: center;
    //  height: 100%;
    //  width: 100%;
    .ivu-input-number-input {
      padding-left: 26px;
      //    height: 100%;
      //    font-size: 18px;
    }
  }
}

.control_width {
  width: 100%;
}

.mt30 {
  margin-top: 30px;
}

.mt25 {
  margin-top: 25px;
}

.ml10 {
  margin-left: 10px;
}
</style>

<style scoped lang="less">
.discount-input-box {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e3e5eb;
  font-size: 16px;
  color: #333333;
  line-height: 22px;

  .discount-label-text {
    color: #bbb;
  }
}

.discount-tips {
  color: #fff;
  line-height: 18px;
  text-align: right;
}

:deep(.custom-number-input) {
  background: #ffffff;
  border-radius: 0px;

  &:focus {
    box-shadow: none;
  }

  box-shadow: none !important;

  .el-input__inner {
    line-height: 32px;
    font-size: 16px;
    border: none;
    text-align: right;
    border-radius: 0px !important;
    border-bottom: 1px solid transparent;
    padding-left: 8px;
    padding-right: 8px;

    &::placeholder {
      font-size: 14px;
      color: #bbbbbb;
    }

    &:hover {
      border-bottom: 1px solid #1157e5;
    }
  }
}

:deep(.discount-input-box-percent) {
  .el-input__inner {
    padding-right: 8px;
  }
}

.k-collect-dialog {
  ::v-deep .ivu-modal-body {
    padding: 10px;
    max-height: 500px;
    min-height: 520px;
    overflow-y: auto;
  }

  ::v-deep .ivu-modal-header {
    padding: 12px 20px;
    border-bottom: 1px solid #e8e8e8;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }
}

::v-deep .ivu-input-wrapper,
.ivu-select {
  max-width: unset;
}

::v-deep .ivu-select-selection,
.ivu-checkbox-inner {
  border: 1px solid #dcdee2;
}

.pay-tooltip-content {
  width: 216px;

  .pay-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    line-height: 18px;

    &:last-child {
      margin-bottom: 0;
    }

    .pay-label {
      color: rgba(255, 255, 255, 0.75);
    }

    .pay-value {
      color: #fff;
    }
  }

  .actual-pay-item {
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.rst-tip {
  color: red;
}

::v-deep .ivu-modal-body {
  padding: 10px;
  max-height: 500px;
  min-height: 520px;
  overflow-y: auto;
}

::v-deep .ivu-input-wrapper,
.ivu-select {
  max-width: unset;
}

::v-deep .ivu-select-selection,
.ivu-checkbox-inner {
  border: 1px solid #dcdee2;
}

::v-deep .content-right .ivu-radio-group {
  display: flex;
  flex-wrap: wrap;

  .ivu-radio-wrapper {
    margin-right: 4%;
    border-radius: 4px;

    &:nth-of-type(2n) {
      margin-right: 0px !important;
    }

    flex: 1;
    min-width: 48%;
    max-width: 48%;
    height: 40px;
    background: #fff;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    padding-left: 10px;
    margin-bottom: 10px;
  }

  ::v-deep .ivu-radio-wrapper-checked {
    background: rgba(17, 87, 229, 0.04) !important;
    color: #1157e5 !important;
  }
}

.select-wallet {
  display: flex;
  justify-content: flex-end;
}
</style>
