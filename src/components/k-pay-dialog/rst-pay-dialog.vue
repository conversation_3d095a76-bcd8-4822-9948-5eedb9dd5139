<template>
  <div>
    <Modal
      :value="visible"
      class="k-collect-dialog"
      title="收款"
      class-name="vertical-center-modal"
      :width="840"
      :closable="true"
      :mask-closable="false"
      @on-visible-change="changeVisible"
    >
      <div class="title-wrapper" slot="header">
        收款
        <span v-if="first_pay_order === '1'" class="sales-promotion-text">
          <Icon type="md-alert" :size="16" class="mr-8" color="#FFC107" />
          用户首次到店</span
        >
        <img
          v-if="isVip980"
          src="https://img-sn01.rsjxx.com/image/2025/0728/150234_20867.png"
          style="width: 63px; height: 22px; margin-left: 8px"
        />
      </div>
      <div class="content flex" v-if="visible">
        <div class="content-left flex-1">
          <div class="padding-block">
            <div>
              <div class="title-text">订单金额</div>
              <div class="order-money-block flex flex-item-align flex-item-between">
                <div class="label-text">应付金额</div>
                <div class="flex flex-item-align">
                  <span
                    class="pay-money"
                    :class="{ deleteLine: !isSamePayment }"
                    v-text-format.number="ori_payment"
                  ></span>
                  <!--  支付金额与原订单金额不一致时展示           -->
                  <span class="actual-money" v-if="!isSamePayment" v-text-format.number="actual_payment"></span>
                </div>
              </div>
            </div>

            <div class="mt-30">
              <div class="flex flex-item-align flex-item-between">
                <div class="title-text">优惠活动</div>
              </div>
              <Select
                :disabled="disabledDiscount"
                class="control_width"
                v-model="formData.sp_id"
                @on-change="changeVoucher"
                clearable
                placeholder="请选择优惠活动"
                size="large"
              >
                <Option
                  v-for="item in calcActivityList"
                  :value="item.id"
                  :key="item.id"
                  :disabled="pay_activeId === '3' && item.type === '30'"
                >
                  {{ item.name }}
                </Option>
              </Select>
            </div>
            <div class="discount-type-box mt-10 mb-10" v-if="discountRules.length > 0">
              <RadioGroup
                v-model="formData.sp_item_id"
                button-style="solid"
                type="button"
                @on-change="changeDiscountType"
              >
                <Radio v-for="item in discountRules" style="margin-bottom: 6px" :label="item.id" :key="item.id">
                  {{ item.name }}
                </Radio>
              </RadioGroup>
            </div>
            <div v-if="showDiscount" class="discount-input-box">
              <span class="discount-label-text flex-1">{{ getDiscountInfo.label }}</span>
              <span class="unit-text" v-if="formData.sp_item_id !== '1'">￥</span>
              <el-input-number
                :class="`control_width custom-number-input ${
                  formData.sp_item_id === '1' ? 'discount-input-box-percent' : ''
                }`"
                ref="discountInput"
                :max="Number(getDiscountInfo.max)"
                :min="Number(getDiscountInfo.min) || 0"
                :precision="getDiscountInfo.precision"
                :active-change="false"
                :placeholder="getDiscountInfo.placeholder"
                style="width: 88px"
                :controls="false"
                v-model="formData.sp_item_value"
                @change="calculateAmount"
                @focus="e => handleFocus(e)"
                @blur="handleLimitValue"
              />
              <span class="unit-text" v-if="formData.sp_item_id === '1'">折</span>
            </div>
            <div v-if="formData.sp_id" class="reduction-money">-<span v-text-format.number="discountAmount" /></div>
          </div>

          <div class="actual-payment-block">
            <div style="color: #999">
              <span>结算明细</span>
              <el-tooltip effect="dark" placement="top-start" max-width="240">
                <Icon type="ios-arrow-dropright" class="ml-2 cursor" />
                <div class="pay-tooltip-content" slot="content">
                  <div class="pay-item">
                    <span class="pay-label">订单金额</span>
                    <span class="pay-amount" v-text-format.number="ori_payment"></span>
                  </div>
                  <div class="pay-item" v-if="!isSamePayment">
                    <span class="pay-label">会员优惠</span>
                    <span> -<span class="pay-amount" v-text-format.number="vip_discount"></span> </span>
                  </div>
                  <div class="pay-item">
                    <span class="pay-label">优惠活动</span>
                    <span class="pay-amount">{{ getActivityName }}</span>
                  </div>
                  <div class="pay-item" v-for="(item, index) in discountList" :key="'discount' + index">
                    <span class="pay-label">{{ item.name }}</span>
                    <span> -<span class="pay-amount" v-text-format.number="item.discount_fee"></span> </span>
                  </div>

                  <div class="pay-item actual-pay-item">
                    <span class="pay-label">实际支付金额</span>
                    <span class="pay-amount" v-text-format.number="paymentFee"></span>
                  </div>
                </div>
              </el-tooltip>
            </div>
            <div>
              <span class="actual-pay-label">
                <el-tooltip effect="dark" placement="top-start" max-width="240">
                  <svg-icon class="cursor mr-4" name="tip"></svg-icon>
                  <div slot="content" class="discount-tips">注：实际支付金额不能低于订单应付金额的50%</div>
                </el-tooltip>
                实际支付</span
              >
              <span class="actual-pay-amount">
                <span class="pay-symbol">￥</span>
                {{ Number(paymentFee).toFixed(2) }}
              </span>
            </div>
          </div>
        </div>

        <div class="content-right hidden-scroll flex-1">
          <div>
            <div class="title-text">收款方式</div>
            <div
              class="payment-methods-block"
              :class="{
                'user-recharge-special-block': pay_activeId === '3',
                'payment-methods-block-style': pay_activeId !== '2',
              }"
            >
              <!-- 购买会员隐藏支付方式选择，仅支持扫码 -->
              <div v-if="is_ry_order !== '1'" class="flex flex-item-between methods-block-item-wrapper">
                <div
                  class="payment-methods-block-item"
                  :class="{
                    'payment-methods-block-item--active': pay_activeId === item.type,
                    'item-balance': item.type === '3',
                    'item-disabled': item.type === '3' && getActivityType === '30',
                  }"
                  @click="paymentMethodsChange(item.type)"
                  v-for="(item, index) in cPaymentMethods"
                  :key="index"
                  v-show="item.type != 3 || (item.type == 3 && canUseRecharge)"
                >
                  <div>{{ item.label }}</div>

                  <!-- <div
                    class="blance-block"
                    v-show="item.type === '3' && pay_activeId !== '3'"
                    v-text-format.number="balance"
                  ></div> -->

                  <div class="triangle" v-if="pay_activeId === item.type">
                    <svg-icon iconClass="check-mark"></svg-icon>
                  </div>

                  <div
                    class="pay-discount"
                    :class="{ 'pay-discount--active': pay_activeId === item.type }"
                    v-if="getDiscountFee != 0 && item.type === '3'"
                  >
                    <span class="discount-text">减<span v-text-format.number="getDiscountFee"></span></span>
                  </div>
                </div>
              </div>
              <div class="pay-methods-change-block">
                <!-- 线下 -->
                <div v-if="pay_activeId === '1'" class="cash-pay">
                  <RadioGroup v-model="offlinePayMethod" @on-change="changeOfflinePayMethod">
                    <Radio :label="item.id" v-for="item in offlinePayMethods" :value="item.id" :key="item.id">
                      <img :src="item.imageUrl" />
                      <span>{{ item.pay_name }}</span>
                    </Radio>
                  </RadioGroup>
                </div>

                <!-- 扫码 -->
                <div class="scan-pay" v-if="pay_activeId === '2' && ap_switch === 'ENABLE'">
                  <div class="scan-methods-wrapper">
                    <div
                      class="scan-method-card"
                      :class="{ active: checkoutPayMethod === item.type }"
                      v-for="item in scanPayments"
                      :key="item.type"
                      @click="checkoutPayMethod = item.type"
                    >
                      <div class="scan-method-title" :class="{ active: checkoutPayMethod === item.type }">
                        {{ item.name }}
                      </div>
                      <div class="scan-method-img">
                        <img :src="item.type === 'scan-pay' ? payeeCode1 : payeeCode2" />
                      </div>
                      <div class="scan-method-desc">
                        <div v-if="item.type == 'scan-pay'" class="scan-method-desc-txt">
                          <span>用户出示</span>

                          <div class="img-wx"></div>
                          <!-- <img
                            :src="
                              checkoutPayMethod === item.type
                                ? OfflinePaymentIcon.OFFLINE_WXCODE
                                : OfflinePaymentIcon.OFFLINE_WXCODE_G
                            "
                            alt=""
                          /> -->
                          <span>或</span>
                          <div class="img-zhifubao"></div>
                          <!-- <img
                            :src="
                              checkoutPayMethod === item.type
                                ? OfflinePaymentIcon.OFFLINE_ALIPAY
                                : OfflinePaymentIcon.OFFLINE_ALIPAY_G
                            "
                            alt=""
                          /> -->
                          <span>的收款码</span>
                        </div>
                        <div v-if="item.type == 'collection-pay'" class="scan-method-desc-txt">
                          <span>用户使用</span>

                          <div class="img-wx"></div>
                          <span>或</span>
                          <div class="img-zhifubao"></div>

                          <span>的扫码功能</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 储值余额支付 -->
                <div class="stored-pay" v-if="pay_activeId === '3'">
                  <!-- <div v-if="!showStoredNewOrOld(userWallets)">
                    <div class="select-wallet" v-if="showWalletSelect">
                      <Select v-model="trade_uid" style="width: 220px" placeholder="请选择储值金额钱包">
                        <Option
                          v-for="wallet in walletList"
                          :key="wallet.uid"
                          :value="wallet.uid"
                          :label="wallet.real_name + '（¥' + wallet.balance + '）'"
                        >
                          <div class="wallet-info">
                            <span class="name">{{ wallet.real_name }}</span>
                            <span class="balance">（¥{{ wallet.balance }}）</span>
                          </div>
                        </Option>
                      </Select>
                    </div>
                    <div
                      class="user-recharge-pay"
                      :style="{ backgroundImage: `url('${isMyWallet ? mineWalletImg : familyWalletImg}')` }"
                    >
                      <span class="balance">
                        <span class="unit">¥</span>
                        <span class="money" v-text-format.money="getWalletBalance"></span>
                      </span>
                      <span class="cardholder">持卡人：{{ walletInfo?.real_name }}</span>
                    </div>
                  </div> -->

                  <div class="stored-methods-wrapper">
                    <div
                      v-for="item in userWallets"
                      :key="item.pay_type"
                      class="stored-method-card"
                      :class="getCardClasses(item)"
                      @click="onCheckStored(item)"
                    >
                      <!--                      && rechargeYztWalletInfo.user_role === 'SHARE'-->
                      <!-- @click="checkStored = item.pay_type" -->
                      <div class="stored-card-content" v-if="item.pay_type === 'user_recharge'">
                        <div class="real-name">
                          持卡人：{{ rechargeWalletInfo?.real_name }}
                          <img
                            v-if="item.wallet_list.length > 1"
                            src="https://img-sn01.rsjxx.com/image/2025/0615/132647_77087.png"
                            class="arrow-icon"
                          />
                          <el-select
                            v-if="item.wallet_list.length > 1"
                            v-model="rechargeUid"
                            size="mini"
                            class="wallet-select"
                            popper-class="wallet-popper"
                            @change="changeWallet"
                          >
                            <el-option
                              :value="wallet_item.uid"
                              v-for="wallet_item in item.wallet_list"
                              :key="wallet_item.uid"
                              :label="wallet_item.real_name"
                            >
                              <div class="flex">
                                <span style="font-weight: 400; font-size: 13px; color: #333333; margin-right: 10px">{{
                                  wallet_item.real_name
                                }}</span>
                                <span
                                  style="font-weight: 400; font-size: 12px; color: #999999"
                                  v-text-format.number="wallet_item.balance"
                                ></span>
                              </div>
                            </el-option>
                          </el-select>
                        </div>
                        <div class="money" v-text-format.money="rechargeWalletInfo.balance"></div>
                      </div>
                      <div class="stored-card-content" v-if="item.pay_type === 'user_recharge_yzt'">
                        <div class="real-name">
                          持卡人：{{ rechargeYztWalletInfo?.real_name }}
                          <img
                            v-if="item.wallet_list.length > 1"
                            src="https://img-sn01.rsjxx.com/image/2025/0615/132647_77087.png"
                            class="arrow-icon"
                          />
                          <el-select
                            v-if="item.wallet_list.length > 1"
                            v-model="rechargeYztUid"
                            size="mini"
                            class="wallet-select"
                            popper-class="wallet-popper"
                            @change="changeYztWallet"
                          >
                            <el-option
                              v-for="wallet_item in item.wallet_list"
                              :value="wallet_item.uid"
                              :key="wallet_item.uid"
                              :label="wallet_item.real_name"
                            >
                              <div class="flex">
                                <span style="font-weight: 400; font-size: 13px; color: #333333; margin-right: 10px">{{
                                  wallet_item.real_name
                                }}</span>
                                <span
                                  style="font-weight: 400; font-size: 12px; color: #999999"
                                  v-text-format.number="wallet_item.balance"
                                ></span>
                              </div>
                            </el-option>
                          </el-select>
                        </div>
                        <div class="money" v-text-format.money="rechargeYztWalletInfo.balance"></div>
                      </div>
                      <div class="stored-method-desc">
                        <div class="p-txt" :class="item.pay_type">{{ item.desc }}</div>
                        <div class="p-tip" v-if="item.pay_type === 'user_recharge'">
                          数字化升级前，用户的历史储值余额。请尽快消耗～
                        </div>
                      </div>

                      <div v-show="isActiveCard(item)" class="stored-triangle">
                        <svg-icon iconClass="check-mark"></svg-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <div class="title-text mb-12" style="margin-top: 20px">收款备注</div>
            <Input
              class="control_width"
              v-model="remark"
              maxlength="50"
              show-word-limit
              type="textarea"
              style="max-width: 100%"
              :autosize="{ minRows: 4, maxRows: 4 }"
              placeholder="请输入内容"
            />
          </div>
        </div>
      </div>
      <div slot="footer">
        <div class="flex flex-item-align flex-item-between">
          <div class="rst-tip">
            <span v-if="is_rst && is_ry_order == '1'">注：会员权益卡只能原价购买</span>
            <span v-if="!is_ry_order">
              <span v-if="getActivityType === '30' || (pay_activeId === '3' && isExistCoupon)">
                注：优惠券不可与储值余额同时使用
              </span>
              <span v-if="pay_activeId !== '3' && !formData.sp_id && isExistCoupon">注：当前用户有可用的优惠券</span>
            </span>
          </div>
          <div>
            <Button @click="handleCancel">取消</Button>
            <Button type="primary" v-if="isInsufficientBalance && pay_activeId === '3'" class="ml10" :disabled="true">
              余额不足
            </Button>
            <Button
              v-else
              type="primary"
              @click="handleNext"
              :loading="loading || blurLoading || calculateLoading || payLoading"
              :disabled="!canSubmit"
            >
              {{ pay_activeId === '2' ? '下一步' : '确认支付' }}
            </Button>
          </div>
        </div>
      </div>
    </Modal>

    <!-- 支付状态弹窗 -->
    <pay-status-dialog
      v-model="payStatusVisible"
      :is-scan-pay="isScanPay"
      :payment_fee="paymentFee"
      :code-url="codeUrl"
      :order_id="orderId"
      :trade_record_id="tradeRecordId"
      :scan-params="scanParams"
      :close-modal="handleCancel"
      :isLocalClose="isLocalClose"
      @on-success="handlePaySuccess"
      @changeVisible="handlePayStatusVisibleChange"
    />

    <!-- 支付成功弹窗 -->
    <success-pay-dialog
      v-model="successVisible"
      :success-data="successData"
      v-if="successVisible"
      :order_id="orderId"
      :isLocalClose="isLocalClose"
      @input="successClose"
    />
  </div>
</template>

<script>
import PayStatusDialog from './pay-status-dialog.vue';
import SuccessPayDialog from './pay-status/SuccessPayDialog.vue';
import { $operator } from '@/libs/operation';
import { isRstOpcClinic } from '@/libs/runtime';

export default {
  name: 'RstPayDialog',
  components: {
    PayStatusDialog,
    SuccessPayDialog,
  },
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
      default: '',
    },
    disabledDiscount: {
      type: Boolean,
      default: false,
    },
    is_rst: {
      type: Boolean,
      default: true,
    },
    is_ry_order: {
      type: String,
      default: '',
    },
    isLocalClose: {
      type: Boolean,
      default: false,
    },
    // 是否支持储值支付
    canUseRecharge: {
      type: Boolean,
      default: true,
    },
    completeAfterPay: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      // paymentMethods: [
      //   { name: '扫码枪收款', desc: '使用扫码枪扫描客户付款码', type: 'scan-pay', icon: 'scan-pay-icon' },
      //   { name: '收款码收款', desc: '客户扫我的收款码向我付款', type: 'collection-pay', icon: 'code-pay-icon' },
      //   { name: '其他收款', type: 'other-pay' },
      // ],
      paymentMethods: [
        { label: '扫码支付', type: '2', sort: 3 },
        { label: '储值余额支付', type: '3', sort: 2 },
        { label: '标记收款', type: '1', sort: 1 },
      ], // 收款方式枚举
      scanPayments: [
        { name: '扫码枪收款', desc: '使用扫码枪扫描客户付款码', type: 'scan-pay' },
        { name: '收款码收款', desc: '客户扫我的收款码向我付款', type: 'collection-pay' },
      ],

      checkoutPayMethod: 'scan-pay', // scan-pay 扫码枪支付   collection-pay   收款码支付

      checkStored: 'user_recharge', //user_recharge原储值  user_recharge_zyt云直通
      pay_activeId: '2', //2扫码 3储值 1线下
      ap_switch: 'ENABLE',
      balance: '', // 储值余额
      stored_total_fee: '', // 储值金额应当支付的金额
      is_recharge_buy: 'no', // 是否是充值购买
      walletList: [],
      trade_uid: '', // 交易用户钱包id
      mineWalletImg: require('@/assets/image/pay/collection/user_recharge_mine.png'),
      familyWalletImg: require('@/assets/image/pay/collection/user_recharge_family.png'),

      // 收款码-扫码枪
      payeeCode1: require('@/assets/image/pay/collection/payee_code_1.png'),
      // 收款码
      payeeCode2: require('@/assets/image/pay/collection/payee_code_2.png'),

      discountAmount: 0,
      remark: '',
      activityList: [], // 这里可以通过API获取抵扣券列表
      voucherAmount: 0, // 抵扣券金额
      // 支付相关状态
      payStatusVisible: false,
      successVisible: false,
      isScanPay: false,
      paymentFee: '',
      codeUrl: '',
      tradeRecordId: '',
      scanParams: {},
      successData: {},
      ori_payment: 0, // 订单原始金额
      actual_payment: 0, // 订单创建后的实际支付金额
      discountType: '1', // 1 折扣 2 立减 3 一口价
      amount: 0, // 订单金额
      extra_discount: undefined, // 额外优惠
      formData: {
        order_id: '',
        sp_id: '',
        sp_item_id: '',
        sp_item_value: '',
      },
      discountList: [],
      first_pay_order: '',
      canSubmit: true,
      calculateLoading: false,
      blurLoading: false,
      offlinePayMethods: [], // 线下支付方式列表
      offlinePayMethod: '', // 线下支付方式
      OfflinePaymentIcon: {
        CASH: require('@/assets/image/pay/collection/cash.png'), // 现金收款
        OFFLINE_WXCODE_G: require('@/assets/image/pay/collection/weixin_g.png'), // 微信收款
        OFFLINE_WXCODE: require('@/assets/image/pay/collection/weixin.png'), // 微信收款
        OFFLINE_ALIPAY_G: require('@/assets/image/pay/collection/zhifubao_g.png'), // 支付宝收款
        OFFLINE_ALIPAY: require('@/assets/image/pay/collection/zhifubao.png'), // 支付宝收款
        OFFLINE_LAKALA: require('@/assets/image/pay/collection/lakala.png'), // 拉卡拉收款
        OFFLINE_UNION_PAY: require('@/assets/image/pay/collection/union.png'), // 银联收款
        OFFLINE_MEITUAN: require('@/assets/image/pay/collection/meituan.png'), // 美团团购
        OFFLINE_DOUYIN: require('@/assets/image/pay/collection/douyin.png'), // 抖音团购
        OFFLINE_YB: require('@/assets/image/pay/collection/yibao.png'), // 医保
        OFFLINE_HJK: require('@/assets/image/pay/collection/hjk.png'), // 好聚客
        OFFLINE_CLINIC_SK: require('@/assets/image/pay/collection/gonghu.png'), // 公户收款
        OFFLINE_CI_PAY: require('@/assets/image/pay/collection/shangbao.png'), // 商保收款
      },
      payLoading: false,

      // 储值支付
      userWallets: [],

      // 选中的储值卡
      checkWallet: {},

      rechargeWalletInfo: {},
      rechargeYztWalletInfo: {},
      rechargeUid: '',
      rechargeYztUid: '',
      user_vip_info: {},
      isVip980: false,
    };
  },
  computed: {
    is_rst_opc() {
      return isRstOpcClinic();
    },
    calcActivityList() {
      return this.activityList.filter(item => {
        // 如果是抖音美团，活动全可以选
        if (this.offlinePayMethod === 'offline_meituan' || this.offlinePayMethod === 'offline_douyin') {
          return true;
        }
        if (this.is_rst_opc) return true;
        if (!this.is_rst_opc && !this.is_rst) return true;
        // 只有数字化普通诊所有筛选逻辑
        // 去掉开业活动  ID 2和3都是开业活动，type是 rsj 和 sjh
        if (2 === +item.id && 0 === +item.type) {
          return false;
        }
        if (3 === +item.id && 20 === +item.type) {
          return false;
        }
        return true;
      });
    },
    cPaymentMethods() {
      return this.paymentMethods;
    },
    getWalletBalance() {
      return this.walletInfo?.balance || 0;
    },
    walletInfo() {
      return this.walletList.find(item => item.uid === this.trade_uid) || {};
    },
    isMyWallet() {
      return this.trade_uid && this.trade_uid === this.walletList[0].uid;
    },
    // 是否显示储值余额群组钱包选择
    showWalletSelect() {
      if (this.walletList.length < 2) {
        return false;
      }
      return true;
    },
    // 储值支付是否余额不足, true,表示余额不足
    isInsufficientBalance() {
      let walletbalance =
        this.checkStored === 'user_recharge_yzt' ? this.rechargeYztWalletInfo.balance : this.rechargeWalletInfo.balance;
      return Number(this.paymentFee) > Number(walletbalance);
      // return this.balanceAmount > Number(this.balance);
      // return Number(this.paymentFee) > Number(this.getWalletBalance);
    },
    // 是否可以储值购买
    notRechargeBuy() {
      return this.is_recharge_buy == 'no';
    },
    getDiscountFee() {
      if (this.is_recharge_buy === 'no') {
        return 0;
      } else {
        return $operator.subtract(this.receivable_fee, this.stored_total_fee);
      }
    },

    // ==============
    getActivityName() {
      return this.activityList.find(item => item.id === this.formData.sp_id)?.name || '';
    },
    // 30为优惠券，其余为活动
    getActivityType() {
      return this.activityList.find(item => item.id === this.formData.sp_id)?.type || '';
    },
    discountRules() {
      if (!this.formData.sp_id) return [];
      return this.activityList.find(item => item.id === this.formData.sp_id)?.rule?.items || [];
    },
    // 实付金额
    getDiscountInfo() {
      let num = this.formData.sp_item_id == 2 ? 1.25 : 5;
      let halfPrice = $operator.divide(this.ori_payment, num);
      if (!this.isSamePayment) {
        halfPrice = $operator.divide(this.actual_payment, num);
      } else {
        halfPrice = $operator.divide(this.ori_payment, num);
      }

      const discountMap = {
        1: {
          placeholder: '请输入折扣',
          label: '折扣',
          precision: 1,
          max: 9.9,
          min: 2,
        },
        2: {
          placeholder: '请输入金额',
          label: '立减',
          precision: 2,
          max: halfPrice,
          min: 0,
        },
        3: {
          placeholder: '请输入金额',
          label: '一口价',
          precision: 2,
          max: this.ori_payment,
          min: halfPrice,
        },
      };
      return discountMap[this.formData.sp_item_id];
    },
    showDiscount() {
      return this.formData.sp_id === '2'; // 这里可以判断是否显示抵扣券输入框
    },
    getPayIcon() {
      return this.getCurrentPayMethod.icon;
    },
    getPayTip() {
      return this.getCurrentPayMethod.desc;
    },
    getCurrentPayMethod() {
      return this.paymentMethods.find(item => item.type === this.checkoutPayMethod);
    },
    vip_discount() {
      return Number(this.ori_payment) - Number(this.actual_payment);
    },
    // 订单原始金额 与 订单创建后生成的实际支付金额（会员价逻辑会导致不同） 是否是相同的支付价格
    isSamePayment() {
      return this.ori_payment === this.actual_payment;
    },
    // 当前订单是否存在优惠券
    isExistCoupon() {
      return this.activityList.some(item => item.type === '30');
    },
  },
  watch: {},
  methods: {
    isActiveCard(item) {
      console.log('🚀 ~ isActiveCard ~ item=>', item);
      let flag = false;

      if (this.userWallets.length > 1) {
        flag = this.checkStored == item.pay_type;
      }

      return flag;
    },

    successClose() {
      if (this.isLocalClose) {
        this.$emit('localClose');
      }
    },
    // 收款方式切换
    paymentMethodsChange(type) {
      console.log('🚀 ~ paymentMethodsChange ~ type=>', type);
      // 收款方式切换到线下收款时， 默认选择第一个收款方式
      if (type === '1') {
        this.offlinePayMethod = this.offlinePayMethods[0] && this.offlinePayMethods[0].id;
      } else {
        this.offlinePayMethod = '';
      }
      // 如果新的活动列表与选中的不满足，则重置活动及折扣价
      if (this.calcActivityList.every(item => this.formData.sp_id !== item.id)) {
        this.discountList = [];
        this.formData.sp_id = '';
        this.formData.sp_item_value = undefined;
        this.resetDiscount();
      }
      // 当前优惠活动使用了优惠券，不可使用储值余额支付
      if (type === '3' && this.getActivityType === '30') {
        return;
      }
      this.pay_activeId = type;
    },
    // 线下收款方式切换
    changeOfflinePayMethod() {
      this.$nextTick(() => {
        // 如果新的活动列表与选中的不满足，则重置活动及折扣价
        if (this.calcActivityList.every(item => this.formData.sp_id !== item.id)) {
          this.discountList = [];
          this.formData.sp_id = '';
          this.formData.sp_item_value = undefined;
          this.resetDiscount();
        }
      });
    },

    // 切换机器支付
    changeCheckoutType() {},

    handleLimitValue() {
      this.canSubmit = true;
      this.blurLoading = true;
      setTimeout(() => {
        this.blurLoading = false;
      }, 300);
    },
    handleFocus(e) {
      e.target.select();
      this.canSubmit = false;
    },
    calculateAmount(v) {
      if (!v) {
        this.discountList = [];
        this.discountAmount = 0;
        this.paymentFee = this.isSamePayment ? this.ori_payment : this.actual_payment;
        return;
      }
      const params = {
        ...this.formData,
        order_id: this.orderId,
      };
      this.calculateLoading = true;
      this.$api
        .getRstOrderAmount(params)
        .then(res => {
          this.paymentFee = res.payment_fee;
          if (!this.isSamePayment) {
            this.discountAmount = $operator.subtract(res.order_payment_fee, res.payment_fee);
          } else {
            this.discountAmount = $operator.subtract(res.order_total_fee, res.payment_fee);
          }

          this.discountList = res.discount_list;
        })
        .catch(() => {
          this.formData.sp_item_value = undefined;
        })
        .finally(() => {
          this.calculateLoading = false;
        });
    },
    changeDiscountType() {
      this.discountList = [];
      this.formData.sp_item_value = undefined;
      this.resetDiscount();
      this.focusDiscountInput();
    },
    resetDiscount() {
      this.discountAmount = 0;
      this.paymentFee = this.isSamePayment ? this.ori_payment : this.actual_payment;
    },
    changeVoucher(v) {
      console.log('%c=>(rst-pay-dialog.vue:297) v', 'color: #ECA233;font-size: 16px;', v);
      this.discountList = [];
      if (!v) {
        this.formData.sp_item_id = '';
        this.formData.sp_item_value = undefined;
        this.resetDiscount();
        this.discountList = [];
        return;
      }
      this.formData.sp_id = v;
      if (v === '2') {
        this.formData.sp_item_id = '';
        this.formData.sp_item_value = undefined;
        this.resetDiscount();
        if (this.discountRules.length) {
          this.formData.sp_item_id = this.discountRules[0].id;
          this.focusDiscountInput();
        }
        return;
      }
      if (v === '1') {
        this.formData.sp_item_id = '';
      }
      this.calculateAmount(true);
    },
    focusDiscountInput() {
      this.$nextTick(() => {
        console.log(this.$refs.discountInput);
        this.$refs.discountInput.select();
      });
    },
    changeVisible(visible) {
      console.log('🚀 ~ changeVisible ~ visible=>', visible);
      if (visible) {
        this.pay_activeId = '2';
        this.checkoutPayMethod = 'scan-pay';
        this.getOfflinePayMethods();
        if (this.orderId) {
          this.getOrderInfo();
        }
        this.scanParams = {};
      } else {
        this.cancel();
        this.clearData();
      }
    },

    getOfflinePayMethods() {
      this.$api
        .getOrderPayOptions({
          order_id: this.orderId,
        })
        .then(res => {
          console.log('%c=>(rst-pay-dialog.vue:422) res', 'color: #ECA233;font-size: 16px;', res);
          this.offlinePayMethods = [];
          for (let pay_key in res.offline_pay_desc) {
            this.offlinePayMethods.push({
              id: pay_key,
              pay_name: res.offline_pay_desc[pay_key].desc,
              imageUrl: this.OfflinePaymentIcon[pay_key.toUpperCase()],
            });
          }
        });
    },
    cancel() {
      if (this.visible) {
        this.$emit('changeVisible', false);
      }
    },
    // 选择储值余额钱包逻辑
    handleWalletSelect() {
      // 自己余额足够
      if (Number(this.balance) >= Number(this.paymentFee) || this.walletList.length < 2) {
        this.trade_uid = this.walletList[0].uid;
        return;
      }
      // 选择储值钱包 余额balance最多的

      let max_balance = 0;
      let max_wallet_id = '';
      for (let i = 0; i < this.walletList.length; i++) {
        const wallet = this.walletList[i];
        if (Number(wallet.balance) > Number(max_balance) && Number(wallet.balance) >= Number(this.paymentFee)) {
          max_balance = wallet.balance;
          max_wallet_id = wallet.uid;
        }
      }
      if (max_wallet_id) {
        this.trade_uid = max_wallet_id;
      } else {
        this.trade_uid = this.walletList[0].uid;
      }
    },

    //选择不同储值卡
    onCheckStored(item) {
      this.checkStored = item.pay_type;
      this.checkWallet = item;
    },
    getCardClasses(item) {
      const isYzt = item.pay_type === 'user_recharge_yzt';
      const isRecharge = item.pay_type === 'user_recharge';
      const role = isYzt ? this.rechargeYztWalletInfo.user_role : this.rechargeWalletInfo.user_role;
      return {
        active: this.isActiveCard(item),
        new: isYzt && role === 'SELF' && this.userWallets.length > 1,
        single_new: isYzt && role === 'SELF' && this.userWallets.length === 1,
        new_share: isYzt && role === 'SHARER' && this.userWallets.length > 1,
        single_new_share: isYzt && role === 'SHARER' && this.userWallets.length === 1,
        old: isRecharge && role === 'SELF',
        old_share: isRecharge && role === 'SHARER',
      };
    },

    // 获取订单信息
    getOrderInfo() {
      // this.orderId
      this.$api.getOrderInfo({ order_id: this.orderId }).then(res => {
        // this.ori_payment = res.order.ori_payment;
        this.ori_payment = res.order.total_fee;
        this.actual_payment = res.order.payment_fee;
        this.activityList = res.sales_promotion;
        console.log(' this.activityList: ',  this.activityList);
        this.first_pay_order = res.first_pay_order;
        this.walletList = res.user_wallet_list;
        this.user_vip_info = res.user_vip_info;
        this.isVip980 = res.user_vip_info?.some(item => item.user_type === '1');

        let user_wallets = [];
        if (res.wallets?.recharge?.length > 0) {
          let obj = {
            pay_type: 'user_recharge',
            key: 'recharge',
            desc: '原门店储值余额',
            wallet_list: res.wallets?.recharge,
          };
          user_wallets.push(obj);
          this.rechargeWalletInfo = res.wallets?.recharge[0];
        }
        if (res.wallets?.recharge_yzt?.length > 0) {
          let obj = {
            pay_type: 'user_recharge_yzt',
            key: 'recharge_yzt',
            desc: '云储值余额',
            wallet_list: res.wallets?.recharge_yzt,
          };
          user_wallets.push(obj);
          this.rechargeYztWalletInfo = res.wallets?.recharge_yzt[0];
        }
        this.userWallets = user_wallets;
        console.log('=>(rst-pay-dialog.vue:786) user_wallets', user_wallets);
        // if()
        // 储值钱包
        // this.userWallets = res.user_wallets;

        // 默认选中钱包
        if (this.userWallets.length !== 0) {
          this.checkStored = this.userWallets[0].pay_type;
          this.checkWallet = this.userWallets[0];
        }
        console.log('🚀 ~ this.$api.getOrderInfo ~ this.userWallets=>', this.userWallets);

        this.handleWalletSelect();
        this.balance = res.user_wallet.total_money;
        // 禁用优惠时，不默认选中活动
        if (!this.disabledDiscount) {
          this.formData.sp_id = this.activityList.find(item => item.type === '1')?.id || '';
        }
        if (this.formData.sp_id) {
          this.calculateAmount(true);
        }
        this.resetDiscount();
      });
    },
    handleCancel() {
      this.cancel();
      this.clearData();
    },

    async handleNext() {
      if (this.loading) return;
      this.loading = true;

      try {
        const sales_promotion = {
          sp_id: this.formData.sp_id,
          sp_item_id: this.formData.sp_item_id,
          sp_item_value: this.formData.sp_item_value,
        };
        if (!this.formData.sp_item_value) {
          if (sales_promotion.sp_id === '2') {
            sales_promotion.sp_id = '';
          }
          sales_promotion.sp_item_id = '';
          sales_promotion.sp_item_value = '';
        }
        const params = {
          order_id: this.orderId,
          remark: this.remark,
          pay_platform: 'ap',
          sales_promotion,
        };

        // 如果需要支付后直接完成服务
        if (this.completeAfterPay) {
          params.complete_reserve = 1;
        }

        if (this.pay_activeId === '1') {
          params.pay_platform = this.offlinePayMethod;
        } else if (this.pay_activeId === '2') {
          params.pay_platform = this.ap_switch === 'ENABLE' ? 'ap' : 'wxcode';
          if (this.ap_switch === 'ENABLE') {
            this.isScanPay = this.checkoutPayMethod === 'scan-pay';
            if (this.isScanPay) {
              this.wxpayment_fee = this.paymentFee;
              this.scanParams = params;
              this.payStatusVisible = true;
              // this.cancel();
              this.payLoading = false;
              return;
            }
          } else {
            params.pay_platform = 'wxcode';
          }
          this.isWXPayProgress = true;
        } else if (this.pay_activeId === '3') {
          console.log('checkStored=>', this.checkStored);

          // return;
          // 储值支付
          params.pay_platform = this.checkStored;
          params.trade_uid =
            this.checkStored === 'user_recharge_yzt' ? this.rechargeYztWalletInfo.uid : this.rechargeWalletInfo.uid;
        }

        // 调用支付接口
        this.payLoading = true;
        console.log('🚀 ~ handleNext ~ params=>', params);
        const res = await this.$api.orderPayShop(params);

        console.log(res, 'res')
        if (this.pay_activeId == '2') {
          this.payLoading = false;
          this.trade_record_id = res.trade_record_id;
          this.codeUrl = res.pay_params.code_url;
          this.wxpayment_fee = res.payment_fee;
          this.cancel();
          this.payStatusVisible = true;
        } else {
          this.orderPayConfirm();
        }
        // 处理支付结果
      } catch (error) {
        console.log(error, 'error')
        !error.errmsg && this.$Message.error('支付失败');
        // this.orderPayConfirm = false;
        this.payLoading = false;
      } finally {
        this.loading = false;
      }
    },
    // 确认支付获取支付成功信息
    orderPayConfirm() {
      let params = {
        order_id: this.orderId,
        uid: this.checkStored === 'user_recharge_yzt' ? this.rechargeYztWalletInfo.uid : this.rechargeWalletInfo.uid,
      };
      this.$api
        .orderPayConfirm(params)
        .then(res => {
          this.cancel();
          this.successVisible = true;
          this.successData = res;
          // 支付成功后立即触发localClose事件刷新看板
          this.successClose();
        })
        .finally(() => (this.payLoading = false));
    },
    handlePaySuccess(data) {
      this.successData = data;
      this.payStatusVisible = false;
      this.successVisible = true;
      // 扫码支付成功后立即触发localClose事件刷新看板
      this.successClose();
    },
    // 处理支付状态弹窗关闭
    handlePayStatusVisibleChange(visible) {
      if (!visible && this.isLocalClose) {
        // 支付状态弹窗关闭时也触发localClose事件刷新看板
        this.$emit('localClose');
      }
    },
    // 清空数据
    clearData() {
      this.coupon_pay = '';
      this.remark = '';
      this.extra_discount = undefined;
      this.loading = false;
      this.blurLoading = false;
      this.calculateLoading = false;
      this.payLoading = false;
      this.formData = {
        order_id: '',
        sp_id: '',
        sp_item_id: '',
        sp_item_value: undefined,
      };
      // this.scanParams = {}
    },

    changeWallet(val) {
      let userRechargeWallet = this.userWallets.find(item => item.pay_type === 'user_recharge').wallet_list;
      this.rechargeWalletInfo = userRechargeWallet.find(item => item.uid === val);
      this.$nextTick(() => {
        this.rechargeUid = '';
      });
    },
    changeYztWallet(val) {
      console.log('=>(rst-pay-dialog.vue:949) val', val);
      let userRechargeYztWallet = this.userWallets.find(item => item.pay_type === 'user_recharge_yzt').wallet_list;
      this.rechargeYztWalletInfo = userRechargeYztWallet.find(item => item.uid === val);
      this.$nextTick(() => {
        this.rechargeYztUid = '';
      });
    },
  },
};
</script>

<style lang="less" scoped>
.title-wrapper {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  display: flex;
  align-items: center;

  .sales-promotion-text {
    font-size: 12px;
    padding: 8px 12px;
    background: rgba(249, 156, 63, 0.1);
    border-radius: 3px;
    color: #f99c3f;
    line-height: 16px;
    margin-left: 16px;
  }
}

.content {
  height: 490px;

  .title-text {
    height: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 18px;
    margin-bottom: 16px;
  }

  .content-left {
    position: relative;
    height: 100% !important;

    &::after {
      content: ' ';
      background-image: url('~@/assets/image/pay/collection/lace_base.png');
      display: inline-block;
      width: 100%;
      height: 20px;
      bottom: -16px;
      position: absolute;
      background-repeat: round;
    }

    .padding-block {
      padding: 20px;
    }

    background: #fafbfc;
    box-shadow: 0px 2px 4px 0px rgba(236, 236, 236, 0.5);

    .order-money-block {
      width: 100%;
      padding: 20px 16px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #f3f3f5;

      .label-text {
        font-size: 14px;
        font-weight: 400;
        color: #bbbbbb;
        line-height: 18px;
      }

      .pay-money {
        font-size: 22px;
        font-weight: 500;
        color: #333333;
        line-height: 28px;
        margin-left: 12px;
      }
      .deleteLine {
        color: #ccc;
        text-decoration: line-through;
      }
      .actual-money {
        font-size: 22px;
        font-weight: 500;
        color: #333333;
        line-height: 28px;
        margin-left: 12px;
      }
    }

    .reduction-money {
      font-size: 16px;
      font-weight: 400;
      color: #f74441;
      line-height: 18px;
      margin-right: 16px;
      text-align: right;
      margin-top: 4px;
    }

    // 实际支付
    .actual-payment-block {
      margin-right: 16px;
      border-top: 1px solid #f4f4f4;
      display: flex;
      align-items: center;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .actual-pay-label {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        margin-right: 8px;
      }

      .actual-pay-amount {
        font-size: 22px;
        font-weight: 500;
        color: #f74441;
        line-height: 32px;

        .pay-symbol {
          font-size: 16px;
        }
      }
    }
  }

  .content-right {
    overflow-y: auto;
    padding: 20px;

    .payment-methods-block {
      position: relative;
      background: rgba(21, 91, 212, 0.01);
      border-radius: 4px;

      .methods-block-item-wrapper {
        .payment-methods-block-item {
          flex: 1;
          position: relative;
          cursor: pointer;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          width: 110px;
          height: 54px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #dfe1e6;
          color: #666666;
          font-size: 14px;
          margin-right: 12px;

          &:last-child {
            margin-right: 0px;
          }

          &:hover {
            border: 1px solid #1157e5;
            color: #1157e5;
          }

          .triangle {
            width: 0;
            height: 0;
            border-bottom: 25px solid #1157e5;
            border-left: 25px solid transparent;
            position: absolute;
            right: 0px;
            bottom: 0px;

            .svg-icon {
              position: absolute;
              font-size: 14px;
              left: -15px;
              bottom: -25px;
              color: #fff;
            }
          }
        }

        .payment-methods-block-item--active {
          position: relative;
          border: 1px solid #1157e5;
          color: #1157e5;
          background: #f3f7ff;
        }

        .item-disabled {
          color: #ccc !important;
          cursor: not-allowed !important;
          &:hover {
            border: 1px solid #dfe1e6 !important;
            color: #ccc !important;
          }
        }
      }
    }

    .payment-section {
      text-align: center;
      min-height: 226px;
      padding-top: 10px;
      .payment-wrapper {
        display: inline-block;
        background: #ffffff;
        border-radius: 4px;
        margin-top: 20px;

        .qr-code-img {
          width: 280px;
          height: 163px;
          margin-bottom: 2px;
        }

        .tip-text {
          color: #aaaaaa;
        }
      }
    }

    .offline-pay {
      padding: 26px 10px 10px;
      border: 1px solid #f3f3f3;
      border-radius: 4px;

      :deep(.ivu-radio-group) {
        display: flex;
        flex-wrap: wrap;

        .ivu-radio-wrapper {
          margin-right: 4%;
          border-radius: 4px;
          flex: 1;
          min-width: 48%;
          max-width: 48%;
          height: 40px;
          background: #fff;
          border: 1px solid #f0f0f0;
          display: flex;
          align-items: center;
          padding-left: 10px;
          margin-bottom: 10px;
          &:nth-child(2n) {
            margin-right: 0;
          }
        }
      }

      img {
        width: 28px;
        height: 28px;
        margin-right: 8px;
      }
    }
  }
}

.discount-input-box {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e3e5eb;
  font-size: 16px;
  color: #333333;
  line-height: 22px;

  .discount-label-text {
    color: #bbb;
  }
}

.discount-tips {
  color: #fff;
  line-height: 18px;
  text-align: right;
}

:deep(.custom-number-input) {
  background: #ffffff;
  border-radius: 0px;

  &:focus {
    box-shadow: none;
  }

  box-shadow: none !important;

  .el-input__inner {
    line-height: 32px;
    font-size: 16px;
    border: none;
    text-align: right;
    border-radius: 0px !important;
    border-bottom: 1px solid transparent;
    padding-left: 8px;
    padding-right: 8px;

    &::placeholder {
      font-size: 14px;
      color: #bbbbbb;
    }

    &:hover {
      border-bottom: 1px solid #1157e5;
    }
  }
}

:deep(.discount-input-box-percent) {
  .el-input__inner {
    padding-right: 8px;
  }
}

.k-collect-dialog {
  ::v-deep .ivu-modal-body {
    padding: 10px;
    max-height: 500px;
    min-height: 520px;
    overflow-y: auto;
  }

  ::v-deep .ivu-modal-header {
    padding: 12px 20px;
    border-bottom: 1px solid #e8e8e8;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }
}

::v-deep .ivu-input-wrapper,
.ivu-select {
  max-width: unset;
}

::v-deep .ivu-select-selection,
.ivu-checkbox-inner {
  border: 1px solid #dcdee2;
}

.pay-tooltip-content {
  width: 216px;

  .pay-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    line-height: 18px;

    &:last-child {
      margin-bottom: 0;
    }

    .pay-label {
      color: rgba(255, 255, 255, 0.75);
    }

    .pay-value {
      color: #fff;
    }
  }

  .actual-pay-item {
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
.rst-tip {
  color: red;
}

.pay-methods-change-block {
  height: inherit;
  margin-top: 8px;

  .cash-pay {
    padding: 26px 16px 10px 16px;
    border: 1px solid #f3f3f3;
    border-radius: 4px;

    img {
      width: 28px;
      height: 28px;
      margin-right: 8px;
    }
  }

  .user-recharge-pay {
    margin-top: 20px;
    height: 187px;

    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .balance {
      display: flex;
      align-items: center;

      .unit {
        font-size: 20px;
        font-weight: normal;
        color: #ffffff;
        line-height: 24px;
        font-size: 20px;
        margin-right: 5px;
      }

      .money {
        font-size: 40px;
        color: #ffffff;
        line-height: 48px;
      }
    }

    .cardholder {
      font-size: 13px;
      color: rgba(255, 255, 255, 0.75);
      line-height: 18px;
      text-align: right;
      position: absolute;
      right: 16px;
      bottom: 16px;
    }
  }
}
</style>

<style lang="less" scoped>
.content-right .scan-pay {
  padding: 16px;
  border-radius: 4px;
  background: #fcfdfe;
  border: 1px solid #f9f9f9;

  .scan-methods-wrapper {
    display: flex;
    gap: 12px;
  }

  .scan-method-card {
    width: 162px;
    height: 175px;
    background: #ffffff;
    border: 1.5px solid #e5e6eb;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 15px;
    font-weight: 600;
    flex: 1;
    color: #999999;
    cursor: pointer;
    padding: 16px 0;
    transition: border 0.2s, box-shadow 0.2s;

    .img-wx {
      background-image: url(../../assets/image/pay/collection/weixin_g.png);
      background-size: 100% 100%;
      width: 12px;
      height: 12px;
      margin: 0 2px;
    }
    .img-zhifubao {
      background-image: url(../../assets/image/pay/collection/zhifubao_g.png);
      background-size: 100% 100%;
      width: 12px;
      height: 12px;
      margin: 0 2px;
    }

    &:hover {
      .img-wx {
        background-image: url(../../assets/image/pay/collection/weixin_1.png);
      }

      .img-zhifubao {
        background-image: url(../../assets/image/pay/collection/zhifubao_1.png);
      }

      border: 1.5px solid #1157e5;
      box-shadow: 0 2px 8px rgba(17, 87, 229, 0.08);
      color: #1157e5;
    }

    &.active {
      .img-wx {
        background-image: url(../../assets/image/pay/collection/weixin_1.png);
      }

      .img-zhifubao {
        background-image: url(../../assets/image/pay/collection/zhifubao_1.png);
      }
      border: 1.5px solid #1157e5;
      box-shadow: 0 2px 8px rgba(17, 87, 229, 0.08);
      color: #1157e5;
    }

    .scan-method-title {
      // color: #333;

      &.active {
        color: #1157e5;
      }
    }

    .scan-method-img {
      width: 100%;
      height: 90px;
      margin: 12px 0 4px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .scan-method-desc {
      font-size: 11px;
      // color: #7b8190;
      text-align: center;
      .scan-method-desc-txt {
        display: flex;
        align-items: center;

        img {
          width: 12px;
          height: 12px;
          margin: 0 2px;
        }
      }
    }
  }

  ::v-deep .ivu-radio-group {
    display: flex;
    flex-direction: column;

    .ivu-radio-group-item {
      width: 100%;
      max-width: 100%;
      padding: 12px;

      .pay-content {
        margin-left: 12px;
        font-size: 14px;

        .pay-name {
          font-weight: 500;
          line-height: 20px;
        }

        .pay-desc {
          font-size: 12px;
          line-height: 16px;
        }
      }
    }
  }
}

.content {
  height: 490px;

  .title-text {
    height: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 18px;
    margin-bottom: 10px;
  }

  .content-left {
    position: relative;
    height: 100% !important;

    .discount-type-box {
      ::v-deep .ivu-radio-group-item {
        max-width: unset;
        min-width: unset;
      }
    }

    &::after {
      content: ' ';
      background-image: url('~@/assets/image/pay/collection/lace_base.png');
      display: inline-block;
      width: 100%;
      height: 20px;
      bottom: -16px;
      position: absolute;
      background-repeat: round;
    }

    .padding-block {
      padding: 20px;
    }

    background: #fafbfc;
    box-shadow: 0px 2px 4px 0px rgba(236, 236, 236, 0.5);

    .order-moeny-block {
      width: 100%;
      padding: 20px 16px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #f3f3f5;

      .label-text {
        font-size: 14px;
        font-weight: 400;
        color: #bbbbbb;
        line-height: 18px;
      }

      .underline-money {
        font-size: 16px;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 20px;
        text-decoration: line-through;
      }

      .pay-money {
        font-size: 22px;
        font-weight: 500;
        color: #333333;
        line-height: 28px;
        margin-left: 12px;
      }
    }

    .reduction-money {
      font-size: 16px;
      font-weight: 400;
      color: #f74441;
      line-height: 18px;
      margin-right: 16px;
      text-align: right;
      margin-top: 4px;
    }

    // 实际支付
    .actual-payment-block {
      margin-right: 16px;
      border-top: 1px solid #f4f4f4;
      display: flex;
      align-items: center;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .actual-pay-label {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        margin-right: 8px;
      }

      .actual-pay-amount {
        font-size: 22px;
        font-weight: 500;
        color: #f74441;
        line-height: 32px;

        .pay-symbol {
          font-size: 16px;
        }
      }
    }
  }

  .content-right {
    overflow-y: auto;
    padding: 20px 20px 0px 20px;

    .user-recharge-special-block {
      border: none !important;
      background: #fff !important;
    }

    .payment-methods-block-style {
      min-height: 240px;
      //border: 1px solid #f3f3f3;
      //border-top: unset;
    }

    .payment-methods-block {
      position: relative;
      background: rgba(21, 91, 212, 0.01);
      border-radius: 4px;

      .pay-methods-change-block {
        height: inherit;
        margin-top: 8px;

        .cash-pay {
          padding: 26px 16px 10px 16px;
          border: 1px solid #f3f3f3;
          border-radius: 4px;

          img {
            width: 28px;
            height: 28px;
            margin-right: 8px;
          }
        }

        .user-recharge-pay {
          margin-top: 20px;
          height: 187px;

          background-repeat: no-repeat;
          background-size: cover;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;

          .balance {
            display: flex;
            align-items: center;

            .unit {
              font-size: 20px;
              font-weight: normal;
              color: #ffffff;
              line-height: 24px;
              font-size: 20px;
              margin-right: 5px;
            }

            .money {
              font-size: 40px;
              color: #ffffff;
              line-height: 48px;
            }
          }

          .cardholder {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.75);
            line-height: 18px;
            text-align: right;
            position: absolute;
            right: 16px;
            bottom: 16px;
          }
        }
      }

      .methods-block-item-wrapper {
        //padding: 0 8px;

        .payment-methods-block-item {
          position: relative;
          cursor: pointer;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          width: 110px;
          height: 54px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #dfe1e6;
          color: #666666;
          font-size: 14px;
          margin-right: 12px;

          &:last-child {
            margin-right: 0px;
          }

          &:hover {
            border: 1px solid #1157e5;
            color: #1157e5;
          }

          .blance-block {
            position: relative;
            bottom: -4px;
            min-height: 23px;
            display: flex;
            justify-content: center;
            align-items: center;
            word-break: break-all;
            width: 100%;
            background: #f0f4f9;
            border-radius: 0px 0px 3px 3px;
            font-size: 12px;
            font-weight: 400;
            color: #afb6be;
            line-height: 16px;
          }

          .pay-discount {
            position: absolute;
            top: -20px;
            right: 0px;
            background: #c3cad7;
            padding: 1px 4px;
            height: 20px;
            border-radius: 4px 4px 0px 0px;
            color: #fff;
            font-size: 14px;
            font-weight: 400;

            .discount-text {
              display: flex;
              transform: scale(0.9);
            }
          }

          .pay-discount--active {
            background: #1157e5;
          }
        }

        .item-balance {
          display: flex;
          flex-direction: column !important;
          //justify-content: space-around !important;
        }

        .payment-methods-block-item--active {
          position: relative;
          border: 1px solid #1157e5;
          color: #1157e5;

          .triangle {
            width: 0;
            height: 0;
            border-bottom: 25px solid #1157e5;
            border-left: 25px solid transparent;
            position: absolute;
            right: 0px;
            bottom: 0px;

            .svg-icon {
              position: absolute;
              font-size: 14px;
              left: -15px;
              bottom: -25px;
              color: #fff;
            }
          }
        }
      }
    }
  }
}

.active-tip {
  color: red;
}

::v-deep .custom-number-input {
  //height: 57px;
  background: #ffffff;
  border-radius: 4px;
  position: relative;

  .ivu-input-number-input-wrap::before {
    content: '¥';
    font-size: 14px;
    position: absolute;
    left: 10px;
    top: -1px;
    color: #333333;
  }

  .ivu-input-number-input-wrap {
    //  display: flex;
    //  align-items: center;
    //  height: 100%;
    //  width: 100%;
    .ivu-input-number-input {
      padding-left: 26px;
      //    height: 100%;
      //    font-size: 18px;
    }
  }
}

.control_width {
  width: 100%;
}

.mt30 {
  margin-top: 30px;
}

.mt25 {
  margin-top: 25px;
}

.ml10 {
  margin-left: 10px;
}
</style>
<style scoped lang="less">
.discount-input-box {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e3e5eb;
  font-size: 16px;
  color: #333333;
  line-height: 22px;

  .discount-label-text {
    color: #bbb;
  }
}

.discount-tips {
  color: #fff;
  line-height: 18px;
  text-align: right;
}

:deep(.custom-number-input) {
  background: #ffffff;
  border-radius: 0px;

  &:focus {
    box-shadow: none;
  }

  box-shadow: none !important;

  .el-input__inner {
    line-height: 32px;
    font-size: 16px;
    border: none;
    text-align: right;
    border-radius: 0px !important;
    border-bottom: 1px solid transparent;
    padding-left: 8px;
    padding-right: 8px;

    &::placeholder {
      font-size: 14px;
      color: #bbbbbb;
    }

    &:hover {
      border-bottom: 1px solid #1157e5;
    }
  }
}

:deep(.discount-input-box-percent) {
  .el-input__inner {
    padding-right: 8px;
  }
}

.k-collect-dialog {
  ::v-deep .ivu-modal-body {
    padding: 10px;
    max-height: 500px;
    min-height: 520px;
    overflow-y: auto;
  }

  ::v-deep .ivu-modal-header {
    padding: 12px 20px;
    border-bottom: 1px solid #e8e8e8;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }
}

::v-deep .ivu-input-wrapper,
.ivu-select {
  max-width: unset;
}

::v-deep .ivu-select-selection,
.ivu-checkbox-inner {
  border: 1px solid #dcdee2;
}

.pay-tooltip-content {
  width: 216px;

  .pay-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    line-height: 18px;

    &:last-child {
      margin-bottom: 0;
    }

    .pay-label {
      color: rgba(255, 255, 255, 0.75);
    }

    .pay-value {
      color: #fff;
    }
  }

  .actual-pay-item {
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.rst-tip {
  color: red;
}

::v-deep .ivu-modal-body {
  padding: 10px;
  max-height: 500px;
  min-height: 520px;
  overflow-y: auto;
}

::v-deep .ivu-input-wrapper,
.ivu-select {
  max-width: unset;
}

::v-deep .ivu-select-selection,
.ivu-checkbox-inner {
  border: 1px solid #dcdee2;
}

::v-deep .content-right .ivu-radio-group {
  display: flex;
  flex-wrap: wrap;

  .ivu-radio-wrapper {
    margin-right: 4%;
    border-radius: 4px;

    &:nth-of-type(2n) {
      margin-right: 0px !important;
    }

    flex: 1;
    min-width: 48%;
    max-width: 48%;
    height: 40px;
    background: #fff;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    padding-left: 10px;
    margin-bottom: 10px;
  }

  ::v-deep .ivu-radio-wrapper-checked {
    background: rgba(17, 87, 229, 0.04) !important;
    color: #1157e5 !important;
  }
}

// 储值支付
.stored-pay {
  padding: 16px;
  border-radius: 4px;
  background: #fcfdfe;
  border: 1px solid #f9f9f9;
}

.stored-methods-wrapper {
  display: flex;
  gap: 14px;
  align-items: center;
  justify-content: center;
  .stored-method-card {
    flex: 1;
    // width: 168px;
    height: 175px;
    border-radius: 4px;
    border: 1px solid #e3e5eb;
    text-align: center;
    cursor: default;
    position: relative;
    &.active {
      border: 1.5px solid #1157e5;
      box-shadow: 0 2px 8px rgba(17, 87, 229, 0.08);
    }

    .stored-card-content {
      .real-name {
        position: absolute;
        top: 12px;
        right: 12px;
        color: #ffffff;
        font-size: 12px;
        .arrow-icon {
          width: 10px;
          height: 10px;
        }
      }
      .money {
        font-size: 32px;
        color: #ffffff;
      }
      .wallet-select {
        position: absolute;
        top: -12px;
        right: 0;
        opacity: 0;
      }
    }

    .stored-triangle {
      width: 0;
      height: 0;
      border-top: 25px solid #1157e5;
      border-right: 25px solid transparent;
      position: absolute;
      top: 0;
      left: 0;

      .svg-icon {
        position: absolute;
        font-size: 14px;
        top: -25px;
        color: #fff;
      }
    }

    .stored-card-tip {
      font-size: 15px;
      color: #ffffff;
    }
    .stored-method-desc {
      .p-txt {
        font-size: 15px;
      }
      .user_recharge_yzt {
        color: #333333;
      }
      .user_recharge_yzt {
        color: #ffffff;
      }

      .p-tip {
        font-size: 11px;
        padding: 0 2px;
        color: #999999;
      }
    }
  }

  .old {
    background-image: url('~@/assets/image/pay/collection/store_card_old.png');
    background-size: contain;
    background-repeat: no-repeat;
    .money {
      margin: 40px 0 20px 0;
    }
  }
  .old_share {
    background-image: url('~@/assets/image/pay/collection/store_share_card_old.png');
    background-size: contain;
    background-repeat: no-repeat;
    .money {
      margin: 40px 0 20px 0;
    }
  }

  .new {
    background-image: url('~@/assets/image/pay/collection/store_card_new.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .new_share {
    background-image: url('~@/assets/image/pay/collection/store_share_card_new.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .single_new {
    background-image: url('~@/assets/image/pay/collection/single_self_card_new.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .single_new_share {
    background-image: url('~@/assets/image/pay/collection/single_share_card_new.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  // .cloud_one {
  //   width: 100%;
  // }

  .active {
    border: 1px solid #1157e5;
  }
}

.select-wallet {
  display: flex;
  justify-content: flex-end;
}
</style>
<style lang="less">
.wallet-popper {
  z-index: 999999 !important;
}
</style>
