<template>
  <div class="pay-content flex flex-c flex-item-center">
    <p class="pay-money">待支付金额:<span class="money" v-text-format.number="payment_fee"></span></p>
    <div class="pay-qr">
      <svg-icon name="scan-code" size="200"></svg-icon>
    </div>
    <div class="scan-code">
      <span class="scan">使用扫码枪扫描客户付款码</span>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';

export default {
  name: 'CollectionCode',
  props: {
    payment_fee: {
      type: [String, Number],
      default: 0,
    },
    scanParams: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isStorePay: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [],
  data() {
    return {
      successData: {},
      scanner: null,
      trade_record_id: '',
      order_id: '',
      isScanning: false,
    };
  },
  methods: {
    initScanner() {
      this.scanner = this.$scanner({
        callback: this.scanOk,
      });
    },
    cancelScan() {
      this.scanner?.cancel();
      this.clearIntervalEvent();
    },
    scanOk(code) {
      if(this.isScanning) return;
      // 云闪付、微信支付、支付宝支付 收款码正则校验
      // const wepayReg = /^1[01]\d*$/;
      // const alipayReg = /^2[5-7]\d*$/;
      // const ysfpayReg = /^62\d*$/;
      const reg = /^[A-Za-z0-9]+$/;
      if (!code || !reg.test(code)) {
        this.$Message.error('请出示微信、支付宝或者云闪付付款码进行支付');
        return;
      }
      this.isScanning = true;
      const params = cloneDeep(this.scanParams);
      params.ap_pay_code_info = code;
      params.ap_pay_source = '1';
      const apiName = this.isStorePay ? 'getROrderpayparams' : 'orderPayShop';
      this.$api[apiName](params).then(res => {
        this.trade_record_id = res.trade_record_id;
        this.order_id = this.scanParams.order_id;
        this.waitPay();
      });
    },
    // ************微信付款***********
    //  清除定时器
    clearIntervalEvent() {
      clearInterval(this.timer);
      this.timer = null;
    },
    waitPay() {
      this.clearIntervalEvent();
      this.isScanning = false;
      this.scanner?.cancel();
      this.$emit('showScanPayLoading', true);
      this.timer = setInterval(() => {
        let params = {
          order_id: this.order_id,
          trade_record_id: this.trade_record_id,
        };
        const apiName = this.isStorePay ? 'getRPayState' : 'getPaystate';
        this.$api[apiName](params).then(res => {
          if (res.has_pay === '1') {
            this.clearIntervalEvent();
            // 确认支付获取支付成功信息
            this.orderPayConfirm();
          }
          if (res.pay_status === '3') {
            this.clearIntervalEvent();
            this.$emit('payFailed');
          }
        });
      }, 1000);
    },

    // 确认支付获取支付成功信息
    orderPayConfirm() {
      let params = {
        order_id: this.order_id,
      };
      const apiName = this.isStorePay ? 'getRPayConfirm' : 'orderPayConfirm';
      this.$api[apiName](params).then(res => {
        this.cancel();
        // this.successVisible = true;
        this.successData = res;
        this.$emit('payOk', this.successData);
      });
    },

    // ************微信付款***********

    // 收款弹窗关闭,清理定时器
    codeVisibleChange(val) {
      if (val) {
        this.waitPay();
      } else {
        this.clearIntervalEvent();
        this.cancel();
      }
    },

    cancel() {
      this.$emit('input', false);
    },
  },
};
</script>

<style lang="less" scoped>
.pay-content {
  margin: 0 auto;
  width: 240px;

  .wait-pay-money {
    font-size: 20px;
    line-height: 34px;
    color: #000;
    margin-bottom: 20px;

    .ori-price {
      font-size: 14px;
      color: #999999;
      line-height: 16px;
      text-decoration: line-through;
      margin-left: 8px;
    }
  }

  .pay-title {
    font-weight: 400;
    line-height: 24px;
    color: #666;
    font-size: 13px;
  }

  .pay-money {
    font-size: 16px;

    .money {
      color: rgb(233, 98, 8);
      font-weight: 600;
      font-size: 20px;
    }
  }

  .pay-qr {
    margin-top: 8px;
    width: 178px;
    height: 178px;
    display: flex;
    justify-content: center;
  }

  .scan {
    padding: 8px 12px;
    margin-top: 16px;
    font-size: 13px;
    background: rgba(17, 87, 229, 0.08);
    border-radius: 16px;
    color: #1157e5;
  }
}
</style>
