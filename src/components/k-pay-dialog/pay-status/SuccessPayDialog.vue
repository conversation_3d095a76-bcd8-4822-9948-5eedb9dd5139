<!--  -->
<template>
  <div class="pay-s-dia">
    <Modal
      :mask-closable="false"
      :value="value"
      @on-visible-change="successChange"
      title="收款成功"
      width="800"
      class-name="vertical-center-modal"
    >
      <div class="pay-s-content">
        <div class="pay-s-img">
          <i class="el-icon-success"></i>
        </div>
        <h3 class="pay-tip">收款成功</h3>
        <div class="pay-box">
          <div class="pay-item">
            <span class="label">已收金额</span>
            <span class="pay-val">￥{{ Number(successData.payment_fee || 0).toFixed(2) }}</span>
          </div>
          <div class="pay-item">
            <span class="label">支付方式</span> <span class="pay-val">{{ successData.pay_platform_text }}</span>
          </div>
          <div class="pay-item">
            <span class="label">支付时间</span>
            <span class="pay-val">{{ successData.pay_time | data_format('YYYY-MM-DD HH:mm') }}</span>
          </div>
          <div v-show="successData.remark" class="pay-item">
            <span class="label">备注</span> <span class="pay-val">{{ successData.remark || '-' }}</span>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer flex flex-item-center">
        <Button type="default" @click="back">关闭</Button>
        <Button v-if="order_id" type="default" @click="jumpToDetail">查看订单</Button>
        <Button v-if="!isStorePay &&isRstClinic" type="primary" @click="jumpToAccount">核算业绩</Button>
      </div>
    </Modal>
    <tip-modal v-model="tipVisible" :content="successData?.gift_info?.notice"></tip-modal>
  </div>
</template>

<script>
import { isRstClinic } from '@/libs/runtime';
import tipModal from '../tipModal.vue';
export default {
  name: 'successpay',
  components: {
    tipModal,
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    successData: {
      type: Object,
      default: () => {},
    },
    order_id: {
      type: String,
      default: '',
    },
    isStorePay: {
      type: Boolean,
      default: false,
    },
    isLocalClose: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tipVisible: false,
      isRstClinic: isRstClinic(),
    };
  },
  computed: {},
  watch: {
    // 报缺货弹窗
    value: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            let gift_info = this.successData.gift_info || {};
            if (gift_info.gift_status === '2' && gift_info.notice) {
              this.tipVisible = true;
            }
          });
        }
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    // 关闭支付成功弹窗
    emitClose() {
      this.$emit('input', false);
    },
    back() {
      this.successChange(false);
    },

    successChange(val) {
      if (!val) {
        this.emitClose();
        if (this.isLocalClose) {
          return;
        }
        if (this.isStorePay) {
          this.$router.push({
            path: '/trade/give/list',
          });
          return;
        }
        if (this.$route.query.from) {
          this.$router.back();
          return;
        }
        this.$router.push({
          path: '/trade/order/list',
        });
      }
    },

    jumpToAccount() {
      // trade/order/account?order_id=15675&source=SHOP
      this.$router.replace({
        path: '/trade/order/account',
        query: {
          order_id: this.order_id,
          source: 'SHOP',
        },
      });
      // const query = this.isStorePay ? { id: this.order_id } : { orderid: this.order_id, orderType: 'shop_order' };
    },

    jumpToDetail() {
      this.emitClose();
      if (!this.order_id) {
        this.$router.back();
        return;
      }
      const path = this.isStorePay ? '/trade/give/detail' : '/trade/order/detail';
      const query = this.isStorePay ? { id: this.order_id } : { orderid: this.order_id, orderType: 'shop_order' };
      this.$router.replace({
        path,
        query,
      });
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.pay-s-content {
  max-width: 60%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .pay-s-img {
    display: block;

    > i {
      font-size: 78px;
      color: #36a21f;
    }
  }

  .pay-tip {
    font-size: 36px;
    font-weight: 500;
    line-height: 61px;
    color: #666666;
    margin-bottom: 42px;
  }

  .pay-item {
    display: flex;
    margin-bottom: 12px;

    .label {
      margin-right: 20px;
      min-width: 80px;
      text-align: right;
      font-size: 15px;
      line-height: 26px;
      color: #999999;
    }

    .pay-val {
      font-size: 15px;
      line-height: 26px;
      color: #333333;
    }
  }
}
</style>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  padding: 10px;
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

:v-deep .ivu-modal-header-inner {
  font-size: 18px;
}

:v-deep .ivu-modal-header-inner:before {
  display: inline-block;
  content: '';
  width: 2px;
  height: 12px;
  background: #1157e5;
  margin-right: 6px;
}
</style>
