<template>
  <div>
    <Modal
      ref="customModal"
      :value="value"
      width="840px"
      :title="title"
      :footer-hide="false"
      :mask-closable="false"
      :lock-scroll="true"
      class-name="vertical-center-modal open-vip-modal"
      @on-visible-change="changeVisible"
    >
      <div class="content">
        <div class="info-box">
          <div class="avatar-box">
            <Avatar
              :src="row.avatar | imageStyle('B.200', 'http://static.rsjxx.com/rsjxx/2023/1220/151459_81411.png')"
              shape="square"
              icon="ios-person"
              class="avatar"
            />
          </div>
          <div class="info-wrapper">
            <div class="name">{{ row.real_name }}</div>
            <div class="mobile">{{ row.mobile || row.user_mobile }}</div>
          </div>
        </div>
        <div class="content-box" v-if="is_rst">
          <div class="content-left">
            <div class="card-name-box">
              <img class="name-img" src="https://img-sn01.rsjxx.com/image/2025/0616/152202_43895.png" />
            </div>
            <div class="card-content">
              <div class="card-style-box rst-card">
                <div class="card-text">榕粉权益卡 尊享三大好礼</div>
              </div>

              <div class="present-box">
                <div class="present-item" v-for="(item, index) in rstPresentList" :key="index">
                  <div class="present-title">
                    <img :src="item.title_img" alt="" class="title-img" />
                  </div>
                  <div class="present-desc">「{{ item.desc }}」</div>
                  <div class="present-service-box">
                    <div
                      class="service-item"
                      v-for="(service_item, service_index) in item.service_list"
                      :key="service_index"
                    >
                      <div class="service-left">
                        <img :src="service_item.icon" class="service-icon" alt="" />
                        <div class="service-name">{{ service_item.name }}</div>
                      </div>
                      <div class="service-right">
                        <img
                          src="https://img-sn01.rsjxx.com/image/2025/0616/160230_38108.png"
                          class="mark-icon"
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content-box" v-if="!is_rst">
          <div class="content-left">
            <div class="card-name-box">
              <img class="name-img" src="https://static.rsjxx.com/image/2025/0215/140525_81664.png" />
            </div>
            <div class="card-content">
              <div class="card-style-box">
                <div class="card-text">980身份权益卡 尊享下方六大权益</div>
              </div>

              <div class="interest-box">
                <div class="interest-item" v-for="(item, index) in interestList" :key="index">
                  <img class="interest-icon" :src="interestIconObj[index]" />
                  <div class="title">{{ item.title }}</div>
                  <div class="desc">{{ item.desc }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <Button class="cancel-btn" @click="closeModal">取消</Button>
        <div class="button-group-modal">
          <Button
            :class="['confirm-btn', is_rst && !isRstOpcClinic ? 'main-button-modal' : '']"
            :loading="confirmLoading"
            type="primary"
            @click="open"
          >
            立即支付
          </Button>
          <Dropdown
            @on-click="handleConfirmAction"
            placement="bottom-end"
            class="dropdown-button-modal"
            v-if="is_rst && !isRstOpcClinic"
          >
            <Button type="primary" class="dropdown-trigger-modal">
              <Icon type="ios-more"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="giveVip">赠送会员(无需支付)</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>
    </Modal>

    <!-- 支付 -->
    <template v-if="is_rst">
      <rst-pay-dialog
        v-model="rstPayVisible"
        :disabled-discount="disabledDiscount"
        :orderId="order_id"
        :is_rst="is_rst"
        :canUseRecharge="false"
        is_ry_order="1"
      ></rst-pay-dialog>
    </template>
    <template v-else>
      <k-pay-dialog
        v-model="payVisible"
        :order_id="order_id"
        :is_rst="is_rst"
        @changeVisible="changeVisible"
      ></k-pay-dialog>
    </template>

    <!-- 赠送会员二次确认弹窗 -->
    <Modal
      v-model="confirmModalVisible"
      :width="480"
      :mask-closable="false"
      :closable="true"
      title="赠送会员"
      class="give-vip-modal"
      class-name="vertical-center-modal"
    >
      <div class="give-vip-content">
        <div class="tip-section">
          <div class="tip-title">温馨提示</div>
          <div class="tip-text">本次将为您直接赠送会员资格，无需支付费用。确认后，您可享受理疗服务专享会员价。</div>
        </div>

        <div class="notice-section">
          <div class="notice-title">请注意</div>
          <div class="notice-list">
            <div class="notice-item">• 本次赠送会员不会生成支付订单；</div>
            <div class="notice-item">• 不会赠送会员专属优惠券；</div>
            <div class="notice-item">• 如您需要获得完整会员权益，建议通过付费方式开通。</div>
          </div>
        </div>
      </div>

      <div slot="footer">
        <Button @click="confirmModalVisible = false">取消</Button>
        <Button type="primary" :loading="giveVipLoading" @click="giveVipMembership">确认赠送</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import KPayDialog from '@/components/k-pay-dialog/index.vue';
import RstPayDialog from '@/components/k-pay-dialog/rst-pay-dialog.vue';
import { isRstClinic, isRstOpcClinic } from '@/libs/runtime';

export default {
  name: 'openVipModal',
  mixins: [],

  components: { RstPayDialog, KPayDialog },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '开通会员',
    },
    row: {
      type: Object,
      default: () => {},
    },
    interestList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      confirmLoading: false,
      interestIconObj: {
        0: 'https://static.rsjxx.com/image/2025/0215/141403_36450.png',
        1: 'https://static.rsjxx.com/image/2025/0215/143622_31659.png',
        2: 'https://static.rsjxx.com/image/2025/0215/143654_12577.png',
        3: 'https://static.rsjxx.com/image/2025/0215/143654_21446.png',
        4: 'https://static.rsjxx.com/image/2025/0215/143654_21446.png',
        5: 'https://static.rsjxx.com/image/2025/0215/143654_21446.png',
      },

      // 支付逻辑
      payVisible: false, // 支付弹窗显示的标识
      rstPayVisible: false, // 支付弹窗显示的标识
      order_id: '', // 订单id
      real_mobile: '',

      // 二次确认弹窗
      confirmModalVisible: false,
      giveVipLoading: false,
      rstPresentList: [
        // {
        //   title_img: 'https://img-sn01.rsjxx.com/image/2025/0616/164156_49497.png',
        //   desc: '消费越多回馈越多',
        //   service_list: [{ icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_85272.png', name: '消费积分' }],
        // },
      ],
      serviceBaseInfo: {
        title_img: 'https://img-sn01.rsjxx.com/image/2025/0616/164156_53219.png',
        desc: '健康消费更实惠',
        service_list: [
          { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_58505.png', name: '理疗服务专项会员价' },
          // { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_86755.png', name: '购买商品8折起' },
          // { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_25053.png', name: '门诊处方9折起' },
        ],
      },
      serviceInfo: {
        title_img: 'https://img-sn01.rsjxx.com/image/2025/0616/155452_80873.png',
        desc: '价值424元礼包免费领',
        service_list: [
          { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/160230_48132.png', name: '祛湿除寒1次' },
          { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_75431.png', name: '葫芦灸1次' },
          { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_81065.png', name: '局部推拿1次' },
        ],
      },
    };
  },

  computed: {
    is_rst() {
      return isRstClinic();
    },
    isRstOpcClinic() {
      return isRstOpcClinic();
    },
    disabledDiscount() {
      if (this.is_rst) {
        return true;
      }
      return false;
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    // 检测是否存在开通中的订单
    getUserGetrywaitpayorder(row) {
      let params = {
        uid: row.uid,
      };
      this.$api.getUserGetrywaitpayorder(params).then(res => {
        this.order_id = res.order.id;
        this.real_mobile = res.real_mobile;
      });
    },
    open() {
      if (!this.order_id) {
        this.orderCreate();
      } else {
        this.preCollection();
      }
    },
    preCollection() {
      if (+this.order_id) {
        if (this.is_rst) {
          this.rstPayVisible = true;
        } else {
          this.payVisible = true;
        }
        this.closeModal();
      }
    },
    async init() {
      this.order_id = '';
      this.rstPresentList = [];
      this.rstPresentList.push(this.serviceBaseInfo);
      if (this.is_rst && !isRstOpcClinic()) {
        this.serviceInfo.desc = '价值百元礼包免费领';
        const serviceNames = ['祛湿排寒一次', '局部艾灸一次', '颈肩推拿一次'];
        this.serviceInfo.service_list.map((item, index) => {
          item.name = serviceNames[index];
        });
      }
      this.rstPresentList.unshift(this.serviceInfo);
      await this.getUserGetrywaitpayorder(this.row);
    },
    // 创建榕益卡订单
    orderCreate: async function () {
      this.confirmLoading = true;
      let params = {
        is_ry_order: 1, // 标识榕益卡订单
        version: 2,
        mobile: this.row.real_mobile || this.real_mobile,
        name: this.row.real_name,
        uid: this.row.uid,
        listof: {}, // 写死空对象
        service_of: {}, // 写死空对象,
        need_address: 0, //
        consignee_address: {},
      };
      await this.$api
        .orderCreate(params)
        .then(res => {
          this.order_id = res.id;
          this.preCollection();
        })
        .finally(() => (this.confirmLoading = false));
    },

    changeVisible(visible) {
      if (visible) {
        this.init();
      } else {
        this.closeModal();
      }
    },

    clearData() {},

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    // 处理确认按钮dropdown菜单点击事件
    handleConfirmAction(action) {
      if (action === 'giveVip') {
        this.showGiveVipModal();
      }
    },

    // 显示赠送会员确认弹窗
    showGiveVipModal() {
      this.confirmModalVisible = true;
    },

    // 执行赠送会员操作
    async giveVipMembership() {
      this.giveVipLoading = true;
      try {
        // 调用免费开通会员的API
        await this.$api.giftVipMembership({
          uid: this.row.uid,
        });

        this.$Message.success('会员赠送成功');
        this.confirmModalVisible = false;
        this.closeModal();
        // 触发父组件刷新用户列表
        this.$emit('success');
      } catch (error) {
        // this.$Message.error(error.message || '赠送失败，请稍后重试');
      } finally {
        this.giveVipLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.open-vip-modal {
  ::v-deep .ivu-modal-body {
    max-height: 600px;
    min-height: 600px;
    overflow-y: auto;
  }

  ::v-deep .ivu-modal-header {
    display: none;
  }

  ::v-deep .ivu-modal-footer {
    border-top: none;
  }
}

.content {
  .info-box {
    display: flex;
    align-items: center;
    .avatar-box {
      min-width: 36px;
      height: 36px;
      background: #fff;
      border-radius: 50%;
      margin-right: 6px;
      .avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
      }
    }
    .info-wrapper {
      .name {
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
      }
      .mobile {
        font-weight: 400;
        font-size: 14px;
        color: #827f73;
        line-height: 20px;
      }
    }
  }

  .content-box {
    margin-top: 20px;
    display: flex;
    .content-left {
      flex: 1;
      background: rgba(255, 255, 255, 0.93);
      box-shadow: 0px 0px 15px 0px #c1cfe2;
      border-radius: 12px;
      .card-name-box {
        height: 70px;
        //background: linear-gradient(180deg, #d4e4f7 0%, #ffffff 100%);
        background: linear-gradient(360deg, #d4e4f7 0%, #ffffff 100%);
        border-radius: 12px 12px 0px 0px;
        display: flex;
        justify-content: center;
        align-items: center;
        .name-img {
          width: 306px;
          height: 35px;
        }
      }
      .card-content {
        position: relative;
        padding: 12px;
        background: #fff;
        border-radius: 12px;
        .card-style-box {
          height: 175px;
          background: url('https://img-sn-i01s-cdn.rsjxx.com/image/2025/0227/104612_64307.png') no-repeat;
          background-size: cover;
          position: relative;
          border-radius: 6px;
          .card-text {
            position: absolute;
            left: 24px;
            bottom: 6px;
            font-weight: 400;
            font-size: 16px;
            color: rgba(88, 107, 136, 0.7);
            line-height: 26px;
          }
        }
        .rst-card {
          background: url('https://img-sn01.rsjxx.com/image/2025/0616/163527_14023.png') no-repeat;
          background-size: cover;
          margin-bottom: 12px;
          height: 180px;
        }

        .interest-box {
          display: flex;
          flex-wrap: wrap;
          .interest-item {
            width: 32%;
            margin-top: 10px;
            margin-right: 2%;
            background: #f4f8fe;
            border-radius: 8px;
            padding: 8px;
            &:nth-child(3n) {
              margin-right: 0px;
            }
            .interest-icon {
              width: 27px;
              height: 27px;
            }
            .title {
              margin-top: 10px;
              font-weight: 500;
              font-size: 14px;
              color: #536b93;
              line-height: 20px;
            }
            .desc {
              margin-top: 4px;
              font-weight: 400;
              font-size: 12px;
              color: #849cc2;
              line-height: 18px;
            }
          }
        }
        .present-box {
          display: flex;
          gap: 12px;
          justify-content: space-between;
          .present-item {
            flex: 1;

            border-radius: 8px;
            background-color: #f4f8fe;
            .present-title {
              height: 36px;
              background: linear-gradient(270deg, #d1e0f6 0%, #e2ebf9 100%);
              border-radius: 8px 8px 0 0;
              text-align: center;
              padding-top: 8px;
              .title-img {
                height: 17px;
              }
            }
            .present-desc {
              font-weight: 400;
              font-size: 12px;
              color: #8fa3c4;
              padding: 12px 0;
              text-align: center;
            }
            .present-service-box {
              padding: 0 10px;
              .service-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 14px 0 14px;
                border-top: 1px dashed #ced9e9;
                &:first-child {
                  border-top: 0;
                  padding-top: 0;
                }
                .service-left {
                  display: flex;
                  //align-items: center;
                  .service-icon {
                    width: 16px;
                    height: 16px;
                    margin-right: 5px;
                  }
                  .service-name {
                    font-weight: 500;
                    font-size: 13px;
                    color: #536b93;
                  }
                }
                .service-right {
                  .mark-icon {
                    width: 10px;
                    height: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.cancel-btn {
  width: 104px;
  height: 38px;
  background: #f6faff;
  border-radius: 8px;
  border: 1px solid #b9c5da;
  font-weight: 400;
  font-size: 16px;
  color: #7689a7;
  line-height: 22px;
  text-align: center;
}
.confirm-btn {
  width: 104px;
  height: 38px;
  background: linear-gradient(270deg, #6686b5 0%, #83a4d5 53%, #6786b6 100%);
  border-radius: 8px;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  border: 1px solid transparent;
}

/* 组合按钮样式 */
.button-group-modal {
  display: inline-block;
  margin-left: 8px;

  .main-button-modal {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-right: none !important;
  }

  .dropdown-button-modal {
    .dropdown-trigger-modal {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
      padding: 0 8px !important;
      min-width: auto !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      background: linear-gradient(270deg, #6686b5 0%, #83a4d5 53%, #6786b6 100%);
      height: 38px;
      border-color: #6786b6;
      border-left: 1px solid rgba(255, 255, 255, 0.2) !important;
    }
  }
}

/* 赠送会员弹窗样式 */
.give-vip-modal {
  ::v-deep .ivu-modal-body {
    padding: 20px 24px;
  }

  ::v-deep .ivu-modal-footer {
    padding: 16px 24px 24px;
  }
}

.give-vip-content {
  .tip-section {
    margin-bottom: 16px;

    .tip-title {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 8px;
    }

    .tip-text {
      font-size: 14px;
      color: #666666;
      line-height: 1.4;
    }
  }

  .notice-section {
    .notice-title {
      font-size: 14px;
      font-weight: 500;
      color: #ff4d4f;
      margin-bottom: 8px;
    }

    .notice-list {
      .notice-item {
        font-size: 13px;
        color: #666666;
        line-height: 1.4;
        margin-bottom: 6px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
<style lang="less">
.open-vip-modal {
  .ivu-modal-content {
    overflow: hidden;
    background: url('https://static.rsjxx.com/image/2025/0306/180203_52299.png') no-repeat;
    //background: url('https://static.rsjxx.com/image/2025/0215/125212_68168.png') no-repeat;
    background-size: cover;
    border-radius: 12px;
  }
}
</style>
