<template>
  <div>
    <Form :label-width="112" ref="queryFormData" :label-colon="true" :model="queryFormData" :rules="ruleValidate">
      <!-- 预约到店时间-->
      <FormItem label="">
        <div slot="label" class="label">
          <div class="mark">*</div>
          <div>预约到店时间:</div>
        </div>
        <div class="flex flex-item-align">
          <time-poptip-v2
            :dates="dates"
            @change="changeTimeV2"
            :reserve_time="type == 2 ? queryFormData.reserve_time_flag_text : queryFormData.reserve_time"
            :date="queryFormData.reserve_date"
            :list="timeRangeList"
            :type="type"
            :physio_id="queryFormData.services[0]?.physio_id"
            :disabled="!uid"
          >
            <div style="margin-left: 0px; width: 520px" @click="clickTimePoptip" class="custom-mock-input select-box">
              <div class="time" v-if="queryFormData.reserve_date">
                <span>{{ queryFormData.reserve_date }}</span>
                <span v-if="type == 2" style="margin-left: 4px">{{ queryFormData.reserve_time_flag_text }}</span>
                <span v-else style="margin-left: 4px">{{ queryFormData.reserve_time }}</span>
              </div>
              <div class="time time-placeholder" v-else>请选择</div>
              <Icon class="close" @click="clearReserveTimeV2" size="18px" type="ios-close-circle" />
              <img class="time-icon" src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0108/171825_33871.png" />
            </div>
          </time-poptip-v2>
        </div>
      </FormItem>

      <!-- v2版本预约服务 -->
      <FormItem label="" v-if="type == 1" style="margin-bottom: 10px">
        <div slot="label" class="label">
          <div class="mark">*</div>
          <div>预约服务</div>
        </div>
        <div class="reserve-service-box" v-if="queryFormData.services[0]?.goods_service_id">
          <div class="reserve-service-left">
            <div class="service-info">
              <div class="service-name">{{ queryFormData.services[0]?.serve_name }}</div>
              <div class="service-minute">{{ queryFormData.services[0]?.duration }}分钟</div>
            </div>

            <div class="physical-info">
              <img class="avatar" :src="queryFormData.services[0]?.physical_avatar || default_avatar" />
              <div class="physical-name">{{ queryFormData.services[0]?.physical_name }}</div>
              <div class="physical-tag">{{ queryFormData.services[0]?.role_name }}</div>
              <div class="physical-level">{{ queryFormData.services[0]?.level_name }}</div>
            </div>
          </div>
          <div class="reserve-service-right">
            <!--            <div class="service-price" v-if="is_rst && isVip && queryFormData.deductible_flag !== '1'">-->
            <!--              ¥{{ Number(queryFormData.services[0]?.vip_price || 0).toFixed(2) }}-->
            <!--            </div>-->
            <!--            <div class="service-price" v-else>¥{{ Number(queryFormData.services[0]?.price || 0).toFixed(2) }}</div>-->
            <div class="service-price">¥{{ Number(queryFormData.services[0]?.price || 0).toFixed(2) }}</div>
            <img
              @click="resetReserve"
              class="close-img"
              src="https://static.rsjxx.com/image/2025/0612/140633_59151.png"
            />
          </div>
        </div>
        <div class="reserve-service-empty-box" @click="chooseService" v-else>
          <div class="empty-text">选择预约服务</div>
          <img class="empty-arrow" src="https://static.rsjxx.com/image/2025/0612/141916_52952.png" />
        </div>
      </FormItem>

      <!-- v2版预约医生 -->
      <FormItem label="" v-if="type == 2">
        <div slot="label" class="label">
          <div class="mark">*</div>
          <div>预约医生</div>
        </div>
        <div class="reserve-doctor-box" v-if="queryFormData.services[0]?.physio_id">
          <div class="reserve-doctor-left">
            <img class="avatar" :src="queryFormData.services[0]?.physical_avatar || default_avatar" />
            <div class="doctor-name">{{ queryFormData.services[0]?.physical_name }}</div>
            <div class="doctor-tag">医生</div>
          </div>
          <div class="reserve-doctor-right">
            <img
              @click="resetReserve"
              class="close-img"
              src="https://static.rsjxx.com/image/2025/0612/140633_59151.png"
            />
          </div>
        </div>
        <div v-else>
          <reserve-doctor-v2
            :disabled="!uid"
            :id="queryFormData.services[0]?.physio_id"
            :reserve_date="queryFormData.reserve_date"
            :reserve_time_flag_text="queryFormData.reserve_time_flag_text"
            @change="chooseDoctor($event, 0)"
          >
            <div class="reserve-doctor-empty-box" @click="clickReserveDoctor">
              <div class="empty-text">选择预约医生</div>
              <img class="empty-arrow" src="https://static.rsjxx.com/image/2025/0612/141916_52952.png" />
            </div>
          </reserve-doctor-v2>
        </div>
      </FormItem>

      <!-- 服务占用时 -->
      <FormItem label="" prop="name" v-if="type != '2'" style="margin-bottom: 0px">
        <div slot="label" class="label">
          <!--          <div class="mark">*</div>-->
          <div>服务占用时长:</div>
        </div>
        <div class="flex flex-item-align" style="padding-top: 2px">
          <div>{{ queryFormData.services[0]?.duration ? `${queryFormData.services[0]?.duration}分钟` : '-' }}</div>
          <div style="color: #999999" v-if="Number(queryFormData.services[0]?.prepare_duration || 0)">
            <span>（</span>
            <span>{{ queryFormData.services[0]?.service_duration }}分钟</span>
            <span>+{{ queryFormData.services[0]?.prepare_duration }}分钟准备</span>
            <span>）</span>
          </div>
        </div>
      </FormItem>

      <!-- 外部预约渠道, 理疗师，且rst,且服务绑定了外部才展示-->
      <FormItem
        v-if="type === '1' && is_rst && queryFormData.services[0]?.bind_sales_channel === '1'"
        label="外部预约渠道"
        prop="out_sales_channel"
        style="margin-bottom: 0px"
      >
        <div slot="label" class="label">
          <div class="mark">*</div>
          <div>
            <span>外部预约渠道</span>
            <Tooltip
              :content="oustSalesChannerTip"
              max-width="300"
              theme="dark"
              placement="top"
              style="position: relative; top: 1px"
            >
              <Icon type="md-alert" color="#FFAA00" size="18" style="cursor: pointer" />
            </Tooltip>
            <span>:</span>
          </div>
        </div>
        <RadioGroup
          style="position: relative; top: 2px"
          v-model="queryFormData.out_sales_channel"
          @on-change="changeOutSalesChannel"
        >
          <Radio
            v-show="isShowOutChannel(item) || item.id == '0'"
            v-for="item in out_sales_channel_desc"
            :label="item.id"
            :key="item.id"
          >
            {{ item.desc }}
          </Radio>
        </RadioGroup>
      </FormItem>

      <!-- 只做榕树堂的卡券抵扣 -->
      <FormItem label="" prop="name" v-if="isShowCardUseModel" style="margin-bottom: 0px">
        <div slot="label" class="label">
          <div>使用卡券:</div>
        </div>
        <div class="flex flex-item-align">
          <!-- 展示使用卡券，要么使用，要么不适用，'暂无卡券可抵扣' 的场景不应该出现 -->
          <!--          <div class="no-card-tip" v-if="can_use_card != '1'">暂无卡券可抵扣</div>-->
          <RadioGroup v-model="queryFormData.deductible_flag">
            <Radio :label="card_item.id" v-for="(card_item, card_index) in cardList" :key="'card' + card_index">
              <span>{{ card_item.label }}</span>
            </Radio>
          </RadioGroup>
        </div>
      </FormItem>
    </Form>

    <!-- 选择服务 -->
    <reserve-service-v2
      v-model="chooseServiceVisible"
      :reserve_date="queryFormData.reserve_date"
      :reserve_time="queryFormData.reserve_time"
      :uid="uid"
      :physio_id="row.physio_id"
      :isVip="isVip"
      @confirm="chooseServiceConfirm($event, 0)"
    ></reserve-service-v2>
  </div>
</template>

<script>
import { getPhysioName, isRstClinic } from '@/libs/runtime';
import moment from 'moment';
// import physicPoptip from './components/physicPoptip.vue';
// import servePoptip from './components/servePoptip.vue';
// import timePoptip from './components/timePoptip.vue';
// import doctorTimePoptip from './components/doctorTimePoptip.vue';

import timePoptipV2 from './components/timePoptipV2.vue';
import reserveServiceV2 from './components/reserveServiceV2.vue';
import reserveDoctorV2 from './components/reserveDoctorV2.vue';

export default {
  name: 'index',
  components: {
    // physicPoptip,
    // servePoptip,
    // timePoptip,
    // doctorTimePoptip,
    timePoptipV2,
    reserveServiceV2,
    reserveDoctorV2,
  },
  mixins: [],
  props: {
    value: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: '1',
    },
    uid: {
      type: String,
      default: '',
    },
    can_use: {
      type: String,
      default: '',
    },
    row: {
      type: Object,
      default: () => {},
    },
    options: {
      type: Object,
      default: () => ({
        out_sales_channel_desc: [],
      }),
    },

    isVip: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      disabledTime: {
        disabledDate: date => {
          let format_date = moment(date).format('YYYY-MM-DD');
          let isHas = this?.dates?.includes(format_date);
          return !isHas;
        },
      },
      oustSalesChannerTip: '当用户在美团/抖音上购买了服务，经门店员工创建预约单，此时外部预约渠道选择美团/抖音',
      queryFormData: {
        deductible_flag: '', // 选择了是否使用卡券抵扣绑定的值
        services: [{ goods_service_id: '', physio_id: '', physical_avatar: '', level_name: '', role_name: '' }],
        reserve_date: '',
        reserve_time: '',
        reserve_time_flag: '', // 医生班次
        reserve_time_flag_text: '', // 医生班次时间描述
        out_sales_channel: '', // 外部预约渠道
        duration: {
          hour: null,
          minute: null,
        },
      },
      ruleValidate: {},
      timeRangeList: [], // 理疗师时刻
      dates: [], // 日期
      physical_avatar: '',
      cardList: [
        { id: '1', label: '使用' },
        { id: '2', label: '不使用' },
      ], // 使用卡券
      can_use_card: '', // 是否展示使用卡券抵扣的标识
      default_avatar: 'https://static.rsjxx.com/image/2025/0610/174630_91091.png',
      chooseServiceVisible: false, // 选择预约服务
    };
  },
  computed: {
    /**
     * 是否展示对应的外部渠道
     * */
    isShowOutChannel() {
      return val => {
        let list = this.queryFormData.services[0]?.out_sales_channel_list || [];
        let is_Has = list?.some(item => item.channel === val.id);
        return is_Has;
      };
    },
    /**
     * @desc 是否展示使用卡券的内容
     * rst && 理疗师 && 如果选择的服务绑定了外部渠道， 外部渠道选项没有选择外部的渠道（即选择了否）&& 服务可以使用卡券抵扣
     * 选择了外部的渠道不可以与卡券抵扣同时使用，二者互斥
     * */
    isShowCardUseModel() {
      return this.is_rst && this.type != '2' && this.can_use_card === '1' && !+this.queryFormData.out_sales_channel;
    },
    is_rst() {
      return isRstClinic();
    },
    out_sales_channel_desc() {
      if (this.options.out_sales_channel_desc.length > 0) {
        return [{ id: '0', desc: '否' }, ...this.options.out_sales_channel_desc];
      }
      return [];
    },
    getPhysioName() {
      return getPhysioName();
    },
    // 获取对于的服务id
    getServiceId() {
      return index => {
        return this.queryFormData?.services[index]?.goods_service_id || '';
      };
    },
    // 获取对于的理疗师id
    getPhycalId() {
      return index => {
        return this.queryFormData?.services[index]?.physio_id || '';
      };
    },
    // 是否选择了到店人，rst选择服务要先选择到店人
    isChooseUid() {
      if (this.is_rst && !Number(this.uid || 0)) {
        return true;
      }
      return false;
    },
    // 是否选择了预约到店时间
    isChooseTime() {
      if (!this.queryFormData.reserve_date || !this.queryFormData.reserve_time) {
        return true;
      }
      return false;
    },
    // 是否可以选择医生的排班
    isCanChooseDoctorTime() {
      if (this.type == '2') {
        if (!Number(this.queryFormData.services[0]?.physio_id || 0)) {
          return true;
        }
        if (!this.queryFormData.reserve_date) {
          return true;
        }
        return false;
      }
      return false;
    },
  },
  watch: {
    value: {
      handler(val) {
        this.queryFormData = val;
        this.$nextTick(() => {
          if (val.reserve_date) {
            this.$set(this.queryFormData, 'reserve_date', this.$moment(val.reserve_date).format('YYYY-MM-DD'));
          }
        });
      },
      immediate: true,
    },
    queryFormData: {
      handler(val) {
        this.$emit('input', val);
      },
      deep: true,
    },
    can_use: {
      handler(val) {
        this.can_use_card = val;
      },
      immediate: true,
    },
    can_use_card(val) {
      if (val != '1') {
        this.queryFormData.deductible_flag = '';
      }
    },
  },
  created() {
    this.getReservev2ReserveTimerange();
  },
  mounted() {},
  methods: {
    clickReserveDoctor() {
      if (!this.uid) {
        this.$Message.error('请选择到店人');
      }
    },
    // 清除时刻
    clearReserveTime(event) {
      event.preventDefault();
      event.stopPropagation();
      this.queryFormData.reserve_time = '';
      this.resetReserve();
    },
    doctorChangeTime(item) {
      console.log('🚀 ~ doctorChangeTime ~ item: ', item);
      this.$set(this.queryFormData, 'reserve_time_flag', item.flag);
      this.$set(this.queryFormData, 'reserve_time_flag_text', `${item.st} - ${item.et}`);
      this.$set(this.queryFormData.duration, 'minute', Number(item.duration || 0) || null);
    },
    reserveDateChange(val) {
      if (this.type == '2') {
        if (!val || val != this.queryFormData.reserve_date) {
          this.queryFormData.reserve_time_flag = '';
        }
        this.queryFormData.reserve_date = val;
      } else {
        if (val != this.queryFormData.reserve_date) {
          this.resetReserve();
        }
        if (!val) {
          this.queryFormData.reserve_time = '';
          this.resetReserve();
        }
        this.queryFormData.reserve_date = val;
      }
    },
    // 选择预约医生
    chooseDoctor(val, index) {
      let item = {
        physical_name: val.name || '',
        physical_avatar: val.avatar || '',
        physio_id: val.id || '',
      };
      let list = this.queryFormData.services;
      this.$set(list, index, { ...list[index], ...item });
    },
    // 选择预约服务
    chooseService() {
      if (!this.uid) {
        this.$Message.error('请选择到店人');
        return;
      }
      this.chooseServiceVisible = true;
    },

    // 清除所有信息
    resetAllInfo() {
      this.queryFormData.reserve_date = '';
      this.queryFormData.reserve_time = '';
      this.queryFormData.reserve_time_flag = '';
      this.queryFormData.reserve_time_flag_text = '';
      this.resetReserve();
    },

    /**
     * @desc 重置服务,理疗师信息
     * */
    resetReserve() {
      // 重置是否使用卡券抵扣
      this.can_use_card = '';
      // 重置可使用卡券抵扣，重置选中的值
      this.queryFormData.deductible_flag = '';
      this.queryFormData.duration = {
        hour: null,
        minute: null,
      };
      // 重置外部预约渠道
      this.queryFormData.out_sales_channel = '';
      this.queryFormData.services = [{ goods_service_id: '', physio_id: '', quantity: 1 }];
    },

    /**
     * @desc 选中预约服务返回的数据
     * { Object } info { physio: {}, serve_info } 获取理疗师信息和选中的服务信息
     * */
    chooseServiceConfirm(info = {}, index) {
      let physio = info.physio || {};
      let serve_info = info.serve_info || {};

      // 整合信息进行同步
      let item = {
        // 以下同步服务信息
        serve_name: serve_info.name,
        goods_service_id: serve_info.id,
        price: serve_info.price,
        vip_price: serve_info.vip_price,

        duration: serve_info.duration, // 总时长
        service_duration: serve_info.service_duration, // 服务时长
        prepare_duration: serve_info.prepare_duration, // 准备时长
        out_goods_name: serve_info.out_goods_name,
        bind_sales_channel: serve_info.bind_sales_channel, // 服务是否绑定外部渠道
        out_sales_channel_list: serve_info.out_sales_channel_list, // 服务绑定了哪些服务

        // 以下同步理疗师信息
        physical_name: physio.name || '',
        physio_id: physio.id || '',
        physical_avatar: physio.avatar || '',
        role_name: physio.role_name || '',
        level_name: physio.level_name || '',
      };
      // 此处根据选择的服务判断是否有卡券可抵扣
      this.can_use_card = serve_info.can_use_card;
      if (serve_info.can_use_card == '1') {
        // 默认使用
        this.queryFormData.deductible_flag = '1';
      }
      this.queryFormData.duration.minute = Number(serve_info.duration || 0) || null;
      let list = this.queryFormData.services;
      this.$set(list, index, { ...list[index], ...item });
    },
    clickTimePoptip() {
      if (!this.uid) {
        this.$Message.error('请选择到店人');
      }
    },
    /**
     * @desc 新版获取到店时间段
     * */
    changeTimeV2(val) {
      this.$set(this.queryFormData, 'reserve_date', val.day);
      if (this.type == 2) {
        this.$set(this.queryFormData, 'reserve_time_flag', val.time.flag);
        this.$set(this.queryFormData, 'reserve_time_flag_text', `${val.time.st}-${val.time.et}`);
      } else {
        this.$set(this.queryFormData, 'reserve_time', val.time);
      }
    },

    /**
     * @desc 清除时刻
     * */
    clearReserveTimeV2(event) {
      event.preventDefault();
      event.stopPropagation();
      this.queryFormData.reserve_date = '';
      this.queryFormData.reserve_time = '';
      // this.resetReserve();
    },

    close() {
      this.$emit('close');
    },

    // 选择外部预约渠道
    changeOutSalesChannel(val) {
      if (+val) {
        this.queryFormData.deductible_flag = '';
      }
    },

    // 获取时刻
    getReservev2ReserveTimerange() {
      this.loading = true;
      let params = {};
      this.$api
        .getReservev2ReserveTimerange(params)
        .then(res => {
          this.dates = res.dates;
          this.timeRangeList = res.times;
        })
        .finally(() => (this.loading = false));
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ivu-table-header thead tr th {
  padding: 0px;
}

::v-deep .ivu-select-selection,
.ivu-checkbox-inner {
  border: none;
  background: transparent;
}

.select-box {
  cursor: pointer;
  display: flex;
  align-content: center;
  margin-left: 16px;
  width: 252px;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #dcdcdc;
  padding: 8px 12px;
  font-weight: 400;
  font-size: 12px;
  color: #333;
  line-height: 16px;
  position: relative;

  .time-icon {
    position: absolute;
    right: 12px;
    top: 10px;
    width: 12px;
    min-width: 12px;
    height: 12px;
  }

  .time {
    height: 20px;
  }
  .time-placeholder {
    color: #ccc;
  }
}

// suffix
.suffix-box {
  display: flex;
  align-items: center;
  position: relative;

  .suffix {
    width: fit-content;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 0 10px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 1px;
    top: 1px;
    z-index: 1;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 16px;
  }
}

.custom-mock-input {
  width: 252px;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #dcdcdc;
  padding: 8px 12px;
  font-weight: 400;
  font-size: 12px;
  color: #333;
  line-height: 16px;
  text-align: left;
  position: relative;

  .arrow-icon {
    position: absolute;
    right: 12px;
    top: 13px;
    width: 12px;
    min-width: 12px;
    height: 7px;
  }

  .close {
    position: absolute;
    right: 10px;
    top: 8px;
    font-size: 16px;
    color: #808695;
    display: none;
  }

  .content-box {
    font-weight: 400;
    font-size: 12px;
    color: #333333;
    line-height: 16px;
    display: flex;
    align-items: center;
    position: relative;
    .duration-box {
      color: #999999;
      margin-left: 8px;
      min-width: fit-content;
    }
  }
}
.close-hover {
  &:hover {
    .close {
      display: unset;
    }
    .time-icon {
      display: none;
    }
  }
}
.input-number {
  width: 252px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #dcdcdc;
  ::v-deep .ivu-input-number-input {
    padding-left: 12px;
  }
}
.date-picker {
  ::v-deep .ivu-input {
    border-radius: 4px;
    height: 32px;
    background: #ffffff;
    border: 1px solid #dcdcdc;
    padding-left: 12px;
  }
}
.label {
  font-weight: 400;
  font-size: 12px;
  color: #333333;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .mark {
    color: #fa4f4f;
  }
}
.common-disabled {
  background: #f3f3f3;
  color: #aaa !important;
  cursor: not-allowed;
  div,
  p,
  span {
    color: #aaa !important;
  }
}
.physical_avatar {
  width: 20px;
  min-width: 20px;
  height: 20px;
  margin-right: 6px;
  margin-top: -3px;
  border-radius: 50%;
  object-fit: cover;
  position: absolute;
}
.inner-box {
  //display: flex;
  //align-items: center;
  padding-right: 20px;
}
.no-card-tip {
  color: #bbb;
  margin-top: 2px;
}
::v-deep .ivu-input-number-input::placeholder {
  color: #939393;
}

// 预约服务
.reserve-service-box {
  width: 520px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .reserve-service-left {
    .service-info {
      display: flex;
      align-items: center;
      .service-name {
        font-weight: 500;
        font-size: 15px;
        color: #333333;
        line-height: 22px;
      }
      .service-minute {
        height: 16px;
        min-width: fit-content;
        padding: 0 4px;
        margin-left: 4px;
        font-weight: 400;
        font-size: 11px;
        color: #999999;
        line-height: 16px;
        background: #f5f6f8;
        border-radius: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .physical-info {
      margin-top: 12px;
      display: flex;
      align-items: center;
      .avatar {
        border-radius: 50%;
        width: 20px;
        min-width: 20px;
        height: 20px;
        object-fit: cover;
      }
      .physical-name {
        margin-left: 4px;
        font-weight: 400;
        font-size: 12px;
        color: #444444;
        line-height: 18px;
      }
      .physical-tag {
        border: 1px solid #155bd4;
        min-width: fit-content;
        padding: 0 4px;
        height: 16px;
        margin-left: 4px;
        border-radius: 2px;
        font-weight: 400;
        font-size: 11px;
        color: #155bd4;
        line-height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .physical-level {
        margin-left: 4px;
        min-width: 20px;
        height: 16px;
        border-radius: 2px;
        border: 1px solid #dcdfe6;
        font-weight: 400;
        font-size: 11px;
        color: #666666;
        line-height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .reserve-service-right {
    margin-left: 20px;
    display: flex;
    align-items: center;
    .service-price {
      font-weight: 600;
      font-size: 16px;
      color: #ee3838;
      line-height: 22px;
    }
    .close-img {
      cursor: pointer;
      width: 12px;
      height: 12px;
      margin-left: 16px;
      &:hover {
        scale: 1.1;
      }
    }
  }
}

.reserve-service-empty-box {
  width: 520px;
  height: 72px;
  background: rgba(255, 255, 255, 0);
  border-radius: 4px;
  border: 1px dashed #dcdde0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  &:hover {
    border-color: #155bd4;
    .empty-text,
    .empty-arrow {
      scale: 1.1;
      margin-left: 4px;
    }
  }

  .empty-text {
    font-weight: 400;
    font-size: 13px;
    color: #666666;
    line-height: 16px;
  }
  .empty-arrow {
    width: 12px;
    height: 12px;
    margin-left: 2px;
  }
}

// 预约医生
.reserve-doctor-box {
  width: 520px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .reserve-doctor-left {
    display: flex;
    align-items: center;
    .avatar {
      border-radius: 50%;
      width: 20px;
      min-width: 20px;
      height: 20px;
      object-fit: cover;
    }
    .doctor-name {
      margin-left: 4px;
      font-weight: 400;
      font-size: 12px;
      color: #444444;
      line-height: 18px;
    }
    .doctor-tag {
      border: 1px solid #155bd4;
      min-width: fit-content;
      padding: 0 4px;
      height: 16px;
      margin-left: 4px;
      border-radius: 2px;
      font-weight: 400;
      font-size: 11px;
      color: #155bd4;
      line-height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .reserve-doctor-right {
    margin-left: 20px;
    display: flex;
    align-items: center;
    .close-img {
      cursor: pointer;
      width: 12px;
      height: 12px;
      margin-left: 16px;
      &:hover {
        scale: 1.1;
      }
    }
  }
}
.reserve-doctor-empty-box {
  width: 520px;
  height: 44px;
  background: rgba(255, 255, 255, 0);
  border-radius: 4px;
  border: 1px dashed #dcdde0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  &:hover {
    border-color: #155bd4;
    .empty-text,
    .empty-arrow {
      scale: 1.1;
      margin-left: 4px;
    }
  }

  .empty-text {
    font-weight: 400;
    font-size: 13px;
    color: #666666;
    line-height: 16px;
  }
  .empty-arrow {
    width: 12px;
    height: 12px;
    margin-left: 2px;
  }
}
</style>
