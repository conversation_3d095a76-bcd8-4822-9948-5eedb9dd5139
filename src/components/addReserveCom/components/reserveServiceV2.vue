<!-- 当前页面是一个三维数据渲染，脱离业务逻辑，所有的选中回显滚动都由索引完成(索引参与的滚动计算)，需要数据时，可由索引进行查找 -->
<template>
  <Modal
    :value="value"
    width="880px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
    :styles="{ top: '5vh' }"
  >
    <div class="content" v-if="loading">
      <Spin class="spin"></Spin>
    </div>
    <div class="content empty" v-else-if="!data_list.length">
      <img class="empty-img" src="https://static.rsjxx.com/image/2025/0616/173344_5425.png" />
      <div class="empty-text">当前暂无理疗师可以提供服务</div>
    </div>
    <div v-else class="content">
      <div ref="typeScrollRef" class="type-scroll hidden-scroll">
        <div
          class="type-service-item"
          @click="changeTab(index)"
          :class="{ 'type-service-item--active': active_tab === index }"
          v-for="(item, index) in data_list"
          :key="index + 'type'"
        >
          <div class="ecs">{{ item.serv_type_text }}</div>
        </div>
      </div>

      <div ref="serviceRef" class="service-scroll hidden-scroll">
        <div
          class="physical-box"
          :ref="`physio-${p_item.physio.id}`"
          v-for="(p_item, p_index) in physical_info"
          :key="p_index + 'p'"
        >
          <div class="physical-info">
            <img class="avatar" :src="p_item?.physio?.avatar || default_avatar" />
            <div class="physical-name">{{ p_item.physio?.name }}</div>
            <div class="physical-tag">{{ p_item.physio?.role_name }}</div>
            <div class="physical-level">{{ p_item.physio?.level_name }}</div>
          </div>

          <div class="service-box">
            <div
              class="service-item"
              :ref="`service-${p_item.physio?.id}-${s_item}`"
              :class="{ 'service-item--active': checked_id === `${p_item.physio?.id}-${s_item}` }"
              @click="serviceChange(p_item.physio?.id, s_item)"
              v-for="(s_item, s_index) in p_item.serv_list"
              :key="s_index + 's'"
            >
              <div class="service-item-left">
                <div class="service-item-info">
                  <Tooltip
                    placement="top"
                    :content="serv_list[s_item].name"
                    :disabled="serv_list[s_item]?.name?.length < 11"
                  >
                    <div class="service-item-name ecs">{{ serv_list[s_item].name }}</div>
                  </Tooltip>
                  <Tooltip
                    v-if="getOutChannel(serv_list[s_item].out_sales_channel_list, '1')?.channel === '1'"
                    class="out-logo-box"
                    placement="top"
                    :content="getOutChannel(serv_list[s_item].out_sales_channel_list, '1')?.out_goods_name"
                  >
                    <img
                      class="out-logo"
                      v-if="getOutChannel(serv_list[s_item].out_sales_channel_list, '1')?.channel === '1'"
                      alt="美团"
                      src="https://static.rsjxx.com/image/2025/0619/144358_21725.png"
                    />
                  </Tooltip>
                  <Tooltip
                    v-if="getOutChannel(serv_list[s_item].out_sales_channel_list, '2')?.channel === '2'"
                    class="out-logo-box"
                    placement="top"
                    :content="getOutChannel(serv_list[s_item].out_sales_channel_list, '2')?.out_goods_name"
                  >
                    <img
                      class="out-logo"
                      v-if="getOutChannel(serv_list[s_item].out_sales_channel_list, '2')?.channel === '2'"
                      alt="抖音"
                      src="https://static.rsjxx.com/image/2025/0619/144358_49841.png"
                    />
                  </Tooltip>
                  <div class="service-item-minute" v-if="serv_list[s_item].duration">
                    {{ serv_list[s_item].duration }}分钟
                  </div>
                </div>

                <div class="flex flex-item-align">
                  <div class="service-item-price">¥{{ Number(serv_list[s_item].price || 0).toFixed(2) }}</div>

                  <img
                    v-if="is_rst && serv_list[s_item].can_use_card == '1'"
                    class="deduction-img"
                    src="https://static.rsjxx.com/image/2025/0619/144312_32950.png"
                  />

                  <Tooltip class="vip-tooltip" theme="light" placement="top" v-if="is_rst && isVip">
                    <div slot="content">
                      会员专享
                      <span style="color: red">￥{{ Number(serv_list[s_item].vip_price).toFixed(2) }}</span>
                    </div>
                    <div class="vip-tag-box">
                      <img class="vip-icon" src="https://img-sn01.rsjxx.com/image/2025/0616/175205_87738.png" />
                      <span class="vip-price">￥{{ serv_list[s_item].vip_price }}</span>
                    </div>
                  </Tooltip>
                </div>
              </div>

              <div class="service-item-right">
                <img
                  v-if="checked_id === `${p_item.physio?.id}-${s_item}`"
                  src="https://static.rsjxx.com/image/2025/0610/180705_17242.png"
                />
                <img v-else src="https://static.rsjxx.com/image/2025/0610/180705_43705.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" :disabled="loading || !this.checked_id" type="primary" @click="confirm"
        >确定</Button
      >
    </div>
  </Modal>
</template>

<script>
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'choose-service',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '选择预约服务',
    },
    // 预约日期
    reserve_date: {
      type: String,
      default: '',
    },
    // 预约时刻
    reserve_time: {
      type: String,
      default: '',
    },
    // 下单人id
    uid: {
      type: String,
      default: '',
    },
    // 理疗师id
    physio_id: {
      type: String,
      default: '',
    },
    isVip: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      confirmLoading: false,
      service_options: [],
      // active_index: '', // 选中的服务 eg: 0-0, 默认不选中服务 jx todo 更新了逻辑,所有的滚动去除，ref改成了id的合集
      // checked_index: '', // 选中的服务 eg: 0-0-0, 类型索引-理疗师索引-服务索引
      active_tab: 0, // 选中的类型， 默认选中第一个类型
      default_avatar: 'https://static.rsjxx.com/image/2025/0610/174630_91091.png',
      list: [],
      loading: false,
      serv_list: [], // 所有服务
      data_list: [], // 所有类型+服务
      checked_id: '', // 理疗师id+服务id
    };
  },

  computed: {
    getOutChannel() {
      return (list, type) => {
        let c_item = list?.find(item => item.channel === type);
        return c_item || {};
      };
    },
    scrollTop() {
      return (this.active_tab - 3) * 42;
    },
    physical_info() {
      return this.data_list[this.active_tab]?.items || [];
    },
    is_rst() {
      return isRstClinic();
    },
    // 获取选中的服务的ref
    serviceRef() {
      return `service-${this.active_index}`;
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    init() {
      this.$nextTick(() => {
        this.scrollToPhysic();
        // 此处处理回显时的滚动，赋值active_tab，active_index 即可
        // this.active_tab = 4;
        // this.active_index = '3-2';
        // this.checked_index = `${this.active_tab}-${this.active_index}`;
        // this.serviceScrollInit(this.active_tab);
      });
    },
    // 只有当类型是全部，且有理疗师id的时候，将服务滚动的指定的理疗师
    scrollToPhysic() {
      if (!+this.physio_id) return;
      let ref = this.$refs[`physio-${this.physio_id}`] && this.$refs[`physio-${this.physio_id}`][0];
      let top = ref?.offsetTop ? ref?.offsetTop - 10 : 0;
      this.$refs?.serviceRef?.scrollTo({ top: top, behavior: 'smooth' });
    },
    serviceChange(physic_id, serv_id) {
      this.checked_id = `${physic_id}-${serv_id}`;
    },
    changeTab(index) {
      this.active_tab = index;
      this.scrollToTop();
      // this.serviceScrollInit(index);
    },
    // 切换类型，服务模块滑动到最顶部，先清后赋值，触发监听
    // serviceScrollInit(index) {
    //   let index_list = this.checked_index.split('-');
    //   if (this.active_tab == index_list[0] && this.checked_index) {
    //     this.scrollInit();
    //   } else {
    //     this.scrollToTop(index);
    //   }
    // },
    // 滚动到选中的服务
    // scrollInit() {
    //   this.$nextTick(() => {
    //     if (this.serviceRef && this.$refs[this.serviceRef]) {
    //       let top = this.$refs[this.serviceRef][0].offsetTop - 10;
    //       this.$refs.serviceRef.scrollTo({ top: top, behavior: 'smooth' });
    //     }
    //   });
    // },
    // 滑动到顶部
    scrollToTop(index) {
      let top = (index - 3) * 42;
      this.$refs.typeScrollRef.scrollTo({ top: top, behavior: 'smooth' });
      this.$refs.serviceRef.scrollTo({ top: 0, behavior: 'smooth' });
    },
    clearData() {
      this.active_tab = 0;
      // this.active_index = '';
      // this.checked_index = '';
      this.checked_id = '';
      this.list = [];
    },

    changeVisible(visible) {
      if (visible) {
        this.getReserveTechnicians();
      } else {
        this.closeModal();
      }
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      if (!this.checked_id) {
        return this.$Message.error('请选择预约服务');
      }
      this.$emit('confirm', this.getReserveInfo());
      this.closeModal();
    },

    /**
     * @desc 根据索引转换数据
     * */
    getReserveInfo() {
      let id_list = this.checked_id.split('-');
      let info = {
        physio: this.getPhysicInfo(id_list[0]),
        serve_info: this.serv_list[id_list[1]],
      };
      return info || {};
    },

    getPhysicInfo(id) {
      if (!+id) return;
      let physic_info = {};
      this.data_list.some(item =>
        item.items.some(c_item => {
          if (c_item.physio.id === id) {
            physic_info = c_item.physio;
          }
        })
      );
      return physic_info || {};
    },

    // 获取服务
    getReserveTechnicians() {
      this.loading = true;
      let params = {
        uid: this.uid,
        reserve_date: this.reserve_date,
        reserve_time: this.reserve_time,
      };
      this.$api
        .getReserveTechnicians(params)
        .then(res => {
          this.loading = false;
          this.data_list = res.data_list || [];
          this.serv_list = res.serv_list || [];
          // mock 服务绑定
          // this.list.forEach(item => {
          //   item.items.forEach(item_1 => {
          //     item_1.serv_list.forEach(item_2 => {
          //       item_2.bind_sales_channel = '1';
          //       item_2.out_sales_channel_list = [
          //         {
          //           channel: '1',
          //           channel_desc: '美团',
          //           out_goods_name: '美团商品',
          //         },
          //         {
          //           channel: '2',
          //           channel_desc: '抖音',
          //           out_goods_name: '抖音商品',
          //         },
          //       ];
          //     });
          //   });
          // });
          this.init();
        })
        .finally(() => (this.loading = false));
    },
  },
};
</script>

<style scoped lang="less">
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
  padding: 0px;
}
.empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .empty-img {
    width: 240px;
    min-width: 240px;
    height: 240px;
  }
  .empty-text {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 17px;
    margin-top: -20px;
  }
}
.content {
  display: flex;
  height: 500px;
  box-sizing: border-box;
  position: relative;
  .spin {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%);
    transform: translateY(-50%);
  }
  .type-scroll {
    height: 100%;
    padding-top: 8px;
    background: #f9fafb;
    width: 100px;
    min-width: 100px;
    overflow-y: auto;
    .type-service-item {
      cursor: pointer;
      padding: 0 10px;
      height: 42px;
      box-sizing: border-box;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 13px;
      color: #888888;
      line-height: 18px;
      &:hover {
        color: #155bd4;
      }
    }
    .type-service-item--active {
      &:before {
        content: ' ';
        width: 3px;
        height: 18px;
        background: #155bd4;
        border-radius: 0px 2px 2px 0px;
        position: absolute;
        left: 0px;
        top: 10px;
      }
      background: #ffffff;
      font-weight: 500;
      font-size: 13px;
      color: #155bd4;
      line-height: 18px;
    }
  }

  .service-scroll {
    flex: 1;
    margin-left: 16px;
    padding-top: 8px;
    padding-right: 10px;
    overflow-y: auto;
    .physical-box {
      margin-bottom: 14px;
      .physical-info {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .avatar {
          width: 40px;
          min-width: 40px;
          height: 40px;
          object-fit: cover;
          border-radius: 50%;
        }
        .physical-name {
          margin-left: 10px;
          font-weight: 500;
          font-size: 15px;
          color: #333333;
          line-height: 22px;
        }
        .physical-tag {
          border: 1px solid #155bd4;
          min-width: fit-content;
          padding: 0 4px;
          height: 16px;
          margin-left: 4px;
          border-radius: 2px;
          font-weight: 400;
          font-size: 11px;
          color: #155bd4;
          line-height: 16px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .physical-level {
          margin-left: 4px;
          min-width: 20px;
          height: 16px;
          border-radius: 2px;
          border: 1px solid #dcdfe6;
          font-weight: 400;
          font-size: 11px;
          color: #666666;
          line-height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .service-box {
        display: flex;
        flex-wrap: wrap;
        .service-item {
          &:hover {
            background: rgba(21, 91, 212, 0.06);
          }
          box-sizing: border-box;
          border: 1px solid transparent;
          &:nth-child(3n -1) {
            margin-left: 1.25%;
            margin-right: 1.25%;
          }
          width: 32.5%;
          min-width: 32.5%;
          cursor: pointer;
          background: #f9fafb;
          border-radius: 4px;
          padding: 8px 12px;
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          .service-item-left {
            flex: 1;
            .service-item-info {
              margin-bottom: 6px;
              display: flex;
              align-items: center;
              .service-item-name {
                font-weight: 400;
                font-size: 13px;
                color: #333333;
                line-height: 18px;
              }
              .out-logo-box {
                margin-top: -6px;
                min-width: fit-content;
                .out-logo {
                  margin-left: 4px;
                  width: 16px;
                  min-width: 1px;
                  height: 16px;
                }
              }
              .service-item-minute {
                margin-top: -3px;
                height: 16px;
                margin-left: 4px;
                min-width: fit-content;
                padding: 0 4px;
                font-weight: 400;
                font-size: 11px;
                color: #999999;
                background: #f5f6f8;
                border-radius: 2px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }

            .service-item-price {
              font-weight: 600;
              font-size: 12px;
              color: #ee3838;
              line-height: 22px;
              display: flex;
              align-items: flex-end;
            }

            .deduction-img {
              margin-left: 4px;
              width: 53px;
              min-width: 53px;
              height: 14px;
            }
            .vip-tooltip {
              display: flex;
              align-items: center;
              .vip-tag-box {
                position: relative;
                margin-left: 4px;
                background: linear-gradient(148deg, #cdd8e6 0%, #809cc1 100%);
                border-radius: 2px;
                display: flex;
                align-items: center;
                .vip-icon {
                  width: 90px;
                  height: 18px;
                  //width: 26px;
                  //min-width: 26px;
                  //height: 14px;
                }
                .vip-price {
                  position: absolute;
                  right: 0;
                  top: 2px;
                  font-weight: 500;
                  font-size: 10px;
                  color: #ffffff;
                  line-height: 14px;
                  text-align: left;
                  min-width: fit-content;
                  padding-right: 4px;
                }
              }
            }
          }

          .service-item-right {
            min-width: fit-content;
            margin-left: 10px;
            img {
              width: 14px;
              height: 14px;
            }
          }
        }
        .service-item--active {
          border: 1px solid #155bd4;
          background: rgba(21, 91, 212, 0.06);
        }
      }
    }
  }
}
</style>
