<template>
  <Poptip
    popper-class="custom-radius-poptip"
    trigger="click"
    width="520"
    ref="pop"
    :disabled="disabled"
    @on-popper-show="popShow"
    @on-popper-hide="popHide"
  >
    <div slot="content" class="time-content">
      <Spin v-if="loading" class="spin"></Spin>
      <div class="content" v-else>
        <!-- 展示日期模块 -->
        <div class="day-wrapper">
          <img @click="leftScroll" class="left-arrow" src="https://static.rsjxx.com/image/2025/0611/153414_55263.png" />
          <img
            @click="rightScroll"
            class="right-arrow"
            src="https://static.rsjxx.com/image/2025/0611/153414_66195.png"
          />
          <div class="day-box hidden-scroll" ref="dayRef">
            <div
              class="day-item"
              :class="{ 'day-item--active': active_day === d_item }"
              v-for="(d_item, d_index) in dates"
              :key="'dates' + d_index"
              @click="changeDay(d_item, d_index)"
            >
              <div class="weekday-text">{{ formatWeekday(d_item) }}</div>
              <div class="day-text">{{ formatDay(d_item) }}</div>
            </div>
          </div>
        </div>

        <!--        <div class="header">-->
        <!--          <div @click="expandEvent">{{ expand ? '收起过去时间' : '展开过去时间' }}</div>-->
        <!--          <Icon v-if="!expand" type="md-arrow-dropdown" color="#8558fa" size="20" />-->
        <!--          <Icon v-else type="md-arrow-dropup" color="#8558fa" size="20" />-->
        <!--        </div>-->
        <div class="item-wrapper" ref="timeRef">
          <Spin
            v-if="getTimeLoading || getDocLoading"
            class="flex flex-item-align flex-item-center"
            style="height: 100%"
          ></Spin>
          <template v-else>
            <div class="empty-tip" v-if="type == '2' ? !doctorTimeList.length : !list.length">暂无数据</div>
            <template v-for="(item, index) in type == '2' ? doctorTimeList : list">
              <!--            v-if="expand || compareTimes(item)"-->
              <div
                :class="{
                  'time-item-box--disabled': isTimeRange && type == '2' && item.status !== '1',
                  'time-item-box--active':
                    isTimeRange && type == '2' && `${active_day} ${item.st}-${item.et}` === checked_time,
                  'time-item--disabled': !isTimeRange && type != '2' && !timeIsAllow(item),
                  'time-item--active': !isTimeRange && type != '2' && `${active_day} ${item}` === checked_time,
                  'time-item-box': isTimeRange,
                  'time-item': !isTimeRange,
                }"
                @click="changeTime(item)"
                :ref="'timeRef-' + index"
              >
                <div v-if="type == 2" class="time-item-text">{{ `${item.p_flag_text} ${item.st}-${item.et}` }}</div>
                <div v-else class="time-item-text">{{ item }}</div>
              </div>
            </template>
          </template>
        </div>
      </div>
      <div class="footer-wrapper">
        <div class="checked-time">
          <span class="label">预约时间：</span>
          <!--          <span class="day">{{ active_day }}</span>-->
          <span class="time">{{ checked_time }}</span>
        </div>
        <div class="btn-group">
          <Button type="default" style="width: 64px" @click="cancel">取消</Button>
          <Button type="primary" style="width: 64px; margin-left: 8px" @click="confirm">确定</Button>
        </div>
      </div>
    </div>
    <slot></slot>
  </Poptip>
</template>

<script>
import moment from 'moment';
export default {
  name: 'timePoptipV2',
  components: {},
  mixins: [],
  props: {
    // 可以预约的日期
    dates: {
      type: Array,
      default: () => [],
    },
    // 时刻数据/时间段数据
    list: {
      type: Array,
      default: () => [],
    },
    // 理疗师选中的时刻
    reserve_time: {
      type: String,
      default: '',
    },
    // 选中的日期
    date: {
      type: [String, Date],
      default: '',
    },
    // 1: 理疗师 2:医生
    type: {
      type: String,
      default: '',
    },
    // 理疗师id
    physio_id: {
      type: String,
      default: '',
    },
    // 禁止触发poptip
    disabled: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      getTimeLoading: false, // 根据日期获取可以预约时间的loading
      getDocLoading: false, // 预约医生根据日期获取可以预约时间的loading
      doctorTimeList: [], // 医生时刻
      expand: false, // 默认过滤掉过去日期
      time: '',
      loading: false,
      active_day: '', // 当前选中的日期 eg:2025-06-08
      checked_time: '', // 完整的选中日期（日期 + 时刻） eg: 2025-06-08 11:00
      available_time_pieces: [], // 理疗师可以预约的时间段，由单独接口查询获取
    };
  },
  computed: {
    /**
     * @desc 获取当前选中的日期
     * */
    getCheckedDate() {
      let list = this.checked_time.split(' ');
      console.log('=>(timePoptipV2.vue:146) list', list);
      return list[0] || '';
    },
    /**
     * 判断当前的时间是否允许预约
     * */
    timeIsAllow() {
      return item => {
        if (this.type == '1') {
          return this.available_time_pieces.includes(item);
        }
        return true;
      };
    },

    /**
     * @desc 是否是时间段
     * 时刻没有背景块, 时间段有背景块
     * */
    isTimeRange() {
      return this.type == 2;
    },
    // 转化星期
    computedWeekDesc() {
      return day => {
        switch (day) {
          case 0:
            return '周一';
          case 1:
            return '周二';
          case 2:
            return '周三';
          case 3:
            return '周四';
          case 4:
            return '周五';
          case 5:
            return '周六';
          case 6:
            return '周日';
        }
      };
    },
    // 格式化周形式
    formatWeekday() {
      return item => {
        let day = moment(item).weekday();
        let today = moment().format('YYYY-MM-DD');
        if (item === today) {
          return '今天';
        }
        if (item === moment(today).add(1, 'days').format('YYYY-MM-DD')) {
          return '明天';
        }
        return this.computedWeekDesc(day);
      };
    },
    // 格式化日期形式
    formatDay() {
      return item => {
        return moment(item).format('MM月DD日');
      };
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * @desc 根据索引，时刻滚动到索引的位置
     * */
    scrollToIndex(index) {
      this.$nextTick(() => {
        let time_scroll_distance = '';
        let default_top = this.$refs[`timeRef-0`] && this.$refs[`timeRef-0`][0]?.offsetTop;
        time_scroll_distance = this.$refs[`timeRef-${index}`][0]?.offsetTop - default_top || 0;
        this.$refs.timeRef.scrollTo({ top: time_scroll_distance, behavior: 'smooth' });
      });
    },
    /**
     * @desc 将最近的时间滚动到最顶部
     * */
    scrollToTop() {
      if (this.type === '1') {
        this.list.some((item, index) => {
          if (this.timeIsAllow(item)) {
            this.scrollToIndex(index);
            return true;
          }
        });
      }

      if (this.type === '2') {
        this.doctorTimeList?.some((item, index) => {
          if (item.status == '1') {
            this.scrollToIndex(index);
            return true;
          }
        });
      }
    },
    changeDay(day) {
      this.active_day = day;
      if (this.type == '1') {
        this.getAvailableTimePieces().then(() => this.scrollInit());
      }
      if (this.type == '2') {
        this.getDocTimeSelect().then(() => this.scrollInit());
      }
    },
    leftScroll() {
      let left_distance = this.$refs.dayRef.scrollLeft;
      let dot_distance = (left_distance / 70).toFixed(1).split('.');
      let scroll_distance = 0;
      if (dot_distance[1] == 0 || dot_distance[1] < 5) {
        scroll_distance = dot_distance[0] * 70 - 70;
      } else {
        scroll_distance = dot_distance[0] * 70;
      }
      this.$refs.dayRef.scrollTo({ left: scroll_distance, behavior: 'smooth' });
    },
    rightScroll() {
      let left_distance = this.$refs.dayRef.scrollLeft;
      let dot_distance = (left_distance / 70).toFixed(1).split('.');
      let scroll_distance = 0;
      if (dot_distance[1] == 0 || dot_distance[1] >= 5) {
        scroll_distance = dot_distance[0] * 70 + 70;
      } else {
        scroll_distance = dot_distance[0] * 70;
      }
      this.$refs.dayRef.scrollTo({ left: scroll_distance, behavior: 'smooth' });
    },
    validData() {
      if (!this.active_day || !this.time) {
        this.$Message.error('请完善预约到店时间');
        return false;
      }
      return true;
    },
    confirm() {
      if (this.validData()) {
        this.$emit('change', { day: this.active_day, time: this.time });
        this.closePopTip();
      }
    },
    cancel() {
      this.closePopTip();
    },
    closePopTip() {
      this.$refs.pop.handleClose();
    },
    changeTime(item) {
      if (this.type == '1' && !this.timeIsAllow(item)) return;
      if (this.type == '2' && item.status != '1') return;
      if (this.type == 2) {
        this.checked_time = `${this.active_day} ${item.st}-${item.et}`;
      } else {
        this.checked_time = `${this.active_day} ${item}`;
      }
      this.time = item;
    },
    async popShow() {
      this.active_day = this.dates[0];
      if (this.date) {
        this.active_day = this.date;
        this.checked_time = `${this.date} ${this.reserve_time}`;
      }
      if (this.reserve_time) {
        this.time = this.reserve_time;
      }
      if (this.type == '1') {
        await this.getAvailableTimePieces().then(() => this.scrollInit());
      }
      if (this.type == '2') {
        await this.getDocTimeSelect().then(() => this.scrollInit());
      }
    },
    // 根据回显的日期，进行滚动初始化
    scrollInit() {
      this.$nextTick(() => {
        // 有日期，滚动日期
        if (this.active_day) {
          let scroll_distance = '';
          let index = this.dates.findIndex(item => item === this.active_day);
          if (index !== -1) {
            scroll_distance = index * 70;
            this.$refs.dayRef.scrollTo({ left: scroll_distance, behavior: 'smooth' });
          }
        }
        // 有时刻，滚动时刻
        if (this.time) {
          if (this.active_day == this.getCheckedDate) {
            if (this.type == '1') {
              let index = this.list.findIndex(item => item === this.time);
              if (index !== -1) {
                this.scrollToIndex(index);
              }
            }

            if (this.type == '2') {
              let index = this.doctorTimeList.findIndex(item => `${item.st}-${item.et}` === this.time);
              if (index !== -1) {
                this.scrollToIndex(index);
              }
            }
          } else {
            // 非当前选中的日期天数，自动滚动到最近的时刻
            this.scrollToTop();
          }
        }

        // 没有时刻，自动滚动到最近的时刻
        if (!this.time) {
          this.scrollToTop();
        }
      });
    },
    popHide() {
      this.time = '';
      this.active_day = '';
      this.checked_time = '';
      this.$refs.dayRef.scrollLeft = 0;
      this.$refs.timeRef.scrollTop = 0;
    },
    expandEvent() {
      this.expand = !this.expand;
    },
    /**
     * @dec 比较两个时间段的大小 time1 < time 2
     * @note 之前版本，由前端比对时间进行控制，现在由接口判断，当前函数暂不使用，也不要删除
     * */
    // compareTimes(time, symbol) {
    //   let st = this.type == 2 ? time.st : time;
    //   const today_date = moment().format('YYYY-MM-DD');
    //   // 当选择的日期不是当天的，直接放出所有日期
    //   if (this.active_day && moment(this.active_day).isAfter(today_date)) {
    //     return true;
    //   }
    //   const today_timestamp = moment().valueOf();
    //   const current_date = `${today_date} ${st}`;
    //   const current_timestamp = moment(current_date).valueOf();
    //   if (symbol === '<=') {
    //     return today_timestamp <= current_timestamp;
    //   } else {
    //     return today_timestamp < current_timestamp;
    //   }
    // },

    // 预约理疗师-可用时段
    getAvailableTimePieces() {
      return new Promise(resolve => {
        let params = {
          reserve_date: this.active_day,
          physio_id: this.physio_id,
        };
        this.getTimeLoading = true;
        this.$api
          .getAvailableTimePieces(params)
          .then(res => {
            this.getTimeLoading = false;
            this.available_time_pieces = res.available_time_pieces;
            resolve();
          })
          .finally(() => (this.getTimeLoading = false));
      });
    },

    // 获取医生的可选可选时间和范围(不根据时间查询)
    getDocTimeSelect() {
      return new Promise(resolve => {
        this.doctorTimeList = [];
        this.getDocLoading = true;
        let params = {
          date: this.active_day,
          doctor_id: this.physio_id,
        };
        this.$api
          .getDocTimeSelect(params)
          .then(res => {
            this.getDocLoading = false;
            this.doctorTimeList = res.list;
            resolve();
          })
          .finally(() => (this.getDocLoading = false));
      });
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ivu-poptip-title {
  display: none;
}
.time-content {
  height: 386px;
  .content {
    height: 340px;
    overflow-y: auto;
    position: relative;
    // 日期模块
    .day-wrapper {
      position: relative;
      padding: 0 30px;
      position: sticky;
      top: 0px;
      height: 60px;
      background: #fff;
      .left-arrow,
      .right-arrow {
        width: 12px;
        min-width: 12px;
        height: 12px;
        cursor: pointer;
      }
      .left-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 15px;
      }
      .right-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 10px;
      }
      .day-box {
        display: flex;
        align-items: center;
        overflow-x: auto;
        .day-item {
          cursor: pointer;
          padding: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          .weekday-text {
            font-weight: 400;
            font-size: 13px;
            color: #333333;
            line-height: 18px;
            text-shadow: 0px 1px 0px #ebedf0;
          }
          .day-text {
            margin-top: 2px;
            font-weight: 300;
            font-size: 11px;
            color: #333333;
            line-height: 16px;
            text-shadow: 0px 1px 0px #ebedf0;
          }
        }

        .day-item--active {
          color: #155bd4;
          position: relative;
          .weekday-text,
          .day-text {
            color: #155bd4;
          }
          &:after {
            content: ' ';
            width: 56px;
            height: 2px;
            background: #155bd4;
            box-shadow: 0px 1px 0px 0px #ebedf0;
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
      }
    }
    .header {
      font-size: 14px;
      padding: 10px 0px;
      width: 100%;
      color: #8558fa;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: sticky;
      top: 60px;
      background: #fff;
      border-bottom: 1px solid #f2f2f2;
    }
    .item-wrapper {
      //display: flex;
      //flex-wrap: wrap;
      margin-top: 12px;
      height: 260px;
      overflow-y: auto;
      padding-left: 10px;
      white-space: normal;
      // 普通时刻
      .time-item {
        display: inline-block;
        cursor: pointer;
        width: 11%;
        margin-bottom: 20px;
        text-align: center;
        line-height: 100%;
        &:hover {
          color: #8558fa;
          scale: 1.1;
        }
      }
      .time-item--active {
        color: #8558fa;
      }
      .time-item--disabled {
        color: #bbbbbb !important;
        cursor: default !important;
        &:hover {
          scale: 1 !important;
        }
      }

      // 带背景色的内容
      .time-item-box {
        display: inline-block;
        cursor: pointer;
        &:hover {
          color: #155bd4;
          background: rgba(21, 91, 212, 0.08);
          .time-item-text {
            scale: 1.1;
          }
        }
        width: 32%;
        &:nth-child(3n - 1) {
          margin-left: 2%;
          margin-right: 2%;
        }
        height: 44px;
        background: #f9fafb;
        border-radius: 4px;
        padding: 12px 10px;
        font-weight: 400;
        font-size: 13px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 12px;
        box-sizing: border-box;
        text-align: center;
      }
      .time-item-box--active {
        color: #155bd4;
        background: rgba(21, 91, 212, 0.08);
      }
      .time-item-box--disabled {
        cursor: default !important;
        background: #f9fafb !important;
        color: #bbbbbb !important;
        &:hover {
          .time-item-text {
            scale: 1 !important;
          }
        }
      }
    }
  }
  .footer-wrapper {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    position: absolute;
    bottom: 0px;
    left: 0px;
    background: #fff;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-top: 1px solid #f2f2f2;
    font-weight: 400;
    font-size: 13px;
    color: #999999;
    line-height: 22px;
    text-shadow: 0px -1px 0px #ebedf0;
    .checked-time {
      .day,
      .time {
        font-weight: 400;
        font-size: 13px;
        color: #333333;
        line-height: 22px;
        text-shadow: 0px -1px 0px #ebedf0;
      }
      .time {
        margin-left: 4px;
      }
    }
  }
}
.spin {
  flex-wrap: wrap;
  width: 100%;
  display: flex;
  justify-content: center;
  align-content: center !important;
}
.time {
  font-size: 12px;
  color: #999;
  line-height: 17px;
}
.empty-tip {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ccc;
}
</style>
