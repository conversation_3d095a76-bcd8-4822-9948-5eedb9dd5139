<template>
  <div>
    <Poptip
      :disabled="disabled"
      popper-class="custom-radius-poptip"
      trigger="click"
      width="520"
      ref="pop"
      @on-popper-show="popShow"
      @on-popper-hide="popHide"
    >
      <div slot="content" ref="doctorRefs" class="doctor-content">
        <Spin v-if="loading" class="spin"></Spin>
        <div v-else style="height: 100%">
          <div class="empty-text" v-if="!doctor_list.length">当前没有处于排班状态的医生，请先进行排班</div>
          <div
            v-else
            class="doctor-item"
            :class="{ 'doctor-item--active': id === item.id }"
            @click="chooseDoctor(item)"
            v-for="(item, index) in doctor_list"
            :key="index"
            :ref="`doctor-${index}`"
          >
            <img class="avatar-img" :src="item.avatar || default_avatar" />
            <div class="name">{{ item.name }}</div>
            <div class="type-tag">{{ item.role_name }}</div>
          </div>
        </div>
      </div>
      <slot></slot>
    </Poptip>
  </div>
</template>

<script>
export default {
  name: 'reserveDoctorV2',
  components: {},
  mixins: [],
  props: {
    id: {
      type: String,
      default: '',
    },
    // 日期
    reserve_date: {
      type: String,
      default: '',
    },
    // 时刻
    reserve_time_flag_text: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      loading: false,
      default_avatar: 'https://static.rsjxx.com/image/2025/0610/174630_91091.png',
      doctor_list: [],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    chooseDoctor(item) {
      this.$emit('change', item);
      this.closePopTip();
    },
    popShow() {
      this.getueryDoctor();
    },
    popHide() {
      this.$refs.doctorRefs.scrollTo({ top: 0, behavior: 'auto' });
    },
    closePopTip() {
      this.$refs.pop.handleClose();
    },
    // 获取医生
    getueryDoctor() {
      this.doctor_list = [];
      let reserve_time = this.reserve_time_flag_text?.split('-') || [];
      let params = {
        reserve_date: this.reserve_date,
        reserve_time: reserve_time[0],
      };
      this.loading = true;
      this.$api
        .getueryDoctor(params)
        .then(res => {
          this.doctor_list = res.list;
          this.scrollToDoctor(this.id);
        })
        .finally(() => (this.loading = false));
    },

    /**
     * @desc 滚动指定医生id位置
     * @param { id } String 医生id
     * */
    scrollToDoctor(id) {
      if (!id) return;
      setTimeout(() => {
        this.$nextTick(() => {
          let index = this.doctor_list.findIndex(item => item.id === id);
          if (index !== -1) {
            let top = 0;
            top = this.$refs[`doctor-${index}`][0]?.offsetTop || 0;
            this.$refs.doctorRefs.scrollTo({ top: top - 10, behavior: 'smooth' });
          }
        });
      }, 0);
    },
  },
};
</script>

<style lang="less" scoped>
.doctor-content {
  height: 240px;
  overflow-y: auto;
  .spin {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);
    transform: translateY(-50%);
  }

  .empty-text {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 17px;
  }

  .doctor-item {
    padding: 8px 16px;
    margin-bottom: 1px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
      background: #f2f4f7;
    }
    .avatar-img {
      border-radius: 50%;
      width: 29px;
      min-width: 29px;
      height: 29px;
      object-fit: cover;
    }
    .name {
      margin-left: 13px;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      white-space: normal;
    }

    .type-tag {
      margin-left: 8px;
      border-radius: 2px;
      border: 1px solid #155bd4;
      padding: 0 4px;
      font-weight: 400;
      font-size: 11px;
      color: #155bd4;
      line-height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .doctor-item--active {
    background: #f2f4f7;
  }
}
</style>
