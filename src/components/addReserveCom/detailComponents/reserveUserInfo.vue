<!-- 到店人信息展示, 当前组件只做样式展示抽离 -->
<template>
  <div class="user-wrapper">
    <div class="user-box">
      <div class="avatar-box">
        <img
          :src="userInfo.avatar || default_avatar"
          class="avatar"
          :style="{
            border: isVip ? '2px solid #b0c3dd' : '2px solid  transparent',
          }"
        />
        <img v-if="isVip" class="head980vip" :src="vip980Avatar" alt="#" />
      </div>
      <div class="info-box">
        <div class="top">
          <div class="name">{{ userInfo.name }}</div>
          <div class="sex">{{ userInfo.sex_desc }}</div>
          <div class="line" v-if="userInfo.age"></div>
          <div class="age" v-if="userInfo.age">{{ userInfo.age }}岁</div>
        </div>
        <div class="mobile">{{ userInfo.mobile }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import vip980Avatar from '@/assets/image/user/980vip.png';
export default {
  name: 'reserveUserInfo',
  components: {},
  mixins: [],
  props: {
    userInfo: {
      type: Object,
      default: () => {},
    },
    isVip: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      default_avatar: 'https://static.rsjxx.com/image/2025/0616/114550_79591.png',
      vip980Avatar,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.user-wrapper {
  .user-box {
    background: #f9fafb;
    border-radius: 4px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    .avatar-box {
      position: relative;
      .avatar {
        width: 40px;
        min-width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }
      .head980vip {
        width: 33px;
        height: 14px;
        position: absolute;
        bottom: -2px;
        left: 4px;
        object-fit: cover;
      }
    }

    .info-box {
      margin-left: 12px;
      .top {
        display: flex;
        align-items: center;
        .name {
          font-weight: 600;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
        }
        .sex,
        .age {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 18px;
        }
        .sex {
          margin-left: 8px;
        }
        .line {
          background: #ccc;
          width: 1px;
          height: 10px;
          margin: 0 6px;
        }
      }
      .mobile {
        margin-top: 2px;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        line-height: 18px;
      }
    }
  }
}
</style>
