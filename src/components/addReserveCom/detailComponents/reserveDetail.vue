<template>
  <div class="detail-box">
    <div class="item-box">
      <div class="head-box">
        <div class="head">预约渠道与状态</div>
        <div v-if="type == 1 && showEditBtn" class="update-action" @click="editReserve">
          <img class="update-icon" src="https://static.rsjxx.com/image/2025/0616/142147_20879.png" />
          <a>修改预约单</a>
        </div>
      </div>

      <div class="content">
        <div class="flex">
          <div class="lv-item flex-1">
            <div class="lv-label">预约状态：</div>
            <div class="lv-value">{{ detailInfo.reserve_status_desc }}</div>
          </div>

          <div class="lv-item flex-1">
            <div class="lv-label">下单方式：</div>
            <div class="lv-value">{{ detailInfo.source_desc }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="item-box">
      <div class="head">预约信息</div>

      <div class="lv-item">
        <div class="lv-label">到店人：</div>
        <div class="lv-value">
          <reserve-user-info :userInfo="detailInfo?.reserve_user || {}" :isVip="isVip"></reserve-user-info>
        </div>
      </div>

      <div class="lv-item">
        <div class="lv-label">预约到店时间：</div>
        <div class="lv-value lv-value-bg">{{ detailInfo.reserve_date }} {{ detailInfo.reserve_time }}</div>
      </div>

      <div class="lv-item" v-if="type == 1">
        <div class="lv-label">预约服务：</div>
        <div class="lv-value lv-value-bg">
          <div class="reserve-service-box">
            <div class="reserve-service-left">
              <div class="service-info">
                <div class="service-name">{{ services?.goods_service?.name }}</div>
                <div class="service-minute">{{ detailInfo?.duration?.minute }}分钟</div>
              </div>

              <div class="physical-info">
                <img class="avatar" :src="services?.physio?.avatar || default_avatar" />
                <div class="physical-name">{{ services?.physio?.name }}</div>
                <div class="physical-tag">{{ services?.physio?.role_name }}</div>
                <div class="physical-level">{{ services?.physio?.level_name }}</div>
              </div>
            </div>
            <div class="reserve-service-right">
              <!--              <div class="service-price" v-if="is_rst && isVip && detailInfo.deductible_flag !== '1'">-->
              <!--              ¥{{ Number(services?.goods_service?.vip_price || 0).toFixed(2) }}-->
              <!--              </div>-->
              <!--              <div class="service-price" v-else>¥{{ Number(services?.goods_service?.price || 0).toFixed(2) }}</div>-->
              <div class="service-price">¥{{ Number(services?.goods_service?.price || 0).toFixed(2) }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="lv-item" v-if="type == 2">
        <div class="lv-label">预约医生：</div>
        <div class="lv-value lv-value-bg">
          <div class="reserve-doctor-box">
            <img class="avatar" :src="services?.physio?.avatar || default_avatar" />
            <div class="doctor-name">{{ services?.physio?.name }}</div>
            <div class="doctor-tag">{{ services?.physio?.role_name }}</div>
          </div>
        </div>
      </div>

      <div class="lv-item">
        <div class="lv-label">预约占用时长：</div>
        <div class="lv-value">
          {{ detailInfo?.duration?.minute }}分钟
          <span v-if="+detailInfo?.prepare_duration?.minute" class="tip"
            >（{{ detailInfo?.service_duration?.minute }}分钟服务+{{
              detailInfo?.prepare_duration?.minute
            }}分钟准备）</span
          >
        </div>
      </div>
      <div class="lv-item" v-if="detailInfo.out_sales_channel_desc">
        <div class="lv-label">外部预约渠道：</div>
        <div class="lv-value">{{ detailInfo.out_sales_channel_desc || '-' }}</div>
      </div>

      <div
        class="lv-item"
        v-if="is_rst && Number(detailInfo.out_sales_channel || 0) == 0 && detailInfo.can_use_card == '1'"
      >
        <div class="lv-label">使用卡券抵扣：</div>
        <div class="lv-value" v-if="detailInfo.deductible_flag">
          {{ detailInfo.deductible_flag == '1' ? '使用' : '不使用' }}
        </div>
        <div class="lv-value" v-else>{{ detailInfo.deductible_flag == '1' ? '使用' : '不使用' }}</div>
      </div>
    </div>

    <div class="item-box">
      <div class="head">备注信息</div>

      <div class="lv-item" style="align-items: flex-start" v-if="detailInfo.user_remark">
        <div class="lv-label">用户备注：</div>
        <div class="lv-value">{{ detailInfo.user_remark || '-' }}</div>
      </div>

      <div class="lv-item" style="align-items: flex-start">
        <div class="lv-label">门店备注：</div>
        <div class="lv-value">{{ detailInfo.remark || '-' }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import reserveUserInfo from '@/components/addReserveCom/detailComponents/reserveUserInfo.vue';
import { isRstClinic } from '@/libs/runtime';
export default {
  name: 'reserveDetail',
  components: {
    reserveUserInfo,
  },
  mixins: [],
  props: {
    type: {
      type: String,
      default: '',
    },
    detailInfo: {
      type: Object,
      default: () => {},
    },
    showEditBtn: {
      type: Boolean,
      default: false,
    },
    isVip: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      default_avatar: 'https://static.rsjxx.com/image/2025/0610/174630_91091.png',
    };
  },
  computed: {
    services() {
      return (this.detailInfo?.services && this.detailInfo?.services[0]) || {};
    },
    is_rst() {
      return isRstClinic();
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    editReserve() {
      this.$emit('edit');
    },
  },
};
</script>

<style lang="less" scoped>
.detail-box {
  padding: 0 20px 0px 30px;
  .head {
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    position: relative;
    margin-bottom: 16px;
    &:before {
      content: ' ';
      width: 3px;
      height: 14px;
      background: #155bd4;
      position: absolute;
      left: -12px;
      top: 3px;
    }
  }
  .item-box {
    .head-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .update-action {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        color: #155bd4;
        line-height: 18px;
        .update-icon {
          width: 16px;
          min-width: 16px;
          height: 16px;
          margin-right: 3px;
          cursor: pointer;
        }
      }
    }
  }
  // 预约医生
  .reserve-doctor-box {
    display: flex;
    align-items: center;
    .avatar {
      width: 40px;
      min-width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
    }
    .doctor-name {
      margin-left: 12px;
      height: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
    }
    .doctor-tag {
      border: 1px solid #155bd4;
      min-width: fit-content;
      padding: 0 4px;
      height: 16px;
      margin-left: 4px;
      border-radius: 2px;
      font-weight: 400;
      font-size: 11px;
      color: #155bd4;
      line-height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  // 预约服务
  .reserve-service-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .reserve-service-left {
      .service-info {
        display: flex;
        align-items: center;
        .service-name {
          font-weight: 500;
          font-size: 15px;
          color: #333333;
          line-height: 22px;
        }
        .service-minute {
          height: 16px;
          min-width: fit-content;
          padding: 0 4px;
          margin-left: 4px;
          font-weight: 400;
          font-size: 11px;
          color: #999999;
          line-height: 16px;
          background: #f5f6f8;
          border-radius: 2px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .physical-info {
        margin-top: 12px;
        display: flex;
        align-items: center;
        .avatar {
          border-radius: 50%;
          width: 20px;
          min-width: 20px;
          height: 20px;
          object-fit: cover;
        }
        .physical-name {
          margin-left: 4px;
          font-weight: 400;
          font-size: 12px;
          color: #444444;
          line-height: 18px;
        }
        .physical-tag {
          border: 1px solid #155bd4;
          min-width: fit-content;
          padding: 0 4px;
          height: 16px;
          margin-left: 4px;
          border-radius: 2px;
          font-weight: 400;
          font-size: 11px;
          color: #155bd4;
          line-height: 16px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .physical-level {
          margin-left: 4px;
          min-width: 20px;
          height: 16px;
          border-radius: 2px;
          border: 1px solid #dcdfe6;
          font-weight: 400;
          font-size: 11px;
          color: #666666;
          line-height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .reserve-service-right {
      margin-left: 20px;
      margin-right: 20px;
      display: flex;
      align-items: center;
      .service-price {
        font-weight: 600;
        font-size: 16px;
        color: #ee3838;
        line-height: 22px;
      }
    }
  }
}

.lv-item {
  width: 100%;
  display: flex;
  align-items: center;
  min-width: 100px;
  margin-bottom: 20px;
  .lv-label {
    width: 92px;
    min-width: fit-content;
    font-weight: 400;
    font-size: 13px;
    color: #999999;
    line-height: 20px;
    text-align: right;
  }
  .lv-value {
    font-weight: 400;
    font-size: 13px;
    color: #333333;
    line-height: 20px;
    flex: 1;
  }
  .lv-value-bg {
    padding: 11px 12px;
    background: #f9fafb;
    border-radius: 4px;
  }
}
.tip {
  font-weight: 400;
  font-size: 13px;
  color: #999999 !important;
  line-height: 20px;
}
</style>
