const configFiles = require.context('./', true, /\.js$/);
const configs = configFiles.keys().reduce((configs, configPath) => {
  const fileName = configPath.replace(/^\.\/(.*)\.\w+$/, '$1');
  if (fileName == 'index') {
    return configs;
  }
  const value = configFiles(configPath);
  configs[fileName] = value;
  return configs;
}, {});

// let env = process.env.VUE_APP_NODE_ENV === 'production'
// 	? 'prod'
// 	: 'test'
let env = process.env.VUE_APP_NODE_ENV;
// console.log(process.env.VUE_APP_NODE_ENV); // 移除构建时的日志输出
if (process.env.VUE_APP_NODE_ENV === 'production') {
  env = 'prod';
}
export default configs['index.' + env];
