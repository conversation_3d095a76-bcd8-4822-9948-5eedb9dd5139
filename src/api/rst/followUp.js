export default class FollowUp {
  /**
   * @description: 随访记录相关接口
   * */
  // 获取随访记录列表
  async getFollowRecordList(params) {
    return await this.$http.get('/clinic/follow.up.list', { params });
  }

  // 获取随访枚举
  async getFollowUpOptions() {
    return await this.$http.get('/clinic/follow.up.options');
  }

  // 获取随访历史记录列表
  async getFollowHistoryList(params) {
    return await this.$http.get('/clinic/follow.history.list', { params });
  }

  // 获取随访记录详情
  async getFollowRecordDetail(params) {
    return await this.$http.get('/clinic/follow.record.detail', { params });
  }

  // 获取随访记录统计信息
  async getFollowRecordStats(params) {
    return await this.$http.get('/clinic/follow.record.stats', { params });
  }
}
