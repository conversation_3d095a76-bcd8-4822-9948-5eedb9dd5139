export default class Warning {
  // 编辑货品是否预警
  async getStockgWarningtypedesc(params) {
    return await this.$http.get('/clinic/product.stock.getwarningtypedesc', params);
  }

  // 编辑货品是否预警
  async setStockEditWarning(params) {
    return await this.$http.post('/clinic/product.stock.editwarning', params);
  }

  // 标记正常
  async setStockIgnorestockabnormal(params) {
    return await this.$http.post('/clinic/product.stock.ignorestockabnormal', params);
  }

  // 更改动态库存预警配置
  async setSavestockconfig(params) {
    return await this.$http.post('/clinic/clinic.savestockconfig', params);
  }

  // 获取固定库存列表
  async getStockRegularstocklist(params) {
    return await this.$http.get('/clinic/product.stock.regularstocklist', { params });
  }

  // 固定库存列表重置功能
  async stockRegularstockreset(params) {
    return await this.$http.get('/clinic/product.stock.regularstockreset', { params });
  }

  // 固定库存列表保存
  async productStockBatcheditwarning(params) {
    return await this.$http.post('/clinic/product.stock.batcheditwarning', params);
  }

  // 批量修改库存预警数量
  async excelStockBatcheditwarning(params) {
    return await this.$http.post('/clinic/product.stock.batcheditwarning', params);
  }

  // 删除库存预警
  async stockDeleteregularstock(params) {
    return await this.$http.post('/clinic/product.stock.deleteregularstock', params);
  }

  // 获取动态库存预警
  async stockGetclinicstockconfig(params) {
    return await this.$http.post('/clinic/product.stock.getclinicstockconfig', params);
  }

  // 库存预警-有异常或者缺货数据个数
  async stockListnotice(params) {
    return await this.$http.get('/clinic/product.stock.listnotice', { params });
  }
}
