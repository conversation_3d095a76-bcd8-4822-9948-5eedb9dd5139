class record {
  // 获取枚举
  async getFeeOptions(params) {
    return await this.$http.get('/clinic/statisv2.fee.option', { params: params });
  }

  // 概况
  async getFeeOverview(params) {
    return await this.$http.get('/clinic/statisv2.fee.overview', { params: params });
  }

  // 创建费用
  async getFeeCreate(params) {
    return await this.$http.post('/clinic/statisv2.fee.create', params);
  }

  // 本月费用明细
  async getFeeDetailslist(params) {
    return await this.$http.get('/clinic/statisv2.fee.detailslist', { params: params });
  }
}

export default record;
