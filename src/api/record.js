class Record {
  // 获取回溯记录详情    通用service 单独签名key
  async getBehaviorReplayDetail(params) {
    return await this.$http.get('/services/behavior.replay.details', {
      params: {
        ...params,
        is_rrweb_service: 1
      }
    });
  }

  // 获取回溯记录枚举   通用service 单独签名key
  async getBehaviorReplayOptions(params) {
    return await this.$http.get('/services/behavior.replay.options', {
      params: {
        ...params,
        is_rrweb_service: 1
      }
    });
  }

  // 创建回溯记录
  async createBehaviorReplay(params) {
    return await this.$http.post('/services/behavior.replay.create', params);
  }

  // 上传回溯记录
  async saveBehaviorReplay(params) {
    console.log('%c=>(record.js:29) params', 'font-size: 18px;color: #FF7043;', params);
    return await this.$http.post('/services/behavior.replay.save', params);
  }

  // 获取回放分页数据
  async getBehaviorScope(params) {
    return await this.$http.get('/services/behavior.replay.scope', {
      params: {
        ...params,
        is_rrweb_service: 1
      }
    });
  }

  /**
   * @description 改版rrweb接口
   * */

  // 获取回溯记录列表   通用service 单独签名key
  async getBehaviorSessionList(params) {
    return await this.$http.get('/services/behavior.replay.sessionlist', {params});
  }

  // 创建追踪记录
  async setBehaviorTraceCreate(params) {
    return await this.$http.post('/services/behavior.trace.create', params);
  }

  // 获取回放列表
  async getBehaviorReplayList(params) {
    return await this.$http.get('/services/behavior.replay.list', { params });
  }

  // 获取回放事件
  async getBehaviorReplayDetail(params) {
    return await this.$http.get('/services/behavior.replay.detail', { params });
  }

}

export default Record;
