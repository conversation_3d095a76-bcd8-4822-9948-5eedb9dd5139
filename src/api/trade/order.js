class Order_methods {
  async getOrderOptions(params) {
    return await this.$http.get('/clinic/order.order.options', { params });
  }

  async getOrderList(params) {
    return await this.$http.get('/clinic/order.order.list', { params });
  }

  /*
   * params {
   *  order_id: '', String required
   * */
  async getHisOrderDetail(params) {
    return await this.$http.get('/clinic/order.order.hisdetail', { params });
  }

  /*
   * clinic_id	    非必填	    医院id
   * orderid	    必填	    订单id
   * */
  async getShopOrderDetail(params) {
    return await this.$http.get('/clinic/order.order.detail', { params });
  }

  async getStaffList() {
    return await this.$http.get('/clinic/member.stafflist');
  }

  async editStaff(params) {
    return await this.$http.post('/clinic/order.order.relatestaff', params);
  }

  async downloadOrderExcel(params) {
    return await this.$http.get('/clinic/order.report.getclinicorderlisturl', { params });
  }

  async changeSelfAutoPay(params) {
    return await this.$http.get('/clinic/pms.goodssku.selfautopay', { params });
  }

  // 获取支付枚举
  async getOrderPayOptions(params) {
    return await this.$http.get('/clinic/order.pay.options', { params });
  }

  // 检测优惠券
  async getOrderPayCheckcoupon(params) {
    return await this.$http.get('/clinic/order.pay.checkcoupon', { params });
  }

  // 取消(关闭)订单
  async getOrderClose(params) {
    return await this.$http.post('/clinic/order.order.close', params);
  }

  // 获取订单发货信息
  async getOrderDeliveryinfo(params) {
    return await this.$http.get('/clinic/order.order.deliveryinfo', { params });
  }

  // 修改订单发货信息
  async orderModifydeliver(params) {
    return await this.$http.post('/clinic/order.order.modifydeliver', params);
  }

  // 获取直营诊所退款商品明细
  async getDirectRefundGoods(params) {
    return await this.$http.get('/clinic/order.order.directClinicRefundInfo', { params });
  }

  // 获取榕树堂诊所退款商品明细
  async getRstRefundGoods(params) {
    return await this.$http.get('/clinic/order.order.rstClinicRefundInfo', { params });
  }

  // 获取诊所或者his收费订单打印
  async getOrderprint(params) {
    return await this.$http.get('/clinic/order.order.orderprint', { params });
  }

  // 获取订单医保支付的药详情
  async getYBOrderDetail(params) {
    return await this.$http.get('/clinic/order.order.getyborderdetail', { params });
  }

  // 获取订单可发劵列表
  async getOrdercansendcard(params) {
    return await this.$http.get('/clinic/order.order.getordercansendcard', { params });
  }

  // 订单发放卡劵
  async sendcouponbyorder(params) {
    return await this.$http.get('/clinic/order.order.sendcouponbyorder', { params });
  }

  // 订单历史发放记录
  async getordersendhistory(params) {
    return await this.$http.get('/clinic/order.order.getordersendhistory', { params });
  }

  // 最新售卖列表
  async getSalesList(params) {
    return await this.$http.get('/clinic/goods.index.saleslist', { params });
  }

  // 获取平台活动列表
  async getActivitylist(params) {
    return await this.$http.get('/clinic/goods.index.activitylist', { params });
  }

  // 获取省公司活动列表
  async getActivitycomlist(params) {
    return await this.$http.get('/clinic/goods.index.activitycomlist', { params });
  }

  // 可下单服务列表
  async getSalesgoodsservice(params) {
    return await this.$http.get('/clinic/goods.index.salesgoodsservice', { params });
  }

  // 获取订单业绩明细
  async getOrderRevenueDetail(params) {
    return await this.$http.get('/clinic/order.revenue.detail', { params });
  }

  // 获取匹配分成方案
  async getOrderRevenuePickPlan(params) {
    return await this.$http.get('/clinic/order.revenue.pickplan', { params });
  }

  // 获取分成人员列表
  async getOrderRevenueMembers(params) {
    return await this.$http.get('/clinic/order.revenue.members', { params });
  }
  // 获取RSt分成人员列表
  async getOrderRstRevenueMembers(params) {
    return await this.$http.get('clinic/order.revenue.dividemembers', { params });
  }

  // 修改订单分账信息
  async updateOrderRevenue(params) {
    return await this.$http.post('/clinic/order.revenue.update', params);
  }

  /**
   * 计算订单金额
   * @param order_id 订单id String required
   * @param sp_id 促销活动id String optional
   * @param sp_item_id 促销活动子项目id String optional
   * @params sp_item_value 促销活动子项目值 String optional
   * @returns {Promise<*>}
   */
  // 计算rst订单金额
  async getRstOrderAmount(params) {
    return await this.$http.get('/clinic/order.pay.calcsalepromotionorder', { params });
  }
  // 获取订单商品快照
  async getOrderGoodsSnapShot(params) {
    return await this.$http.get('/clinic/order.order.getordergoodssnapshot', { params });
  }
  // 获取商品修改记录
  async getClinicGoodsOperationLog(params) {
    return await this.$http.get('/clinic/goods.index.operationlog', { params });
  }

  // 撤销核销卡券
  async cancelCheckIn(params) {
    return await this.$http.post('/clinic/service.card.cancelcheckin', params);
  }

  // 检测体验优惠券是否可用
  async getOrderPayCheckorderusecoupon(params) {
    return await this.$http.post('/clinic/order.pay.checkorderusecoupon', params);
  }
}

export default Order_methods;
