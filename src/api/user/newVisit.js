class NewVisit {
  // 获取看板列表
  async getVisitPanelV2(params) {
    return await this.$http.get('/clinic/follow.up.panelList', { params });
  }
  // 看板统计
  async getVisitCountListV2(params) {
    return await this.$http.get('/clinic/follow.up.countList', { params });
  }
  // 获取options
  async getVisitOptionsV2(params) {
    return await this.$http.get('/clinic/follow.up.options', { params });
  }
  // 患者搜索
  async getVisitSearchPatientList(params) {
    return await this.$http.get('/clinic/follow.up.searchPatient', { params });
  }
  // 员工搜索
  async getVisitSearchStaffList(params) {
    return await this.$http.get('/clinic/follow.up.searchMember', { params });
  }
  // 获取随访详情
  async getVisitDetailV2(params) {
    return await this.$http.get('/clinic/follow.up.detail', { params });
  }
  // 获取随访记录
  async getVisitRecordV2(params) {
    return await this.$http.get('/clinic/follow.up.records', { params });
  }
  // 创建常规随访
  async createFollowUpV2(params) {
    return await this.$http.post('/clinic/follow.up.create', params);
  }
  // 完成随访
  async followUpFinished(params) {
    return await this.$http.post('/clinic/follow.up.finished', params);
  }
}

export default NewVisit;
