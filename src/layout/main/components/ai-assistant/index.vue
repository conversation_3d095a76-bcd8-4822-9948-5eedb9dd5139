<template>
  <div class="">
    <div class="ai-assistant" @click="handleShowModal">
      <!-- <svg-icon name="" size="18"></svg-icon> -->
      <svg-icon name="AI-assistant-logo" style="width: 69px; height: 18px"></svg-icon>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ai-assistant',
  data() {
    return {};
  },
  computed: {
    showAssistantModal: {
      get() {
        return this.$store.state.app.assistantModalVisible;
      },
      set(value) {
        this.$store.commit('app/SET_ASSISTANT_MODAL_VISIBLE', value);
      },
    },
    isHalfScreen: {
      get() {
        return this.$store.state.app.assistantModalHalfScreen;
      },
      set(value) {
        this.$store.commit('app/SET_ASSISTANT_MODAL_HALF_SCREEN', value);
      },
    },
  },
  mounted() {
    // window.addEventListener('keydown', this.handleKeyDown);
  },
  beforeDestroy() {
    // window.removeEventListener('keydown', this.handleKeyDown);
  },
  methods: {
    handleShowModal() {
      this.$store.dispatch('app/showAssistantModal');
    },
    // handleKeyDown(event) {
    //   if (this.showAssistantModal && event.ctrlKey && event.shiftKey && event.key.toLowerCase() === 'h') {
    //     this.$store.dispatch('app/toggleAssistantModalHalfScreen');
    //   }
    // },
  },
};
</script>

<style scoped lang="less">
.ai-assistant {
  display: flex;
  align-items: center;
  padding: 7px 12px;
  border-radius: 16px;
  cursor: pointer;
  margin-right: 12px;
  background: linear-gradient(315deg, #155bd4 0%, #7166ff 100%);
  border-image: linear-gradient(315deg, rgba(21, 91, 212, 1), rgba(113, 102, 255, 1)) 1 1;
  &:hover {
    background: linear-gradient(315deg, #3a7bdd 0%, #a79aff 100%);
    border-image: linear-gradient(315deg, rgba(58, 123, 221, 1), rgba(167, 154, 255, 1)) 1 1;
  }
  > img:first-child {
    width: 12px;
    height: 12px;
    margin-right: 4px;
  }
  > img:last-child {
    width: 69px;
    height: 16px;
  }
  > div {
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
