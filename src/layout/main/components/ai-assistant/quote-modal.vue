<template>
  <transition name="slide-fade">
    <div class="quote-modal" v-show="modelVisible" @click.stop>
      <div class="quote-header">
        <div class="quote-title">引用来源（{{ quoteList.length }}）</div>
        <div class="quote-close" @click="closeQuoteModal">
          <svg-icon name="AI-modal-close" :size="12"></svg-icon>
        </div>
      </div>
      <div class="quote-content" ref="quoteContent">
        <div v-if="quoteList && quoteList.length > 0">
          <div class="quote-item" v-for="quote in quoteList" :key="quote.index" @click="handleQuoteClick(quote)">
            <div class="quote-item-header">{{ quote.title }}</div>
            <div class="quote-item-content">
              {{ quote.main_text }}
            </div>
            <div class="quote-item-footer">
              <div class="quote-item-header-source">
                <img
                  loading="lazy"
                  :src="quote.site_icon || defaultSiteImg"
                  @error="$event.target.src = defaultSiteImg"
                />
                <div>{{ quote.site_name }}</div>
              </div>
              <div class="quote-item-header-count">{{ quote.index }}</div>
            </div>
          </div>
        </div>
        <div v-else class="empty-quote">
          <div class="empty-text">暂无引用数据</div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import defaultSiteImg from '@/assets/image/ai/site_img.png';

export default {
  name: 'quote-modal',
  props: {
    isHalfScreen: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    messageId: {
      type: String,
      default: '',
    },
    quoteList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    modelVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        console.log('val: ', val);
        this.$emit('update:visible', val);
      },
    },
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.$refs.quoteContent.scrollTop = 0;
          });
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      defaultSiteImg,
    };
  },
  methods: {
    closeQuoteModal() {
      this.$emit('update:visible', false);
      this.$emit('update:messageId', '');
    },
    handleQuoteClick(quote) {
      if (quote.url) {
        window.open(quote.url, '_blank');
      }
    },
  },
};
</script>

<style scoped lang="less">
.quote-modal {
  width: 310px;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #f5f6f8;
  box-shadow: 0 2px 12px 0 rgba(48, 49, 51, 0.16);
  border-radius: 10px 0 0 10px;
  z-index: 301;
  .quote-header {
    width: 100%;
    height: 59px;
    padding: 18px 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ecedf0;
    .quote-title {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      margin-right: auto;
    }
    .quote-close {
      color: #888888;
      cursor: pointer;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .quote-close:hover {
      color: #115bd4;
    }
  }
  .quote-content {
    padding: 8px;
    overflow-y: auto;
    height: 100%;
    padding-bottom: 80px;
    .quote-item {
      width: 100%;
      padding: 10px 12px;
      cursor: pointer;
      .quote-item-header {
        width: 100%;
        font-weight: 600;
        font-size: 14px;
        color: #333333;
        line-height: 22px;
        word-break: break-all;
        word-wrap: break-word;
        white-space: pre-wrap;
        margin-bottom: 8px;
      }
      .quote-item-content {
        width: 100%;
        height: 36px;
        font-size: 12px;
        color: #888888;
        line-height: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        margin-bottom: 8px;
      }
      .quote-item-footer {
        width: 100%;
        display: flex;
        align-items: center;
        .quote-item-header-source {
          max-width: e('calc(100% - 16px)');
          display: flex;
          align-items: center;
          margin-right: auto;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          > img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
          > div {
            font-size: 12px;
            line-height: 12px;
            color: #888888;
          }
        }
        .quote-item-header-count {
          width: 16px;
          height: 16px;
          background: #ecedf0;
          border-radius: 8px;
          line-height: 16px;
          text-align: center;
          font-size: 12px;
          color: #888888;
        }
      }
    }
    .quote-item:hover {
      background: #ffffff;
    }
  }
  .quote-content::-webkit-scrollbar {
    display: none;
  }
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}
.empty-quote {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
  .empty-text {
    font-size: 14px;
    color: #999999;
    margin-top: 10px;
  }
}
</style>
