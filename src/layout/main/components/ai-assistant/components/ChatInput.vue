<template>
  <div>
    <footer class="chat-footer">
      <div v-show="showScrollButton" class="toBottom">
        <div :class="['btn', isLoading ? 'btn-loading' : '']" @click="$bus.$emit('scrollToBottom', true)">
          <svg-icon name="ai-loading" :size="32" />
        </div>
      </div>
      <!-- <div class="shortcuts-wrap">
        <div class="shortcuts-item" @click="shortcutHandle('createUser')">
          <svg-icon name="AI-create-user" :size="14" />
          <div class="shortcuts-item-text">创建用户</div>
        </div>
        <div class="shortcuts-item" @click="handleAddShortcut('预约服务')">
          <svg-icon name="ai-serve" :size="14" />
          <div class="shortcuts-item-text">预约服务</div>
        </div>
        <div class="shortcuts-item" @click="handleAddShortcut('预约医生')">
          <svg-icon name="ai-doctor" :size="16" />
          <div class="shortcuts-item-text">预约医生</div>
        </div>
        <div class="shortcuts-item" @click="handleAddShortcut('创建患者')">
          <svg-icon name="ai-patient" :size="14" />
          <div class="shortcuts-item-text">创建患者</div>
        </div>
      </div> -->
      <div class="input-wrap" :class="{ active: focusInput }" @click="$refs.inputRef?.focus()">
        <div class="ai-input">
          <Input
            class="input"
            ref="inputRef"
            v-model="message"
            type="textarea"
            placeholder="输入你的问题"
            :autosize="{ minRows: 1, maxRows: 5 }"
            @keydown.native="handleKeydown"
            @compositionstart.native="handleCompositionStart"
            @compositionend.native="handleCompositionEnd"
            @on-focus="focusInput = true"
            @on-blur="focusInput = false"
          />
        </div>
        <div class="action-wrap">
          <div
            v-if="!isLoading"
            :class="['action-send', { ready: message.length > 0, disabled: message.length === 0 }]"
            @click="handleSend"
          >
            <svg-icon class="ai-send" name="ai-send" :size="32" />
          </div>
          <div v-if="isLoading" :class="['action-send', 'ready']" @click="handleStop">
            <svg-icon class="ai-send" name="ai-pause" :size="32" />
          </div>
        </div>
      </div>
    </footer>
    <!-- <BottomCreateUserCard :visible="showCreateUserCard" @close="showCreateUserCard = false" /> -->
  </div>
</template>
<script>
import { throttle  } from 'lodash-es';

export default {
  name: 'ChatInput',
  components: {},
  props: {
    isLoading: {
      type: Boolean,
      default: false,
    },
    showScrollButton: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      message: '',
      isComposing: false,
      enableDeepThinking: true, // 保留用于发送消息时使用
      inputRef: null,
      focusInput: false,
      showCreateUserCard: false,
    };
  },
  mounted() {
    this.$bus.$on('sendMessage', this.handSendCardData);
    this.$nextTick(() => {
      this.$refs.inputRef?.focus();

      // 从sessionStorage中读取设置
      const savedDeepThinking = sessionStorage.getItem('enableDeepThinking');

      if (savedDeepThinking) {
        this.enableDeepThinking = savedDeepThinking === 'true';
      }
    });
  },
  beforeDestroy() {
    this.$bus.$off('sendMessage', this.handSendCardData);
  },
  methods: {
    focusInputHandler() {
      this.$nextTick(() => {
        this.$refs.inputRef?.focus();
      });
    },
    shortcutHandle(name) {
      if (name === 'createUser') {
        this.createUser();
      }
    },
    createUser() {
      this.showCreateUserCard = true;
    },

    handSendCardData(cardData) {
      if (this.isLoading) {
        return;
      }
      this.$emit('send', '', this.enableDeepThinking, true, cardData);
    },
    handleSend() {
      console.log('this.isLoading: ', this.isLoading);
      if (this.isLoading) {
        this.$Message.warning('回答输出中，请稍后操作或点击停止回答');
        return;
      }

      if (!this.message.trim()) return;
      this.message = this.message.trim();
      this.$emit('send', this.message, this.enableDeepThinking);
      this.message = '';
    },
    handleStop: throttle(function () {
      if (this.isLoading) {
        this.$emit('stop');
      }
    }, 1000),
    handleKeydown(e) {
      if (e.key === 'Enter' && !e.shiftKey && !this.isComposing) {
        e.preventDefault();
        this.handleSend();
      }
    },
    handleCompositionStart() {
      this.isComposing = true;
    },
    handleCompositionEnd() {
      this.isComposing = false;
    },
    handleAddShortcut(text) {
      this.message += '@' + text + ' ';
      this.$nextTick(() => {
        this.$refs.inputRef?.focus();
      });
    },
    // 外部设置消息内容
    setMessage(message) {
      this.message = message;
      this.$nextTick(() => {
        this.$refs.inputRef?.focus();
      });
    },
  },
};
</script>

<style scoped lang="less">
.chat-footer {
  width: 100%;
  border-radius: 10px;
  position: relative;
  background: #ffffff;

  .toBottom {
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: -36px;
    left: 50%;
    transform: translateX(-50%);

    .btn {
      width: 32px;
      height: 32px;
      color: #888888;
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: #ffffff;
      box-shadow: 0 2px 12px 0 rgba(48, 49, 51, 0.16);

      :deep(svg) {
        position: relative;
        z-index: 1;
      }
    }

    .btn:hover {
      color: #000;
    }

    .btn-loading::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: conic-gradient(
        from 0deg,
        #f5f6f8 0%,
        #679de1 20%,
        #6299e0 40%,
        #4a85dc 60%,
        #3370d8 80%,
        #115bd4 100%
      );
      animation: loading-rotate 1s linear infinite;
      z-index: 0;
    }

    @keyframes loading-rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }

  .shortcuts-wrap {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 8px;
    box-shadow: 0 -16px 12px 0 rgba(255, 255, 255, 1);
    // box-shadow: 0 -2px 1px 0 rgba(48, 49, 51, 0.16);
    .shortcuts-item {
      width: 84px;
      height: 28px;
      background: #f5f6f8;
      border-radius: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #333;
      margin-right: 8px;

      .shortcuts-item-text {
        margin-left: 4px;
        font-size: 12px;
        line-height: 1;
      }
    }

    .shortcuts-item:last-child {
      margin-right: 0;
    }

    .shortcuts-item:hover {
      background-color: #ecedf0;
    }
  }

  .input-wrap {
    width: e('calc(100% - 32px)');
    margin: 0 16px;
    padding: 12px;
    background: #ffffff;
    // box-shadow: 0 4px 8px 0 rgba(21, 91, 212, 0.08);
    border-radius: 10px;
    border: 1px solid #ecedf0;
    transition: all 0.1s;
    // box-shadow: 0 -16px 12px 0 rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 1px 0px #eaeaea;
    cursor: text;
    margin-bottom: 2px;
    &:hover {
      border-color: #ccc;
    }

    &.active {
      border-color: #115bd4;
      outline: 1px solid #115bd4;
    }

    .ai-input {
      width: 100%;
      height: fit-content;
      display: flex;

      .input {
        max-width: 100%;
        color: #bbbbbb;
        line-height: 1.5;
      }

      :deep(.ivu-input) {
        min-height: 48px;
        max-height: 144px; // 6行的高度限制
        border: unset;
        resize: none;
        padding: 0;
        font-size: 14px;
        line-height: 24px;
      }

      :deep(.ivu-input::-webkit-scrollbar) {
        width: 0;
        height: 0;
      }

      :deep(.ivu-input:focus) {
        border: unset;
        box-shadow: unset;
        resize: none;
      }
    }

    .action-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .action-send {
        cursor: pointer;
        display: flex;
        align-items: center;
        color: #dcdde0;
      }

      .action-send.ready {
        color: #155bd4 !important;

        &:hover {
          color: #3370d8 !important;
        }
      }

      .action-send.disabled {
        color: #dcdde0 !important;
        cursor: not-allowed;
      }
    }
  }
}

</style>
