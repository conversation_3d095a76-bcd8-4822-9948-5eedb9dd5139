<template>
  <div :class="[disabled ? '' : 'glow-text', customClass]" :data-text="text" :style="combinedStyle">
    {{ text }}
  </div>
</template>

<script>
export default {
  name: 'GlowText',
  props: {
    // 显示的文字内容
    text: {
      type: String,
      required: true,
      default: '流光文字',
    },
    // 流光颜色 (带透明度)
    glowColor: {
      type: String,
      default: 'rgb(25, 116, 210)',
    },
    // 动画持续时间(秒)
    duration: {
      type: Number,
      default: 3,
    },
    gradient: {
      type: String,
      default: 'linear-gradient(120deg, #bad5fa 0%, #2a6de8 25%, #c30376 50%, #115bd4 75%, #a3caff 100%)',
    },
    // 文字粗细
    fontWeight: {
      type: String,
      default: 'bold',
    },
    // 动画方向 ('ltr': 从左到右, 'rtl': 从右到左)
    direction: {
      type: String,
      default: 'ltr',
      validator: value => ['ltr', 'rtl'].includes(value),
    },
    // 自定义CSS类
    customClass: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // 计算最终的内联样式
    combinedStyle() {
      return {
        fontWeight: this.fontWeight,
        '--glow-color': this.glowColor,
        '--animation-duration': `${this.duration}s`,
        '--direction': this.direction === 'ltr' ? 'reverse' : 'normal',
      };
    },
  },
};
</script>

<style scoped>
.glow-text {
  position: relative;
}

.glow-text::before {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: v-bind(gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 200% 100%;
  animation: shine var(--animation-duration) linear infinite;
  animation-direction: var(--direction);
  z-index: 2;
  mix-blend-mode: screen;
}

@keyframes shine {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
