<template>
  <div class="shimmer-wrapper">
    <span
      :class="{ 'shimmer-text': !disabled }"
      :style="{
        '--base-color': baseColor,
        '--shimmer-color': shimmerColor,
        '--duration': duration,
      }"
    >
      {{ text }}
    </span>
  </div>
</template>

<script>
export default {
  name: 'ShimmerText',
  props: {
    text: {
      type: String,
      required: true,
    },
    shimmerColor: {
      type: String,
      default: 'rgba(255, 255, 255, 0.5)',
    },
    baseColor: {
      type: String,
      default: '#7e7d7d',
    },
    duration: {
      type: String,
      default: '3s',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped>
.shimmer-wrapper {
  display: inline-block;
  position: relative;
  color: transparent;
}

.shimmer-text {
  display: inline-block;
  position: relative;
  background: linear-gradient(
    120deg,
    var(--base-color) 0%,
    var(--base-color) 45%,
    var(--shimmer-color) 50%,
    var(--base-color) 55%,
    var(--base-color) 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer var(--duration) infinite linear;
}

/* 窄光带的动画 */
@keyframes shimmer {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -0% 0;
  }
}
</style>
