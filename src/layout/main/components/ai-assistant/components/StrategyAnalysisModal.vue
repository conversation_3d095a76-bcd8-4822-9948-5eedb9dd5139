<template>
  <div>
    <transition name="strategy-modal-bg-fade">
      <div class="strategy-analysis-wrap" v-if="visible" @click="handleMaskClick">
        <div class="strategy-analysis-modal" @click.stop>
          <div class="strategy-modal-content">
            <div class="header">
              <div class="title">工具调用策略分析</div>
              <div class="close-btn" @click="handleClose">
                <svg-icon name="AI-modal-close" :size="12" />
              </div>
            </div>
            <div class="content">
              <div class="analysis-content">
                <MarkdownRender v-if="analysisContent" :message="{ content: analysisContent }" :isResponding="false" />
                <div v-else class="no-content">暂无分析内容</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import MarkdownRender from './MarkdownRender.vue';

export default {
  name: 'StrategyAnalysisModal',
  components: {
    MarkdownRender,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    analysisContent: {
      type: String,
      default: '',
    },
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        // 弹窗打开时添加键盘事件监听
        document.addEventListener('keydown', this.handleKeydown);
      } else {
        // 弹窗关闭时移除键盘事件监听
        document.removeEventListener('keydown', this.handleKeydown);
      }
    },
  },

  beforeDestroy() {
    // 组件销毁时确保移除事件监听
    document.removeEventListener('keydown', this.handleKeydown);
  },

  methods: {
    handleClose() {
      this.$emit('update:visible', false);
    },
    handleMaskClick() {
      // 点击遮罩层关闭弹窗
      this.handleClose();
    },
    handleKeydown(event) {
      // ESC键关闭弹窗
      if (event.key === 'Escape' || event.keyCode === 27) {
        this.handleClose();
      }
    },
  },
};
</script>

<style lang="less" scoped>
// 遮罩层样式
.strategy-analysis-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  // backdrop-filter: blur(10px);
  z-index: 1500; // 确保盖住助手弹窗
  display: flex;
  align-items: center;
  justify-content: center;
}

// 弹窗本体样式 - 与助手弹窗尺寸一致
.strategy-analysis-modal {
  width: 70%;
  min-width: 700px;
  max-width: 840px;
  height: calc(~'100vh - 60px');
  margin-top: 30px;
  margin-bottom: 30px;
  background: #fff;
  // box-shadow: 0 2px 24px 0 rgba(48, 49, 51, 0.18);
  border-radius: 6px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.strategy-modal-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  padding: 0 16px;
  display: flex;
  min-height: 56px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ecedf0;
  background: #fff;
  border-radius: 6px 6px 0 0;
  z-index: 1;
  flex-shrink: 0;

  .title {
    font-size: 15px;
    font-weight: 500;
    color: #333;
  }

  .close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;

    &:hover {
      color: #333;
      background: #f5f6f8;
    }
  }
}

.content {
  width: 100%;
  height: auto;
  overflow: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.analysis-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px 40px;
  height: 100%;

  .no-content {
    text-align: center;
    color: #999;
    padding: 40px 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}

// 过渡动画
.strategy-modal-bg-fade-enter-active,
.strategy-modal-bg-fade-leave-active {
  transition: opacity 0.3s ease;
}

.strategy-modal-bg-fade-enter,
.strategy-modal-bg-fade-leave-to {
  opacity: 0;
}

.strategy-modal-bg-fade-enter-active .strategy-analysis-modal,
.strategy-modal-bg-fade-leave-active .strategy-analysis-modal {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.strategy-modal-bg-fade-enter .strategy-analysis-modal,
.strategy-modal-bg-fade-leave-to .strategy-analysis-modal {
  transform: scale(0.8);
  opacity: 0;
}
</style>
