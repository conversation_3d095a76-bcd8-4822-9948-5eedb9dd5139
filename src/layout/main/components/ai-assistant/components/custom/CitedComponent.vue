<template>
  <Poptip
    placement="top"
    :visible-arrow="false"
    :open-delay="100"
    theme="light"
    transfer-class-name="citation-tooltip"
    :max-width="350"
    ref="tooltipRef"
    transfer
    trigger="click"
    :reference="reference"
  >
    <template #content>
      <div @click="handleOpenLink">
        <div class="content-basic">
          <img
            v-if="getResult?.site_icon"
            class="site-icon"
            :src="getResult?.site_icon"
            @error="$event.target.src = defaultIcon"
            alt=""
          />
          <span class="title-text">
            <span class="line-clamp-1" style="color: #333"> {{ getResult?.index }}.{{ getResult?.title }} </span>
          </span>
        </div>
        <div class="content">
          <span class="line-clamp-2">
            {{ getResult?.summary }}
          </span>
        </div>
        <div class="content-footer">
          <div class="link-url">
            <svg-icon name="link"></svg-icon>
            网页
            <Divider type="vertical"></Divider>
            {{ getResult?.site_name || getUrlDomain }}
          </div>
          <span class="date">{{ getResDate }}</span>
        </div>
      </div>
    </template>
  </Poptip>
</template>

<script>
export default {
  name: 'CitedComponent',
  components: {},
  props: {
    searchResults: {
      type: Object,
      default: () => {},
    },
    reference: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      showTooltip: false,
      citationRef: null,
      tooltipInstance: null,
      defaultIcon: require('@/assets/image/ai/site-icon.png'),
    };
  },
  computed: {
    getResult() {
      return this.searchResults;
    },
    getResDate() {
      return this.$moment(this.getResult?.date_last_crawled).format('YYYY-MM-DD');
    },
  },
  methods: {
    show() {
      if (this.$refs.tooltipRef) {
        // 先销毁之前的tooltip，然后重新显示
        setTimeout(() => {
          this.$refs.tooltipRef.visible = true;
        }, 10);
      }
    },
    // 判断鼠标
    doClose() {
      console.log('doClose');
      // this.$refs.tooltipRef.doDestroy();
      this.$refs.tooltipRef.visible = false;
    },
    getUrlDomain(url) {
      if (!url) return '';
      try {
        const parsedUrl = new URL(url);
        return parsedUrl.hostname;
      } catch (error) {
        console.error('Invalid URL:', url);
        return '';
      }
    },
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString();
    },
    handleOpenLink() {
      window.open(this.getResult?.url, '_blank');
    },
  },
  beforeDestroy() {
    // this.unmountTooltip();
    console.log(this.componentId, 'beforeDestroy');
  },
};
</script>

<style scoped lang="less">
.citation {
  vertical-align: middle;
  font-variant: tabular-nums;
  box-sizing: border-box;
  color: #888888;
  cursor: pointer;
  background: #ecedf0;
  border-radius: 9px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  height: 16px;
  margin-left: 4px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: 400;
  display: inline-flex;
  top: -2px;
}
// :deep(.citation) {
//   vertical-align: middle;
//   font-variant: tabular-nums;
//   box-sizing: border-box;
//   color: #404040;
//   cursor: pointer;
//   background: #e5e5e5;
//   border-radius: 9px;
//   flex-shrink: 0;
//   justify-content: center;
//   align-items: center;
//   height: 16px;
//   margin-left: 4px;
//   padding: 0 6px;
//   font-size: 12px;
//   font-weight: 400;
//   display: inline-flex;
//   position: relative;
//   top: -2px;
// }
.citation:hover:not(.citation--loading) {
  background: #dcdde0;
  color: #333333;
  /* color: #1976d2; */
}

.citation--loading {
  cursor: default;
  opacity: 0.7;
}

.citation[data-active='true']:not(.citation--loading) {
  background: rgba(25, 118, 210, 0.2);
  /* color: #1976d2; */
}
</style>
<style lang="less">
.citation-tooltip {
  width: 350px;
  .ivu-poptip-arrow {
    display: none !important;
  }
  .ivu-poptip-content {
    cursor: pointer;
  }
  .ivu-poptip-inner {
    background-color: #fff;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 16px !important;
  }
  .ivu-tooltip-arrow {
    display: none !important;
  }
  .content-basic {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    .site-icon {
      display: block;
      width: 16px;
    }
    .title-text {
      font-weight: 500;
      color: #333;
      flex: 1;
      flex-basis: 0;
      min-width: 0;
    }
  }

  .date {
    opacity: 0.7;
    font-size: 12px;
    margin-left: auto;
  }

  .content {
    font-size: 13px;
    line-height: 1.6;
    color: #333;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-word;
    min-height: 40px;
    padding: 10px 0;
  }
  .content-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    padding-top: 12px;
    .link-url {
      display: inline-flex;
      align-items: center;
    }
  }
  .citation-tooltip[style] {
    opacity: 1;
  }
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 32px;
    white-space: normal;
  }
}
</style>
