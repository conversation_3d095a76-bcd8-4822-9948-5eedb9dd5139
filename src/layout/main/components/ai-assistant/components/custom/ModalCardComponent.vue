<template>
  <div class="modal-card">
    <div class="card-wrap">
      <div class="card-header">
        <div class="header-title">{{ cardName }}</div>
        <div v-if="cardStatus === EDIT_STATUS" class="header-status">待完善</div>
        <div v-else class="header-status done" @click="toDetail">查看详情</div>
      </div>
      <div class="card-content">
        <div class="card-item" v-for="(value, key) in cardMap" :key="key">
          <div class="label" :style="{ width: maxLabelWidth }">{{ cardMap[key] }}</div>
          <div class="text" :style="{ width: 'calc(100% - maxLabelWidth)' }">{{ validParam[key] || '-' }}</div>
        </div>
      </div>
      <div class="card-footer">
        <div
          :class="['card-footer-btn', cardStatus !== EDIT_STATUS && 'done']"
          @click="cardStatus === EDIT_STATUS && handleAdd()"
        >
          <svg-icon v-if="cardStatus === EDIT_STATUS" name="ai-add" :size="12" style="margin-right: 4px" />
          <svg-icon v-else name="ai-done-2" :size="12" style="margin-right: 4px" />
          {{ cardStatus === EDIT_STATUS ? cardName : '已完成' }}
        </div>
      </div>
    </div>

    <!-- 创建用户 -->
    <create-user-modal
      :showCreateInfo="() => {}"
      :visible.sync="dialogVisible"
      :level-list="levelList"
      :name="validParam.name"
      :quickCreate="true"
      :quickCreateInfo="{
        ...validParam,
        gender: validParam.gender === '男' ? '1' : '2',
      }"
      @success="addUserSuccess"
      :extra-loading="submitLoading"
    ></create-user-modal>

    <!-- 添加预约 -->
    <add-reserve-modal
      v-model="addReserveVisible"
      @success="addReserveSuccess"
      :row="currentRow"
      :uid="validParam.uid || '14'"
      :type="reserve_type"
    ></add-reserve-modal>
  </div>
</template>

<script>
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';
import { assistantService } from '@/libs/SSE/SSEService';
import router from '@/router';
import addReserveModal from '@/view/reserve/listing/components/addReserveModal.vue';
import moment from 'moment';

export default {
  name: 'ModalCardComponent',
  components: { addReserveModal, CreateUserModal },
  props: {
    cardName: {
      type: String,
      required: true,
    },
    parameters: {
      type: Object,
      default: () => ({}),
    },
    message: {
      type: Object,
      required: true,
      default: () => ({ id: '', content: '', search_results: [] }),
    },
  },
  data() {
    return {
      EDIT_STATUS: '编辑并提交',
      cardStatus: '',
      validParam: {},
      submitLoading: false,
      dialogVisible: false,
      addReserveVisible: false,
      currentRow: {},
      reserve_type: '1',
      maxLabelWidth: 'auto',
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],
      cardMap: {},
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.calculateMaxLabelWidth();
    });
  },
  watch: {
    parameters: {
      handler(val) {
        const copyVal = JSON.parse(JSON.stringify(val));
        console.log('%c [ copyVal ]-52', 'font-size:13px; background:pink; color:#bf2c9f;', copyVal, this.$props);
        if (Object.keys(val).length > 0) {
          for (const key in copyVal) {
            if (key === '__sort') continue;
            if (key === '__card_status') {
              this.cardStatus = copyVal[key];
              console.log('%c [ cardStatus ]-59', 'font-size:13px; background:pink; color:#bf2c9f;', this.cardStatus);
            } else {
              this.validParam[key] = copyVal[key];
            }
          }
          this.$bus.$emit('scrollToBottom', false);
        }
      },
      immediate: true,
      deep: true,
    },
    cardName: {
      handler(val) {
        if (val === '创建用户') {
          this.cardMap = {
            uid: '用户ID',
            name: '姓名',
            mobile: '手机号',
            gender: '性别',
            birthday: '生日',
          };
        }
        if (val === '创建预约理疗师') {
          this.cardMap = {
            id: '预约ID',
            name: '姓名',
            mobile: '手机号',
            service_name: '预约项目',
            technician_name: '服务理疗师',
          };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleAdd() {
      if (this.cardName === '创建用户') {
        this.dialogVisible = true;
      }
      if (this.cardName === '创建预约理疗师') {
        this.addReserve('1');
      }
    },
    /**
     * val 1:服务 2:医生
     * */
    addReserve(val) {
      console.log(val, this.validParam, 'val');
      this.reserve_type = val;
      this.currentRow = this.validParam;
      this.addReserveVisible = true;
    },
    toDetail() {
      if (this.cardName === '创建用户') {
        const uid = this.validParam.uid;
        if (!uid) return;
        const path = '/user/detail';
        this.openNewPage(path, { uid });
      }
      if (this.cardName === '创建预约理疗师') {
        console.log(this.validParam, 'this.validParam');
        const id = this.validParam.id;
        if (!id) return;
        const path = '/user/detail';
        this.openNewPage(path, { id, type: 'PHYSIO' });
      }
    },
    openNewPage(path, query) {
      const href = router.resolve({
        path,
        query,
      }).href;
      window.open(href, '_blank');
    },
    calculateMaxLabelWidth() {
      const labels = this.$el.querySelectorAll('.card-item .label');
      if (labels.length === 0) return;

      let maxWidth = 0;
      labels.forEach(label => {
        const width = label.getBoundingClientRect().width;
        maxWidth = Math.max(maxWidth, Math.ceil(width));
      });

      this.maxLabelWidth = maxWidth + 'px';
    },
    async addUserSuccess(params) {
      this.submitLoading = true;
      try {
        const { message, parameters, cardName } = this.$props;
        const sessionId = localStorage.getItem('ai_assistant_session_id');
        await assistantService.updateCardStatus({
          session_id: sessionId || '',
          card_num: +(parameters?.__sort || 1),
          message_id: message.id,
          card_type: cardName,
          card_info: JSON.stringify({
            ...params,
            name: params?.real_name || this.validParam.name,
            gender: params.sex === '1' ? '男' : '女',
          }),
        });
        this.cardStatus = '已完成';
        // this.$Message.success('提交成功');
        this.validParam = {
          ...this.validParam,
          ...params,
          name: params?.real_name || this.validParam.name,
          gender: params.sex === '1' ? '男' : '女',
        };
        this.submitLoading = false;
        this.dialogVisible = false;
        this.$bus.$emit('sendMessage', {
          card_num: +(parameters?.__sort || 1),
          card_type: cardName,
          sessionId: sessionId,
          messageId: message.id,
        });
      } catch (error) {
        console.error('更新状态失败:', error);
        // ElMessage.error('更新状态失败')
        this.submitLoading = false;
        this.dialogVisible = false;
      }
    },
    async addReserveSuccess(_, __, params) {
      console.log(params, 'params');
      this.submitLoading = true;
      try {
        const { message, parameters, cardName } = this.$props;
        const sessionId = localStorage.getItem('ai_assistant_session_id');
        const arrival_time = moment(params.reserve_time * 1000).format('YYYY-MM-DD HH:mm:ss');
        const service_name = params.services?.[0]?.goods_service?.name;
        const technician_name = params.services?.[0]?.physio?.name;
        const uid = params.reserve_user?.uid;
        await assistantService.updateCardStatus({
          session_id: sessionId || '',
          card_num: +(parameters?.__sort || 1),
          message_id: message.id,
          card_type: cardName,
          card_info: JSON.stringify({
            id: params.id,
            name: params.name,
            mobile: params.mobile,
            arrival_time,
            service_name,
            technician_name,
            uid,
          }),
        });
        this.cardStatus = '已完成';
        // this.$Message.success('提交成功');
        this.validParam = {
          ...this.validParam,
          id: params.id,
          name: params.name,
          mobile: params.mobile,
          arrival_time,
          service_name,
          technician_name,
          uid,
        };
        this.submitLoading = false;
        this.dialogVisible = false;
        this.$bus.$emit('sendMessage', {
          card_num: +(parameters?.__sort || 1),
          card_type: cardName,
          sessionId: sessionId,
          messageId: message.id,
        });
      } catch (error) {
        console.error('更新状态失败:', error);
        // ElMessage.error('更新状态失败')
        this.submitLoading = false;
        this.dialogVisible = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.modal-card {
  margin-bottom: 12px;
  max-width: 338px;
  width: 100%;
  margin-top: 16px;
  .card-wrap {
    width: 100%;
    padding: 16px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #ecedf0;
    .card-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .header-title {
        flex: 1;
        font-weight: 600;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
      }
      .header-status {
        font-size: 12px;
        line-height: 18px;
        text-align: right;
        font-style: normal;
        color: #ffaa00;
      }
      .header-status.done {
        color: #155bd4;
        cursor: pointer;
      }
      .header-status.done:hover {
        color: #3a7bdd;
      }
    }
    .card-content {
      width: 100%;
      margin: 12px 0 16px 0;
      .card-item {
        display: flex;
        margin-top: 8px;
        .label {
          font-size: 13px;
          color: #999999;
          line-height: 20px;
          text-align: right;
          flex-shrink: 0;
        }
        .text {
          height: 20px;
          padding-left: 16px;
          font-size: 13px;
          color: #333333;
          line-height: 20px;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .card-item:first-child {
        margin-top: 0;
      }
    }
    .card-footer {
      width: 100%;

      .card-footer-btn {
        width: 100%;
        height: 32px;
        background: #155bd4;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        color: #ffffff;
        cursor: pointer;
      }
      .card-footer-btn:hover {
        background: #3a7bdd;
      }
      .card-footer-btn.done {
        background: #98bcee;
        border-radius: 4px;
        cursor: not-allowed;
      }
    }
  }
  .card-wrap:hover {
    box-shadow: 0 2px 8px 0 rgba(48, 49, 51, 0.08);
  }
}

.dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
