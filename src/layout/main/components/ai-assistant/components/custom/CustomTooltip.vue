<!-- 自定义Tooltip组件 -->
<template>
  <div
    class="custom-tooltip"
    :class="{ 'is-visible': visible, 'tooltip-top': position === 'top', 'tooltip-bottom': position === 'bottom' }"
    :style="tooltipStyle"
    v-show="visible"
    ref="tooltipRef"
  >
    <div class="custom-tooltip-content">
      <div @click="handleOpenLink">
        <div class="content-basic">
          <img
            class="site-icon"
            :src="data?.site_icon || defaultIcon"
            @error="$event.target.src = defaultIcon"
            alt=""
          />
          <span class="title-text">
            <span class="line-clamp-1" style="color: #333"> {{ getDisplayTitle }} </span>
          </span>
        </div>
        <div class="content">
          <span class="line-clamp-2">
            {{ data?.main_text }}
          </span>
        </div>
        <div class="content-footer">
          <div class="link-url">
            <svg-icon name="link" class="mr-2"></svg-icon>
            网页
            <Divider type="vertical"></Divider>
            {{ data?.site_name || getUrlDomain(data?.url) }}
          </div>
          <span class="date">{{ formatDate(data?.date_last_crawled) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomTooltip',
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: false,
      position: 'top', // 始终朝上显示
      tooltipStyle: {
        top: '0px',
        left: '0px',
      },
      sourceElement: null,
      hideTimer: null,
      tooltipHeight: 220, // 预设高度，用于首次计算
      lastDataTitle: '',
      defaultIcon: require('@/assets/image/ai/site-icon.png'),
    };
  },
  computed: {
    // 获取显示标题：优先显示site_name，没有site_name时显示URL，最后显示标题
    getDisplayTitle() {
      if (this.data?.title) {
        // 如果没有URL但有标题，显示标题
        return this.data.title;
      } else if (this.data?.site_name) {
        // 优先显示站点名称
        return this.data.site_name;
      } else if (this.data?.url) {
        // 如果没有站点名称但有URL，显示URL
        return this.data.url;
      } else if (this.data?.index && this.data?.title) {
        // 如果都有，显示索引+标题（保持原有格式）
        return `${this.data.index}.${this.data.title}`;
      }
      return this.data?.title || '';
    },
    // 获取显示的URL：优先显示site_name，其次显示完整URL，最后显示域名
    getDisplayUrl() {
      if (this.data?.site_name) {
        return this.data.site_name;
      } else if (this.data?.url) {
        return this.data.url;
      }
      return this.getUrlDomain(this.data?.url);
    },
  },
  methods: {
    show(target) {
      if (this.visible) {
        if (this.sourceElement === target && this.data?.title === this.lastDataTitle) return;
        this.hide();
      }
      this.lastDataTitle = this.data?.title;

      if (!target || !this.data || !this.data.title || this.visible) return;

      // 先移除之前的事件监听
      this.cleanupEventListeners();

      const rect = target.getBoundingClientRect();
      // 获取在页面中的位置
      const { top, left, width } = rect;

      // 添加鼠标移入移出监听
      target.addEventListener('mouseleave', this.handleSourceMouseLeave);

      // 保存触发元素引用
      this.sourceElement = target;

      // 获取元素位置并立即设置tooltip位置
      const tooltipWidth = 350; // 预设宽度
      const tooltipHeight = this.tooltipHeight;

      // 计算上方可用空间
      const topSpace = top;
      const bottomSpace = window.innerHeight - (top + rect.height);

      // 决定朝向 - 优先朝上，但如果上方空间不足则朝下
      let tooltipTop;
      if (topSpace >= tooltipHeight + 10) {
        // 朝上显示（优先）
        tooltipTop = top - tooltipHeight - 8;
        this.position = 'top';
      } else if (bottomSpace >= tooltipHeight + 10) {
        // 朝下显示（其次）
        tooltipTop = top + rect.height + 8;
        this.position = 'bottom';
      } else {
        // 如果上下都不够，选择较大的方向
        if (topSpace > bottomSpace) {
          tooltipTop = 10; // 贴近顶部
          this.position = 'top';
        } else {
          tooltipTop = window.innerHeight - tooltipHeight - 10; // 贴近底部
          this.position = 'bottom';
        }
      }

      // 水平居中对齐
      let tooltipLeft = left + width / 2 - tooltipWidth / 2;

      // 水平方向边界检查
      if (tooltipLeft < 10) {
        tooltipLeft = 10;
      } else if (tooltipLeft + tooltipWidth > window.innerWidth - 10) {
        tooltipLeft = window.innerWidth - tooltipWidth - 10;
      }

      // 设置位置并立即显示
      this.tooltipStyle = {
        top: `${tooltipTop}px`,
        left: `${tooltipLeft}px`,
      };

      // 立即显示tooltip
      this.visible = true;

      // 在下一帧中重新测量tooltip的实际尺寸并调整位置
      this.$nextTick(() => {
        if (this.$refs.tooltipRef) {
          const tooltipRect = this.$refs.tooltipRef.getBoundingClientRect();

          // 保存实际高度供下次使用
          this.tooltipHeight = tooltipRect.height;

          // 重新评估位置和方向
          const actualTopSpace = top;
          const actualBottomSpace = window.innerHeight - (top + rect.height);

          if (this.position === 'top' && actualTopSpace < tooltipRect.height + 10) {
            // 如果原来朝上但实际高度超出了，则调整为朝下
            if (actualBottomSpace >= tooltipRect.height + 10) {
              tooltipTop = top + rect.height + 8;
              this.position = 'bottom';
            }
          } else if (this.position === 'bottom' && actualBottomSpace < tooltipRect.height + 10) {
            // 如果原来朝下但实际高度超出了，则调整为朝上
            if (actualTopSpace >= tooltipRect.height + 10) {
              tooltipTop = top - tooltipRect.height - 8;
              this.position = 'top';
            }
          } else {
            // 保持原方向，但用实际高度重新计算位置
            if (this.position === 'top') {
              tooltipTop = top - tooltipRect.height - 8;
            } else {
              tooltipTop = top + rect.height + 8;
            }
          }

          // 更新位置
          this.tooltipStyle = {
            top: `${tooltipTop}px`,
            left: `${tooltipLeft}px`,
          };

          // 添加tooltip的鼠标移入移出事件
          this.$refs.tooltipRef.addEventListener('mouseenter', this.handleTooltipMouseEnter);
          this.$refs.tooltipRef.addEventListener('mouseleave', this.handleTooltipMouseLeave);
        }
      });

      // 添加点击外部区域关闭tooltip的事件
      setTimeout(() => {
        document.addEventListener('click', this.hideOnClickOutside);
      }, 10);
    },

    handleSourceMouseLeave(event) {
      // 判断是否移入到tooltip内
      const tooltipEl = this.$refs.tooltipRef;
      if (tooltipEl) {
        const relatedTarget = event.relatedTarget;
        if (relatedTarget && (tooltipEl.contains(relatedTarget) || tooltipEl === relatedTarget)) {
          // 鼠标直接移入tooltip内，不做任何操作
          return;
        }
      }

      // 启动延迟隐藏，给用户时间移入tooltip
      this.startHideTimer();
    },

    handleTooltipMouseEnter() {
      // 鼠标移入tooltip，取消隐藏计时器
      this.cancelHideTimer();
    },

    handleTooltipMouseLeave(event) {
      // 检查是否移回到源元素
      const relatedTarget = event.relatedTarget;
      if (relatedTarget && this.sourceElement && this.sourceElement.contains(relatedTarget)) {
        // 鼠标移回到源元素，不隐藏
        return;
      }
      // 鼠标移出tooltip，启动隐藏
      this.hide();
    },

    startHideTimer() {
      // 取消之前的计时器
      this.cancelHideTimer();

      // 设置新的计时器，给用户足够时间移入tooltip
      this.hideTimer = setTimeout(() => {
        this.hide();
      }, 150); // 150ms延迟，足够用户移入tooltip
    },

    cancelHideTimer() {
      if (this.hideTimer) {
        clearTimeout(this.hideTimer);
        this.hideTimer = null;
      }
    },

    cleanupEventListeners() {
      // 清理所有事件监听
      document.removeEventListener('click', this.hideOnClickOutside);

      if (this.sourceElement) {
        this.sourceElement.removeEventListener('mouseleave', this.handleSourceMouseLeave);
        this.sourceElement = null;
      }

      if (this.$refs.tooltipRef) {
        this.$refs.tooltipRef.removeEventListener('mouseenter', this.handleTooltipMouseEnter);
        this.$refs.tooltipRef.removeEventListener('mouseleave', this.handleTooltipMouseLeave);
      }

      this.cancelHideTimer();
    },

    hide() {
      this.visible = false;
      this.cleanupEventListeners();
    },

    hideOnClickOutside(event) {
      if (this.$refs.tooltipRef && !this.$refs.tooltipRef.contains(event.target)) {
        this.hide();
      }
    },

    getUrlDomain(url) {
      if (!url) return '';
      try {
        const parsedUrl = new URL(url);
        return parsedUrl.hostname;
      } catch (error) {
        console.error('Invalid URL:', url);
        return '';
      }
    },

    formatDate(date) {
      if (!date) return '';
      return this.$moment(date).format('YYYY-MM-DD');
    },

    handleOpenLink() {
      if (this.data?.url) {
        window.open(this.data.url, '_blank');
      }
      this.hide();
    },
  },
  beforeDestroy() {
    this.cleanupEventListeners();
  },
};
</script>

<style scoped lang="less">
.custom-tooltip {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2222; // 提高层级，避免被遮挡
  max-width: 355px;
  width: 355px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  opacity: 0;
  visibility: hidden;
  border: 0.5px solid #dcdcdc;
  pointer-events: auto;
  cursor: pointer;
  // 关键：让tooltip浮层不会影响原有hover区域
  // 鼠标在tooltip上时，保持阴影
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: opacity 0.2s ease, visibility 0.2s ease;
  will-change: opacity, visibility;
  &.is-visible {
    opacity: 1;
    visibility: visible;
  }

  // .custom-tooltip-content {
  //   cursor: pointer;
  // }

  .content-basic {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
    .site-icon {
      display: block;
      width: 16px;
    }
    .title-text {
      font-weight: 500;
      color: #333;
      flex: 1;
      flex-basis: 0;
      min-width: 0;
    }
  }

  .date {
    opacity: 0.7;
    font-size: 12px;
    margin-left: auto;
  }

  .content {
    font-size: 13px;
    line-height: 1.6;
    color: #333;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-word;
    min-height: 40px;
    padding: 10px 0;
  }

  .content-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    padding-top: 12px;
    .link-url {
      display: inline-flex;
      align-items: center;
    }
  }

  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 32px;
    white-space: normal;
  }
}
</style>
