<template>
  <transition name="slide-up">
    <div v-if="visible" class="bottom-create-user-card">
      <div class="card-header">
        <span>创建用户</span>
        <i class="close-btn" @click="$emit('close')">×</i>
      </div>
      <div class="card-content">
        <Form
          ref="formData"
          class="chat-create-user-form"
          :model="formData"
          inline
          :label-width="80"
          :label-colon="true"
          :rules="ruleValidate"
        >
          <FormItem label="用户手机" prop="mobile">
            <Input v-model="formData.mobile" maxlength="11" style="width: 100%" placeholder="请输入用户手机号" />
          </FormItem>
          <FormItem label="验证码" prop="authcode">
            <div class="flex flex-item-align">
              <Input v-model="formData.authcode" maxlength="6" style="width: 100%" placeholder="请输入验证码" />
              <!-- <Button type="primary" :disabled="!formData.mobile" @click="sendAuthCode">获取验证码</Button> -->
            </div>
          </FormItem>
          <FormItem label="用户姓名" prop="real_name">
            <Input
              v-model="formData.real_name"
              style="width: 100%"
              placeholder="请输入用户姓名"
              maxlength="16"
              show-word-limit
            />
          </FormItem>
          <FormItem label="用户性别" prop="sex">
            <Select v-model="formData.sex" style="width: 100%" placeholder="请选择用户性别">
              <Option v-for="item in sexList" :key="item.value" :value="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
          <FormItem label="出生日期" prop="birthday">
            <DatePicker
              type="date"
              style="width: 100%"
              placeholder="请选择出生日期"
              v-model="formData.birthday"
              @on-change="changeDate"
            />
          </FormItem>
          <FormItem label="用户年龄">
            <Input disabled v-model="userAge" style="width: 100%" placeholder="根据出生日期自动生成" />
          </FormItem>
          <FormItem label="用户来源">
            <Select v-model="formData.source" style="width: 100%" placeholder="请选择用户来源">
              <Option v-for="item in sourceListOptions" :key="item.id" :value="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem label="用户等级">
            <Select v-model="formData.offline_level" style="width: 100%" placeholder="请选择用户等级">
              <Option v-for="level in levelList" :key="level" :value="level">{{ level }}</Option>
            </Select>
          </FormItem>
        </Form>
      </div>
      <div class="card-footer">
        <Button @click="$emit('close')">取消</Button>
        <Button type="primary" :loading="loading" @click="handleCreate">创建</Button>
      </div>
    </div>
  </transition>
</template>

<script>
/**
 * 底部弹出创建用户卡片组件
 */
export default {
  name: 'BottomCreateUserCard',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    levelList: {
      type: Array,
      default: () => [],
    },
    sourceList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      formData: {
        real_name: '',
        mobile: '',
        authcode: '',
        birthday: '',
        sex: '',
        source: '',
        offline_level: '',
      },
      sexList: [
        { label: '男', value: '1' },
        { label: '女', value: '2' },
      ],
      ruleValidate: {
        real_name: [{ required: true, message: '请输入用户姓名', trigger: 'change' }],
        sex: [{ required: true, message: '请选择用户性别', trigger: 'change' }],
        birthday: [{ required: true, message: '请选择用户出生日期', trigger: 'change' }],
        mobile: [
          { required: true, message: '请输入用户手机号', trigger: 'change' },
          {
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!/^1[3-9]\d{9}$/.test(value)) {
                callback(new Error('请输入正确的手机号'));
              } else {
                callback();
              }
            },
          },
        ],
        authcode: [{ required: true, message: '请输入验证码', trigger: 'change' }],
      },
      userAge: '',
      loading: false,
      sourceListOptions: [],
    };
  },
  watch: {
    sourceList: {
      immediate: true,
      handler(val) {
        this.sourceListOptions = val;
      },
    },
  },
  methods: {
    /**
     * 发送验证码（占位）
     */
    sendAuthCode() {
      // TODO: 实现发送验证码逻辑
      this.$Message.info('验证码已发送（模拟）');
    },
    /**
     * 出生日期变化，自动计算年龄
     */
    changeDate(date) {
      if (!date) {
        this.userAge = '';
        return;
      }
      const birth = new Date(date);
      const now = new Date();
      let age = now.getFullYear() - birth.getFullYear();
      const m = now.getMonth() - birth.getMonth();
      if (m < 0 || (m === 0 && now.getDate() < birth.getDate())) {
        age--;
      }
      this.userAge = age + '岁';
    },
    /**
     * 创建用户
     */
    handleCreate() {
      this.$refs.formData.validate(valid => {
        if (!valid) return;
        this.loading = true;
        // TODO: 提交表单逻辑
        setTimeout(() => {
          this.loading = false;
          this.$Message.success('创建成功（模拟）');
          this.$emit('close');
        }, 1000);
      });
    },
  },
};
</script>

<style scoped lang="less">
.bottom-create-user-card {
  position: absolute;
  bottom: 32px;
  left: 16px;
  right: 16px;
  width: e('calc(100% - 32px)');
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #ecedf0;
  box-shadow: 0 -4px 8px rgba(21, 91, 212, 0.08);
  z-index: 9999;
  padding: 12px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
}

.close-btn {
  font-size: 22px;
  cursor: pointer;
}

.card-content {
  max-height: 60vh;
  overflow-y: auto;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.slide-up-enter-active {
  animation: slide-up-in 0.3s ease-out;
}

.slide-up-leave-active {
  animation: slide-up-out 0.3s ease-in;
}

@keyframes slide-up-in {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes slide-up-out {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(100%);
  }
}

.chat-create-user-form {
  :deep(.ivu-form-item) {
    width: e('calc(50% - 10px)');
    .ivu-date-picker-rel .ivu-input-wrapper {
      width: 100%;
    }
  }
}
</style>
