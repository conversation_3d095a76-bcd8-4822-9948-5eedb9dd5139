<template>
  <div class="mermaid-component">
    <!-- 加载状态 -->
    <div v-if="loading" class="echarts-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在生成图表...</div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="echarts-error">
      <div class="error-text">图表加载失败</div>
      <div class="error-detail">{{ error }}</div>
      <!-- <button @click="retry" class="retry-btn">重试</button> -->
    </div>

    <!-- 图表容器 -->
    <div v-show="!loading && !error" class="chart-wrapper" :style="{ height: chartHeight }">
      <!-- 图表控制按钮 -->
      <div class="chart-controls">
        <button class="control-btn" @click="resetChart" v-tooltip="'还原'">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
            />
          </svg>
        </button>
        <button class="control-btn" @click="downloadChart" v-tooltip="'下载图片'">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M5,20H19V18H5V20M14,10V3H10V10H5L12,17L19,10H14Z" />
          </svg>
        </button>
      </div>

      <div ref="chartContainer" class="chart-container" :style="{ height: chartHeight }"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import tooltipDirective from '@/directives/tooltip';
import { mapGetters } from 'vuex';
import * as _ from 'lodash-es';

export default {
  name: 'MermaidComponent',
  directives: {
    tooltip: tooltipDirective,
  },
  inject: ['isLoading'],
  props: {
    content: {
      type: String,
      required: true,
    },
    componentId: {
      type: String,
      default: '',
    },
    contentHash: {
      type: String,
      default: '',
    },
    height: {
      type: [String, Number],
      default: 260,
    },
  },
  data() {
    return {
      // 备用数据，当Vuex模块不可用时使用
      fallbackData: {
        loading: true,
        error: null,
        chartOptions: null,
        title: null,
      },
      useLocalData: false, // 是否使用本地数据
      chartInstance: null,
      loadAttempts: 0,
      maxAttempts: 3,
      isInitialized: false,
      token: '', // 使用token代替contentHash
    };
  },
  computed: {
    ...mapGetters({
      getChartData: 'echartsCard/getChartData',
      getStoreLoading: 'echartsCard/getLoading',
      getStoreError: 'echartsCard/getError',
      getStoreFetching: 'echartsCard/getFetching',
    }),
    chartHeight() {
      return typeof this.height === 'number' ? `${this.height}px` : this.height;
    },
    // 从store获取图表数据
    storeChartData() {
      if (!this.token) return null;
      try {
        return this.getChartData(this.token);
      } catch (error) {
        console.error('获取store图表数据出错:', error);
        return null;
      }
    },
    // 统一的数据访问接口
    chartOptions() {
      if (this.useLocalData) {
        return this.fallbackData.chartOptions;
      }
      return this.storeChartData?.chartOptions || null;
    },
    title() {
      if (this.useLocalData) {
        return this.fallbackData.title;
      }
      return this.storeChartData?.title || null;
    },
    loading() {
      if (this.useLocalData) {
        return this.fallbackData.loading;
      }
      try {
        return this.getStoreLoading(this.token);
      } catch (error) {
        console.error('获取store加载状态出错:', error);
        return false;
      }
    },
    error() {
      if (this.useLocalData) {
        return this.fallbackData.error;
      }
      try {
        return this.getStoreError(this.token);
      } catch (error) {
        console.error('获取store错误状态出错:', error);
        return null;
      }
    },
    // 判断是否正在响应中
    isResponding() {
      return this.isLoading;
    },
  },
  created() {
    // console.log('🔥 MermaidComponent CREATED:', {
    //   componentId: this.componentId,
    //   contentHash: this.contentHash,
    //   timestamp: new Date().toISOString(),
    // });

    if (this.content) {
      // 使用encodeURIComponent(content)作为唯一token
      this.token = encodeURIComponent(this.content);
      // console.log('Using token:', this.token);
    } else {
      console.error('MermaidComponent: Missing required prop "content"');
      this.useLocalData = true;
      this.fallbackData.error = 'Missing required prop: "content"';
      this.fallbackData.loading = false;
    }
  },
  mounted() {
    // 确保DOM已经渲染完成后再初始化图表
    this.$nextTick(() => {
      // 检查store中是否已有数据
      if (this.storeChartData) {
        // console.log('🎯 Found existing data in store:', this.token);
        this.isInitialized = true;
        if (!this.chartInstance && this.chartOptions) {
          this.initChart();
        }
        return;
      }

      // 初始化加载数据，仅在数据不存在时请求
      try {
        if (!this.chartOptions) {
          this.fetchChartData();
        } else if (!this.chartInstance) {
          this.initChart();
        }
      } catch (error) {
        console.error('初始化加载数据出错:', error);
        this.useLocalData = true;
        this.fallbackData.error = '初始化失败';
        this.fallbackData.loading = false;
      }
    });

    // 添加 resize 事件监听
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // console.log('💀 MermaidComponent DESTROYING:', {
    //   componentId: this.componentId,
    //   token: this.token,
    //   timestamp: new Date().toISOString(),
    // });

    // 销毁图表实例
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }

    // 移除 resize 事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {


    // 获取图表数据
    fetchChartData() {
      if (this.useLocalData) {
        this.fetchLocalData();
        return;
      }

      try {
        this.$store.dispatch('echartsCard/fetchChartData', {
          content: this.content,
        });
      } catch (error) {
        console.error('获取图表数据出错:', error);
        this.useLocalData = true;
        this.fetchLocalData();
      }
    },

    // 本地数据获取（备用方法）
    async fetchLocalData() {
      if (!this.content || this.content.trim() === '') {
        this.fallbackData.error = 'Mermaid content is empty';
        this.fallbackData.loading = false;
        return;
      }

      try {
        this.fallbackData.loading = true;
        this.fallbackData.error = null;

        // 直接调用store action，它会处理缓存和API调用
        await this.$store.dispatch('echartsCard/fetchChartData', {
          content: this.content,
        });

        // 检查store中是否有数据
        const storeData = this.getChartData(this.token);
        if (storeData && storeData.chartOptions) {
          this.fallbackData.chartOptions = storeData.chartOptions;
          this.fallbackData.title = storeData.title || null;
          this.fallbackData.loading = false;
          this.fallbackData.error = null;
          this.loadAttempts = 0;

          this.isInitialized = true;

          // 初始化图表
          this.$nextTick(() => {
            this.initChart();
          });
        } else {
          // 检查是否有错误
          const storeError = this.getStoreError(this.token);
          if (storeError) {
            throw new Error(storeError);
          } else {
            throw new Error('No chart data received');
          }
        }
      } catch (error) {
        console.error('获取图表配置失败:', error);
        this.handleError(error);
      }
    },

    // 初始化图表
    initChart() {
      if (!this.$refs.chartContainer || !this.chartOptions) {
        console.warn('Chart container or options not ready');
        return;
      }

      try {
        // 销毁已存在的图表实例
        if (this.chartInstance) {
          this.chartInstance.dispose();
        }

        // 创建新的图表实例，设置设备像素比
        this.chartInstance = echarts.init(this.$refs.chartContainer, null, {
          devicePixelRatio: window.devicePixelRatio || 1
        });
        this.chartInstance.setOption(this.chartOptions);

        // console.log('Chart initialized successfully');
      } catch (error) {
        console.error('初始化图表失败:', error);
        if (this.useLocalData) {
          this.fallbackData.error = `图表初始化失败: ${error.message}`;
        }
      }
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    },

    // 处理错误
    handleError(error) {
      console.error('MermaidComponent error, attempt:', this.loadAttempts + 1);
      this.loadAttempts++;

      if (this.loadAttempts < this.maxAttempts) {
        // 重试获取配置
        setTimeout(() => {
          this.fallbackData.loading = true;
          this.fallbackData.error = null;
          this.fetchLocalData();
        }, 1000 * this.loadAttempts); // 递增延迟
      } else {
        this.fallbackData.loading = false;
        this.fallbackData.error = error.message || '图表加载失败，请稍后重试';
      }
    },

    // 重试
    retry() {
      this.loadAttempts = 0;

      // 清除store中的缓存数据，强制重新请求
      this.$store.dispatch('echartsCard/clearChartData', {
        content: this.content,
      });

      if (this.useLocalData) {
        this.fallbackData.loading = true;
        this.fallbackData.error = null;
        this.fetchLocalData();
      } else {
        this.fetchChartData();
      }
    },

    // 还原图表
    resetChart() {
      if (this.chartInstance) {
        this.chartInstance.dispatchAction({
          type: 'restore',
        });
      }
    },

    // 下载图表为图片
    downloadChart() {
      if (!this.chartInstance) {
        console.warn('Chart instance not available');
        return;
      }

      try {
        const url = this.chartInstance.getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff',
        });

        // 获取图表标题作为文件名
        let fileName = 'chart';
        if (this.title) {
          fileName = this.title.replace(/[^\w\u4e00-\u9fa5]/g, '_');
        } else if (this.chartOptions && this.chartOptions.title && this.chartOptions.title.text) {
          fileName = this.chartOptions.title.text.replace(/[^\w\u4e00-\u9fa5]/g, '_');
        }
        fileName = `${fileName}-${Date.now()}.png`;

        const link = document.createElement('a');
        link.download = fileName;
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        console.error('下载图表失败:', error);
        this.$Message && this.$Message.error('下载图表失败');
      }
    },
  },
  watch: {
    content: {
      handler(newContent, oldContent) {
        if (newContent && newContent !== oldContent) {
          const newToken = encodeURIComponent(newContent);
          // 只有当token发生变化时才重新获取配置
          if (newToken !== this.token) {
            this.token = newToken;
            this.loadAttempts = 0;
            this.isInitialized = false; // 重置初始化状态
            this.fetchChartData();
          } else {
            console.log('Token unchanged, skipping chart regeneration. ComponentId:', this.componentId);
          }
        }
      },
      immediate: false,
    },

    // 监听store中的图表数据变化
    storeChartData: {
      handler(newData) {
        if (newData && newData.chartOptions && !this.chartInstance) {
          this.$nextTick(() => {
            this.initChart();
          });
        }
      },
      immediate: true, // 立即执行一次，确保组件创建时能响应已有的数据
      deep: true,
    },

    // 监听store中的loading状态变化
    loading: {
      handler(newLoading) {
        // 如果loading状态结束，检查是否有图表数据可以初始化
        if (!newLoading && this.chartOptions && !this.chartInstance) {
          this.$nextTick(() => {
            this.initChart();
          });
        }
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.mermaid-component {
  margin: 1em 0;
  position: relative;
  width: 100%;
  min-height: 260px;
  border-radius: 6px;
  overflow: hidden;
}

.echarts-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 260px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }

  .loading-text {
    color: #6c757d;
    font-size: 14px;
  }
}

.echarts-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 260px;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  padding: 20px;
  text-align: center;

  .error-text {
    color: #e53e3e;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .error-detail {
    color: #a0aec0;
    font-size: 14px;
    margin-bottom: 16px;
    word-break: break-word;
  }

  .retry-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;

    &:hover {
      background: #0056b3;
    }
  }
}

.chart-wrapper {
  position: relative;
  width: 100%;
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.chart-controls {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 1;
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;

  &:hover {
    background-color: #f0f0f0;
    color: #333;
  }

  &:active {
    background-color: #e0e0e0;
    color: #333;
  }
}

.chart-container {
  width: 100%;
  min-height: 260px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
