<template>
  <div class="table-card-component rsj-table-container--scroll">
    <div class="shadow-left" :class="{ 'shadow-hidden': !showLeftShadow }"></div>
    <div class="shadow-right" :class="{ 'shadow-hidden': !showRightShadow }"></div>
    <!-- 下载按钮 -->
    <div
      class="table-download-btn"
      :class="{ downloading: isDownloading }"
      @click="downloadTableData"
      v-tooltip="isDownloading ? '正在下载...' : '下载表格'"
      v-show="!isResponding"
    >
      <svg
        v-if="!isDownloading"
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M8 10.5L5.5 8H7V4H9V8H10.5L8 10.5Z" fill="currentColor" />
        <path d="M3 12H13V13H3V12Z" fill="currentColor" />
      </svg>
      <Spin v-else size="small" />
    </div>

    <div class="md-table-wrapper">
      <div class="table-container" ref="tableContainer" @scroll="handleScroll">
        <!-- 加载状态 -->
        <div v-if="loading && !error" class="loading-state">
          <Spin size="large" fix></Spin>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-state">
          <span>{{ error }}</span>
        </div>
        <table class="native-table">
          <thead>
            <tr>
              <th v-for="(col, index) in tableHeaders" :key="index">{{ col.text }}</th>
            </tr>
          </thead>
          <tbody ref="tableBody">
            <tr v-for="(row, rowIndex) in tableData" :key="rowIndex">
              <td v-for="(col, colIndex) in tableHeaders" :key="colIndex">
                <span
                  :class="{ 'table-cell': true, 'overflow-tooltip': isOverFlow(row[col.key]?.value) }"
                  v-if="!row[col.key]?.url"
                  v-overflow-tooltip="{ maxWidth: 448 }"
                  v-html="row[col.key]?.value || '-'"
                >
                </span>
                <a v-else :href="row[col.key].url || '#'" :target="col.target || '_blank'" v-html="row[col.key].value">
                </a>
              </td>
            </tr>
            <tr v-if="tableData.length === 0 && !loading && !error">
              <td :colspan="tableHeaders.length" class="empty-text">暂无数据</td>
            </tr>
          </tbody>
        </table>
        <div class="pagination-container" v-if="total > pageSize">
          <div class="pagination-wrapper">
            <div
              class="page-btn"
              :class="{ disabled: currentPage === 1 }"
              @click="currentPage > 1 && handleCurrentChange(currentPage - 1)"
            >
              上一页
            </div>
            <div
              class="page-btn"
              :class="{ disabled: currentPage >= Math.ceil(total / pageSize) }"
              @click="currentPage < Math.ceil(total / pageSize) && handleCurrentChange(currentPage + 1)"
            >
              下一页
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分页组件 -->
</template>

<script>
// import { businessService } from '@/api/business';
import { mapGetters } from 'vuex';
import axios from 'axios';
import tooltipDirective from '@/directives/tooltip';

export default {
  name: 'TableCardComponent',
  directives: {
    tooltip: tooltipDirective,
  },
  components: {},
  inject: ['isLoading'],
  props: {
    token: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      // 备用数据，当Vuex模块不可用时使用
      fallbackData: {
        tableData: [],
        tableHeaders: [],
        loading: true,
        error: null,
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      useLocalData: false, // 是否使用本地数据
      showLeftShadow: false,
      showRightShadow: true,
      isHovering: false, // 是否鼠标悬停在组件上
      isDownloading: false, // 是否正在下载
    };
  },
  created() {
    // 检查store是否存在
    // 默认使用本地数据模式
    this.useLocalData = true;

    // 如果store存在并且tableCard模块存在，则使用Vuex
    if (this.$store && this.$store.state && this.$store.state.tableCard) {
      try {
        // 尝试调用一个getter来验证store是否正常工作
        const testData = this.getTableData(this.token);
        if (testData !== undefined) {
          this.useLocalData = false;
        }
      } catch (error) {
        console.log('Vuex getter调用失败，使用本地数据模式', error);
      }
    } else {
      console.log('Vuex store不可用，使用本地数据模式');
    }
  },
  computed: {
    ...mapGetters({
      getTableData: 'tableCard/getTableData',
      getPageInfo: 'tableCard/getPageInfo',
      getLoading: 'tableCard/getLoading',
      getError: 'tableCard/getError',
    }),
    isOverFlow() {
      return value => {
        return value && !String(value).includes('\n');
      };
    },
    // 检查消息是否正在响应
    isResponding() {
      return this.isLoading();
    },
    // 表格数据
    tableData() {
      if (this.useLocalData) {
        return this.fallbackData.tableData;
      }

      try {
        return this.getTableData(this.token).items || [];
      } catch (error) {
        console.error('获取表格数据出错:', error);
        this.handleVuexError();
        return this.fallbackData.tableData;
      }
    },

    // 表格表头
    tableHeaders() {
      if (this.useLocalData) {
        return this.fallbackData.tableHeaders;
      }

      try {
        return this.getTableData(this.token).headers || [];
      } catch (error) {
        console.error('获取表格表头出错:', error);
        this.handleVuexError();
        return this.fallbackData.tableHeaders;
      }
    },

    // 总条数
    total() {
      if (this.useLocalData) {
        return this.fallbackData.total;
      }

      try {
        return this.getTableData(this.token).total || 0;
      } catch (error) {
        console.error('获取总条数出错:', error);
        this.handleVuexError();
        return this.fallbackData.total;
      }
    },

    // 当前页
    currentPage() {
      if (this.useLocalData) {
        return this.fallbackData.currentPage;
      }

      try {
        return this.getPageInfo(this.token).currentPage || 1;
      } catch (error) {
        console.error('获取当前页出错:', error);
        this.handleVuexError();
        return this.fallbackData.currentPage;
      }
    },

    // 每页条数
    pageSize() {
      if (this.useLocalData) {
        return this.fallbackData.pageSize;
      }

      try {
        return this.getPageInfo(this.token).pageSize || 10;
      } catch (error) {
        console.error('获取每页条数出错:', error);
        this.handleVuexError();
        return this.fallbackData.pageSize;
      }
    },

    // 加载状态
    loading() {
      if (this.useLocalData) {
        return this.fallbackData.loading;
      }

      try {
        console.log("🚀 ~ loading ~ this.token: ", this.token);
        return this.getLoading(this.token);
      } catch (error) {
        console.error('获取加载状态出错:', error);
        this.handleVuexError();
        return this.fallbackData.loading;
      }
    },

    // 错误信息
    error() {
      if (this.useLocalData) {
        return this.fallbackData.error;
      }

      try {
        return this.getError(this.token);
      } catch (error) {
        console.error('获取错误信息出错:', error);
        this.handleVuexError();
        return this.fallbackData.error;
      }
    },
  },
  mounted() {
    // 初始化加载数据，仅在数据不存在时请求
    try {
      if (!this.tableData.length) {
        this.fetchTableData();
      }
    } catch (error) {
      console.error('初始化加载数据出错:', error);
    }

    // 添加 resize 事件监听
    window.addEventListener('resize', this.updateShadows);
    // 初始更新阴影状态
    this.$nextTick(() => {
      this.updateShadows();
    });
  },
  beforeDestroy() {
    // 移除 resize 事件监听
    window.removeEventListener('resize', this.updateShadows);
  },
  methods: {
    // 切换页码
    handleCurrentChange(page) {
      if (this.useLocalData) {
        this.fallbackData.currentPage = page;
        this.fetchLocalData();
        return;
      }

      try {
        this.$store.dispatch('tableCard/changePage', { token: this.token, page });
      } catch (error) {
        console.error('切换页码出错:', error);
        this.useLocalData = true;
        this.fallbackData.currentPage = page;
        this.fetchLocalData();
      }
    },

    // 切换每页条数
    handleSizeChange(size) {
      if (this.useLocalData) {
        this.fallbackData.pageSize = size;
        this.fallbackData.currentPage = 1;
        this.fetchLocalData();
        return;
      }

      try {
        this.$store.dispatch('tableCard/changePageSize', { token: this.token, size });
      } catch (error) {
        console.error('切换每页条数出错:', error);
        this.useLocalData = true;
        this.fallbackData.pageSize = size;
        this.fallbackData.currentPage = 1;
        this.fetchLocalData();
      }
    },

    // 获取表格数据
    fetchTableData() {
      if (this.useLocalData) {
        this.fetchLocalData();
        return;
      }

      try {
        this.$store.dispatch('tableCard/fetchTableData', { token: this.token });
      } catch (error) {
        console.error('获取表格数据出错:', error);
        this.useLocalData = true;
        this.fetchLocalData();
      }
    },

    // 使用本地方式获取数据
    async fetchLocalData() {
      this.fallbackData.loading = true;
      this.fallbackData.error = null;

      try {
        if (!this.token) return;
        const [ticketToken] = this.token.split('&');

        const res = await axios.get('https://clinic.rsjxx.com/api-server/service/llm_chat/basic.list', {
          params: {
            ticket: ticketToken,
            page: this.fallbackData.currentPage,
            size: this.fallbackData.pageSize,
          },
        });

        this.fallbackData.tableHeaders = res.data.data.header;
        this.fallbackData.tableData = res.data.data.items;
        this.fallbackData.total = res.data.data.total;

        if (this.token === 'invalid') {
          throw new Error('无效的数据标识');
        }
      } catch (err) {
        console.error('获取表格数据失败:', err);
        this.fallbackData.error = err instanceof Error ? err.message : '未知错误';
      } finally {
        this.fallbackData.loading = false;
      }
    },

    // 更新阴影显示状态
    updateShadows() {
      const container = this.$refs.tableContainer;
      if (!container) return;

      // 检查是否滚动到最左侧
      this.showLeftShadow = container.scrollLeft > 0;

      // 检查是否滚动到最右侧
      const maxScrollLeft = container.scrollWidth - container.clientWidth;
      this.showRightShadow = container.scrollLeft < maxScrollLeft;
    },

    handleScroll() {
      this.updateShadows();
    },

    // 鼠标移入表格组件
    handleMouseEnter() {
      this.isHovering = true;
    },

    // 鼠标移出表格组件
    handleMouseLeave() {
      this.isHovering = false;
    },

    // 处理 Vuex 错误，切换到本地数据模式
    handleVuexError() {
      // 使用 nextTick 避免在计算属性中直接修改状态
      this.$nextTick(() => {
        this.useLocalData = true;
      });
    },

    // 下载表格数据为CSV
    async downloadTableData() {
      if (this.isDownloading) {
        return; // 如果正在下载，直接返回
      }

      if (!this.tableHeaders.length) {
        this.$Message.warning('表格数据为空，无法下载');
        return;
      }

      this.isDownloading = true;

      try {
        // 如果没有数据或者总数大于当前数据量，需要获取全部数据
        if (!this.tableData.length || this.total > this.tableData.length) {
          await this.downloadAllTableData();
        } else {
          // 如果当前数据就是全部数据，直接下载
          this.downloadCurrentTableData();
        }
      } catch (error) {
        console.error('下载失败:', error);
        this.$Message.error('下载失败，请重试');
      } finally {
        this.isDownloading = false;
      }
    },

    // 下载当前表格数据
    downloadCurrentTableData() {
      try {
        // 提取表头
        const headers = this.tableHeaders.map(header => header.text || header.key || '');

        // 提取数据行
        const data = this.tableData.map(row => {
          return this.tableHeaders.map(header => {
            const cellData = row[header.key];
            if (cellData && typeof cellData === 'object') {
              return cellData.value || cellData.url || '';
            }
            return cellData || '';
          });
        });

        this.downloadAsCSV(headers, data);
      } catch (error) {
        console.error('下载表格数据失败:', error);
        this.$Message.error('下载失败，请重试');
      }
    },

    // 获取并下载全部表格数据
    async downloadAllTableData() {
      if (!this.token) {
        throw new Error('缺少数据标识');
      }

      const [ticketToken, totalStr] = this.token.split('&');
      const totalCount = this.total || parseInt(totalStr) || 0;

      if (totalCount <= 0) {
        throw new Error('无法获取数据总数');
      }

      // 直接使用total作为pageSize，一次性获取所有数据
      const res = await axios.get('https://clinic.rsjxx.com/api-server/service/llm_chat/basic.list', {
        params: {
          ticket: ticketToken,
          page: 1,
          size: totalCount,
        },
      });

      if (!res.data || !res.data.data || !res.data.data.items || res.data.data.items.length === 0) {
        throw new Error('没有获取到数据');
      }

      const allData = res.data.data.items;

      // 提取表头
      const headers = this.tableHeaders.map(header => header.text || header.key || '');

      // 提取数据行
      const data = allData.map(row => {
        return this.tableHeaders.map(header => {
          const cellData = row[header.key];
          if (cellData && typeof cellData === 'object') {
            return cellData.value || cellData.url || '';
          }
          return cellData || '';
        });
      });

      this.downloadAsCSV(headers, data, allData.length);
    },

    // 下载为CSV格式
    downloadAsCSV(headers, data, totalCount) {
      // 构建CSV内容
      const csvContent = [
        headers.join(','),
        ...data.map(row =>
          row
            .map(cell => {
              // 处理包含逗号、引号或换行符的单元格
              const cellStr = String(cell || '').replace(/"/g, '""');
              return `"${cellStr}"`;
            })
            .join(',')
        ),
      ].join('\n');

      // 创建Blob对象，添加BOM以支持中文
      const blob = new Blob(['\uFEFF' + csvContent], {
        type: 'text/csv;charset=utf-8;',
      });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `table_data_${new Date().getTime()}.csv`);
      link.style.visibility = 'hidden';

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      const count = totalCount || data.length;
      this.$Message.success(`表格数据下载成功，共 ${count} 条记录`);
    },
  },
  watch: {
    // 监听表格数据变化，更新阴影状态
    tableData() {
      this.$nextTick(() => {
        this.updateShadows();
      });
    },
  },
};
</script>

<style lang="less" scoped>
@border-color: #ebeef5;
@disabled-color: #c0c4cc;
@hover-bg: #f0f0f0;
@disabled-bg: #f4f4f5;
@disabled-border: #e4e7ed;

.table-card-component {
  position: relative;
  margin-top: 6px;
  height: auto; // 自适应高度
  min-height: 100px; // 最小高度

  &:hover {
    .pagination-container {
      opacity: 1;
    }
    .table-download-btn {
      opacity: 1 !important;
      background: #f5f5f5;
      color: #333;
      border-color: #d0d0d0;
    }
  }
}

.table-download-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 5; // 降低z-index，确保不高过助手弹窗header
  color: #666;

  &:hover:not(.downloading) {
    background: #f5f5f5;
    color: #333;
    border-color: #d0d0d0;
  }

  &.downloading {
    cursor: not-allowed;
    background: #f0f0f0;
    color: #999;
    border-color: #e0e0e0;
  }

  &.show {
    opacity: 1;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.table-container {
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: auto;
  height: auto; // 自适应高度
  max-height: 500px; // 最大高度限制，避免过高
  &::-webkit-scrollbar {
    height: 8px;
    // display: none;
  }
  tbody > tr:last-child {
    border-bottom: none;
  }
}

.native-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 12px;
  position: relative;

  th,
  td {
    padding: 8px 12px;
    text-align: left;
    white-space: nowrap;
  }

  th {
    background: #f5f6f8;
    color: #606266;
    font-weight: 500;
  }
}

.empty-text {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

.loading,
.error {
  padding: 24px;
  text-align: center;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100px;
}

.error {
  color: #f56c6c;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  right: 16px;
  bottom: 14px;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.pagination--hidden {
  opacity: 0;
  pointer-events: none;
}

.pagination-wrapper {
  display: flex;
  gap: 12px;
  padding: 12px 0;
}

.page-btn {
  padding: 5px 14px;
  border-radius: 14px;
  background: rgba(255, 255, 255, 0.68);
  cursor: pointer;
  border: 1px solid #ecedf0;
  transition: all 0.3s;
  font-size: 12px;
  color: #333333;
  line-height: 18px;
  &:hover:not(.disabled) {
    background: #fff;
  }

  &.disabled {
    cursor: not-allowed;
    color: #333333;
    background: rgba(245, 245, 245, 0.8);
    border-color: #ecedf0;
  }
}

.shadow-hidden {
  display: none;
}
:deep(hr) {
  height: 1px;
  background: #d0d7de3d;
  border: 0;
  margin: 1em 0;
}
.table-cell {
  white-space: pre; /* 保留换行符，但合并空白符 */
}
.overflow-tooltip {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  display: inline-block;
}
</style>
