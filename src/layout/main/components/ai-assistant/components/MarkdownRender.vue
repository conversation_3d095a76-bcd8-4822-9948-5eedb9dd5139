<template>
  <div class="markdown-body">
    <div class="markdown-render-container">
      <div class="custom-markdown">
        <template v-for="(block, idx) in blocks">
          <component v-if="block.type === 'component'" :is="block.name" v-bind="block.props" :key="'md-block' + idx" />
          <div style="display: inline" v-else v-html="block.html" :key="'md-block' + idx"></div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import mathjax3 from 'markdown-it-mathjax3';
import container from 'markdown-it-container';
import TableCardComponent from './custom/TableCardComponent.vue';
import ModalCardComponent from './custom/ModalCardComponent.vue';
import MermaidComponent from './custom/MermaidComponent.vue';
import 'highlight.js/styles/github.css';
import markdownItLinkAttributes from 'markdown-it-link-attributes';
import tippy from 'tippy.js';
// import { debounce } from 'lodash-es';
import md5 from 'js-md5';
export default {
  name: 'MarkdownRender',
  components: {
    TableCardComponent,
    ModalCardComponent,
    MermaidComponent,
  },
  props: {
    message: {
      type: Object,
      required: true,
      default: () => ({ content: '', search_results: [] }),
    },
    searchResults: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      blocks: [],
      md: null,
      citedInfo: {
        currentSearchResult: {},
        citedRef: null,
        content: '',
      },
      lastContentHash: '', // 存储上次内容的哈希值
      // Mermaid代码块收集状态
      mermaidCollectionState: {
        isCollecting: false,
        buffer: [],
        lastProcessedContent: '', // 记录上次处理的内容，避免重复处理
      },
    };
  },
  computed: {
    // 优先使用props中的searchResults，如果没有则使用message的search_results
    messageSearchResults() {
      // 优先使用props传递的searchResults，因为它是正确的
      if (this.searchResults && this.searchResults.length > 0) {
        return this.searchResults;
      }
      // 备用方案：使用message中的search_results
      return this.message.search_results || [];
    },
  },
  watch: {
    'message.content': {
      handler() {
        // 确保防抖函数已经初始化
        // if (this.debouncedRenderMarkdown) {
        //   this.debouncedRenderMarkdown();
        // } else {
        // 如果还没有初始化，直接调用渲染函数
        this.renderMarkdown();
        // }
      },
      immediate: true,
    },
    'message.isReponsing': {
      handler() {
        this.updateTableDownloadButtonsVisibility();
      },
      immediate: false,
    },
  },
  created() {
    // 为每个组件实例创建独立的MarkdownIt实例
    this.initMDRender();
    // 创建防抖渲染函数
    // this.debouncedRenderMarkdown = debounce(this.renderMarkdownWithHashGuard, 200);
  },
  mounted() {
    this.renderMarkdown();
    window.addEventListener('resize', this.updateTableShadows);
  },
  beforeDestroy() {
    this.cleanupAllEvents();
    window.removeEventListener('resize', this.updateTableShadows);
    // 清理防抖函数
    // if (this.debouncedRenderMarkdown) {
    //   this.debouncedRenderMarkdown.cancel();
    // }
  },
  methods: {
    fixMarkdownTable(mdText) {
      // 匹配 Markdown 中的表格（包含表头和表格行）
      const tableRegex = /(\|[^\n]+(?:\r?\n|\r))+/g;

      mdText = mdText.replace(tableRegex, table => {
        const lines = table.split('\n').filter(Boolean);
        if (lines?.length < 2) {
          return table;
        }
        const headers = lines[0]
          .split('|')
          .map(item => item.trim())
          .filter(Boolean);
        const separatorLine = lines[1]
          .split('|')
          .map(item => item.trim())
          .filter(Boolean);
        const lenDiff = headers.length - separatorLine.length;
        if (lenDiff === 0) {
          return '\n' + table;
        }
        const currentLine = lines[1].slice(0, lenDiff * 6);
        lines[1] = currentLine;
        const tableLines = lines.join('\n') + '\n';
        console.log('tableLines: ', tableLines);
        return tableLines;
      });
      return mdText + '\n';
    },

    // 处理颜色标记
    processColorTags(content) {
      // 支持多种颜色语法格式
      const colorPatterns = [
        // {color:red}文本{/color}
        {
          regex: /\{color:([^}]+)\}(.*?)\{\/color\}/g,
          replace: (_, color, text) => `<span style="color: ${this.normalizeColor(color)}">${text}</span>`,
        },
        // [color=red]文本[/color]
        // {
        //   regex: /\[color=([^\]]+)\](.*?)\[\/color\]/g,
        //   replace: (match, color, text) => `<span style="color: ${this.normalizeColor(color)}">${text}</span>`,
        // },
        // <color:red>文本</color>
        // {
        //   regex: /<color:([^>]+)>(.*?)<\/color>/g,
        //   replace: (match, color, text) => `<span style="color: ${this.normalizeColor(color)}">${text}</span>`,
        // },
      ];

      let processedContent = content;
      colorPatterns.forEach(pattern => {
        processedContent = processedContent.replace(pattern.regex, pattern.replace);
      });

      return processedContent;
    },

    // 标准化颜色值
    normalizeColor(color) {
      // 预定义颜色映射
      const colorMap = {
        red: '#ff4d4f',
        green: '#52c41a',
        blue: '#1890ff',
        orange: '#fa8c16',
        purple: '#722ed1',
        yellow: '#fadb14',
        pink: '#eb2f96',
        cyan: '#13c2c2',
        gray: '#8c8c8c',
        grey: '#8c8c8c',
        black: '#000000',
        white: '#ffffff',
        primary: '#115bd4',
        success: '#52c41a',
        warning: '#fa8c16',
        error: '#ff4d4f',
        info: '#1890ff',
      };

      // 如果是预定义颜色名称，返回对应的十六进制值
      if (colorMap[color.toLowerCase()]) {
        return colorMap[color.toLowerCase()];
      }

      // 如果已经是有效的颜色值（十六进制、rgb、rgba等），直接返回
      if (
        /^#[0-9a-fA-F]{3,6}$/.test(color) ||
        /^rgb\(/.test(color) ||
        /^rgba\(/.test(color) ||
        /^hsl\(/.test(color) ||
        /^hsla\(/.test(color)
      ) {
        return color;
      }

      // 默认返回原色值
      return color;
    },
    _citedEnter(e) {
      const citedIndex = e.target.dataset.cited;
      // 直接从当前组件实例的messageSearchResults获取搜索结果
      const results = this.messageSearchResults || [];
      const searchResult = results.find(item => item.index == citedIndex);
      this.citedInfo.currentSearchResult = searchResult;
      this.citedInfo.citedRef = e.target;

      // 通过事件向父组件传递引用数据
      this.$emit('showCitedTooltip', {
        target: e.target,
        data: searchResult,
      });
    },
    _citedLeave() {
      // 不再直接隐藏tooltip，让CustomTooltip组件自己处理鼠标离开事件
      // 这样可以给用户时间移入tooltip内容
      // this.$emit('hideCitedTooltip');
    },

    // 从message的search_results中获取指定索引的搜索结果
    getSearchResultByIndex(index) {
      const results = this.messageSearchResults || [];
      const found = results.find(item => item.index == index);
      return found;
    },

    // 生成基于内容的稳定哈希ID
    generateStableHash(content) {
      let hash = 0;
      if (content.length === 0) return hash.toString(36);
      for (let i = 0; i < content.length; i++) {
        const char = content.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // 转换为32位整数
      }
      return Math.abs(hash).toString(36);
    },

    initMDRender() {
      this.md = new MarkdownIt({
        html: true,
        linkify: true,
        typographer: true,
        highlight: function (str, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return `<pre class="hljs"><code>\n${
                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value
              }\n</code></pre>`;
            } catch (error) {
              // 忽略错误
            }
          }
          return `<pre class="hljs"><code>\n${this.md.utils.escapeHtml(str)}\n</code></pre>`;
        }.bind(this),
      });

      // 自定义代码块渲染规则，处理 mermaid
      // const originalCodeBlock =
      //   this.md.renderer.rules.code_block || this.md.renderer.renderToken.bind(this.md.renderer);
      const originalFenceBlock = this.md.renderer.rules.fence || this.md.renderer.renderToken.bind(this.md.renderer);

      this.md.renderer.rules.fence = (tokens, idx, options, env, renderer) => {
        // console.log('tokens: ', tokens);
        const token = tokens[idx];
        const info = token.info ? token.info.trim() : '';
        const langName = info ? info.split(/\s+/g)[0] : '';

        // 处理 mermaid 代码块
        if (langName && langName.toLowerCase() === 'mermaid') {
          const content = token.content.trim();
          const isResponding = this.message && this.message.isReponsing;

          // 在SSE过程中，通过检查原始消息内容来判断代码块是否完整
          const isComplete = this.isMermaidCodeCompleteInSSE(content);
          console.log('isComplete: ', isComplete);

          // 如果正在响应且代码不完整，显示加载状态
          if (isResponding && !isComplete) {
            return `<div class="mermaid-loading">
              <div class="loading-content">
                <div class="loading-spinner"></div>
                <span>正在生成图表...</span>
              </div>
            </div>`;
          }

          // 只有代码完整时才生成MermaidComponent
          if (isComplete) {
            const contentHash = this.generateContentHash(content);
            const componentId = 'mermaid-' + contentHash;

            // 使用特殊标记来标识Mermaid组件，稍后在splitHtmlToBlocks中处理
            return `<mermaid-placeholder data-content="${encodeURIComponent(
              content
            )}" data-component-id="${componentId}" data-content-hash="${contentHash}"></mermaid-placeholder>`;
          } else {
            // 如果代码不完整且不在响应中，显示为普通代码块
            return originalFenceBlock(tokens, idx, options, env, renderer);
          }
        }

        // 对于其他代码块，使用默认渲染
        return originalFenceBlock(tokens, idx, options, env, renderer);
      };

      this.md.use(mathjax3);
      this.md.use(markdownItLinkAttributes, {
        pattern: /^https?:\/\//, // 只给外部链接加 target="_blank"
        attrs: {
          target: '_blank',
          rel: 'noopener', // 安全推荐
        },
      });

      // 自定义链接渲染规则，处理 CSV 和 XLSX 文件链接
      const originalLinkOpen =
        this.md.renderer.rules.link_open ||
        function (tokens, idx, options) {
          return this.md.renderer.renderToken(tokens, idx, options);
        }.bind(this);

      const originalLinkClose =
        this.md.renderer.rules.link_close ||
        function (tokens, idx, options) {
          return this.md.renderer.renderToken(tokens, idx, options);
        }.bind(this);

      // 存储文件卡片链接的标记
      const fileCardLinks = new Set();

      this.md.renderer.rules.link_open = (tokens, idx, options, env, renderer) => {
        const token = tokens[idx];
        const href = token.attrGet('href');

        // 检查是否是 CSV 或 XLSX 文件链接
        if (href && (href.endsWith('.csv') || href.endsWith('.xlsx'))) {
          // 提取文件扩展名
          const fileExtension = href.split('.').pop().toLowerCase();
          const iconName = fileExtension === 'csv' ? 'AI-csv' : 'AI-xlsx';

          // 标记这是一个文件卡片链接
          fileCardLinks.add(idx);

          // 查找链接文本内容
          let linkText = href.split('/').pop() || href; // 默认使用文件名
          if (idx + 1 < tokens.length && tokens[idx + 1].type === 'text') {
            linkText = tokens[idx + 1].content || linkText;
          }

          // 返回文件卡片的 HTML 结构
          return `<div class="file-card-link" data-href="${href}">
            <div class="file-card">
              <div class="file-card-content">
                <div class="file-icon">
                  <svg class="svg-icon" aria-hidden="true" style="width: 22px; height: 22px;">
                    <use xlink:href="#icon-${iconName}" />
                  </svg>
                </div>
                <div class="file-info">
                  <div class="file-name">${linkText}</div>
                </div>
              </div>
            </div>
          </div>`;
        }

        // 对于普通链接，使用默认渲染
        return originalLinkOpen(tokens, idx, options, env, renderer);
      };

      this.md.renderer.rules.link_close = (tokens, idx, options, env, renderer) => {
        // 查找对应的 link_open token
        let openTokenIdx = idx - 1;
        while (openTokenIdx >= 0 && tokens[openTokenIdx].type !== 'link_open') {
          openTokenIdx--;
        }

        // 如果是文件卡片链接，不输出任何内容（因为已经在 link_open 中完整输出了）
        if (openTokenIdx >= 0 && fileCardLinks.has(openTokenIdx)) {
          return '';
        }

        // 对于普通链接，使用默认渲染
        return originalLinkClose(tokens, idx, options, env, renderer);
      };

      // 处理文件卡片链接的文本内容，跳过渲染
      const originalText =
        this.md.renderer.rules.text ||
        function (tokens, idx, options) {
          return this.md.renderer.renderToken(tokens, idx, options);
        }.bind(this);

      this.md.renderer.rules.text = (tokens, idx, options, env, renderer) => {
        // 检查是否在文件卡片链接内
        let isInFileCardLink = false;
        for (let i = idx - 1; i >= 0; i--) {
          if (tokens[i].type === 'link_open') {
            if (fileCardLinks.has(i)) {
              isInFileCardLink = true;
            }
            break;
          }
          if (tokens[i].type === 'link_close') {
            break;
          }
        }

        // 如果在文件卡片链接内，跳过文本渲染
        if (isInFileCardLink) {
          return '';
        }

        // 对于普通文本，使用默认渲染
        return originalText(tokens, idx, options, env, renderer);
      };
      this.md.renderer.rules.table_open = () =>
        `<div class="md-table-wrap">  <div class="rsj-table-container"><span class="shadow-left"></span> <table class="rsj-table">\n`;
      this.md.renderer.rules.table_close = () => '</table><span class="shadow-right"></span></div></div>\n';
      this.md.renderer.rules.th_open = () => '<th class="md-th">';
      this.md.renderer.rules.td_open = () => '<td class="md-td">';
      this.md.inline.ruler.after('emphasis', 'cited', (state, silent) => {
        if (state.src.charCodeAt(state.pos) !== 0x5b) return false;
        // 修改正则表达式，匹配 [^xxx] 格式
        const match = /^\[\^([^\]]{1,600})\]/.exec(state.src.slice(state.pos));
        if (!match) return false;
        if (silent) return true;
        // 直接使用匹配到的内容，不需要substring处理
        const citationValue = match[1];
        const citations = citationValue.split(/[、,，\s]+/).filter(Boolean);
        state.push('span_open', 'span', 1);
        citations.forEach((citation, index) => {
          if (index > 0) {
            const text = state.push('text', '', 0);
            text.content = ' ';
          }
          const token = state.push('cited', '', 0);
          token.content = citation.trim();
          token.markup = match[0];
        });
        state.push('span_close', 'span', -1);
        state.pos += match[0].length;
        return true;
      });
      // 创建一个闭包来捕获当前组件实例的搜索结果
      const currentMessageSearchResults = () => this.messageSearchResults || [];
      // const instanceId = this.instanceId;
      // const messageId = this.message.id;

      this.md.renderer.rules.cited = (tokens, idx) => {
        const content = tokens[idx].content.trim();
        // 直接使用当前组件实例的搜索结果，避免调用可能被其他实例覆盖的方法
        const results = currentMessageSearchResults();
        const searchResult = results.find(item => item.index == content);

        // console.log(`cited renderer [${instanceId}] - messageId: ${messageId}, content: ${content}`);
        // console.log(`cited renderer [${instanceId}] - results: `, results);
        // console.log(`cited renderer [${instanceId}] - found searchResult: `, searchResult);

        let displayText = content; // 默认显示原内容

        if (searchResult) {
          // 优先显示URL域名，其次显示站点名称，最后显示原内容
          if (searchResult.site_name) {
            displayText = searchResult.site_name;
          } else if (searchResult.url) {
            try {
              const parsedUrl = new URL(searchResult.url);
              displayText = parsedUrl.hostname;
            } catch (error) {
              displayText = searchResult.url;
            }
          } else if (searchResult.title) {
            displayText =
              searchResult.title.length > 20 ? searchResult.title.substring(0, 20) + '...' : searchResult.title;
          }
        }

        return `<span class="custom-cited" data-cited="${content}" >${displayText}</span>`;
      };
      this.md.use(container, 'tablecard', {
        validate: params => params.trim().match(/^tablecard\s+(.*)$/) !== null,
        render: (tokens, idx) => {
          if (tokens[idx].nesting === 1) {
            const m = tokens[idx].info.trim().match(/^tablecard\s+(.*)$/);
            if (m && m[1]) {
              const tableToken = m[1];
              return `<div class="vue-component-wrapper" data-component="TableCardComponent" data-props='{"token":"${tableToken}"}'>`;
            }
            return '<div>';
          }
          return '</div>';
        },
      });
    },
    // 带哈希守护的渲染函数
    renderMarkdownWithHashGuard() {
      if (!this.md) return;
      if (!this.message.content) {
        this.blocks = [];
        this.lastContentHash = '';
        return;
      }

      // 计算内容哈希
      const currentHash = this.generateContentHash(this.message.content);

      // 如果哈希未改变，直接返回，避免重复渲染
      if (currentHash === this.lastContentHash) {
        return;
      }

      // 更新哈希值
      this.lastContentHash = currentHash;

      // 执行实际渲染
      this.renderMarkdown();
    },

    // 生成内容MD5哈希
    generateContentHash(content) {
      return md5(content);
    },
    // 处理SSE消息中的mermaid代码块收集
    processMermaidCodeBlocks(content) {
      // 如果内容没有变化，直接返回
      if (content === this.mermaidCollectionState.lastProcessedContent) {
        return content;
      }

      // 更新上次处理的内容
      this.mermaidCollectionState.lastProcessedContent = content;

      // 查找所有可能的mermaid代码块
      const mermaidRegex = /```mermaid\s*([\s\S]*?)```/g;
      let match;
      const completeMermaidBlocks = [];

      // 收集所有完整的mermaid代码块
      while ((match = mermaidRegex.exec(content)) !== null) {
        completeMermaidBlocks.push({
          fullMatch: match[0],
          content: match[1].trim(),
        });
      }

      // 如果没有找到完整的代码块，检查是否有未完成的代码块
      if (completeMermaidBlocks.length === 0) {
        // 查找最后一个未完成的mermaid代码块
        const lastMermaidStart = content.lastIndexOf('```mermaid');
        if (lastMermaidStart !== -1 && content.indexOf('```', lastMermaidStart + 10) === -1) {
          // 找到了未完成的mermaid代码块
          this.mermaidCollectionState.isCollecting = true;
          this.mermaidCollectionState.buffer = [content.substring(lastMermaidStart)];
        }
      } else {
        // 找到了完整的代码块，重置收集状态
        this.mermaidCollectionState.isCollecting = false;
        this.mermaidCollectionState.buffer = [];
      }

      return content;
    },

    // 在SSE过程中判断mermaid代码块是否完整
    isMermaidCodeCompleteInSSE(tokenContent) {
      // 获取原始消息内容
      const fullContent = this.message.content || '';

      // 查找包含当前token内容的mermaid代码块
      const mermaidRegex = /```mermaid\s*([\s\S]*?)```/g;
      let match;

      // 检查是否有完整的mermaid代码块包含当前内容
      while ((match = mermaidRegex.exec(fullContent)) !== null) {
        const blockContent = match[1].trim();
        if (blockContent === tokenContent) {
          // 找到了完整的代码块
          return true;
        }
      }

      // 检查是否有未完成的mermaid代码块
      const lastMermaidStart = fullContent.lastIndexOf('```mermaid');
      if (lastMermaidStart !== -1) {
        const afterMermaidStart = fullContent.substring(lastMermaidStart + 10);
        const nextTripleBacktick = afterMermaidStart.indexOf('```');

        if (nextTripleBacktick === -1) {
          // 没有找到结束标记，说明代码块不完整
          const incompleteContent = afterMermaidStart.trim();
          if (incompleteContent === tokenContent) {
            return false;
          }
        }
      }

      // 默认情况下，如果无法确定，则认为是完整的（避免阻塞正常渲染）
      return true;
    },

    renderMarkdown() {
      if (!this.md) return;
      if (!this.message.content) {
        this.blocks = [];
        return;
      }
      // let processedContent = this.message.content || '';
      let processedContent = this.fixMarkdownTable(this.message.content) || '';

      // 处理mermaid代码块收集
      // processedContent = this.processMermaidCodeBlocks(processedContent);

      // 处理颜色标记
      processedContent = this.processColorTags(processedContent);
      const validComponentRegex = /\[R@([^\]]{1,200})\]/g;
      const validMatches = [];
      let match;
      while ((match = validComponentRegex.exec(processedContent)) !== null) {
        validMatches.push({
          start: match.index,
          end: match.index + match[0].length,
          original: match[0],
          replacement: this.parseCustomComponent(match[0], match[1]),
        });
      }
      const allSegmentsRegex = /\[R@[^\]]*(?:\]|$)/g;
      const allSegments = [];
      while ((match = allSegmentsRegex.exec(processedContent)) !== null) {
        const isValidMatch = validMatches.some(
          validMatch => validMatch.start === match.index && validMatch.end === match.index + match[0].length
        );
        if (!isValidMatch) {
          allSegments.push({
            start: match.index,
            end: match.index + match[0].length,
            original: match[0],
            replacement: '',
          });
        }
      }
      const allChanges = [...allSegments, ...validMatches].sort((a, b) => b.start - a.start);
      for (const change of allChanges) {
        processedContent =
          processedContent.substring(0, change.start) + change.replacement + processedContent.substring(change.end);
      }
      const html = this.md.render(processedContent);
      const newBlocks = this.splitHtmlToBlocks(html);

      // 智能更新blocks，保留相同key的组件实例
      this.updateBlocksIntelligently(newBlocks);
      this.$nextTick(() => {
        this.calcTableWidth();
        this.bindCitedTooltipEvents();
        this.bindTableDownloadEvents();
        this.bindFileCardEvents();
        // 移除mountVueComponents调用，因为组件已经通过Vue template渲染
        // this.mountVueComponents();
        // 移除cleanupVueComponents调用，因为Vue会自动管理组件生命周期
        // this.$nextTick(() => {
        //   this.cleanupVueComponents();
        // });
      });
    },
    parseCustomComponent(match, componentContent) {
      const content = componentContent.trim();
      // 移除对cited的特殊处理，因为现在使用[^xxx]格式
      if (content.startsWith('表格卡片:')) {
        const tableToken = content.substring(5);
        return `\n::: tablecard ${tableToken}\n:::`;
      }
      if (content.startsWith('模态卡片:')) {
        const cardInfo = content.substring(5);
        const firstColonIndex = cardInfo.indexOf(':');
        if (firstColonIndex > 0) {
          const cardName = cardInfo.substring(0, firstColonIndex);
          const paramsStr = cardInfo.substring(firstColonIndex + 1);
          return `\n::: modalcard ${cardName}:${paramsStr}\n:::`;
        }
      }
      return match;
    },
    splitHtmlToBlocks(html) {
      // 处理两种类型的组件：
      // 1. 传统的vue-component-wrapper（用于TableCard等）
      // 2. 新的mermaid-placeholder（用于Mermaid组件）
      const componentRegex =
        /<span class="vue-component-wrapper-inline"[^>]*data-component="([^"]+)"[^>]*data-props='([^']*)'[^>]*><\/span>|<div class="vue-component-wrapper"[^>]*data-component="([^"]+)"[^>]*data-props='([^']*)'[^>]*(?:data-component-id="([^"]+)"[^>]*)?><\/div>/g;

      const mermaidRegex =
        /<mermaid-placeholder[^>]*data-content="([^"]*)"[^>]*data-component-id="([^"]*)"[^>]*data-content-hash="([^"]*)"[^>]*><\/mermaid-placeholder>/g;

      let lastIndex = 0;
      const blocks = [];
      let idx = 0;

      // 收集所有组件匹配项（包括位置信息）
      const allMatches = [];

      // 收集传统组件
      let match;
      while ((match = componentRegex.exec(html)) !== null) {
        allMatches.push({
          type: 'traditional',
          match,
          index: match.index,
          length: match[0].length,
        });
      }

      // 收集Mermaid组件
      while ((match = mermaidRegex.exec(html)) !== null) {
        allMatches.push({
          type: 'mermaid',
          match,
          index: match.index,
          length: match[0].length,
        });
      }

      // 按位置排序
      allMatches.sort((a, b) => a.index - b.index);

      // 处理所有匹配项
      allMatches.forEach(item => {
        // 添加前面的HTML内容
        if (item.index > lastIndex) {
          blocks.push({
            type: 'html',
            html: html.substring(lastIndex, item.index),
            key: `html-${idx++}`,
          });
        }

        if (item.type === 'traditional') {
          // 处理传统组件
          const match = item.match;
          const name = match[1] || match[3];
          const propsStr = match[2] || match[4];
          const componentId = match[5] || null;
          let props = {};
          try {
            props = JSON.parse(propsStr);
          } catch (e) {
            console.error('JSON解析失败 - propsStr:', propsStr);
            console.error('JSON解析错误:', e);
          }

          let stableKey;
          if (componentId) {
            stableKey = componentId;
          } else if (props.componentId) {
            stableKey = props.componentId;
          } else if (props.token) {
            stableKey = `${name}-${props.token}`;
          } else {
            stableKey = `${name}-${this.generateStableHash(JSON.stringify(props))}`;
          }

          blocks.push({
            type: 'component',
            name,
            props,
            key: stableKey,
          });
        } else if (item.type === 'mermaid') {
          // 处理Mermaid组件
          const match = item.match;
          const encodedContent = match[1];
          const componentId = match[2];
          const contentHash = match[3];

          const content = decodeURIComponent(encodedContent);
          const props = {
            content,
            componentId,
            contentHash,
          };

          // 使用固定的key策略，避免SSE过程中组件重新创建
          // 使用组件在blocks中的索引 + 固定字符串
          const fixedKey = `mermaid-component-${blocks.length}`;

          blocks.push({
            type: 'component',
            name: 'MermaidComponent',
            props,
            key: fixedKey,
          });
        }

        lastIndex = item.index + item.length;
      });

      // 添加剩余的HTML内容
      if (lastIndex < html.length) {
        blocks.push({
          type: 'html',
          html: html.substring(lastIndex),
          key: `html-${idx++}`,
        });
      }

      return blocks;
    },
    // 更新blocks数组
    updateBlocksIntelligently(newBlocks) {
      // Vue的响应式系统已经能很好地处理数组更新，直接赋值即可
      this.blocks = newBlocks;
    },

    // cleanupVueComponents方法已移除，因为Vue会自动管理组件生命周期

    // isMermaidComponentLoading方法已移除，因为不再需要手动管理组件加载状态
    calcTableWidth() {
      const tableContainers = document.querySelectorAll('.markdown-body .rsj-table-container');
      tableContainers.forEach(table => {
        if (table.scrollWidth > table.clientWidth) {
          table.classList.add('rsj-table-container--scroll');
          if (!table.getAttribute('data-scroll-initialized')) {
            table.setAttribute('data-scroll-initialized', 'true');
            const leftShadow = table.querySelector('.shadow-left');
            if (leftShadow) {
              leftShadow.classList.add('shadow-hidden');
            }
            table.addEventListener('scroll', () => {
              this.updateTableShadow(table);
            });
          }
        }
      });
      const customTableContainers = document.querySelectorAll('.table-card-component .table-container');
      customTableContainers.forEach(table => {
        if (table.scrollWidth > table.clientWidth) {
          table.classList.add('rsj-table-container--scroll');
          if (!table.getAttribute('data-scroll-initialized')) {
            table.setAttribute('data-scroll-initialized', 'true');
            const leftShadow = table.querySelector('.shadow-left');
            if (leftShadow) {
              leftShadow.classList.add('shadow-hidden');
            }
            table.addEventListener('scroll', () => {
              this.updateTableShadow(table);
            });
          }
        }
      });
    },
    // mountVueComponents方法已移除，因为组件现在通过Vue template渲染
    updateTableShadows() {
      const tableContainers = document.querySelectorAll('.markdown-body .rsj-table-container');
      tableContainers.forEach(this.updateTableShadow);
      const customTableContainers = document.querySelectorAll('.table-card-component .table-container');
      customTableContainers.forEach(this.updateTableShadow);
    },
    updateTableShadow(table) {
      if (!table) return;
      const leftShadow = table.querySelector('.shadow-left');
      const rightShadow = table.querySelector('.shadow-right');
      if (leftShadow) {
        if (table.scrollLeft > 0) {
          leftShadow.classList.remove('shadow-hidden');
        } else {
          leftShadow.classList.add('shadow-hidden');
        }
      }
      if (rightShadow) {
        const maxScrollLeft = table.scrollWidth - table.clientWidth;
        if (table.scrollLeft < maxScrollLeft) {
          rightShadow.classList.remove('shadow-hidden');
        } else {
          rightShadow.classList.add('shadow-hidden');
        }
      }
    },
    bindCitedTooltipEvents() {
      // 只绑定当前组件实例内的引用元素，避免跨组件事件混乱
      const container = this.$el;
      if (!container) return;

      container.querySelectorAll('.custom-cited').forEach(el => {
        // 移除旧事件避免重复绑定
        el.removeEventListener('mouseenter', this._citedEnter);
        el.removeEventListener('click', this._citedEnter);
        el.removeEventListener('mouseleave', this._citedLeave);

        // 添加鼠标点击和移入事件，同时支持两种交互方式
        el.addEventListener('click', this._citedEnter);
        el.addEventListener('mouseenter', this._citedEnter);
        el.addEventListener('mouseleave', this._citedLeave);
      });
    },

    // 统一的事件清理方法
    cleanupAllEvents() {
      const container = this.$el;
      if (!container) return;

      // 清理引用事件
      container.querySelectorAll('.custom-cited').forEach(el => {
        el.removeEventListener('mouseenter', this._citedEnter);
        el.removeEventListener('click', this._citedEnter);
        el.removeEventListener('mouseleave', this._citedLeave);
      });

      // 清理表格下载按钮事件
      container.querySelectorAll('.table-download-btn').forEach(btn => {
        btn.removeEventListener('click', this.handleTableDownload);
      });

      // 清理文件卡片事件
      container.querySelectorAll('.file-card-link').forEach(fileCard => {
        fileCard.removeEventListener('click', this.handleFileCardClick);
      });
    },

    // 绑定表格下载事件
    bindTableDownloadEvents() {
      const container = this.$el;
      if (!container) return;

      // 为markdown渲染的表格添加下载功能
      container.querySelectorAll('.md-table-wrap').forEach(tableWrap => {
        this.addTableDownloadButton(tableWrap, 'markdown');
      });
    },

    // 绑定文件卡片点击事件
    bindFileCardEvents() {
      const container = this.$el;
      if (!container) return;

      container.querySelectorAll('.file-card-link').forEach(fileCard => {
        // 移除旧事件避免重复绑定
        fileCard.removeEventListener('click', this.handleFileCardClick);

        // 添加点击事件
        fileCard.addEventListener('click', this.handleFileCardClick);
      });
    },

    // 处理文件卡片点击
    handleFileCardClick(event) {
      const href = event.currentTarget.dataset.href;
      if (href) {
        // 在新窗口打开文件链接
        const a = document.createElement('a');
        a.href = href;
        a.download = href || '';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },

    // 为表格添加下载按钮
    addTableDownloadButton(tableContainer, type) {
      // 检查是否已经添加过下载按钮
      if (tableContainer.querySelector('.table-download-btn')) {
        return;
      }

      const downloadBtn = document.createElement('div');
      downloadBtn.className = 'table-download-btn';
      downloadBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 10.5L5.5 8H7V4H9V8H10.5L8 10.5Z" fill="currentColor"/>
          <path d="M3 12H13V13H3V12Z" fill="currentColor"/>
        </svg>
      `;
      downloadBtn.dataset.tableType = type;

      // 添加点击事件
      downloadBtn.addEventListener('click', e => {
        e.stopPropagation();
        this.handleTableDownload(tableContainer, type);
      });

      // 将按钮添加到表格容器
      tableContainer.style.position = 'relative';
      tableContainer.appendChild(downloadBtn);

      // 为下载按钮添加tooltip
      this.$nextTick(() => {
        // 使用tippy.js创建tooltip
        tippy(downloadBtn, {
          content: '下载表格',
          placement: 'top',
          theme: 'custom-dark',
          delay: [0, 0],
          duration: [150, 100],
          arrow: true,
          hideOnClick: false,
          trigger: 'mouseenter focus',
          interactive: false,
          appendTo: () => document.body,
          zIndex: 9999,
        });
      });

      // 添加鼠标悬停事件，但需要检查消息是否正在响应
      const updateButtonVisibility = () => {
        // 检查消息是否正在响应
        const isResponding = this.message && this.message.isReponsing;
        if (isResponding) {
          downloadBtn.style.display = 'none';
        } else {
          downloadBtn.style.display = 'flex';
        }
      };

      tableContainer.addEventListener('mouseenter', () => {
        updateButtonVisibility();
        if (downloadBtn.style.display !== 'none') {
          downloadBtn.style.opacity = '1';
        }
      });

      tableContainer.addEventListener('mouseleave', () => {
        downloadBtn.style.opacity = '0';
      });

      // 初始检查响应状态
      updateButtonVisibility();
    },

    // 处理表格下载
    handleTableDownload(tableContainer, type) {
      let tableData = [];
      let headers = [];

      if (type === 'markdown') {
        const table = tableContainer.querySelector('table');
        if (!table) return;

        // 提取表头
        const headerCells = table.querySelectorAll('thead th');
        headers = Array.from(headerCells).map(th => th.textContent.trim());

        // 提取数据行
        const rows = table.querySelectorAll('tbody tr');
        tableData = Array.from(rows).map(row => {
          const cells = row.querySelectorAll('td');
          return Array.from(cells).map(td => td.textContent.trim());
        });
      }

      this.downloadTableAsCSV(headers, tableData);
    },

    // 下载表格为CSV格式
    downloadTableAsCSV(headers, data) {
      if (!headers.length || !data.length) {
        this.$Message.warning('表格数据为空，无法下载');
        return;
      }

      // 构建CSV内容
      const csvContent = [
        headers.join(','),
        ...data.map(row => row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')),
      ].join('\n');

      // 创建Blob对象
      const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `table_data_${new Date().getTime()}.csv`);
      link.style.visibility = 'hidden';

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.$Message.success('表格数据下载成功');
    },

    // 更新表格下载按钮的显示状态
    updateTableDownloadButtonsVisibility() {
      const container = this.$el;
      if (!container) return;

      const isResponding = this.message && this.message.isReponsing;

      // 更新markdown表格的下载按钮
      container.querySelectorAll('.table-download-btn').forEach(btn => {
        if (isResponding) {
          btn.style.display = 'none';
        } else {
          btn.style.display = 'flex';
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.markdown-body {
  background-color: transparent !important;
  font-size: 15px;
}

.markdown-render-container {
  position: relative;
}

:deep(.rsj-table-container) {
  max-width: 100%;
  font-size: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: auto;
}
:deep(.rsj-table) {
  tbody > :last-child {
    border-bottom: none;
  }
}
:deep(.md-table-wrap) {
  position: relative;
}
:deep(.rsj-table-container--scroll) {
  overflow-x: auto;
  .shadow-left {
    width: 16px;
    background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, rgba(48, 49, 51, 0.12) 100%);
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    border-radius: 6px 0 0 6px;
    &.shadow-hidden {
      display: none;
    }
  }
  .shadow-right {
    width: 16px;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(48, 49, 51, 0.12) 100%);
    z-index: 2;
    border-radius: 0 6px 6px 0;
    &.shadow-hidden {
      display: none;
    }
  }
}

:deep(.vue-component-wrapper-inline) {
  display: inline-flex;
  vertical-align: middle;
}

:deep(.vue-component-error) {
  color: #f44336;
  font-size: 12px;
  background: #ffebee;
  padding: 2px 4px;
  border-radius: 4px;
}

:deep(table) {
  font-size: 12px;
  border-collapse: collapse;
  border-spacing: 0;
  min-width: 100%;
  width: max-content;
  max-width: max-content;
  line-height: 1.6;
  // border-radius: 8px;

  tr {
    border-collapse: collapse;
    border-spacing: 0;
    border-bottom: 1px solid #e8e8e8;
    th,
    td {
      color: rgba(0, 0, 0, 0.6);
      text-align: left !important;
      padding: 0.66em 1em;
      vertical-align: middle;
      max-width: 448px;
      white-space: normal;
      box-sizing: border-box;
    }

    th {
      font-weight: bold;
      background-color: #f5f6f8;
      text-align: left;
      color: rgba(0, 0, 0, 0.9);
    }
    &:hover {
      td {
        background: #eeeeee59 !important;
      }
    }
  }
}

:deep(a) {
  color: #115bd4;
}

:deep(.custom-container) {
  padding: 0.1rem 1.5rem;
  border-left-width: 0.5rem;
  border-left-style: solid;
  margin: 1rem 0;
  border-radius: 4px;
}

:deep(.hljs) {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
}

@md-text-primary: #24292f;
@md-text-secondary: #57606a;
@md-text-tertiary: #6e7781;
@md-border-color: #d0d7de;
@md-code-bg: #f6f8fa;
@md-code-inline-bg: rgba(175, 184, 193, 0.2);
@md-link-color: #0969da;
@md-table-header-bg: #f6f8fa;
@md-table-alternate-bg: rgba(234, 238, 242, 0.5);

@md-dark-text-primary: #e6edf3;
@md-dark-text-secondary: #7d8590;
@md-dark-border-color: #30363d;

:deep(.custom-markdown:not(.pagination-container)) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: @md-text-primary;
  word-wrap: break-word;

  h1 {
    // font-size: 2em;
    margin: 1.8em 0 0.8em;
    border-bottom: 1px solid @md-border-color;
    padding-bottom: 0.3em;
    font-weight: 600;
  }
  h2 {
    // font-size: 1.5em;
    margin: 1.6em 0 0.6em;
    font-weight: 600;
  }
  h3 {
    // font-size: 1.25em;
    margin: 0.4em 0 0.4em;
  }
  h4 {
    // font-size: 1em;
    margin: 1.2em 0 0.2em;
  }
  h5 {
    // font-size: 0.875em;
    margin: 1em 0;
  }
  h6 {
    // font-size: 0.85em;
    color: @md-text-tertiary;
    margin: 1em 0;
  }

  ul,
  ol {
    padding-left: 2em;
    margin: 0.8em 0;
    list-style-position: outside;

    li {
      margin: 0.4em 0;
      padding-left: 0.4em;
      line-height: 1.6;

      & > p {
        margin: 0.4em 0;
      }

      // 嵌套列表的间距调整
      & > ul,
      & > ol {
        margin: 0.4em 0;
        padding-left: 1.8em;
      }
    }
  }

  // 一级无序列表使用实心圆点
  ul > li {
    list-style-type: disc;
  }

  // 二级无序列表使用空心圆点
  ul ul > li {
    list-style-type: circle;
  }

  // 三级及以上无序列表使用方块
  ul ul ul > li {
    list-style-type: square;
  }

  // 一级有序列表使用阿拉伯数字
  ol > li {
    list-style-type: decimal;
  }

  // 二级有序列表使用小写字母
  ol ol > li {
    list-style-type: lower-alpha;
  }

  // 三级有序列表使用小写罗马数字
  ol ol ol > li {
    list-style-type: lower-roman;
  }

  // 四级有序列表使用大写字母
  ol ol ol ol > li {
    list-style-type: upper-alpha;
  }

  // 五级及以上有序列表使用大写罗马数字
  ol ol ol ol ol > li {
    list-style-type: upper-roman;
  }

  // 混合嵌套：有序列表中的无序列表
  ol ul > li {
    list-style-type: disc;
  }

  ol ul ul > li {
    list-style-type: circle;
  }

  ol ul ul ul > li {
    list-style-type: square;
  }

  // 混合嵌套：无序列表中的有序列表
  ul ol > li {
    list-style-type: decimal;
  }

  ul ol ol > li {
    list-style-type: lower-alpha;
  }

  ul ol ol ol > li {
    list-style-type: lower-roman;
  }

  // 任务列表项样式
  .task-list-item {
    list-style-type: none;
    margin-left: -1.2em;

    input[type='checkbox'] {
      margin-right: 0.5em;
      vertical-align: middle;
    }
  }

  // 确保列表项内容对齐
  li::marker {
    color: @md-text-secondary;
    font-weight: normal;
  }

  // 深层嵌套时的额外间距调整
  ul ul ul ul,
  ol ol ol ol {
    padding-left: 1.6em;
  }

  ul ul ul ul ul,
  ol ol ol ol ol {
    padding-left: 1.4em;
  }

  pre {
    background: @md-code-bg;
    padding: 1.2em;
    border-radius: 6px;
    overflow-x: auto;
    margin: 1.2em 0;
    code {
      background: transparent;
      padding: 0;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }
  }

  code {
    font-family: inherit;
    background: @md-code-inline-bg;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 0.9em;
  }

  blockquote {
    margin: 1em 0;
    padding: 0 1em;
    color: @md-text-secondary;
    border-left: 4px solid @md-border-color;
  }
  hr {
    height: 1px;
    background: #d0d7de3d;
    border: 0;
    margin: 1em 0;
  }
  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 1em 0;
  }
  p {
    margin: unset;
    display: block;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    unicode-bidi: isolate;
  }
}
:deep(.custom-cited) {
  vertical-align: middle;
  font-variant: tabular-nums;
  box-sizing: border-box;
  color: rgb(93, 93, 93);
  cursor: pointer;
  background: rgb(244, 244, 244);
  border-radius: 6px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  margin-left: 4px;
  line-height: 16px;
  padding: 0 6px;
  font-size: 9px;
  vertical-align: baseline;
  // font-weight: 900;
  display: inline-flex;
  position: relative;
  align-items: center;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  top: -2px;
  // .cited-text {
  //   transform: scale(0.8);
  //   transform-origin: center;
  // }
  &:hover {
    background: #0d0d0d;
    color: #fff;
    transition: all 0.3s ease;
  }
}

:deep(.md-td) {
  white-space: pre-wrap;
}

// 表格下载按钮样式
:deep(.table-download-btn) {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  box-shadow: 0 0 4px 0px #d0d0d0;
  z-index: 5; // 降低z-index，确保不高过助手弹窗header
  color: #333;

  &:hover {
    // background: #f5f5f5;
    // color: #333;
    background: #efefef;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

// 文件卡片样式
:deep(.file-card-link) {
  display: inline-block;
  cursor: pointer;
  text-decoration: none;
  margin-right: 6px;
  margin-bottom: 8px;
  .file-card {
    border-radius: 6px;
    padding: 10px;
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.04);
    font-size: 14px;
    &:hover {
      background-color: rgba(0, 0, 0, 0.08);
    }

    // 文件信息样式已移至 file-card-content 中

    .file-card-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .file-name {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        flex: 1;
        margin-right: 12px;
        word-break: break-all;
        line-height: 22px;
      }

      .file-icon {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;

        .svg-icon {
          color: #115bd4;
          width: 22px;
          height: 22px;
        }
      }
    }
  }
}

// Mermaid加载状态样式
:deep(.mermaid-loading) {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin: 16px 0;

  .loading-content {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #6c757d;
    font-size: 14px;

    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #e9ecef;
      border-top: 2px solid #115bd4;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
