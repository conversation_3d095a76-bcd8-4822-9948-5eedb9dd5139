<template>
  <div class="message" :class="message.type === 'human' ? 'user' : 'assistant'">
    <!-- 深度搜索显示 -->

    <!-- 思考过程显示 -->
    <!-- <div v-if="isAIMessage && (isThinking || hasReasoning)" class="thinking">
      <div class="thinking-header">深度思考逻辑整理如下：</div>
      <div v-show="hasReasoning" class="reasoning-content">
        <div class="thinking-content">
          {{ reasoningContent }}
        </div>
      </div>
    </div> -->
    <!-- 消息内容 -->
    <div
      class="message-content"
      v-if="message.content?.length > 0"
      :class="{
        'markdown-body': isAIMessage,
        'error-message': isErrorMessage,
        'is-streaming': isAIMessage && message.isReponsing,
      }"
    >
      <!-- 用户消息 -->
      <template v-if="message.type === 'human'">
        <div class="user-message-wrapper" :class="{ editing: isEditing, 'is-responding': isLoading }">
          <div class="user-msg" v-if="!isEditing">{{ message.content }}</div>
          <div class="user-msg-editing" v-else>
            <Input
              ref="editInput"
              v-model="editingContent"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
              placeholder="输入你的问题"
              @keydown.native="handleEditKeydown"
            />
          </div>
          <!-- 用户消息操作按钮 -->
          <div class="user-actions" v-show="!isEditing && !isLoading">
            <div class="action-btn" @click="copyUserMessage" v-tooltip="'复制'">
              <svg-icon name="ai-copy" :size="14" />
            </div>
            <div class="action-btn" @click="startEditing" v-tooltip="'编辑'">
              <svg-icon name="edit" :size="14" />
            </div>
          </div>
          <div class="edit-actions" v-show="isEditing">
            <div class="edit-btn cancel-btn" @click="cancelEditing">取消</div>
            <div class="edit-btn send-btn" @click="confirmEditing">发送</div>
          </div>
        </div>
      </template>

      <!-- AI消息 -->
      <div v-else>
        <markdown-render
          :message="message"
          :search-results="searchResults"
          @showCitedTooltip="$emit('showCitedTooltip', $event)"
          @hideCitedTooltip="$emit('hideCitedTooltip')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownRender from './MarkdownRender.vue';

export default {
  name: 'MessageComponent',
  components: {
    MarkdownRender,
  },
  props: {
    message: {
      type: Object,
      required: true,
    },
    searchResults: {
      type: Array,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      messageState: {
        isReasoningExpanded: this.message?.isReasoningExpanded || false,
        upvote: this.message.upvote || '0',
        oppose: this.message.oppose || '0',
      },
      isEditing: false,
      editingContent: '',
    };
  },
  watch: {
    'message.isReasoningExpanded': {
      handler(newVal) {
        if (newVal !== undefined) {
          this.messageState.isReasoningExpanded = newVal;
        }
      },
      immediate: true,
    },
  },
  computed: {
    isAIMessage() {
      return this.message.type === 'AIMessageChunk';
    },

    aiMessage() {
      return this.isAIMessage ? this.message : null;
    },
    hasReasoning() {
      if (!this.aiMessage) return false;
      const hasContent =
        this.aiMessage.reasoning ||
        (this.aiMessage.additional_kwargs?.reasoning_content &&
          this.aiMessage.additional_kwargs.reasoning_content.length > 0);
      // 如果有内容，确保展开状态
      if (hasContent && !this.isReasoningExpanded) {
        this.$nextTick(() => {
          this.isReasoningExpanded = true;
        });
      }
      return hasContent;
    },
    reasoningContent() {
      if (!this.aiMessage) return '';
      return this.aiMessage.reasoning || this.aiMessage.additional_kwargs?.reasoning_content || '';
    },
    isReasoningExpanded: {
      get() {
        if (!this.aiMessage) return false;
        // 优先使用消息本身的状态，其次使用组件内部状态
        return this.aiMessage.isReasoningExpanded !== undefined
          ? this.aiMessage.isReasoningExpanded
          : this.messageState.isReasoningExpanded;
      },
      set(value) {
        this.messageState.isReasoningExpanded = value;
        if (this.aiMessage) {
          this.aiMessage.isReasoningExpanded = value;
        }
      },
    },
    isThinking() {
      if (!this.aiMessage) return false;
      return this.aiMessage.isThinking;
    },
    thinkDuration() {
      if (!this.aiMessage) return null;
      return this.aiMessage.thinkDuration;
    },
    isErrorMessage() {
      if (!this.isAIMessage) return false;
      return this.message.content.includes('连接错误');
    },
    hasSearchTools() {
      if (!this.aiMessage) return false;
      const tools = this.aiMessage.toolsProgress?.tools || [];
      return tools.some(tool => tool.tool_type === 'internet_search');
    },
    searchData() {
      if (!this.aiMessage) return null;

      // 创建多步骤搜索数据结构
      const searchData = {
        steps: [],
      };

      // 步骤1: 问题分析和规划
      if (this.aiMessage.step_plann_reasoning) {
        searchData.steps.push({
          step: 'analysis',
          title: '问题分析',
          analysis: this.aiMessage.step_plann_reasoning,
        });
      }

      // 提取工具和搜索结果
      const tools = this.aiMessage.toolsProgress?.tools || [];

      // 步骤2: 搜索工具 (非internet_search类型)
      const nonSearchTools = tools.filter(tool => tool.tool_type !== 'internet_search');
      if (nonSearchTools.length > 0) {
        searchData.steps.push({
          step: 'search',
          title: '执行搜索',
          tools: nonSearchTools,
        });
      }

      // 步骤3: 搜索结果
      const searchTool = tools.find(tool => tool.tool_type === 'internet_search');
      if (searchTool && searchTool.tool_other && searchTool.tool_other.length > 0) {
        searchData.steps.push({
          step: 'results',
          title: '搜索结果',
          results: searchTool.tool_other,
        });
      }

      return searchData;
    },
    searchQuery() {
      if (!this.aiMessage) return '';
      // 使用消息内容的前20个字符作为查询词
      return this.message.content?.substring(0, 20) || '';
    },
  },
  methods: {
    // 复制用户消息
    copyUserMessage() {
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard
          .writeText(this.message.content)
          .then(() => {
            this.$Message.success('复制成功');
          })
          .catch(() => {
            this.fallbackCopy(this.message.content);
          });
      } else {
        this.fallbackCopy(this.message.content);
      }
    },

    // 降级复制方法
    fallbackCopy(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        this.$Message.success('复制成功');
      } catch (err) {
        this.$Message.error('复制失败');
      }
      document.body.removeChild(textArea);
    },

    // 开始编辑
    startEditing() {
      this.isEditing = true;
      this.editingContent = this.message.content;
      this.$emit('editingStateChange', { messageId: this.message.id, isEditing: true });
      this.$nextTick(() => {
        this.$refs.editInput?.focus();
      });
    },

    // 取消编辑
    cancelEditing() {
      this.isEditing = false;
      this.editingContent = '';
      this.$emit('editingStateChange', { messageId: this.message.id, isEditing: false });
    },

    // 确认编辑
    confirmEditing() {
      if (!this.editingContent.trim()) {
        this.$Message.warning('消息内容不能为空');
        return;
      }

      // 发射编辑事件给父组件处理
      this.$emit('editMessage', {
        messageId: this.message.id,
        newContent: this.editingContent.trim(),
        is_edit: true,
        groupId: this.message.group_id, // 传递group_id用于消息分组追踪
      });

      this.isEditing = false;
      this.editingContent = '';
      this.$emit('editingStateChange', { messageId: this.message.id, isEditing: false });
    },

    // 处理编辑时的键盘事件
    handleEditKeydown(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.confirmEditing();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        this.cancelEditing();
      }
    },
  },
};
</script>

<style scoped lang="less">
.message {
  // max-width: 800px;
  font-size: 14px;

  :deep(.custom-markdown:not(.pagination-container)) {
    color: #333333;
  }
}

.message.user {
  margin-bottom: 0;
  justify-content: flex-end;
  // display: flex;
  // flex-wrap: wrap;

  .user-message-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    width: 100%;

    &.editing {
      align-items: stretch;
    }

    &:hover:not(.is-responding) .user-actions {
      opacity: 1;
    }
  }

  .user-msg {
    box-sizing: border-box;
    border-radius: 14px;
    padding: 8px 20px;
    background: #e4ecfe;
    border-radius: 10px 0 10px 10px;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .user-msg-editing {
    width: 100%;
    padding: 6px 12px;
    background: #fff;
    border: 2px solid #155bd4;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(21, 91, 212, 0.1);
    flex-basis: 100%;
    :deep(.ivu-input) {
      border: none;
      padding: 0;
      font-size: 14px;
      line-height: 1.5;
      resize: none;
      background: transparent;

      &:focus {
        box-shadow: none;
      }
    }
  }

  .user-actions {
    display: flex;
    margin-top: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;

    .action-btn {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: transparent;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      color: #666666;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
        color: #333333;
      }
    }
  }

  .edit-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    justify-content: flex-end;

    .edit-btn {
      padding: 6px 16px;
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &.cancel-btn {
        background: #f5f5f5;
        color: #666666;
        border: 1px solid #e0e0e0;

        &:hover {
          background: #eeeeee;
          color: #333333;
        }
      }

      &.send-btn {
        background: #155bd4;
        color: #ffffff;
        border: 1px solid #155bd4;

        &:hover {
          background: #3a7bdd;
          border-color: #3a7bdd;
        }
      }
    }
  }
}

.thinking {
  margin-top: 12px;
  padding: 0 14px 0 12px;
  background-color: #fff;
  border-left: 2px solid #dcdde0;
  font-size: 13px;
  color: #888888;
  line-height: 20px;

  :deep(.thinking-content) {
    word-wrap: break-word;
    white-space: pre-wrap;
    font-size: 13px;
    color: #888888;
    line-height: 20px;

    p {
      margin: 12px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.markdown-body {
  :deep(p:last-child) {
    margin-bottom: 0;
  }
}

.expand-icon {
  font-style: normal;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.reasoning-content {
  font-size: 13px;
  line-height: 1.7;
  color: rgba(0, 0, 0, 0.8);
  margin-top: 8px;
}

.message-content {
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.9);
}

.assistant .message-content {
  margin-top: 16px;
}

.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 20px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background-color: #666;
  border-radius: 50%;
  display: inline-block;
  animation: loading-dots 1.4s infinite;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-dots {
  0%,
  100% {
    transform: scale(0.3);
    opacity: 0.2;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
}

.message-actions {
  display: flex;
  gap: 8px;
  opacity: 0.8;
  margin-top: 8px;
}

.message-actions .el-button {
  padding: 4px 8px;
  height: 32px;
  border-radius: 4px;
}

.message-actions .el-button:hover {
  background-color: var(--el-fill-color-light);
}

.message-actions .el-button.active {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.message-actions .el-icon {
  font-size: 16px;
}

.error-message {
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 8px 16px;
  border-radius: 4px;
  margin-bottom: 8px;
}
</style>
