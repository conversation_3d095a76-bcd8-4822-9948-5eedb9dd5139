<template>
  <div class="tools-progress-container">
    <div class="message-status" @click="toggleExpand">
      <div style="margin-right: 8px; display: flex; align-items: center; min-width: 14px" v-if="!isError && !isStop">
        <div v-if="loading" class="loader-box">
          <span class="three-body">
            <div class="three-body__dot"></div>
            <div class="three-body__dot"></div>
            <div class="three-body__dot"></div>
          </span>
        </div>
        <svg-icon v-else name="ai-done-1" :size="14" style="margin-left: -2px" />
      </div>
      <div class="text">
        <span v-if="isStop">已暂停生成</span>
        <span v-else-if="isError">已停止生成</span>
        <span v-else>{{ getToolsStatusText(type).desc }}</span>
        <span v-if="!isError && !isStop && loading" class="loading-dots">
          <span>.</span><span>.</span><span>.</span>
        </span>
      </div>
      <!-- <div v-if="!!duration" class="usage-time">(用时{{ duration }}秒)</div> -->
      <!-- <div class="icon">
        <svg-icon name="ai-t-arrow" :size="11" :class="{ 'arrow-rotated': !isExpanded }" />
      </div> -->
    </div>
    <!-- <template v-if="isExpanded || inAnimation">
      <transition name="collapse" appear>
        <div v-show="isExpanded || inAnimation" class="tools-progress">
          <Timeline>
            <TimelineItem v-for="(progress, i) in steps" :key="i">
              <template #dot>
                <span v-if="progress.status === 'processing' && loading" class="progress-loader"></span>
                <svg-icon v-else name="ai-done" :size="13" style="margin: -2px 0 0 -1px" />
              </template>
              <div class="progress-content">
                <div class="title">
                  <span>{{ progress.title }}</span>
                  <span>{{ progress.status === 'processing' && loading ? '中' : '完成' }}</span>
                  <span v-if="progress.status === 'processing' && loading" class="loading-dots">
                    <span>.</span><span>.</span><span>.</span>
                  </span>
                </div>
                <div v-if="progress.tools?.length > 0" class="tools">
                  <ul class="tools-ul">
                    <li class="tools-li" v-for="(tool, i) in progress.tools" :key="i">
                      <div class="tools-li-wrap">
                        {{ tool.tool_name ? tool.tool_name : tool }}
                        <span class="tools-li-link" v-if="tool?.tool_other?.length" @click="handleOpenQuoteModal(tool)">
                          <span class="link">已搜索到{{ tool.tool_other.length }}个网页</span>
                          <span class="arrow">
                            <svg-icon name="ai-r-arrow" :size="11" />
                          </span>
                        </span>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="progress-content" v-if="progress.content">
                {{ progress.content }}
              </div>
            </TimelineItem>
          </Timeline>
        </div>
      </transition>
    </template> -->
  </div>
</template>

<script>
export default {
  name: 'ToolsProgress',
  components: {},
  props: {
    steps: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    // showTime: {
    //   type: Boolean,
    //   default: false,
    // },
    isStop: {
      type: Boolean,
      default: false,
    },
    isError: {
      type: Boolean,
      default: false,
    },
    duration: {
      type: Number,
      default: 0,
    },
    type: {
      type: String,
      default: '',
    },
    messageId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isExpanded: false,
      inAnimation: false,
    };
  },
  computed: {
    getToolsStatusText() {
      return type => {
        switch (type) {
          // case 'tool_result':
          //   return {
          //     status: 'analysis',
          //     desc: '问题分析中',
          //   };
          case 'tools_using_start':
          case 'step_thinking_start':
          case 'step_thinking':
          case 'step_thinking_end':
          case 'tools_using_end':
          case 'step_analysis_start':
          case 'step_analysis':
          case 'step_analysis_end':
          case 'tool_using':
          case 'step_start':
          case 'step_end':
          case 'tool_result':
          case 'step_status':
          case 'step_status_end':
            return {
              status: 'searching',
              desc: '正在思考中',
            };
          case 'reasoning_start':
          case 'reasoning':
          case 'reasoning_end':
            return {
              status: 'reasoning',
              desc: '深度思考中',
            };
          case 'content_start':
          case 'content':
            return {
              status: 'generating',
              desc: '正在回答中',
            };
          case 'content_end':
            return {
              status: 'done',
              desc: '已回答完成',
            };
          default:
            return {
              status: 'pending',
              desc: '问题分析中',
            };
        }
      };
    },
  },
  watch: {
    // loading: {
    //   handler(newVal) {
    //     console.log('%c [ newVal ]-155-「ToolsProgress.vue」', 'font-size:13px; background:#98918e; color:#dcd5d2;', newVal)
    //     if (newVal) {
    //       this.isExpanded = true;
    //       this.inAnimation = true;
    //     } else {
    //       this.inAnimation = false;
    //       this.isExpanded = false;
    //     }
    //   },
    //   immediate: true,
    // },
  },
  methods: {
    toggleExpand() {
      console.log('toggleExpand');
      this.isExpanded = !this.isExpanded;
      this.inAnimation = !this.inAnimation;
    },
    handleOpenQuoteModal(tools) {
      if (!tools.tool_other || tools.tool_other.length <= 0) {
        this.$Message.error('无引用数据');
        return;
      }
      this.$emit('handleOpenQuoteModal', tools, this.messageId);
    },
  },
};
</script>

<style scoped lang="less">
.tools-progress-container {
  display: flex;
  flex-direction: column;
  position: relative;
}

.tools-progress {
  flex: 1;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  margin-top: 8px;

  .progress-content {
    font-size: 13px;
    color: #888888;
    line-height: 20px;
    margin-top: 8px;
  }
}

/* 高度过渡动画 */
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s;
  max-height: 500px;
  overflow: hidden;
}

.collapse-enter,
.collapse-leave-to {
  max-height: 0;
  opacity: 0;
  margin-top: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 2px;

  span {
    display: inline-block;
    animation: loadingDots 1.4s infinite;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

@keyframes loadingDots {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
}

.message-status {
  width: fit-content;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 16px;
  background: #f9fafb;
  border-radius: 8px;
  cursor: pointer;
  position: relative;

  .text {
    color: #333333;
    margin-right: 8px;
  }

  .usage-time {
    color: #999999;
    margin-right: 8px;
  }

  .icon {
    display: flex;
    align-items: center;
    color: #999999;
    transition: color 0.2s;

    .svg-icon {
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .arrow-rotated {
      transform: rotate(180deg);
    }
  }

  .loader-box {
    position: absolute;
    left: 8px;
    top: 8px;
  }
}

.message-status:hover {
  .icon {
    color: #115bd4;
  }
}

:deep(.ivu-timeline-item-head-custom) {
  background: unset;
  width: 30px;
  margin-top: 8px;
}

:deep(.ivu-timeline-item:last-child) {
  padding-bottom: 0;
}

:deep(.ivu-timeline-item-tail) {
  left: 1px;
  top: 2px;
}

:deep(.ivu-timeline-item-content) {
  padding-top: 0;
  padding-left: 17px;
  padding-bottom: 0;
}

.progress-content {
  .title {
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
  }

  .tools {
    margin-top: 8px;
    font-size: 13px;
    color: #888888;
    line-height: 20px;

    .tools-ul {
      width: 100%;

      .tools-li {
        width: e('calc(100% - 20px)');
        margin-top: 8px;
        list-style-type: disc;
        margin-left: 20px;

        .tools-li-wrap {
          margin-left: -6px;
          font-size: 13px;
          color: #888888;
          line-height: 20px;

          .tools-li-link {
            width: fit-content;
            cursor: pointer;

            > .link {
              text-decoration: underline;
              color: #aaaaaa;
              font-size: 12px;
              margin-right: 2px;
            }
          }

          .tools-li-link:hover {
            color: #115bd4;

            > .link {
              color: #115bd4;
            }
          }
        }
      }

      .tools-li:first-child {
        margin-top: 0;
      }

      .tools-li::marker {
        font-size: 16px;
        color: #888;
        display: inline-block;
      }
    }
  }
}

.loader {
  width: 14px;
  aspect-ratio: 1;
  --_g: no-repeat radial-gradient(farthest-side, #000 100%, #f7f8f9);
  background: var(--_g) 60% 0, var(--_g) 0 96%, var(--_g) 100% 96%;
  background-size: 35% 35%;
  animation: l16 0.8s infinite;
  opacity: 0;
}

@keyframes l16 {
  50% {
    background-position: 100% 96%, 60% 0, 0 96%;
    opacity: 0.7;
  }
  100% {
    background-position: 100% 96%, 60% 0, 0 96%;
    opacity: 1;
  }
}

.progress-loader {
  width: 12px;
  height: 12px;
  display: inline-block;
  position: relative;
}

.progress-loader::after,
.progress-loader::before {
  content: '';
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #155bd4;
  position: absolute;
  left: 0;
  top: 0;
  transform: scale(0);
  animation: progress-animloader 2s linear infinite;
}

.progress-loader::after {
  animation-delay: 1s;
}

@keyframes progress-animloader {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
</style>

<style scoped>
/* From Uiverse.io by G4b413l */
.three-body {
  --uib-size: 14px;
  --uib-speed: 0.8s;
  --uib-color: #115bd4;
  position: relative;
  display: inline-block;
  height: var(--uib-size);
  width: var(--uib-size);
  animation: spin78236 calc(var(--uib-speed) * 2.5) infinite linear;
}

.three-body__dot {
  position: absolute;
  height: 100%;
  width: 30%;
}

.three-body__dot:after {
  content: '';
  position: absolute;
  height: 0%;
  width: 100%;
  padding-bottom: 100%;
  background-color: var(--uib-color);
  border-radius: 50%;
}

.three-body__dot:nth-child(1) {
  bottom: 5%;
  left: 0;
  transform: rotate(60deg);
  transform-origin: 50% 85%;
}

.three-body__dot:nth-child(1)::after {
  bottom: 0;
  left: 0;
  animation: wobble1 var(--uib-speed) infinite ease-in-out;
  animation-delay: calc(var(--uib-speed) * -0.3);
}

.three-body__dot:nth-child(2) {
  bottom: 5%;
  right: 0;
  transform: rotate(-60deg);
  transform-origin: 50% 85%;
}

.three-body__dot:nth-child(2)::after {
  bottom: 0;
  left: 0;
  animation: wobble1 var(--uib-speed) infinite calc(var(--uib-speed) * -0.15) ease-in-out;
}

.three-body__dot:nth-child(3) {
  bottom: -5%;
  left: 0;
  transform: translateX(116.666%);
}

.three-body__dot:nth-child(3)::after {
  top: 0;
  left: 0;
  animation: wobble2 var(--uib-speed) infinite ease-in-out;
}

@keyframes spin78236 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes wobble1 {
  0%,
  100% {
    transform: translateY(0%) scale(1);
    opacity: 1;
  }

  50% {
    transform: translateY(-66%) scale(0.65);
    opacity: 0.8;
  }
}

@keyframes wobble2 {
  0%,
  100% {
    transform: translateY(0%) scale(1);
    opacity: 1;
  }

  50% {
    transform: translateY(66%) scale(0.65);
    opacity: 0.8;
  }
}
</style>
