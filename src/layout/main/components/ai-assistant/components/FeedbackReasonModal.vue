<template>
  <div>
    <transition name="feedback-modal-bg-fade">
      <div class="feedback-reason-wrap" v-if="visible" @click="handleMaskClick">
        <div class="feedback-reason-modal" @click.stop>
          <div class="feedback-modal-content">
            <div class="header">
              <div class="title">谢谢你的反馈，我们会继续优化进步</div>
              <div class="close-btn" @click="handleClose">
                <svg-icon name="AI-modal-close" :size="12" />
              </div>
            </div>
            <div class="content">
              <!-- 理解问题有误 -->
              <div class="reason-section">
                <div class="section-title">理解问题有误</div>
                <div class="reason-options">
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('没有理解问题，答非所问') }"
                    @click="toggleReason('没有理解问题，答非所问')"
                  >
                    没有理解问题，答非所问
                  </div>
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('未理解上下文信息') }"
                    @click="toggleReason('未理解上下文信息')"
                  >
                    未理解上下文信息
                  </div>
                </div>
              </div>

              <!-- 回复存在错误 -->
              <div class="reason-section">
                <div class="section-title">回复存在错误</div>
                <div class="reason-options">
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('有事实性错误') }"
                    @click="toggleReason('有事实性错误')"
                  >
                    有事实性错误
                  </div>
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('存在逻辑问题') }"
                    @click="toggleReason('存在逻辑问题')"
                  >
                    存在逻辑问题
                  </div>
                </div>
                <div class="reason-options">
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('格式错误') }"
                    @click="toggleReason('格式错误')"
                  >
                    格式错误
                  </div>
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('乱码错误') }"
                    @click="toggleReason('乱码错误')"
                  >
                    乱码错误
                  </div>
                </div>
              </div>

              <!-- 存在违法有害信息 -->
              <div class="reason-section">
                <div class="section-title">存在违法有害信息</div>
                <div class="reason-options">
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('出现违法信息、有害建议') }"
                    @click="toggleReason('出现违法信息、有害建议')"
                  >
                    出现违法信息、有害建议
                  </div>
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('价值观不正确') }"
                    @click="toggleReason('价值观不正确')"
                  >
                    价值观不正确
                  </div>
                </div>
                <div class="reason-options">
                  <div
                    class="reason-option"
                    :class="{ active: selectedReasons.includes('存在偏见和歧视内容') }"
                    @click="toggleReason('存在偏见和歧视内容')"
                  >
                    存在偏见和歧视内容
                  </div>
                </div>
              </div>

              <!-- 自定义输入框 -->
              <div class="custom-input-section">
                <Input
                  v-model="customReason"
                  type="textarea"
                  :autosize="{ minRows: 4, maxRows: 7 }"
                  placeholder="请写下更多反馈内容"
                  class="custom-input"
                />
              </div>
            </div>

            <div class="footer">
              <Button type="primary" @click="handleSubmit" :loading="submitting"> 提交 </Button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'FeedbackReasonModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectedReasons: [],
      customReason: '',
      submitting: false,
    };
  },
  watch: {
    visible(newVal) {
      if (!newVal) {
        // 关闭时重置数据
        this.selectedReasons = [];
        this.customReason = '';
        this.submitting = false;
      }
    },
  },
  methods: {
    toggleReason(reason) {
      const index = this.selectedReasons.indexOf(reason);
      if (index > -1) {
        this.selectedReasons.splice(index, 1);
      } else {
        this.selectedReasons.push(reason);
      }
    },

    handleMaskClick() {
      this.handleClose();
    },

    handleClose() {
      this.$emit('update:visible', false);
    },

    async handleSubmit() {
      // 防抖：如果正在提交，直接返回
      if (this.submitting) {
        return;
      }

      this.submitting = true;

      try {
        await this.$emit('confirm', {
          selectedReasons: this.selectedReasons,
          customReason: this.customReason,
        });
      } finally {
        this.submitting = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
// 背景遮罩样式
.feedback-reason-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 弹窗本体样式
.feedback-reason-modal {
  width: 400px;
  max-height: 80vh;
  background: #fff;
  border-radius: 8px;
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
}

.feedback-modal-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .close-btn {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #f5f5f5;
    }
  }
}

.content {
  flex: 1;
  padding: 20px 24px 10px;
  overflow-y: auto;

  .reason-section {
    margin-bottom: 16px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
    }

    .reason-options {
      display: flex;
      flex-wrap: wrap;
      column-gap: 8px;
      row-gap: 6px;
      margin: 0 1px;
    }

    .reason-option {
      box-sizing: border-box;
      cursor: pointer;
      background-color: #F7F7F7;
      color: rgba(0, 0, 0, 0.6);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      line-height: 26px;
      padding: 4px 2px;
      position: relative;
      text-align: center;
      width: calc(~'(100% - 8px) / 2');
      text-align: center;
      // border: 1px solid rgba(0, 0, 0, 0.08);
      margin-bottom: 8px;
      &:hover {
        background: #f2f2f2;
      }

      &.active {
        background: #f2f2f2;
        color: rgba(0, 0, 0, 0.8);
        font-weight: 500;
        box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.9);
      }
    }
  }

  .custom-input-section {
    margin-top: 20px;
    .custom-input {
      width: 100%;
      :deep(.ivu-input) {
        border: none;
        background-color: #f3f3f3;
        padding: 8px;
        border-radius: 6px;
        &:focus {
          // border-color: #40a9ff;
          // box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          background-color: #fff;
          box-shadow: inset 0 0 0 2px #115bd4;
        }
      }
    }
  }
}

.footer {
  padding: 16px 24px 20px;
  // border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;

  .ivu-btn {
    width: 100%;
    min-width: 80px;
  }
}

// 动画效果
.feedback-modal-bg-fade-enter-active,
.feedback-modal-bg-fade-leave-active {
  transition: opacity 0.3s ease;

  .feedback-reason-modal {
    transition: transform 0.3s ease;
  }
}

.feedback-modal-bg-fade-enter,
.feedback-modal-bg-fade-leave-to {
  opacity: 0;

  .feedback-reason-modal {
    transform: scale(0.9);
  }
}
</style>
