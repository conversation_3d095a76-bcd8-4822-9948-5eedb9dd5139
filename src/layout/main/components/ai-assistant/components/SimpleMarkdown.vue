<template>
  <div class="simple-markdown" :class="{ 'streaming-text': isResponding }" v-html="formattedContent"></div>
</template>

<script>
import MarkdownIt from 'markdown-it';

export default {
  name: 'SimpleMarkdown',
  props: {
    content: {
      type: String,
      default: '',
    },
    isResponding: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      md: new MarkdownIt({
        html: false,
        linkify: true,
        typographer: true,
      }),
    };
  },
  computed: {
    formattedContent() {
      if (!this.content) return '';
      // 使用markdown-it将Markdown转换为HTML
      return this.md.render(this.content);
    },
  },
};
</script>

<style scoped lang="less">
.simple-markdown {
  font-size: 14px;
  line-height: 1.6;
  font-family: sans-serif;
  :deep(h1) {
    font-size: 1.8em;
    margin: 0.67em 0;
  }

  :deep(h2) {
    font-size: 1.5em;
    margin: 0.83em 0;
  }

  :deep(h3) {
    font-size: 1.17em;
    margin: 1em 0;
  }

  :deep(pre) {
    background-color: #f5f5f5;
    padding: 8px 12px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 10px 0;
  }

  :deep(code) {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
  }

  :deep(a) {
    color: #0366d6;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  :deep(p) {
    margin: 0;
    line-height: 20px;
    font-size: 13px;
    text-align: justify;
    font-weight: normal;
  }

  :deep(ul),
  :deep(ol) {
    padding-left: 20px;
    margin: 8px 0;
  }

  :deep(blockquote) {
    border-left: 4px solid #dfe2e5;
    padding-left: 16px;
    margin: 8px 0;
    color: #666;
  }

  :deep(hr) {
    border: 0;
    border-top: 1px solid #eaecef;
    margin: 16px 0;
  }

  &.streaming-text {
    position: relative;
  }
  :deep(strong) {
    color: #444;
    font-weight: 500;
  }
}
</style>
