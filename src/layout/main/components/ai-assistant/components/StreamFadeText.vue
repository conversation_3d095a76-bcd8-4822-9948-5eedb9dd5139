<template>
  <p>
    <template v-if="isDisabled">{{ formatText }}</template>
    <template v-else>
      <span v-for="(char, idx) in allChars" :key="`char-${idx}`" :class="getCharClass(idx)">{{ char }}</span>
    </template>
  </p>
</template>

<script>
export default {
  name: 'StreamFadeText',
  props: {
    text: {
      type: String,
      required: true,
    },
    duration: {
      type: Number,
      default: 40,
    },
    fadeDuration: {
      type: Number,
      default: 300,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      allChars: [], // 存储所有字符
      visibleCount: 0, // 已显示的字符数量
      charTimer: null,
    };
  },
  computed: {
    formatText() {
      return this.text.replace('[信息检索已完成]', '');
    },
  },
  watch: {
    text: {
      immediate: true,
      handler(val) {
        val = val.replace('[信息检索已完成]', '');
        if (val) {
          const newChars = val.split('');
          const oldLength = this.allChars.length;

          // 更新所有字符数组
          this.allChars = newChars;

          // 如果有新字符，开始动画
          if (newChars.length > oldLength) {
            this.animateNewChars(oldLength);
          }
        }
      },
    },
  },
  methods: {
    animateNewChars(startIndex) {
      if (this.charTimer) clearInterval(this.charTimer);

      let currentIndex = Math.max(startIndex, this.visibleCount);
      this.charTimer = setInterval(() => {
        if (currentIndex < this.allChars.length) {
          this.visibleCount = currentIndex + 1;
          currentIndex++;
        } else {
          clearInterval(this.charTimer);
        }
      }, this.duration);
    },
    getCharClass(index) {
      if (index < this.visibleCount) {
        return 'char-visible'; // 已显示的字符
      } else {
        return 'char-animating'; // 正在动画的字符
      }
    },
  },
  beforeDestroy() {
    clearInterval(this.charTimer);
  },
};
</script>

<style scoped>
.char-visible {
  opacity: 1;
  display: inline;
  line-height: 20px;
  font-size: 13px;
  margin: 0;
  font-weight: normal;
}

.char-animating {
  opacity: 0;
  display: inline;
  line-height: 20px;
  font-size: 13px;
  margin: 0;
  font-weight: normal;
  animation: fadeInChar var(--fade-duration, 300ms) forwards;
}

@keyframes fadeInChar {
  to {
    opacity: 1;
  }
}

p {
  margin: 0;
  line-height: 20px;
  font-size: 13px;
  font-weight: normal;
}
</style>
