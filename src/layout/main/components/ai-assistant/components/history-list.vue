<template>
  <transition name="slide-fade">
    <div v-show="visible" class="history-list-wrapper" @click.stop>
      <div class="history-list-header">
        <div class="history-title">历史记录</div>
        <div class="close-icon" @click="$emit('update:visible', false)">
          <svg-icon name="AI-modal-close" size="12" />
        </div>
      </div>
      <div class="history-list-content">
        <div class="history-items" v-if="historyList.length">
          <div class="history-group" v-for="(item, index) in groupedHistoryList" :key="index">
            <div class="group-name">{{ item.name }}</div>
            <div
              class="history-item"
              v-for="item in item.list"
              :class="{ active: sessionId === item.session_id }"
              @click="handleSelectItem(item)"
              :key="item.session_id"
            >
              <div class="item-content" v-if="editSessionId !== item.session_id">
                <span class="chat-title">
                  {{ item.title || '新会话' }}
                </span>
                <div class="action-box" @click.stop="handleActionBoxClick(item.session_id)">
                  <Dropdown
                    stop-propagation
                    trigger="custom"
                    placement="bottom-end"
                    :visible="activeDropdownSessionId === item.session_id"
                    @on-visible-change="visible => handleDropdownVisibleChange(visible, item.session_id)"
                    @on-click="name => handleActionClick(name, item)"
                  >
                    <svg-icon name="AI-history-more" size="12" />
                    <DropdownMenu slot="list">
                      <DropdownItem name="edit">编辑名称</DropdownItem>
                      <DropdownItem name="delete" style="color: #ee3838">删除</DropdownItem>
                    </DropdownMenu>
                  </Dropdown>
                </div>

                <div class="action-box-shadow"></div>
              </div>
              <div class="item-input" v-else @click.stop>
                <input
                  type="text"
                  ref="titleInput"
                  v-model="editingTitle"
                  @blur="handleEditNameBlur(item)"
                  @keydown.enter="$refs.titleInput[0]?.blur()"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-history">
          <svg-icon name="AI-history-empty" style="width: 208px; height: 160px"></svg-icon>
          <div class="empty-text">暂无历史数据</div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import TipModel from '@/components/confirmModal/index.js';
import { assistantService } from '@/libs/SSE/SSEService';
// import { debounce  } from 'lodash-es';
import moment from 'moment';
export default {
  name: 'history-list',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    historyList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      sessionId: '',
      editSessionId: '',
      deleteLoading: false,
      editingTitle: '',
      activeDropdownSessionId: '', // 当前激活的下拉菜单的session_id
    };
  },
  computed: {
    // 将历史记录分组
    groupedHistoryList() {
      const startOfToday = new Date();
      startOfToday.setHours(0, 0, 0, 0);
      const todayStart = startOfToday.getTime();
      const startOfMonth = new Date(startOfToday.getFullYear(), startOfToday.getMonth(), 1, 0, 0, 0, 0).getTime();

      let today = [];
      let current = [];
      let earlier = [];
      this.historyList.map(item => {
        const itemTime = new Date(item.updated_time).getTime();
        if (itemTime >= todayStart) {
          today.push(item);
        } else if (itemTime >= startOfMonth) {
          current.push(item);
        } else {
          earlier.push(item);
        }
      });
      let groupedList = [];
      if (today.length) {
        groupedList.push({
          name: '今日',
          list: today,
        });
      }
      if (current.length) {
        groupedList.push({
          name: '本月',
          list: current,
        });
      }
      if (earlier.length) {
        groupedList.push({
          name: '更早',
          list: earlier,
        });
      }
      return groupedList;
    },
  },
  watch: {
    visible(val) {
      if (val && this.historyList.length > 0) {
        // 如果有sessionId，找到匹配的项
        const sessionId = localStorage.getItem('ai_assistant_session_id');
        if (sessionId) {
          this.sessionId = sessionId;
        }
      }
      // 当历史记录弹窗关闭时，关闭所有下拉菜单
      if (!val) {
        this.activeDropdownSessionId = '';
        this.editSessionId = '';
      }
    },
  },
  mounted() {
    // 添加全局点击事件监听器
    document.addEventListener('click', this.handleGlobalClick);
  },
  beforeDestroy() {
    // 移除全局点击事件监听器
    document.removeEventListener('click', this.handleGlobalClick);
  },
  methods: {
    handleEditNameBlur(item) {
      if (!this.editingTitle.trim()) {
        return;
      }
      this.$emit('editTitle', this.editingTitle, item.session_id);
      this.editSessionId = '';
      this.editingTitle = '';
    },
    handleActionClick(name, item) {
      console.log('%c [ name ]-116-「history-list.vue」', 'font-size:13px; background:#215fc3; color:#65a3ff;', name);
      // 点击菜单项后立即关闭dropdown
      this.activeDropdownSessionId = '';

      if (name === 'edit') {
        this.handleEditName(item);
      } else if (name === 'delete') {
        this.handleDeleteClick(item);
      }
    },
    handleEditName(item) {
      // this.editIndex
      this.editSessionId = item.session_id;
      this.editingTitle = item.title;
      // const ref = this.$refs.titleInput;
      // console.log('%c [ ref ]-148-「history-list.vue」', 'font-size:13px; background:#7e759c; color:#c2b9e0;', ref);
      this.$nextTick(() => {
        this.$refs.titleInput[0]?.focus();
      });
    },
    handleSelectItem(item) {
      this.sessionId = item.session_id;
      // 选择其他对话时，关闭当前打开的下拉菜单
      this.activeDropdownSessionId = '';
      this.$emit('select', item);
    },

    // 处理操作按钮点击
    handleActionBoxClick(sessionId) {
      // 如果当前dropdown已经打开，则关闭它
      if (this.activeDropdownSessionId === sessionId) {
        this.activeDropdownSessionId = '';
      } else {
        // 否则打开当前dropdown（会自动关闭其他已打开的dropdown）
        this.activeDropdownSessionId = sessionId;
      }
    },

    // 处理下拉菜单显示状态变化
    handleDropdownVisibleChange(visible, sessionId) {
      if (visible) {
        // 确保只有一个dropdown处于打开状态
        this.activeDropdownSessionId = sessionId;
      } else {
        // 只有当前关闭的dropdown是激活状态时才清空
        if (this.activeDropdownSessionId === sessionId) {
          this.activeDropdownSessionId = '';
        }
      }
    },

    handleDeleteClick(item) {
      TipModel({
        title: '删除对话',
        content: '确定要删除该会话吗？',
        contentText: '删除后不可恢复，是否继续？',
        confirmText: '删除',
        cancelText: '取消',
        loading: this.deleteLoading,
      })
        .then(async () => {
          this.deleteLoading = true;
          try {
            await assistantService.deleteChat({ session_id: item.session_id });
            this.$emit('handleHistoryDeleted', item.session_id);
            this.$Message.success('删除成功');
          } catch (e) {
            this.$Message.error('删除失败');
          } finally {
            this.deleteLoading = false;
          }
        })
        .catch(() => {});
    },

    // 获取项目在原始列表中的索引
    getItemOriginalIndex(item) {
      return this.historyList.findIndex(i => i.id === item.id);
    },

    // 根据选中项设置活跃分组
    setActiveGroupByItem(item) {
      const now = new Date();
      const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const itemTime = new Date(item.time);

      if (itemTime >= currentMonthStart) {
        this.activeGroup = 'current';
      } else {
        this.activeGroup = 'earlier';
      }
    },

    // 格式化时间显示

    // 处理全局点击事件，关闭下拉菜单
    handleGlobalClick(event) {
      // 如果没有激活的dropdown，直接返回
      if (!this.activeDropdownSessionId) {
        return;
      }

      // 检查点击是否在下拉菜单内部
      const dropdown = event.target.closest('.ivu-dropdown-menu') || event.target.closest('.ivu-dropdown');
      const actionBox = event.target.closest('.action-box');

      // 如果点击在下拉菜单或操作按钮内部，不关闭dropdown
      if (dropdown || actionBox) {
        return;
      }

      // 其他情况都关闭dropdown
      this.activeDropdownSessionId = '';
    },
  },
};
</script>

<style lang="less" scoped>
.history-list-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 320px;
  height: 100%;
  background: #ffffff;
  border-radius: 16px 0 0 16px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 16;
}

.history-list-header {
  height: 56px;
  padding: 0 16px;
  border-bottom: 1px solid #ecedf0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.history-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.close-icon {
  cursor: pointer;
  color: #888;

  &:hover {
    color: #333;
  }
}

.history-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.history-filter {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #ecedf0;
}

.filter-item {
  padding: 4px 12px;
  margin-right: 8px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-radius: 4px;

  &:hover,
  &.active {
    background: #f5f6f8;
  }
}

.history-items {
  flex: 1;
  overflow-y: auto;
  padding: 16px 8px;

  .history-group {
    margin-bottom: 12px;
  }

  .group-name {
    font-size: 14px;
    color: #999999;
    line-height: 22px;
    padding-left: 12px;
    margin-bottom: 10px;
    position: sticky;
    top: 0;
    background: #ffffff;
    z-index: 10;
  }
}

.history-item {
  &:hover,
  &.active {
    .item-content {
      background: #f5f6f8;
    }

    .action-box {
      opacity: 1;
    }
  }
}

.item-input {
  margin-bottom: 2px;

  input {
    height: 42px;
    border-radius: 12px;
    border: 1px solid #115bd4;
    padding: 0 12px;
    // border: none;
    outline: none;
    font-size: 14px;
    width: 100%;
  }
}

.item-content {
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  display: flex;
  position: relative;
  padding: 10px 12px;
  cursor: pointer;
  // transition: all 0.2s;
  color: #333333;
  margin-bottom: 2px;
  border-radius: 8px;

  //.action-box-shadow {
  //  content: '';
  //  pointer-events: none;
  //  border-top-right-radius: 12px;
  //  border-bottom-right-radius: 12px;
  //  position: absolute;
  //  top: 0;
  //  bottom: 0;
  //  right: 0;
  //  width: 24px;
  //  background-image: linear-gradient(90deg, rgba(249, 251, 255, 0) 0%, #f9fbff 100%);
  //}

  .chat-title {
    flex: 1;
    white-space: nowrap;
  }

  .action-box {
    z-index: 1;
    border-radius: 4px;
    outline: none;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    display: flex;
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.2s;
    cursor: pointer;
    color: #888888;
    &:hover {
      background: #ffffff;
      color: #333;
    }

    //&.hovered {
    //  opacity: 1;
    //}
  }
}

.delete-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #ed6a0c;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  transition: background 0.2s;

  &:hover {
    background: #ffece3;
    color: #d4380d;
  }
}

.item-time {
  font-size: 12px;
  color: #999;
}

.empty-history {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  height: 100%;
  margin-top: -12px;

  .empty-text {
    margin-top: -12px;
    color: #999;
  }
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}
</style>
