<template>
  <div>
    <transition name="script-modal-bg-fade">
      <div class="script-modal-wrap" v-if="visible" @click="handleMaskClick">
        <div class="script-modal" @click.stop>
          <div class="script-modal-content">
            <div class="header">
              <div class="title">Pandas Processing Script</div>
              <div class="close-btn" @click="handleClose">
                <svg-icon name="AI-modal-close" :size="12" />
              </div>
            </div>
            <div class="content">
              <div class="script-content">
                <MarkdownRender
                  v-if="scriptContent"
                  :message="{ content: formattedScriptContent }"
                  :isResponding="false"
                />
                <div v-else class="no-content">暂无脚本内容</div>

                <!-- 脚本错误信息显示 -->
                <div v-if="scriptError" class="script-error-section">
                  <div class="error-title">
                    <span>脚本执行错误：</span>
                  </div>
                  <div class="error-content">
                    <MarkdownRender
                      v-if="scriptError"
                      :message="{ content: scriptError || '' }"
                      :isResponding="false"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import MarkdownRender from './MarkdownRender.vue';

export default {
  name: 'ScriptModal',
  components: {
    MarkdownRender,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    scriptContent: {
      type: String,
      default: '',
    },
    scriptError: {
      type: String,
      default: '',
    },
  },

  computed: {
    formattedScriptContent() {
      if (!this.scriptContent) return '';
      // 将脚本内容包装在Python代码块中
      // 如果脚本没有包在Python代码块中，则添加
      if (!this.scriptContent.startsWith('```python')) {
        return '```python\n' + this.scriptContent + '\n```';
      }
      return this.scriptContent;
    },
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        // 弹窗打开时添加键盘事件监听
        document.addEventListener('keydown', this.handleKeydown);
      } else {
        // 弹窗关闭时移除键盘事件监听
        document.removeEventListener('keydown', this.handleKeydown);
      }
    },
  },

  beforeDestroy() {
    // 组件销毁时确保移除事件监听
    document.removeEventListener('keydown', this.handleKeydown);
  },

  methods: {
    handleClose() {
      this.$emit('update:visible', false);
    },
    handleMaskClick() {
      // 点击遮罩层关闭弹窗
      this.handleClose();
    },
    handleKeydown(event) {
      // ESC键关闭弹窗
      if (event.key === 'Escape' || event.keyCode === 27) {
        this.handleClose();
      }
    },
  },
};
</script>

<style lang="less" scoped>
// 遮罩层样式
.script-modal-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1500; // 确保盖住助手弹窗
  display: flex;
  align-items: center;
  justify-content: center;
}

// 弹窗本体样式 - 与助手弹窗尺寸一致
.script-modal {
  width: 70%;
  min-width: 700px;
  max-width: 840px;
  height: calc(~'100vh - 60px');
  margin-top: 30px;
  margin-bottom: 30px;
  background: #fff;
  border-radius: 6px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.script-modal-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  padding: 0 16px;
  display: flex;
  min-height: 56px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ecedf0;
  background: #fff;
  border-radius: 6px 6px 0 0;
  z-index: 1;
  flex-shrink: 0;

  .title {
    font-size: 15px;
    font-weight: 500;
    color: #333;
  }

  .close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;

    &:hover {
      color: #333;
      background: #f5f6f8;
    }
  }
}

.content {
  width: 100%;
  height: auto;
  overflow: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.script-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px 40px;
  height: 100%;

  .no-content {
    text-align: center;
    color: #999;
    padding: 40px 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .script-error-section {
    margin-top: 24px;
    border-top: 1px solid #ecedf0;
    padding-top: 16px;

    .error-title {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #e74c3c;

      .svg-icon {
        margin-right: 8px;
      }
    }

    .error-content {
      background: #fdf2f2;
      border: 1px solid #fecaca;
      border-radius: 6px;
      padding: 12px;

      pre {
        margin: 0;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #dc2626;
        white-space: pre-wrap;
        word-break: break-word;

        code {
          background: none;
          padding: 0;
          border: none;
          font-size: inherit;
          color: inherit;
        }
      }
    }
  }
}

// 过渡动画
.script-modal-bg-fade-enter-active,
.script-modal-bg-fade-leave-active {
  transition: opacity 0.3s ease;
}

.script-modal-bg-fade-enter,
.script-modal-bg-fade-leave-to {
  opacity: 0;
}

.script-modal-bg-fade-enter-active .script-modal,
.script-modal-bg-fade-leave-active .script-modal {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.script-modal-bg-fade-enter .script-modal,
.script-modal-bg-fade-leave-to .script-modal {
  transform: scale(0.9);
  opacity: 0;
}
</style>
