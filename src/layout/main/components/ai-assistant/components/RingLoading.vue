<template>
  <div class="conic-loading-container">
    <div class="conic-loading-ring" :style="ringStyle"></div>
  </div>
</template>

<script>
export default {
  name: 'RingLoading',
  props: {
    size: {
      type: Number,
      default: 20,
    },
    thickness: {
      type: Number,
      default: 3,
    },
    startColor: {
      type: String,
      default: 'rgba(21, 91, 212, 0)',
    },
    midColor: {
      type: String,
      default: '#155BD4',
    },
    endColor: {
      type: String,
      default: '#165DFF',
    },
    bgColor: {
      type: String,
      default: 'white',
    },
    speed: {
      type: String,
      default: '1.2s',
    },
  },
  computed: {
    ringStyle() {
      return {
        '--size': `${this.size}px`,
        '--thickness': `${this.thickness}px`,
        '--start-color': this.startColor,
        '--mid-color': this.midColor,
        '--end-color': this.endColor,
        '--bg-color': this.bgColor,
        '--speed': this.speed,
      };
    },
  },
};
</script>

<style scoped lang="less">
.conic-loading-container {
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.conic-loading-ring {
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  background: conic-gradient(
    from 336deg at 50% 50%,
    var(--start-color) 0%,
    var(--mid-color) 100%,
    var(--end-color) 99%
  );
  position: relative;
  animation: rotate var(--speed, 1s) linear infinite;
}

.conic-loading-ring::before {
  content: '';
  position: absolute;
  inset: var(--thickness);
  border-radius: 50%;
  background: var(--bg-color);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
