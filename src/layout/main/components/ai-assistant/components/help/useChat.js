import { nextTick } from 'vue';
import { assistantService } from '@/libs/SSE/SSEService';
import { cloneDeep  } from 'lodash-es';
import { createMessageService } from '@/libs/SSE/SSERequest';
export default {
  data() {
    return {
      messages: [],
      isLoading: false,
      messageService: null,
      thinkingTimer: null,
      searchTimer: null,
    };
  },

  methods: {
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
    },
    // 创建用户消息
    createUserMessage(content, id, groupId = null) {
      const messageId = id || this.generateUUID();
      return {
        id: messageId,
        role: 'user',
        type: 'human',
        content: content.trim(),
        create_time: new Date().toISOString(),
        group_id: groupId || messageId, // 使用传入的groupId或消息ID作为group_id
      };
    },

    // 创建助手消息
    createAssistantMessage(id, groupId = null, answerIndex = 0) {
      return {
        id: id || this.generateUUID(),
        role: 'assistant',
        type: 'AIMessageChunk',
        content: '',
        create_time: new Date().toISOString(),
        group_id: groupId || '', // 预设group_id
        answerIndex: answerIndex, // 答案序号
        isCurrentAnswer: true, // 是否为当前显示的答案
        isReponsing: false,
        isDeepSearching: false,
        choices: [],
        steps: [],
        search_results: [],
        channel: '', // 用于标识消息来源
        toolsProgress: {
          type: 'tools_using_start',
          tools: [],
        },
        deep_search_steps: [], // 一维数组，直接push新数据
        analysis_content: '', // 新增：存储分析内容
        duration: 0,
        search_duration: 0,
        contentStarted: false, // 标识content是否已开始渲染
        contentBuffer: '', // 内容缓冲区
        contentRenderStarted: false, // 标识是否已开始渲染缓冲的内容
        // SSE恢复相关字段
        hasError: false, // 标识是否出现错误
        isRetrying: false, // 标识是否正在重试
        canRetry: false, // 标识是否可以重试
      };
    },

    // 获取最后一条助手消息
    getLastAssistantMessage() {
      const lastMessage = this.messages[this.messages.length - 1];
      return lastMessage?.type === 'AIMessageChunk' ? lastMessage : null;
    },

    // 更新消息状态
    updateMessageState(message, content) {
      // 直接更新消息状态，不使用批处理
      if (content) {
        this.$set(message, 'content', message.content + content);
      }
      // 确保UI更新
      nextTick();
    },

    // 清理计时器
    clearThinkingTimer(timer) {
      if (timer) {
        clearInterval(timer);
        timer = null;
      }
    },

    // 设置消息监听器 - 统一的消息处理逻辑
    setupMessageListeners(lastMessage, options = {}) {
      const { isRetry = false, isNewChat = false, sessionId = null, timer = null } = options;

      // 设置消息回调
      this.messageService.onMessage((data, isResponding) => {
        // 如果是重试消息，收到第一个响应时清空内容重新开始
        if (data && isRetry && lastMessage.isRetrying && !lastMessage.hasCleared) {
          this.$set(lastMessage, 'content', '');
          this.$set(lastMessage, 'choices', []);
          this.$set(lastMessage, 'steps', []);
          this.$set(lastMessage, 'search_results', []);
          this.$set(lastMessage, 'deep_search_steps', []);
          this.$set(lastMessage, 'analysis_content', ''); // 清空分析内容
          this.$set(lastMessage, 'isDeepSearching', false);
          this.$set(lastMessage, 'duration', 0);
          this.$set(lastMessage, 'search_duration', 0);
          this.$set(lastMessage, 'hasCleared', true); // 标记已清空，避免重复清空
        }

        if (data?.channel) {
          this.$set(lastMessage, 'channel', data.channel);
        }
        if (data?.trace_id) {
          this.$set(lastMessage, 'trace_id', data.trace_id);
        }
        if (data?.group_id) {
          this.$set(lastMessage, 'group_id', data.group_id);
          // 修改最后一条用户消息的group_id,新发的消息没有group_id
          // 新发的消息没有group_id
          this.messages[this.messages.length - 2].group_id = data.group_id;
        }
        // 处理info_id（简化版）
        // if (data?.info_id) {
        //   this.$set(lastMessage, 'lastInfoId', data.info_id);
        // }

        !lastMessage.isReponsing && !data?.trace_id && !data?.group_id && this.$set(lastMessage, 'isReponsing', true);
        if (!lastMessage) return;

        if (data?.choices?.[0]) {
          this.$set(lastMessage, 'choices', [...data.choices]);
          this.$set(lastMessage, 'id', data?.message_id || lastMessage.id);
          const choice = data.choices[0];
          if (choice?.delta) {
            const finalData = choice.delta;
            const { type, content: deltaContent } = finalData;
            let deep_search_steps = lastMessage.deep_search_steps || [];

            if (type) {
              this.$set(lastMessage, 'msg_type', type);
              this.handleMessageType(type, finalData, lastMessage, {
                deep_search_steps,
                deltaContent,
                timer,
                isNewChat,
                sessionId,
              });
            }
          }
        }
        // 更新加载状态和消息响应状态
      });

      // 设置错误回调
      this.messageService.onError(error => {
        console.log(lastMessage);
        const message = lastMessage.isRetrying ? '重试连接失败，请稍后再试' : '连接异常，请稍后再试';
        this.$Message.error(message);
        this.isLoading = false;
        // 连接异常，设置为连接错误状态（不可重新编辑，只能重试）
        this.$set(lastMessage, 'isSystemError', false);
        this.$set(lastMessage, 'isConnectError', true);
        this.$set(lastMessage, 'canRetry', true);
        if (this.thinkingTimer) {
          this.clearThinkingTimer(this.thinkingTimer);
          this.thinkingTimer = null;
        }
        if (this.searchTimer) {
          this.clearThinkingTimer(this.searchTimer);
          this.searchTimer = null;
        }
        console.log('设置连接异常状态:', {
          id: lastMessage.id,
          isSystemError: lastMessage.isSystemError,
          isConnectError: lastMessage.isConnectError,
          canRetry: lastMessage.canRetry,
          isRetrying: lastMessage.isRetrying,
        });
        this.handleMessageComplete(lastMessage, false, true);
      });

      // 设置完成回调
      this.messageService.onComplete(data => {
        console.log('%c [ data ]-302-「useChat.js」', 'font-size:13px; background:#4641b5; color:#8a85f9;', data);
        this.$nextTick(() => {
          this.isLoading = false;
        });
      });
    },

    // 处理不同类型的消息 - 新的策略数据结构
    handleMessageType(type, finalData, lastMessage, context) {
      const { deep_search_steps, deltaContent } = context;

      switch (type) {
        case 'error':
          // 系统返回的error类型，设置为系统错误状态（可重新编辑）
          this.$set(lastMessage, 'isSystemError', true);
          this.$set(lastMessage, 'isConnectError', false);
          this.$set(lastMessage, 'canRetry', true);
          this.handleMessageComplete(lastMessage, false, true);
          break;

        case 'tools_using_start': {
          this.$set(lastMessage, 'isDeepSearching', true);
          this.$set(lastMessage, 'showSkeleton', true);
          if (!this.searchTimer) {
            this.$set(lastMessage, 'search_duration', lastMessage.search_duration + 1);
            this.searchTimer = setInterval(() => {
              this.$set(lastMessage, 'search_duration', lastMessage.search_duration + 1);
            }, 1000);
          }
          break;
        }

        // 新的一维数组处理方式
        case 'step_status': {
          // 检查是否已存在相同channel的step_status，如果存在则更新内容，否则创建新的
          let existingIndex = -1;
          if (finalData.channel) {
            for (let i = deep_search_steps.length - 1; i >= 0; i--) {
              if (deep_search_steps[i].type === 'step_status' && deep_search_steps[i].channel === finalData.channel) {
                existingIndex = i;
                break;
              }
            }
          }

          if (existingIndex !== -1) {
            // 更新现有的step_status内容
            this.$set(lastMessage.deep_search_steps[existingIndex], 'content', deltaContent);
          } else {
            // 创建新的step_status
            deep_search_steps.push({
              type: 'step_status',
              content: deltaContent,
              channel: finalData.channel,
            });
            this.$set(lastMessage, 'deep_search_steps', [...deep_search_steps]);
          }
          break;
        }

        case 'step_status_end': {
          // 通过channel找到对应的step_status并移除
          if (finalData.channel) {
            const filteredSteps = deep_search_steps.filter(
              step => !(step.type === 'step_status' && step.channel === finalData.channel)
            );
            this.$set(lastMessage, 'deep_search_steps', filteredSteps);
          }
          break;
        }

        case 'step_thinking': {
          this.$set(lastMessage, 'showSkeleton', false);
          // 找到最后一条类型为step_thinking的消息
          let lastThinkingIndex = -1;
          for (let i = deep_search_steps.length - 1; i >= 0; i--) {
            if (deep_search_steps[i].type === 'step_thinking') {
              lastThinkingIndex = i;
              break;
            }
          }

          if (lastThinkingIndex !== -1) {
            // 如果找到最后一条step_thinking，直接累加内容
            const currentContent = deep_search_steps[lastThinkingIndex].content || '';
            this.$set(lastMessage.deep_search_steps[lastThinkingIndex], 'content', currentContent + deltaContent);
          } else {
            // 如果没有找到step_thinking，创建新的
            deep_search_steps.push({
              type: 'step_thinking',
              content: deltaContent,
            });
            this.$set(lastMessage, 'deep_search_steps', [...deep_search_steps]);
          }
          break;
        }

        case 'tool_using': {
          deep_search_steps.push({
            content: finalData.content,
            channel: finalData.channel,
            tool_result: [],
            tool_status: 'using',
            type: 'tool_using',
          });
          this.$set(lastMessage, 'deep_search_steps', [...deep_search_steps]);
          break;
        }

        case 'tool_result': {
          let queryIndex = -1;
          for (let i = deep_search_steps.length - 1; i >= 0; i--) {
            if (deep_search_steps[i].channel === finalData.channel && deep_search_steps[i].type === 'tool_using') {
              queryIndex = i;
              break;
            }
          }
          if (finalData.other?.length) {
            this.$set(lastMessage, 'search_results', [...lastMessage.search_results, ...finalData.other]);
          }
          if (queryIndex !== -1) {
            this.$set(lastMessage.deep_search_steps[queryIndex], 'tool_result', finalData.other || []);
            this.$set(lastMessage.deep_search_steps[queryIndex], 'content', finalData.content);
            this.$set(lastMessage.deep_search_steps[queryIndex], 'tool_status', finalData.status);
            this.$set(lastMessage.deep_search_steps[queryIndex], 'type', 'tool_result');
          }
          break;
        }

        // step相关类型处理
        case 'step_start': {
          // 步骤开始，不需要特殊处理
          break;
        }

        case 'step_end': {
          // 步骤结束，不需要特殊处理
          break;
        }

        // step_thinking相关类型处理
        case 'step_thinking_start': {
          // 思考开始，创建新的step_thinking项目
          deep_search_steps.push({
            type: 'step_thinking',
            content: '',
          });
          this.$set(lastMessage, 'deep_search_steps', [...deep_search_steps]);
          break;
        }

        case 'step_thinking_end': {
          // 思考结束，不需要特殊处理
          break;
        }

        // 新增的分析类型处理
        case 'step_analysis_start': {
          // 分析开始，像step_status那样展示进度
          deep_search_steps.push({
            type: 'step_analysis_status',
            content: '工具调用策略分析...',
            status: 'analyzing',
          });
          this.$set(lastMessage, 'deep_search_steps', [...deep_search_steps]);
          break;
        }

        case 'step_analysis': {
          // 分析进行中，更新状态并存储分析内容
          let lastAnalysisIndex = -1;
          for (let i = deep_search_steps.length - 1; i >= 0; i--) {
            if (deep_search_steps[i].type === 'step_analysis_status') {
              lastAnalysisIndex = i;
              break;
            }
          }
          if (lastAnalysisIndex !== -1) {
            // 1. 更新分析状态显示
            // this.$set(lastMessage.deep_search_steps[lastAnalysisIndex], 'content', '正在执行策略...');
            this.$set(lastMessage.deep_search_steps[lastAnalysisIndex], 'status', 'executing');

            // 2. 累加分析内容到step_analysis_content字段（不显示，仅存储）
            const currentAnalysisContent = deep_search_steps[lastAnalysisIndex].step_analysis_content || '';
            this.$set(
              lastMessage.deep_search_steps[lastAnalysisIndex],
              'step_analysis_content',
              currentAnalysisContent + deltaContent
            );
          }
          break;
        }

        case 'step_analysis_end': {
          // 分析结束，移除状态显示，添加完成按钮
          // 1. 保存分析内容，然后移除step_analysis_status
          let analysisContent = '';
          const analysisStep = deep_search_steps.find(step => step.type === 'step_analysis_status');
          if (analysisStep && analysisStep.step_analysis_content) {
            analysisContent = analysisStep.step_analysis_content;
          }

          const filteredSteps = deep_search_steps.filter(step => step.type !== 'step_analysis_status');

          // 2. 添加完成按钮，并保存分析内容
          filteredSteps.push({
            type: 'step_analysis_end',
            content: '工具调用策略分析已完成',
            step_analysis_content: analysisContent, // 保存分析内容到完成按钮项中
          });
          this.$set(lastMessage, 'deep_search_steps', filteredSteps);
          break;
        }

        // 脚本相关类型处理
        case 'script_start': {
          // 脚本开始，添加脚本状态显示
          deep_search_steps.push({
            type: 'script_start',
            content: 'Pandas Processing Script...',
            status: 'processing',
          });
          this.$set(lastMessage, 'deep_search_steps', [...deep_search_steps]);
          break;
        }

        // 脚本相关类型处理
        case 'script_error': {
          // script_error 应该存储到最近的script步骤中，因为script和script_error是一一对应的
          // 查找最近的script步骤，将错误信息附加到其中
          let lastScriptIndex = -1;

          lastScriptIndex = deep_search_steps.findLastIndex(
            step => step.type === 'script' || step.type === 'script_end'
          );

          if (lastScriptIndex !== -1) {
            // 将错误信息添加到对应的script步骤中
            this.$set(deep_search_steps[lastScriptIndex], 'script_error', deltaContent);
            this.$set(lastMessage, 'deep_search_steps', [...deep_search_steps]);
          } else {
            // 如果没有找到script步骤，将错误信息存储到消息级别
            this.$set(lastMessage, 'script_error', deltaContent);
          }
          break;
        }

        case 'script': {
          // 脚本进行中，更新状态并存储脚本内容
          let lastScriptIndex = -1;
          for (let i = deep_search_steps.length - 1; i >= 0; i--) {
            if (deep_search_steps[i].type === 'script_start' || deep_search_steps[i].type === 'script') {
              lastScriptIndex = i;
              break;
            }
          }
          if (lastScriptIndex !== -1) {
            // 1. 更新脚本状态显示
            this.$set(lastMessage.deep_search_steps[lastScriptIndex], 'type', 'script');
            this.$set(lastMessage.deep_search_steps[lastScriptIndex], 'content', 'Pandas Processing Script...');
            this.$set(lastMessage.deep_search_steps[lastScriptIndex], 'status', 'executing');

            // 2. 累加脚本内容到script_content字段（不显示，仅存储）
            const currentScriptContent = deep_search_steps[lastScriptIndex].script_content || '';
            this.$set(
              lastMessage.deep_search_steps[lastScriptIndex],
              'script_content',
              currentScriptContent + deltaContent
            );
          }
          break;
        }

        case 'script_end': {
          // 脚本结束，移除状态显示，添加完成按钮
          // 1. 保存脚本内容，然后移除script状态
          let scriptContent = '';
          const scriptStep = deep_search_steps.find(step => step.type === 'script' || step.type === 'script_start');
          if (scriptStep && scriptStep.script_content) {
            scriptContent = scriptStep.script_content;
          }

          const filteredSteps = deep_search_steps.filter(
            step => step.type !== 'script' && step.type !== 'script_start'
          );

          // 2. 添加完成按钮，并保存脚本内容
          filteredSteps.push({
            type: 'script_end',
            content: 'Pandas Processing Script',
            script_content: scriptContent, // 保存脚本内容到完成按钮项中
          });
          this.$set(lastMessage, 'deep_search_steps', filteredSteps);
          break;
        }

        case 'tools_using_end': {
          this.$set(lastMessage, 'content', '');
          this.$set(lastMessage, 'deepSearchComplete', true);

          // 添加"正在整理..."状态
          const organizingStep = {
            type: 'step_status',
            content: '正在整理...',
            channel: 'organizing',
          };
          lastMessage.deep_search_steps.push(organizingStep);
          this.$set(lastMessage, 'deep_search_steps', [...lastMessage.deep_search_steps]);

          if (this.searchTimer) {
            this.clearThinkingTimer(this.searchTimer);
            this.searchTimer = null;
          }
          break;
        }

        case 'content':
          if (deltaContent) {
            // 立即移除"正在整理..."状态并完成深度思考
            if (!lastMessage.contentStarted) {
              this.$set(lastMessage, 'contentStarted', true);

              // 立即移除"正在整理..."状态
              const filteredSteps = lastMessage.deep_search_steps.filter(
                step => !(step.type === 'step_status' && step.channel === 'organizing')
              );
              this.$set(lastMessage, 'deep_search_steps', filteredSteps);

              // 立即完成深度思考
              this.$set(lastMessage, 'isDeepSearching', false);

              // 初始化内容缓冲区
              this.$set(lastMessage, 'contentBuffer', deltaContent);
              this.$set(lastMessage, 'contentRenderStarted', false);

              // 延时500ms开始渲染所有缓冲的content
              setTimeout(() => {
                this.$set(lastMessage, 'contentRenderStarted', true);
                this.updateMessageState(lastMessage, lastMessage.contentBuffer);
                // 清空缓冲区
                this.$set(lastMessage, 'contentBuffer', '');
              }, 500);
            } else if (!lastMessage.contentRenderStarted) {
              // 还在延时期间，将内容添加到缓冲区
              this.$set(lastMessage, 'contentBuffer', (lastMessage.contentBuffer || '') + deltaContent);
            } else {
              // 延时结束，直接更新content
              this.updateMessageState(lastMessage, deltaContent);
            }
          }
          break;

        case 'content_end':
          console.log('first content_end');
          // 只有在没有错误状态时才标记为完成
          if (!lastMessage.isSystemError && !lastMessage.isConnectError) {
            this.handleMessageComplete(lastMessage);
          } else {
            // 有错误状态时，只停止响应状态，不清除错误
            this.$set(lastMessage, 'isReponsing', false);
            this.$set(lastMessage, 'isThinking', false);
            this.$set(lastMessage, 'isDeepSearching', false);
            this.$set(lastMessage, 'isRetrying', false);
            // 即使有错误也要检查是否需要生成标题（异步执行）
            this.checkAndGenerateTitle(false, true).catch(error => {
              console.error('标题生成检查失败:', error);
            });
          }
          if (this.searchTimer) {
            this.clearThinkingTimer(this.searchTimer);
            this.searchTimer = null;
          }
          console.log(this.messages);

          break;
      }
    },

    // 处理消息完成
    handleMessageComplete(message, isStop = false, isError = false) {
      this.$set(message, 'isReponsing', false);
      this.$set(message, 'isThinking', false);
      this.$set(message, 'isDeepSearching', false);
      this.$set(message, 'isRetrying', false);
      this.isLoading = false;

      // 检查是否需要生成标题（异步执行，不阻塞其他逻辑）
      this.checkAndGenerateTitle(isStop, isError).catch(error => {
        console.error('标题生成检查失败:', error);
      });

      if (isStop) {
        this.$set(message, 'isStop', true);
        this.searchTimer && this.clearThinkingTimer(this.searchTimer);
        return;
      }
      if (isError) {
        // 如果没有明确设置错误类型，默认为连接异常
        if (!message.isSystemError && !message.isConnectError) {
          this.$set(message, 'isSystemError', false);
          this.$set(message, 'isConnectError', true);
          this.$set(message, 'canRetry', true);
        }
        return;
      }

      // 成功完成时清除错误状态
      this.$set(message, 'isSystemError', false);
      this.$set(message, 'isConnectError', false);
      this.$set(message, 'canRetry', false);
      this.getRecommendQuestions(this.session_id);
    },

    // 发送消息
    async sendMessage(data, isCardSend = false) {
      if (!data.content.trim() && !isCardSend) return;
      const {
        content,
        sessionId,
        userId,
        userName,
        isNewChat = false,
        groupId,
        isRegenerate = false,
        originalMessageIndex,
        is_edit = false,
      } = data;

      // 当开始新会话时，保存会话ID到localStorage
      if (sessionId && sessionId !== localStorage.getItem('ai_assistant_session_id')) {
        localStorage.setItem('ai_assistant_session_id', sessionId);
      }
      this.messageService = createMessageService(sessionId);
      // 在发送新消息前只清除回调，不断开SSE连接
      this.messageService.clearMessageCallback();

      let assistantMessage;

      if (isRegenerate) {
        // 重新回答：基于group_id进行答案合并

        // 隐藏同一group_id的所有答案
        this.messages.forEach(msg => {
          if (msg.type === 'AIMessageChunk' && msg.group_id === groupId) {
            this.$set(msg, 'isCurrentAnswer', false);
          }
        });

        // 计算新答案的序号
        const existingAnswers = this.messages.filter(msg => msg.type === 'AIMessageChunk' && msg.group_id === groupId);
        const answerIndex = existingAnswers.length;

        // 创建新的AI消息，预设group_id
        assistantMessage = this.createAssistantMessage(null, groupId, answerIndex);
        assistantMessage.group_id = groupId; // 预设group_id
        this.messages.push(assistantMessage);
      } else {
        // 正常发送消息
        assistantMessage = this.createAssistantMessage();
        if (!isCardSend) {
          const userMessage = this.createUserMessage(content, null, groupId);
          this.messages.push(userMessage);
          // 设置AI消息的group_id与用户消息一致
          assistantMessage.group_id = userMessage.group_id;
        }
        this.messages.push(assistantMessage);
      }

      this.isLoading = true;
      // 发出用户消息事件
      this.$bus.$emit('user-message-sent');
      let timer = this.thinkingTimer;
      let searchTimer = this.searchTimer;
      // 使用当前的assistantMessage而不是getLastAssistantMessage，确保重试时使用正确的消息
      const lastMessage = assistantMessage;

      try {
        // 使用统一的消息监听器
        this.setupMessageListeners(lastMessage, {
          isNewChat,
          sessionId,
          timer,
          searchTimer,
        });
        let messageData, url;

        // 正常发送消息
        messageData = {
          session_id: sessionId,
          problem: content,
          user_id: userId,
          clinic_id: this.clinicInfo.clinic_id,
          clinic_name: this.clinicInfo.clinic_name,
          chat_name: 'clinic',
          is_deep: false,
          user_name: userName,
        };

        // 如果是编辑消息，添加group_id和is_edit参数
        if (groupId) {
          messageData.group_id = groupId;
        }
        if (is_edit) {
          messageData.is_edit = true;
        }
        if (isCardSend) {
          messageData.card_num = data.card_num;
          messageData.card_type = data.card_type;
          messageData.message_id = data.messageId;
        }
        // /clinic_chat/content/chat
        // /clinic_chat/content/mode_card_deal
        // /clinic_chat/content/sse
        url = isCardSend ? '/clinic_chat/content/mode_card_deal/sse' : '/clinic_chat/content/chat';

        console.log('messageData: ', messageData);
        await this.messageService.sendMessage(messageData, url);
      } catch (error) {
        console.error('发送消息失败:', error);
        // 发生错误时也要更新消息状态
        if (lastMessage) {
          // 连接异常，设置为连接错误状态（不可重新编辑，只能重试）
          this.$set(lastMessage, 'isSystemError', false);
          this.$set(lastMessage, 'isConnectError', true);
          this.$set(lastMessage, 'canRetry', true);
          console.log('catch块设置连接异常状态:', {
            id: lastMessage.id,
            isSystemError: lastMessage.isSystemError,
            isConnectError: lastMessage.isConnectError,
            canRetry: lastMessage.canRetry,
            isRetrying: lastMessage.isRetrying,
          });
          this.handleMessageComplete(lastMessage, false, false);
        }
        this.$Message.error('发送消息失败，请稍后再试');
        this.isLoading = false;
        this.messageService.clearMessageCallback();
        this.clearThinkingTimer(timer);
        this.clearThinkingTimer(searchTimer);
        // 重置计时器引用
        this.thinkingTimer = null;
        this.searchTimer = null;
      }
    },

    // 检查并生成标题
    async checkAndGenerateTitle(isStop = false, isError = false) {
      const sessionId = this.session_id;

      // 只有在有session_id时才生成标题
      if (!sessionId) return;

      // 检查是否有用户消息和助手消息（至少有一轮对话）
      const hasUserMessage = this.messages.some(msg => msg.role === 'user');
      const hasAssistantMessage = this.messages.some(msg => msg.role === 'assistant');

      if (!hasUserMessage || !hasAssistantMessage) return;

      // 检查当前会话是否已经有标题
      try {
        const hasTitle = await this.checkSessionHasTitle(sessionId);
        if (hasTitle) {
          console.log('会话已有标题，跳过标题生成');
          return;
        }

        console.log('检测到需要生成标题，触发条件:', {
          isStop,
          isError,
          sessionId,
          messageCount: this.messages.length,
        });
        this.generateChatTitle(sessionId);
        this.$emit('newMessageCompleted');
      } catch (error) {
        console.error('检查会话标题失败:', error);
        // 如果检查失败，仍然尝试生成标题（避免因为网络问题导致标题永远不生成）
        this.generateChatTitle(sessionId);
        this.$emit('newMessageCompleted');
      }
    },

    // 检查会话是否已有标题
    async checkSessionHasTitle(sessionId) {
      try {
        // 通过获取历史记录来检查当前会话是否已有标题
        const response = await assistantService.getChatList({
          clinic_id: this.clinicInfo.clinic_id,
          user_id: this.userInfo.uid,
        });

        if (response && response.items && Array.isArray(response.items)) {
          const currentSession = response.items.find(item => item.session_id === sessionId);
          if (
            currentSession &&
            currentSession.title &&
            currentSession.title !== '新会话' &&
            currentSession.title !== '未命名对话' &&
            currentSession.title.trim() !== ''
          ) {
            return true; // 已有有效标题
          }
        }
        return false; // 没有标题或标题无效
      } catch (error) {
        console.error('检查会话标题失败:', error);
        return false; // 检查失败时返回false，允许生成标题
      }
    },

    // 生成聊天标题
    async generateChatTitle(sessionId) {
      try {
        const result = await assistantService.generateChatTitle({
          session_id: sessionId,
          user_id: this.userInfo.uid,
          user_name: this.userInfo.name,
          clinic_id: this.clinicInfo.clinic_id,
          clinic_name: this.clinicInfo.clinic_name,
          chat_name: 'clinic',
        });
        if (result && result.title) {
          // 通知父组件更新标题
          this.$bus.$emit('update-chat-title', {
            sessionId,
            title: result.title,
          });
        }
      } catch (error) {
        console.error('生成聊天标题失败:', error);
      }
    },

    // 终止生成
    async stopGeneration() {
      try {
        const lastMessage = this.getLastAssistantMessage();
        if (lastMessage) {
          this.handleMessageComplete(lastMessage, true, false);
        }
      } catch (error) {
        console.error('终止生成失败:', error);
        this.$Message.error('终止生成失败');
      }
    },
    async handleStop() {
      try {
        // 检查是否有响应中的消息
        const hasRespondingMessage = this.messages.some(
          msg => msg.role === 'assistant' && 'isReponsing' in msg && msg.isReponsing
        );

        // 获取当前会话ID
        const currentSessionId = this.session_id;

        // 如果没有会话ID且没有响应中的消息，则给出提示并返回
        if (!currentSessionId && !hasRespondingMessage) {
          this.$Message.warning('没有活动会话，无需终止请求');
          return;
        }

        // 立即停止加载状态，提供UI反馈

        // 找到当前响应中的助手消息，获取其ID
        let lastMessageId = '';
        let lastUserContent = '';
        let lastChannel = '';
        if (this.messages.length > 0) {
          // 找到最后一条助手消息
          const assistantMessage = this.messages.filter(msg => msg.role === 'assistant').pop();

          if (assistantMessage) {
            lastMessageId = assistantMessage.id;
            lastChannel = assistantMessage.channel;
            // 找到最后一条用户消息
            const userMessage = this.messages.filter(msg => msg.role === 'user').pop();

            if (userMessage) {
              lastUserContent = userMessage.content;
            }
          }
        }

        // 先停止生成（这会断开SSE连接并清理回调）
        this.messageService.stop(currentSessionId);
        await this.stopGeneration();

        // 尝试调用后端接口停止SSE响应 - 只有在有会话ID时才调用
        if (currentSessionId && lastMessageId) {
          try {
            await assistantService.stopGeneration({
              session_id: currentSessionId,
              problem: lastUserContent,
              message_id: lastMessageId,
              channel: lastChannel,
            });
            this.isLoading = false;
            console.log('后端终止接口调用成功');
          } catch (apiError) {
            console.error('后端终止接口调用失败:', apiError);
          }
        } else if (hasRespondingMessage) {
          console.warn('有消息正在响应，但无法获取必要信息调用后端终止接口');
        }
      } catch (error) {
        console.error('终止SSE请求失败:', error);
        this.$Message.error('停止生成失败，请稍后再试');

        // 兜底处理：即使出错也尝试停止当前会话或所有连接
        try {
          this.messageService.stopGeneration();
        } catch (e) {
          console.error('兜底终止请求失败:', e);
        }
      }
    },

    // 重试消息发送 - 独立实现，不依赖sendMessage
    async retryMessage(messageId) {
      const messageIndex = this.messages.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        this.$Message.error('未找到要重试的消息');
        return;
      }

      const message = this.messages[messageIndex];
      console.log('message: ', message);

      // 检查消息是否有channel信息
      if (!message.channel) {
        this.$Message.error('消息缺少channel信息，无法重试');
        return;
      }

      // 重置消息状态，标记为重试
      this.$set(message, 'isSystemError', false);
      this.$set(message, 'isConnectError', false);
      this.$set(message, 'canRetry', false);
      this.$set(message, 'isRetrying', true);

      let timer = this.thinkingTimer;
      let searchTimer = this.searchTimer;

      try {
        this.isLoading = true;
        this.messageService = createMessageService(this.session_id);
        this.messageService.clearMessageCallback();

        // 使用统一的消息监听器
        this.setupMessageListeners(message, {
          isRetry: true,
          isNewChat: false,
          sessionId: this.session_id,
          timer,
          searchTimer,
        });

        // 重试时使用SSE接口和channel参数
        const messageData = {
          channel: message.channel,
          session_id: this.session_id,
        };

        const url = '/clinic_chat/content/chat/sse';
        await this.messageService.sendMessage(messageData, url);
      } catch (error) {
        console.error('重试消息失败:', error);
        // 连接异常，设置为连接错误状态
        this.$set(message, 'isSystemError', false);
        this.$set(message, 'isConnectError', true);
        this.$set(message, 'canRetry', true);
        this.handleMessageComplete(message, false, false);
        this.$Message.error('重试失败，请稍后再试');
      } finally {
        this.isLoading = false;
        this.messageService.clearMessageCallback();
        this.clearThinkingTimer(timer);
        this.clearThinkingTimer(searchTimer);
        // 重置计时器引用
        this.thinkingTimer = null;
        this.searchTimer = null;
      }
    },
  },
};
