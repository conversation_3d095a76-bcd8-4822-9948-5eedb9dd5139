<template>
  <transition
    :name="isDisabled ? '' : 'block-fade'"
    :duration="duration"
    @before-enter="setTransition"
    @before-leave="setTransition"
  >
    <div v-if="show" ref="fadeBlock">
      <slot />
    </div>
  </transition>
</template>

<script>
/**
 * 块元素淡入过渡组件
 * @prop {Boolean} show - 是否显示内容
 * @prop {Number} duration - 动画时长（毫秒）
 */
export default {
  name: 'BlockFadeIn',
  props: {
    show: {
      type: Boolean,
      required: true,
    },
    duration: {
      type: Number,
      default: 400,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    setTransition(el) {
      el.style.transition = `opacity ${this.duration}ms`;
    },
  },
};
</script>

<style scoped>
.block-fade-enter-active,
.block-fade-leave-active {
  /* transition 由js动态设置 */
}
.block-fade-enter,
.block-fade-leave-to {
  opacity: 0;
}
.block-fade-enter-to,
.block-fade-leave {
  opacity: 1;
}
</style>
