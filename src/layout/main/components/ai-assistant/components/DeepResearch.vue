<template>
  <div class="deep-search-container" :style="{ marginBottom: message.deepSearchComplete ? 0 : '42px' }">
    <!-- 头部标题与信息 -->
    <div class="search-header" @click="toggleExpand">
      <div class="search-title">
        <!-- <RingLoading v-if="isDeepSearching" :size="18" /> -->
        <svg-icon name="deep-search" :size="16" />
        <!-- <svg-icon name="deep-search" v-if="!isDeepSearching" :size="16" /> -->
        <span class="ml-8">
          <!-- <GlowText v-if="isDeepSearching" text="思考中..."></GlowText> -->
          <ShimmerText v-if="isDeepSearching" text="分析和收集资料中..."></ShimmerText>
          <span v-else-if="message.deepSearchComplete">分析和收集资料完成</span>
          <span v-else-if="message.isStop || message.isSystemError || message.isConnectError">分析和收集资料中断</span>
        </span>
        <span class="time-used" v-if="searchDuration">{{ searchDuration }}</span>
      </div>
      <div class="expand-action" v-if="!isDeepSearching">
        <svg-icon name="ai-t-arrow" :size="16" :class="{ 'arrow-rotated': !isMinimized }" />
      </div>
    </div>
    <!-- 搜索内容区域 -->
    <div class="skeleton-container" v-show="!!message.showSkeleton && isDeepSearching">
      <el-skeleton :loading="!!message.showSkeleton" animated :throttle="300" :count="1" :rows="3"> </el-skeleton>
    </div>
    <transition
      name="collapse"
      @enter="onEnter"
      @after-enter="onAfterEnter"
      @leave="onLeave"
      @after-leave="onAfterLeave"
    >
      <div v-if="!isMinimized && !message.showSkeleton" class="search-content-wrapper">
        <div class="search-content">
          <!-- 添加骨架屏，当正在搜索且没有内容时显示 -->
          <!-- 新的一维数组数据结构 -->
          <div
            v-for="(item, itemIndex) in filteredSearchSteps"
            :key="`step-${itemIndex}-${item.type}`"
            class="search-step"
            :class="{ 'last-step': itemIndex === filteredSearchSteps.length - 1 }"
          >
            <!-- 步骤内容 -->
            <div class="step-content">
              <template
                v-if="
                  item.content &&
                  (item.type === 'step_thinking' ||
                    item.type === 'step_status' ||
                    item.type === 'step_analysis_status' ||
                    item.type === 'script_start' ||
                    item.type === 'script' ||
                    item.type === 'script_error')
                "
              >
                <div
                  class="streaming-container flex-common"
                  :style="{
                    marginTop: item.type === 'step_status' || item.type === 'step_analysis_status' ? '10px' : 0,
                  }"
                >
                  <div class="tool-icon" style="height: 20px">
                    <!-- 深度搜索状态相关步骤显示loading icon，step_thinking不显示loading -->
                    <RingLoading
                      v-if="
                        (item.type === 'step_status' ||
                          item.type === 'step_analysis_status' ||
                          item.type === 'script_start' ||
                          item.type === 'script') &&
                        isDeepSearching
                      "
                      :size="12"
                      :thickness="2"
                    />
                    <div v-else class="dot"></div>
                  </div>
                  <ShimmerText
                    v-if="
                      item.type === 'step_status' ||
                      item.type === 'step_analysis_status' ||
                      item.type === 'script_start' ||
                      item.type === 'script'
                    "
                    :disabled="!isDeepSearching"
                    class="flex-1"
                    style="line-height: 20px; font-size: 13px"
                    base-color="#7e7d7d"
                    shimmer-color="rgba(255,255,255,0.2)"
                    :text="item.content"
                  >
                  </ShimmerText>
                  <stream-fade-text v-else :isDisabled="!isDeepSearching" class="flex-1" :text="item.content" />
                </div>
              </template>

              <!-- 策略分析完成 - step_analysis_end -->
              <template v-if="item.type === 'step_analysis_end'">
                <div class="analysis-completed-button flex-common" style="margin-top: 10px">
                  <div class="tool-icon" style="height: 20px">
                    <div class="dot"></div>
                  </div>
                  <span class="analysis-text" @click="handleAnalysisClick(item)"
                    >工具调用策略分析已完成
                    <svg-icon
                      name="ai-t-arrow"
                      :size="8"
                      style="transform: rotateZ(90deg); margin-left: 4px"
                    ></svg-icon>
                  </span>
                </div>
              </template>

              <!-- 脚本完成 - script_end -->
              <template v-if="item.type === 'script_end'">
                <div class="analysis-completed-button flex-common" style="margin-top: 10px">
                  <div class="tool-icon" style="height: 20px; width: 16px; margin-right: 8px">
                    <svg-icon name="AI-code" :size="16" />
                  </div>
                  <span class="analysis-text" @click="handleScriptClick(item)"
                    >Pandas Processing Script
                    <svg-icon
                      name="ai-t-arrow"
                      :size="8"
                      style="transform: rotateZ(90deg); margin-left: 4px"
                    ></svg-icon>
                  </span>
                </div>
              </template>

              <!-- 搜索工具 -->
              <template v-if="item.type === 'tool_using' || item.type === 'tool_result'">
                <div class="search-tool-item" :class="getFadeInStatus">
                  <div class="flex-common tool-item" :style="{ animationDelay: itemIndex * 0.2 + 's' }">
                    <div class="tool-icon">
                      <!-- tool_using显示loading，tool_result显示静态图标 -->
                      <RingLoading v-if="item.type === 'tool_using' && isDeepSearching" :size="12" :thickness="2" />
                      <svg-icon v-else name="research" :size="12" />
                    </div>
                    <div class="tool-description mr-4">
                      <simple-markdown :content="getToolDescription(item)" :isResponding="false" />
                    </div>
                  </div>

                  <!-- 统一的底部状态显示 -->
                  <div class="tool-status" v-if="!item.tool_result?.length">
                    <div class="flex-common status-content">
                      <span class="status-text">
                        {{ getToolStatusText(item) }}
                      </span>
                    </div>
                  </div>

                  <!-- 搜索结果 -->
                  <div v-if="item.tool_result && item.tool_result.length > 0" class="search-results">
                    <div class="search-results-title flex-common" :class="getFadeInStatus">
                      <div class="tool-icon">
                        <svg-icon name="result-directory" :size="12" />
                      </div>
                      <span>找到 {{ item.tool_result.length }} 条相关结果</span>
                    </div>
                    <div class="search-results-list" :class="getFadeInStatus">
                      <div
                        v-for="(result, index) in getDisplayedResults(item.tool_result)"
                        :key="index"
                        :class="[
                          'search-result-item',
                          index === getDisplayedResults(item.tool_result)?.length - 1 ? 'last-item' : '',
                        ]"
                      >
                        <img
                          class="result-icon site-icon"
                          :src="result?.site_icon || defaultIcon"
                          @error="$event.target.src = defaultIcon"
                        />
                        <div class="result-title" @click="handleResultClick(result, index, itemIndex)">
                          {{ result.title }}
                        </div>
                        <div class="result-url">{{ getUrlDomain(result.url) }}</div>
                      </div>
                      <div
                        class="view-more"
                        v-if="item.tool_result.length > 5"
                        @click="toggleMoreResults(itemIndex, item.tool_result)"
                      >
                        {{ `查看更多(${item.tool_result.length})` }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div
            v-if="!isDeepSearching && deepSearchSteps.length > 0 && message.deepSearchComplete"
            class="flex-common mt-8 search-complete"
            :class="getFadeInStatus"
          >
            <div class="tool-icon mr-10" style="height: 20px; width: auto; margin-left: -2px">
              <svg-icon name="search-complete" :size="14" />
            </div>
            <div class="tool-description">完成</div>
          </div>
        </div>
      </div>
    </transition>

    <!-- 策略分析弹窗 -->
    <StrategyAnalysisModal :visible.sync="showAnalysisModal" :analysisContent="currentAnalysisContent" />

    <!-- 脚本弹窗 -->
    <ScriptModal
      :visible.sync="showScriptModal"
      :scriptContent="currentScriptContent"
      :scriptError="currentScriptError"
    />
  </div>
</template>

<script>
import SimpleMarkdown from './SimpleMarkdown.vue';
import RingLoading from './RingLoading';
import StreamFadeText from './StreamFadeText.vue';
import ShimmerText from './common/ShimmerText.vue';
import StrategyAnalysisModal from './StrategyAnalysisModal.vue';
import ScriptModal from './ScriptModal.vue';
export default {
  name: 'DeepResearch',
  components: {
    SimpleMarkdown,
    RingLoading,
    StreamFadeText,
    ShimmerText,
    StrategyAnalysisModal,
    ScriptModal,
  },
  props: {
    message: {
      type: Object,
      default: () => ({}),
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    isDeepSearching: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    isDeepSearching: {
      handler(val) {
        if (val) {
          this.isMinimized = false;
        }
      },
      immediate: true,
    },
  },
  mounted() {
    console.log('DEEP_RESEARCH_MOUNTED');
  },
  data() {
    return {
      isMinimized: true,
      browsedResults: [],
      expandedSteps: {}, // 记录每个步骤的展开状态
      defaultIcon: require('@/assets/image/ai/site-icon.png'),
      showAnalysisModal: false, // 控制策略分析弹窗显示
      currentAnalysisContent: '', // 当前分析内容
      currentScriptError: '', // 当前脚本错误信息
      showScriptModal: false, // 控制脚本弹窗显示
      currentScriptContent: '', // 当前脚本内容
    };
  },
  computed: {
    getFadeInStatus() {
      return {
        'fade-in': this.isDeepSearching,
      };
    },
    deepSearchSteps() {
      return this.message.deep_search_steps || [];
    },
    // 过滤掉不需要显示的步骤，保留搜索和分析相关的内容
    filteredSearchSteps() {
      // 过滤掉 script_error 类型，因为它现在会在策略分析弹窗中显示
      return this.deepSearchSteps.filter(step => step.type !== 'script_error');
    },
    searchDuration() {
      const duration = this.message.search_duration || 0;
      if (duration === 0) return '';

      // 格式化为 mm:ss 格式
      const minutes = Math.floor(duration / 60);
      const seconds = duration % 60;
      const formattedMinutes = String(minutes).padStart(2, '0');
      const formattedSeconds = String(seconds).padStart(2, '0');

      return `${formattedMinutes}:${formattedSeconds}`;
    },
    getUrlDomain() {
      return url => {
        if (!url) return '';
        const urlObj = new URL(url);
        return urlObj.hostname;
      };
    },
    getToolDescription() {
      return item => {
        const prefix = item.type === 'tool_using' ? '正在搜索 ' : '已搜索 ';
        return `${prefix}“${item.content}”`;
      };
    },
  },
  methods: {
    toggleExpand() {
      if (this.isDeepSearching) return;
      this.isMinimized = !this.isMinimized;
    },
    // 类似Element UI Collapse的动画实现
    onEnter(el) {
      // 先清除所有可能影响高度的样式，获取内容区域的真实高度
      el.style.height = 'auto';
      el.style.paddingTop = '';
      el.style.paddingBottom = '';
      el.style.marginTop = '';
      el.style.marginBottom = '0'; // 保持margin-bottom为0，避免高度计算错误
      el.style.overflow = 'hidden';
      el.style.opacity = '0'; // 初始透明度为0

      // 获取不包含margin-bottom的内容高度
      const height = el.offsetHeight;

      // 设置初始状态
      el.style.height = '0';
      el.style.paddingTop = '0';
      el.style.paddingBottom = '0';
      el.style.marginTop = '0';

      // 强制重排
      el.offsetHeight;

      // 设置目标高度和透明度
      el.style.height = height + 'px';
      el.style.paddingTop = '';
      el.style.paddingBottom = '';
      el.style.marginTop = '';
      el.style.opacity = '1'; // 展开时透明度变为1
    },
    onAfterEnter(el) {
      // 动画完成后恢复自然状态
      el.style.height = 'auto';
      el.style.marginBottom = '16px'; // 恢复原有的margin-bottom
      el.style.overflow = '';
      el.style.opacity = ''; // 清除内联透明度样式
    },
    onLeave(el) {
      el.style.height = el.scrollHeight + 'px';
      el.style.overflow = 'hidden';
      el.style.opacity = '1'; // 初始透明度为1

      // 强制重排，然后设置目标值触发动画
      el.offsetHeight;

      el.style.height = '0';
      el.style.paddingTop = '0';
      el.style.paddingBottom = '0';
      el.style.marginTop = '0';
      el.style.marginBottom = '0';
      el.style.opacity = '0'; // 收起时透明度变为0
    },
    onAfterLeave(el) {
      // 清理所有内联样式
      el.style.cssText = '';
    },

    getDisplayedResults(results) {
      return results.slice(0, 5);
    },
    toggleMoreResults(itemIndex, results) {
      // 使用消息ID和查询结果打开引用弹窗
      this.$emit('open-quote-modal', {
        messageId: this.message.id, // 使用消息ID
        queryIndex: itemIndex, // 使用项目索引
        results,
      });
    },
    getSourceIcon(source) {
      const sourceMap = {
        web: 'ai-web',
        pdf: 'ai-pdf',
        database: 'ai-database',
        default: 'ai-web',
      };
      return sourceMap[source] || sourceMap.default;
    },
    handleResultClick(result) {
      // 将点击的结果添加到已浏览结果中
      window.open(result?.url, '_blank');
    },
    handleAnalysisClick(item) {
      console.log('item: ', item);
      // 处理策略分析完成按钮点击
      console.log('策略分析完成按钮被点击:', item);

      // 首先检查当前点击的item是否包含分析内容
      if (item && item.step_analysis_content) {
        this.currentAnalysisContent = item.step_analysis_content;
        this.showAnalysisModal = true;
        return;
      }

      // 如果当前item没有内容，再查找其他包含 step_analysis_content 的步骤
      const analysisStep = this.deepSearchSteps.find(step => step.step_analysis_content);
      if (analysisStep && analysisStep.step_analysis_content) {
        this.currentAnalysisContent = analysisStep.step_analysis_content;
        this.showAnalysisModal = true;
      } else {
        console.log('所有步骤:', this.deepSearchSteps);
        this.$Message.warning('暂无策略分析内容');
      }
    },

    handleScriptClick(item) {
      console.log('脚本按钮被点击:', item);

      let scriptContent = '';
      let scriptError = '';

      // 首先检查当前点击的item是否包含脚本内容
      if (item && item.script_content) {
        scriptContent = item.script_content;
        scriptError = item.script_error || '';
      } else {
        // 如果当前item没有内容，再查找其他包含 script_content 的步骤
        const scriptStep = this.deepSearchSteps.find(step => step.script_content);
        if (scriptStep && scriptStep.script_content) {
          scriptContent = scriptStep.script_content;
          scriptError = scriptStep.script_error || '';
        } else {
          // 检查消息级别的 script_error
          scriptError = this.message.script_error || '';
        }
      }

      if (scriptContent || scriptError) {
        this.currentScriptContent = scriptContent;
        this.currentScriptError = scriptError;
        this.showScriptModal = true;
      } else {
        console.log('所有步骤:', this.deepSearchSteps);
        this.$Message.warning('暂无脚本内容');
      }
    },

    // 获取工具状态文本（统一处理 tool_using 和 tool_result）
    getToolStatusText(item) {
      if (item.type === 'tool_using') {
        return '等待结果…';
      } else if (item.type === 'tool_result') {
        const status = item.tool_status || item.status;
        const prefix = status === 'success' ? '已检索到：' : '未检索到：';
        return `${prefix}${item.content}`;
      }
      return '';
    },
  },
};
</script>

<style scoped lang="less">
.deep-search-container {
  width: 100%;
  border-radius: 12px;
  background: #f9fafb;
  // margin-bottom: 16px;
  overflow: hidden;
  // margin-top: 16px;
  color: #7e7d7d;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  &:hover {
    .expand-action {
      color: #333333;
    }
  }
  .search-title {
    display: flex;
    align-items: center;
    color: #333333;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    svg-icon {
      color: #115bd4;
    }

    .time-used {
      font-size: 12px;
      color: #999999;
      margin-left: 10px;
      line-height: 18px;
      font-weight: normal;
    }
  }

  .expand-action {
    cursor: pointer;
    color: #9e9e9e;
  }
}
.skeleton-container {
  margin-left: 24px;
  margin-right: 16px;
  padding-bottom: 16px;
  :deep(.el-skeleton.is-animated .el-skeleton__item) {
    height: 12px;
    margin-top: 4px;
    &.is-first {
      margin-top: 0;
    }
  }
}
.search-content {
  overflow: hidden;
  margin-left: 24px;
  border-left: 1px solid #dcdde0;
  margin-bottom: 16px;
  padding-right: 24px;
}

.search-step {
  margin-bottom: 10px;
  padding-left: 16px;
}
.last-step {
  margin-bottom: 0;
  .streaming-container,
  .search-results,
  .tool-results {
    margin-bottom: 0;
  }
}
.tool-results {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  &:last-child {
    margin-bottom: 0;
  }
}

.search-tool-item {
  .tool-info {
    .tool-title {
      font-size: 13px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 4px;
    }

    .tool-description {
      font-size: 13px;
      color: #888888;
    }
  }
  .clinic-tool-result {
    height: 18px;
    background: rgba(21, 91, 212, 0.08);
    border-radius: 4px;
    font-size: 11px;
    color: #155bd4;
    line-height: 16px;
    padding: 1px 4px;
    margin-left: 4px;
  }
  .tool-item {
    display: flex;
    margin-top: 10px;
    .tool-icon {
      align-items: flex-start;
      margin-top: 4px;
    }
  }

  // 统一的工具状态样式
  .tool-status {
    margin-top: 8px;
    font-size: 13px;

    .status-content {
      background-color: #ebebeb;
      display: inline-block;
      padding: 2px 6px;
      margin-left: 24px;
      border-radius: 5px;
      cursor: pointer;
      color: #7e7d7d;
      max-width: 240px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        background-color: #dedede;
      }

      // .status-text {
      //   color: #888888;
      // }
    }
  }
}

.search-results {
  display: flex;
  flex-direction: column;
  .search-results-list {
    margin-left: 24px;

    .last-item {
      margin-bottom: 0;
    }
  }
  .search-results-title {
    margin-bottom: 10px;
    margin-top: 8px;
    font-size: 13px;
    color: #444444;
    line-height: 20px;
  }
}

.search-result-item {
  display: flex;
  align-items: center;
  border-radius: 6px;
  margin-bottom: 8px;

  .result-icon {
    margin-right: 12px;
    width: 16px;
    height: 16px;
    display: block;
  }

  .result-title {
    font-size: 13px;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
    max-width: 400px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }

  .result-url {
    font-size: 12px;
    color: #888888;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
    width: 121px;
    margin-left: 6px;
  }
}

.view-more {
  text-align: center;
  font-size: 13px;
  margin-top: 10px;
  color: #666666;
  line-height: 20px;
  cursor: pointer;
  display: inline-block;
  flex-shrink: 0;
  &:hover {
    color: #333;
    text-decoration: underline;
  }
}

.browsed-result {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;

  .result-title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 8px;
  }

  .result-content {
    font-size: 13px;
    line-height: 1.6;
    color: #555555;
  }
}

// 展开收起动画 - 类似Element UI Collapse
.collapse-enter-active,
.collapse-leave-active {
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1), padding-top 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    padding-bottom 0.3s cubic-bezier(0.4, 0, 0.2, 1), margin-top 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    margin-bottom 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapse-enter-from,
.collapse-leave-to {
  height: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  opacity: 0 !important;
}

.search-content-wrapper {
  margin-bottom: 16px;
}

// .fade-in {
//   animation: fadeIn 0.8s ease forwards;
//   opacity: 0.3;
// }

// @keyframes fadeIn {
//   from {
//     opacity: 0.3;
//   }
//   to {
//     opacity: 1;
//   }
// }

// 流式渐显文本容器样式
.streaming-container {
  position: relative;
  margin-bottom: 10px;
  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #666666;
    line-height: 20px;
  }
  :deep(.custom-markdown.streaming-text) {
    &::after {
      content: '';
      position: absolute;
      display: inline-block;
      right: 0;
      bottom: 0;
      width: 100px;
      height: 1.6em;
      background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1) 80%);
      pointer-events: none;
      z-index: 2;
    }
  }
}

// 策略分析完成按钮样式
.analysis-completed-button {
  transition: all 0.2s ease;
  display: inline-flex;
  .analysis-text {
    cursor: pointer;
    display: inline-flex;
    border-radius: 2px;
    padding-right: 2px;
    line-height: 20px;
    font-size: 13px;
    color: #7e7d7d;
    align-items: center;
    &:hover {
      color: #333;
    }
  }

  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #666666;
    line-height: 20px;
  }
}
.py-script {
  padding: 6px 12px !important;
  border-radius: 18px !important;
  border: 1px solid #ebebeb;
  background: #f5f5f5;
  cursor: pointer;
  &:hover {
    border-color: #dcdde0;
  }
}
.expand-action {
  display: flex;
  align-items: center;
  color: #999999;
  transition: color 0.2s;
  transform: rotate(90deg);
  .svg-icon {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .arrow-rotated {
    transform: rotate(90deg);
  }
}
:deep(.flex-common) {
  display: flex;
  margin-right: 16px;

  .tool-icon {
    margin-right: 12px;
    line-height: 20px;
    display: inline-flex;
    align-items: center;
    width: 12px;
    justify-content: center;
  }
}
.analysis-center {
  align-items: center;
  font-size: 13px;
}

.search-complete {
  align-items: center;
  padding-left: 16px;
  font-size: 13px;
}
</style>
