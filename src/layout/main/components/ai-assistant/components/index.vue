<template>
  <div class="chat-main">
    <div class="answered-wrap">
      <div class="answered-content" v-if="messages.length > 0">
        <chat-content
          ref="chat-content"
          :messages="messages"
          :is-loading="isLoading"
          @setIsShowScrollButton="flag => (showScrollButton = flag)"
          @handleOpenQuoteModal="(tools, id) => $emit('handleOpenQuoteModal', tools, id)"
          @setChatInput="setChatInput"
          @editAndResendMessage="handleEditAndResendMessage"
          @regenerateAnswer="handleRegenerateAnswer"
          @retryMessage="handleRetryMessage"
          @networkDisconnected="handleNetworkDisconnected"
          @showCitedTooltip="$emit('showCitedTooltip', $event)"
          @hideCitedTooltip="$emit('hideCitedTooltip')"
          :recommendQuestions="recommendQuestions"
          :sendQuestion="sendQuestion"
          :session_id="session_id"
        ></chat-content>
      </div>
      <template v-else>
        <div class="notAnswered">
          <div class="title">{{ getMoment }}好，{{ getUname() }}</div>
          <div class="welcome">Hi，我是你身边的AI智能助手，可以为你答疑解惑，请把你的任务交给我吧～</div>
          <div class="recommend-questions" v-if="recommendQuestions.length">
            <Divider plain>
              <div class="question-title">推荐问题</div>
            </Divider>
            <div class="question-list">
              <div
                class="question-item"
                v-for="(question, index) in recommendQuestions"
                :key="index"
                @click="sendQuestion(question)"
              >
                <span>{{ question }}</span>
                <svg-icon name="AI-arrow-right" color="#9d9999" class="ml-8" :size="14"></svg-icon>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="answered-input">
      <chat-input
        ref="main-chat-input"
        :show-scroll-button="showScrollButton"
        :is-loading="isLoading"
        @send="handleSend"
        :session_id="session_id"
        @stop="handleStop"
      ></chat-input>
    </div>
  </div>
</template>

<script>
import ChatInput from './ChatInput.vue';
import ChatContent from './ChatContent.vue';
import { assistantService } from '@/libs/SSE/SSEService';
import * as runtime from '@/libs/runtime.js';
import useChat from './help/useChat';
import { getUname } from '@/libs/runtime.js';

export default {
  name: 'ChatMain',
  components: {
    ChatInput,
    ChatContent,
  },
  mixins: [useChat],
  props: {
    currentSessionId: {
      type: String,
      default: '',
    },
  },
  inject: {
    getClinicInfo: {
      default: () => () => ({
        clinic_id: '1652',
        clinic_name: '南宁榕树家景晖中医诊所',
      }),
    },
  },
  data() {
    return {
      getUname,
      key: 'value',
      isLoading: false,
      showScrollButton: false,
      messages: [],
      session_id: '',
      userInfo: runtime.getUser(),
      isNewSessionFlag: false,
      recommendQuestions: [],
    };
  },
  provide() {
    return {
      isLoading: () => this.isLoading,
    };
  },
  created() {
    // 监听会话加载事件
    this.$bus.$on('load-session', this.onSessionLoad);
    // 监听新会话事件
    this.$bus.$on('new-session', this.onNewSession);
    // 监听取消回调事件
    this.$bus.$on('cancel-session-callbacks', this.cancelSessionCallbacks);
  },
  watch: {
    currentSessionId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.session_id = newVal;

          // 如果是新建会话，不加载消息并重置标记
          if (this.isNewSessionFlag) {
            this.isNewSessionFlag = false;
            return;
          }

          // 其他情况下加载消息
          // this.loadMessages(newVal);
        }
      },
    },
    session_id(newVal) {
      // 通知父组件session_id更新
      this.$emit('update:sessionId', newVal);
    },
  },
  beforeDestroy() {
    // 清理事件监听
    this.$bus.$off('load-session', this.onSessionLoad);
    this.$bus.$off('new-session', this.onNewSession);
    this.$bus.$off('cancel-session-callbacks', this.cancelSessionCallbacks);
  },
  computed: {
    clinicInfo() {
      return this.getClinicInfo();
    },
    getMoment() {
      const hour = new Date().getHours();
      if (hour < 12) {
        return '上午';
      } else if (hour < 18) {
        return '下午';
      } else {
        return '晚上';
      }
    },
  },
  methods: {
    scrollToBottom() {
      this.$nextTick(() => {
        const chatContent = this.$refs['chat-content'];
        if (chatContent) {
          chatContent.scrollToBottom();
        }
      });
    },
    async getRecommendQuestions(sessionId) {
      try {
        const res = await assistantService.getRecommendQuestions({
          session_id: sessionId,
          user_id: this.userInfo.uid,
          user_name: this.userInfo.name,
          clinic_id: this.clinicInfo.clinic_id,
          clinic_name: this.clinicInfo.clinic_name,
          chat_name: 'clinic',
        });
        this.recommendQuestions = res.suggest_list;
      } catch (e) {
        this.recommendQuestions = [];
      }
    },
    clearRecommendQuestions() {
      this.recommendQuestions = [];
    },
    // 发送问题
    sendQuestion(question) {
      if (this.isLoading) return;
      const enableDeepThinking = this.$refs['main-chat-input'].enableDeepThinking;
      this.handleSend(question, enableDeepThinking);
    },
    // 当加载已有会话时的处理函数
    onSessionLoad({ sessionId }) {
      if (sessionId) {
        this.session_id = sessionId;
        this.loadMessages(sessionId);
        // this.getRecommendQuestions(sessionId);
      }
    },

    // 当创建新会话时的处理函数
    onNewSession({ sessionId, isNewSession }) {
      // 无论sessionId是否为空，都清空当前消息
      this.messages = [];
      // 重置滚动按钮状态
      this.showScrollButton = false;
      // 设置session_id，但不加载消息
      this.session_id = sessionId || '';
      this.isLoading = false;
      // 如果是新会话，标记一个状态以防止后续自动加载消息
      if (isNewSession) {
        this.isNewSessionFlag = true;
      }
      this.getRecommendQuestions(sessionId);
      this.$refs['main-chat-input']?.focusInputHandler();
      console.log('清空聊天详情，准备创建新会话');
    },

    // 取消当前会话的回调，但不终止消息
    cancelSessionCallbacks() {
      if (this.session_id) {
        this.messageService?.clearMessageCallback();
      }
    },

    async handleSend(content, enableDeepThinking, isCardSend = false, cardData = {}) {
      this.isLoading = true;
      this.recommendQuestions = [];
      this.$emit('handleCloseQuoteModal');
      // 标记是否为新会话
      const isNewChat = !this.session_id;

      // 如果没有session_id，需要先创建新会话
      if (isNewChat) {
        try {
          const res = await assistantService.addNewChat({
            user_id: this.userInfo.uid,
            clinic_name: this.clinicInfo.clinic_name,
            clinic_id: this.clinicInfo.clinic_id,
            chat_name: 'clinic',
          });

          this.session_id = res.session_id;
          // 保存session_id到localStorage
          localStorage.setItem('ai_assistant_session_id', res.session_id);

          // 通知父组件更新会话ID
          this.$emit('update:sessionId', res.session_id);

          console.log('创建新会话成功, session_id:', res.session_id);
        } catch (error) {
          console.error('创建新会话失败:', error);
          this.isLoading = false;
          this.$Message.error('创建新会话失败，请重试');
          return;
        }
      }
      const userInfo = {
        userId: this.userInfo.uid,
        userName: this.userInfo.name,
      };
      if (isCardSend) {
        await this.sendMessage({ ...cardData, ...userInfo, content: '' }, true);
      } else {
        // 发送消息，传递是否为新会话的标志
        await this.sendMessage({
          content,
          sessionId: this.session_id,
          userId: this.userInfo.uid,
          ...userInfo,
          enableDeepThinking,
          isNewChat,
        });
      }
    },

    setChatInput(message) {
      const findIndex = this.messages.findIndex(msg => msg.id === message.id);
      const preIndex = findIndex - 1 < 0 ? 0 : findIndex - 1;
      const content = this.messages?.[preIndex]?.content || '';
      this.$refs['main-chat-input']?.setMessage(content);
      this.$nextTick(() => {
        this.$set(this.messages[findIndex], 'isCopyedMessage', true);
      });
    },

    // 处理编辑并重新发送消息
    async handleEditAndResendMessage({ groupId, content, is_edit, deleteGroupId }) {
      console.log('编辑重发消息 - groupId: ', groupId, 'is_edit: ', is_edit, 'deleteGroupId: ', deleteGroupId);
      if (!content.trim()) return;

      // 先删除相关的消息（用户消息和AI回复）
      if (deleteGroupId) {
        console.log('删除前消息数量:', this.messages.length);
        console.log('要删除的group_id:', deleteGroupId);
        console.log(
          '所有消息的group_id:',
          this.messages.map(msg => ({ id: msg.id, group_id: msg.group_id, role: msg.role }))
        );

        const beforeLength = this.messages.length;
        this.messages = this.messages.filter(msg => msg.group_id !== deleteGroupId);
        console.log('删除后消息数量:', this.messages.length, '删除了', beforeLength - this.messages.length, '条消息');
      }

      const userInfo = this.userInfo;

      // 发送编辑后的消息，携带group_id和is_edit参数
      await this.sendMessage({
        content,
        sessionId: this.session_id,
        userId: this.userInfo.uid,
        userName: userInfo.name,
        is_edit: true, // 确保is_edit参数传递
        enableDeepThinking: true, // 默认启用深度思考
        isNewChat: false,
        groupId, // 添加group_id参数
      });
    },

    // 处理重新回答
    async handleRegenerateAnswer({ questionId, questionContent, currentAnswerId, originalMessageIndex }) {
      console.log('重新回答参数: ', { questionId, questionContent, currentAnswerId, originalMessageIndex });
      if (!questionContent.trim()) return;

      const userInfo = this.userInfo;

      // 发送重新回答请求，携带group_id参数
      await this.sendMessage({
        content: questionContent,
        sessionId: this.session_id,
        userId: this.userInfo.uid,
        userName: userInfo.name,
        enableDeepThinking: true,
        isNewChat: false,
        groupId: questionId, // 添加group_id参数
        isRegenerate: true, // 标识这是重新回答
        originalMessageIndex, // 传递原消息索引
      });
    },

    async loadMessages(sessionId) {
      console.log('🚀 ~ loadMessages ~ sessionId: ', sessionId);
      if (!sessionId) {
        console.warn('尝试加载消息但sessionId为空');
        return;
      }
      try {
        this.isLoading = false;
        const response = await assistantService.getChatHistoryDetail({ session_id: sessionId });
        // 处理消息并设置答案索引
        this.messages = response.map(msg => {
          if (msg.type === 'human') {
            return this.createUserMessage(msg.content, msg.id, msg.group_id);
          } else {
            const assistantMsg = this.createAssistantMessage(msg.id);
            assistantMsg.content = msg.content || '';
            assistantMsg.oppose = msg.oppose || 0;
            assistantMsg.upvote = msg.upvote || 0;
            assistantMsg.isReponsing = false;
            assistantMsg.deepSearchComplete = true;
            assistantMsg.trace_id = msg.trace_id || '';
            assistantMsg.group_id = msg.group_id || '';

            // 处理is_choose字段，控制消息显示
            assistantMsg.isCurrentAnswer = isNaN(msg.is_choose) ? true : !msg.is_choose ? false : true;

            // 处理思考内容
            // if (msg.additional_kwargs?.reasoning_content) {
            //   assistantMsg.reasoning = msg.additional_kwargs.reasoning_content;
            //   // 如果有思考内容，默认展开
            //   assistantMsg.isReasoningExpanded = true;
            //   assistantMsg.thinkDuration = msg.thinkDuration || 0;
            // } else if (msg.reasoning) {
            //   assistantMsg.reasoning = msg.reasoning;
            //   // 如果有思考内容，默认展开
            //   assistantMsg.isReasoningExpanded = true;
            //   assistantMsg.thinkDuration = msg.thinkDuration || 0;
            // }
            if (msg.steps?.length) {
              let result = [];
              msg.steps.forEach(step => {
                if (step.type === 'tool_result') {
                  result.push(...step.tool_result);
                }
                if (step.type === 'step_analysis') {
                  step.type = 'step_analysis_end';
                  step.step_analysis_content = step.content;
                  step.content = '工具调用策略分析...';
                }
                if (step.type === 'panda_script') {
                  step.type = 'script_end';
                  step.script_content = step.content;
                  step.content = 'Pandas Processing Script';
                }
              });
              assistantMsg.search_results = result;
            }
            // 设置工具使用信息
            // const tools = msg.steps;
            // const steps = [
            //   {
            //     id: 'reasoning',
            //     title: '问题分析',
            //     status: 'done',
            //   },
            //   {
            //     id: 'content',
            //     title: '答案生成',
            //     status: 'done',
            //   },
            // ];
            // if (tools?.length) {
            //   steps.splice(1, 0, {
            //     id: 'toolsUsing',
            //     title: '关键数据检索',
            //     status: 'done',
            //     tools: tools,
            //   });
            // }
            // const searchResult = tools.find(item => item.tool_type === 'internet_search');
            // if (searchResult && searchResult?.tool_other?.length) {
            //   assistantMsg.search_results = searchResult.tool_other;
            // }
            // assistantMsg.toolsProgress.type = 'content_end';
            // assistantMsg.steps = steps;
            assistantMsg.msg_type = 'content_end';
            assistantMsg.deep_search_steps = msg.steps;
            return assistantMsg;
          }
        });

        // 为同一group_id的消息设置答案索引
        const groupMap = new Map();
        this.messages.forEach(msg => {
          if (msg.type === 'AIMessageChunk' && msg.group_id) {
            if (!groupMap.has(msg.group_id)) {
              groupMap.set(msg.group_id, []);
            }
            groupMap.get(msg.group_id).push(msg);
          }
        });

        // 为每个组的消息设置answerIndex
        groupMap.forEach(messages => {
          messages.forEach((msg, index) => {
            msg.answerIndex = index;
          });
        });

        console.log('this.messages: ', this.messages);
        this.$emit('loaded');
      } catch (error) {
        console.error('Failed to load messages:', error);
        this.$Message.error('加载历史消息失败');
        this.$emit('loaded');
      }
      this.$refs['main-chat-input']?.focusInputHandler();
    },

    // 重试消息
    handleRetryMessage(messageId) {
      // 调用useChat混入中的retryMessage方法
      this.retryMessage(messageId);
    },

    // 处理网络断开事件
    handleNetworkDisconnected(messageId) {
      // 停止loading状态
      this.isLoading = false;
      console.log('网络断开，停止loading状态，消息ID:', messageId);
    },
  },
};
</script>

<style lang="less" scoped>
.chat-main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .answered-wrap {
    width: 100%;
    flex: 1;
    padding: 0 34px;
    position: relative;
    overflow: hidden;

    .answered-content {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .answered-content::-webkit-scrollbar {
      display: none;
    }

    .answered-input {
      width: 100%;
    }
  }

  .notAnswered-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .notAnswered {
    width: 100%;
    height: 100%;
    padding: 0 32px 16px 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title {
      width: 100%;
      height: 36px;
      font-weight: 600;
      font-size: 24px;
      color: #333333;
      line-height: 36px;
      margin-bottom: 12px;
      text-align: center;
    }

    .welcome {
      width: 100%;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
      text-align: center;
    }
  }

  .recommend-questions {
    width: 100%;
    margin-top: 22px;

    .question-title {
      font-size: 12px;
      color: #bbbbbb;
      line-height: 18px;
    }

    .question-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      .question-item {
        font-size: 12px;
        color: #333333;
        margin-right: 12px;
        line-height: 18px;
        padding: 7px 12px;
        border: 1px solid rgba(220, 221, 224, 0.8);
        border-radius: 8px;
        margin-bottom: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        &:hover {
          background-color: #f5f6f8;
        }
      }
    }
  }
}
</style>
