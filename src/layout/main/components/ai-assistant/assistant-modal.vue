<template>
  <div>
    <transition name="ai-modal-bg-fade">
      <div
        class="ai-assistant-wrap"
        v-if="showModel"
        :style="{
          right: `${12 - scrollbarWidth}px`,
        }"
      >
        <div class="ai-assistant-modal" @click="showHistoryList = false">
          <!-- 历史记录侧边栏 -->
          <div class="ai-modal-content">
            <history-list
              :visible.sync="showHistoryList"
              :history-list="historyList"
              @select="handleSelectHistory"
              @handleHistoryDeleted="handleHistoryDeleted"
              @editTitle="saveTitleFromList"
            />
            <quote-modal
              :quote-list="quoteList"
              :visible.sync="quoteModalVisible"
              :message-id.sync="quoteModalMessageId"
              :is-half-screen="isHalfScreen"
              @click.stop
            />
            <!-- 引用弹出组件 -->
            <CustomTooltip ref="customTooltipRef" :data="citedTooltipData" />
            <div class="header">
              <div class="logo">
                <svg-icon name="AI-modal-logo" style="width: 96px; height: 20px"></svg-icon>
              </div>
              <div v-if="isEditingTitle" class="edit-title-wrap">
                <input
                  ref="titleInput"
                  v-model="editingTitle"
                  class="title-input"
                  :maxlength="30"
                  @blur="handleSaveTitle"
                  @keydown.enter="handleSaveTitle"
                  @input="updateTitleInputWidth"
                  :style="{
                    width: titleInputWidth,
                    minWidth: '40px',
                    maxWidth: '100%',
                    border: isTitleInputFocus ? '1.5px solid #115bd4' : '1px solid #ecedf0',
                    borderRadius: '6px',
                    padding: '4px 8px',
                    fontSize: '15px',
                    fontWeight: 500,
                    color: '#333',
                    outline: 'none',
                    transition: 'border 0.2s',
                  }"
                  @focus="isTitleInputFocus = true"
                />
                <span ref="titleInputSpan" class="title-input-span">{{ editingTitle || '' }}</span>
              </div>
              <div v-else class="session-title" :title="currentSessionTitle" @click="handleEditTitle">
                {{ currentSessionTitle || '未命名对话' }}
              </div>
              <div class="right-action">
                <div
                  v-tooltip="{ content: '历史记录' }"
                  class="action-btn head-history"
                  @click.stop="toggleHistoryList"
                >
                  <svg-icon name="AI-history" :size="18" />
                </div>
                <div v-tooltip="{ content: '新建会话' }" class="action-btn head-history" @click.stop="createNewSession">
                  <svg-icon name="AI-newchat" :size="18" />
                </div>
                <div v-tooltip="{ content: '关闭' }" class="action-btn head-close" @click.stop="handleHideModal">
                  <svg-icon name="AI-modal-close" :size="12" />
                </div>
              </div>
            </div>
            <div
              class="content"
              @click="
                quoteModalVisible = false;
                quoteModalMessageId = '';
              "
            >
              <div v-if="sessionDetailLoading" class="session-detail-loading">
                <Spin size="large"></Spin>
              </div>
              <ChatMain
                v-show="!sessionDetailLoading"
                :current-session-id="currentSessionId"
                @update:sessionId="handleSessionIdUpdate"
                @handleOpenQuoteModal="handleOpenQuoteModal"
                @handleCloseQuoteModal="handleCloseQuoteModal"
                @loaded="handleSessionLoaded"
                @newMessageCompleted="handleSessionLoaded"
                @showCitedTooltip="handleShowCitedTooltip"
                @hideCitedTooltip="handleHideCitedTooltip"
                ref="chatMain"
              />
            </div>
            <div class="footer">结果由 AI 大模型生成，请核查重要信息</div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
import { throttle  } from 'lodash-es';
import QuoteModal from '@/layout/main/components/ai-assistant/quote-modal.vue';
import ChatMain from './components/index.vue';
import HistoryList from './components/history-list.vue';
import CustomTooltip from './components/custom/CustomTooltip.vue';
import clickoutside from 'element-ui/src/utils/clickoutside';
import { assistantService } from '@/libs/SSE/SSEService';
import { getUser, getEnv } from '@/libs/runtime.js';
export default {
  name: 'assistant-modal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    isHalfScreen: {
      type: Boolean,
      default: false,
    },
  },
  directives: {
    clickoutside,
  },
  provide() {
    return {
      getClinicInfo: () => this.getClinicInfo,
    };
  },
  components: {
    QuoteModal,
    ChatMain,
    HistoryList,
    CustomTooltip,
  },
  data() {
    return {
      enableDeepThinking: false,
      enableInternetSearch: false,
      isInitAi: true,
      isMouseInContent: false,
      messages: [],
      quoteList: [],
      quoteModalMessageId: '',
      sseRequesting: false,
      quoteModalVisible: false,
      isLoading: false,
      scrollbarWidth: 0,
      showHistoryList: false,
      currentSessionId: '',
      currentSessionTitle: '',
      isEditingTitle: false,
      editingTitle: '',
      historyList: [],
      userInfo: getUser(),
      isTitleInputFocus: false,
      titleInputWidth: 'auto',
      sessionDetailLoading: false,
      citedTooltipData: {}, // 引用弹出层数据
    };
  },
  computed: {
    isProdMode() {
      return getEnv() === 'production';
    },
    getClinicInfo() {
      if (this.isProdMode) {
        return {
          clinic_id: this.userInfo.clinicid,
          clinic_name: this.userInfo.clinic_name,
        };
      }
      return {
        clinic_id: '1652',
        clinic_name: '南宁榕树家景晖中医诊所',
      };
    },
    showModel: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  created() {
    // 只在created中设置事件监听，不再调用fetchHistoryList，因为watch中已经处理了
    this.$bus.$on('update-chat-title', this.updateChatTitle);
  },
  beforeMount() {
    this.isMouseInContent = false;
    document.body.style.overflow = 'auto';
  },
  mounted() {
    document.addEventListener('keydown', this.handleEscClose, true);
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleEscClose, true);
    // 清理事件监听
    this.$bus.$off('update-chat-title', this.updateChatTitle);
  },
  methods: {
    handleHistoryDeleted(sessionId) {
      if (this.currentSessionId === sessionId) {
        this.createNewSession();
      }
      this.fetchHistoryList(false);
    },
    // 初始化会话，从localStorage获取session_id，并根据historyList统一抛出选中的历史会话
    initSessionFromStorage(historyList) {
      if (!historyList || historyList.length === 0) {
        this.$refs.chatMain.getRecommendQuestions();
        return;
      }
      const sessionId = localStorage.getItem('ai_assistant_session_id');
      let targetSession = null;
      if (sessionId) {
        targetSession = historyList.find(item => item.session_id === sessionId);
      }
      // if (!targetSession) {
      //   targetSession = historyList[0];
      // }
      if (targetSession) {
        this.handleSelectHistory(targetSession);
      } else {
        this.createNewSession();
      }
    },

    // 获取历史会话列表
    async fetchHistoryList(initSession = true) {
      try {
        const response = await assistantService.getChatList({
          clinic_id: this.getClinicInfo.clinic_id,
          user_id: this.userInfo.uid,
        });

        if (response && response.items && Array.isArray(response.items)) {
          // 先转换历史记录格式
          const allHistory = response.items.map(item => ({
            id: item.session_id,
            content: item.title || '未命名对话',
            time: item.create_time,
            session_id: item.session_id,
            ...item,
          }));
          this.historyList = allHistory;
          // 统一入口：初始化会话
          if (initSession) {
            this.$nextTick(() => this.initSessionFromStorage(this.historyList));
          }
        } else {
          console.warn('历史记录响应格式不正确:', response);
          this.historyList = [];
        }
      } catch (error) {
        console.error('获取历史会话列表失败:', error);
        this.historyList = [];
        // 即使获取历史列表失败，也尝试从localStorage初始化会话
        if (initSession) {
          this.$nextTick(() => this.initSessionFromStorage(this.historyList));
        }
      }
    },

    handleClickOutside() {
      this.$store.commit('app/SET_ASSISTANT_MODAL_VISIBLE', false);
      this.showHistoryList = false;
      this.quoteModalVisible = false;
      this.quoteModalMessageId = '';
    },

    handleEscClose(e) {
      if (this.showModel && (e.key === 'Escape' || e.key === 'Esc')) {
        // 如果引用列表正在显示，先关闭引用列表
        if (this.quoteModalVisible) {
          this.quoteModalVisible = false;
          this.quoteModalMessageId = '';
          e.stopPropagation(); // 阻止事件传播
          return;
        }
        // 如果历史列表正在显示，先关闭历史列表
        if (this.showHistoryList) {
          this.showHistoryList = false;
          e.stopPropagation(); // 阻止事件传播
          return;
        }
        // this.$store.commit('app/SET_ASSISTANT_MODAL_VISIBLE', false);
      }
    },

    handleMouseEnter() {
      this.isMouseInContent = true;
      document.body.style.overflow = 'hidden';
      // 获取滚动条宽度
      this.scrollbarWidth = 0;
    },

    handleMouseLeave() {
      this.isMouseInContent = false;
      document.body.style.overflow = 'auto';
      // 获取滚动条宽度
      this.scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    },

    handleSend: throttle(function () {
      if (!this.input || this.input.trim() === '') return;
      this.isInitAi = false;
      this.sseRequesting = true;
      console.log('请求sse');
      setTimeout(() => {
        this.sseRequesting = false;
      }, 5000);
    }, 1000),

    handlePause: throttle(function () {
      this.sseRequesting = false;
    }),

    handleHideModal() {
      this.$emit('update:visible', false);
      this.quoteModalVisible = false;
    },

    handleOpenQuoteModal(tool, id) {
      this.quoteList = [];
      // 检查是否来自深度搜索的查看更多事件
      if (tool && tool.messageId && tool.results) {
        // 来自深度搜索的查看更多事件
        if (id === this.quoteModalMessageId && !!this.quoteModalMessageId) {
          this.quoteModalVisible = false;
          this.quoteModalMessageId = '';
          return;
        }

        setTimeout(() => {
          this.quoteModalVisible = true;
          this.quoteModalMessageId = tool.messageId;
          this.quoteList = tool.results || [];
        }, 36);
        return;
      }

      // 处理普通引用事件
      if (id === this.quoteModalMessageId && !!this.quoteModalMessageId) {
        this.quoteModalVisible = false;
        this.quoteModalMessageId = '';
        return;
      }
      setTimeout(() => {
        this.quoteModalVisible = true;
        this.quoteModalMessageId = id;
        this.quoteList = tool?.tool_other || [];
      }, 36);
    },

    handleCloseQuoteModal() {
      console.log(111);
      this.quoteModalVisible = false;
      this.quoteModalMessageId = '';
    },

    toggleHistoryList() {
      this.showHistoryList = !this.showHistoryList;
      if (this.showHistoryList) {
        // 如果正在打开历史列表且当前没有历史数据，先刷新历史列表
        // if (this.historyList.length === 0) {
        this.fetchHistoryList(false);
        // }
        console.log('打开历史记录列表');
      }
    },

    handleSelectHistory(item) {
      console.log('选择历史记录:', item);
      this.sessionDetailLoading = true;
      // 如果有其他会话正在响应，取消接收消息回调但不终止消息
      // 保存当前选择的会话ID到localStorage
      this.currentSessionId = item.session_id;
      localStorage.setItem('ai_assistant_session_id', item.session_id);
      this.currentSessionTitle = item.title;
      this.showHistoryList = false;
      // 清理引用弹出层数据
      this.citedTooltipData = {};
      this.handleHideCitedTooltip();
      this.$refs.chatMain.clearRecommendQuestions();
      // 通过事件总线通知chat组件加载会话
      this.$nextTick(() => {
        this.$bus.$emit('load-session', { sessionId: item.session_id });
      });
    },

    handleSessionLoaded() {
      this.sessionDetailLoading = false;
      // 新建会话后，AI 回答结束时刷新历史列表
      if (!this.historyList.some(item => item.session_id === this.currentSessionId) && this.currentSessionId) {
        this.fetchHistoryList(false);
      }
    },

    async createNewSession() {
      // 如果有其他会话正在响应，取消接收消息回调但不终止消息
      this.$bus.$emit('cancel-session-callbacks');

      // 清空当前会话ID
      this.currentSessionId = '';
      localStorage.removeItem('ai_assistant_session_id');

      // 设置临时标题
      this.currentSessionTitle = '新会话';

      // 重置编辑状态
      this.isEditingTitle = false;
      this.editingTitle = '';

      // 关闭历史记录弹窗
      this.showHistoryList = false;

      // 清理引用弹出层数据
      this.citedTooltipData = {};
      this.handleHideCitedTooltip();

      // 通知chat组件清空消息，传递一个标记表明是新建会话
      this.$nextTick(() => {
        this.$bus.$emit('new-session', { sessionId: '', isNewSession: true });
      });
    },

    // 处理标题更新
    updateChatTitle({ sessionId, title }) {
      if (sessionId === this.currentSessionId) {
        this.currentSessionTitle = title;

        // 同时更新历史列表中的标题
        const index = this.historyList.findIndex(item => item.session_id === sessionId);
        if (index !== -1) {
          this.$set(this.historyList[index], 'content', title);
          this.$set(this.historyList[index], 'title', title);
        }
      }
    },

    // 处理sessionId更新
    handleSessionIdUpdate(sessionId) {
      if (sessionId && sessionId !== this.currentSessionId) {
        this.currentSessionId = sessionId;
        localStorage.setItem('ai_assistant_session_id', sessionId);
      }
    },

    // 开始编辑标题
    handleEditTitle() {
      if (!this.currentSessionId) return; // 如果没有会话ID，不允许编辑
      this.isEditingTitle = true;
      this.editingTitle = this.currentSessionTitle;
      this.$nextTick(() => {
        this.$refs.titleInput.focus();
      });
    },
    saveTitleFromList(title, sessionId) {
      this.saveTitle(sessionId, title);
    },
    // 保存标题
    handleSaveTitle() {
      if (!this.editingTitle.trim()) {
        this.editingTitle = this.currentSessionTitle;
      }

      if (this.editingTitle !== this.currentSessionTitle && this.currentSessionId) {
        this.saveTitle(this.currentSessionId, this.editingTitle);
      }
      this.isEditingTitle = false;
    },
    async saveTitle(sessionId, title) {
      try {
        await assistantService.setChat({
          session_id: sessionId,
          title: title,
          clinic_id: this.getClinicInfo.clinic_id,
          clinic_name: this.getClinicInfo.clinic_name,
          chat_name: 'clinic',
        });

        // 更新当前标题和历史列表中的标题
        this.currentSessionTitle = title;
        const index = this.historyList.findIndex(item => item.session_id === sessionId);
        if (index !== -1) {
          this.$set(this.historyList[index], 'title', title);
        }
      } catch (error) {
        console.error('保存标题失败:', error);
        this.$Message.error('标题修改失败，请重试');
      }
    },
    updateTitleInputWidth() {
      // 通过隐藏span计算input宽度
      if (this.$refs.titleInputSpan) {
        const span = this.$refs.titleInputSpan;
        span.textContent = this.editingTitle || ' ';
        const width = span.offsetWidth + 16; // padding
        this.titleInputWidth = width + 'px';
      }
    },

    // 显示引用弹出层
    handleShowCitedTooltip({ target, data }) {
      this.citedTooltipData = data || {};
      this.$nextTick(() => {
        if (this.$refs.customTooltipRef) {
          this.$refs.customTooltipRef.show(target);
        }
      });
    },

    // 隐藏引用弹出层
    handleHideCitedTooltip() {
      if (this.$refs.customTooltipRef) {
        this.$refs.customTooltipRef.hide();
      }
    },
  },
  watch: {
    visible: {
      async handler(val) {
        if (val) {
          // 弹窗打开时，刷新历史列表
          await this.fetchHistoryList();
          console.log('弹窗打开，刷新历史列表');
        }
      },
      immediate: true,
    },
    editingTitle() {
      this.$nextTick(this.updateTitleInputWidth);
    },
  },
};
</script>

<style scoped lang="less">
.ai-assistant-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  z-index: 300;
}

.ai-assistant-modal {
  width: 70%;
  min-width: 700px;
  max-width: 840px;
  height: 100vh;
  margin-top: 30px;
  margin-bottom: 30px;
  background: #fff;
  box-shadow: 0 2px 24px 0 rgba(48, 49, 51, 0.18);
  border-radius: 6px;
  position: relative;
  display: flex;
  flex-direction: column;
  height: e('calc(100vh - 60px)');

  .ai-modal-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 历史列表定位
    /deep/ .history-list-wrapper {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      z-index: 16;
      border-top-left-radius: 16px;
      border-bottom-left-radius: 16px;
    }
  }

  .header {
    padding: 0 16px;
    display: flex;
    min-height: 56px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ecedf0;
    // background: #fff;
    border-radius: 6px 6px 0 0;
    z-index: 1;
    background: linear-gradient(180deg, #fff 0, #fff 50%, hsla(0, 0%, 100%, 0.94) 75%, hsla(0, 0%, 100%, 0.88) 100%);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    .logo {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      width: 110px;
    }

    .session-title {
      text-align: center;
      font-size: 15px;
      font-weight: 500;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 15px;
      cursor: pointer;
      max-width: 400px;

      &:hover {
        color: #115bd4;
      }
    }

    .right-action {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex-shrink: 0;
      width: 110px;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333333;
        cursor: pointer;

        &:not(.head-close) {
          padding: 8px;
          border-radius: 4px;
          margin-left: 4px;

          &:hover {
            background: #f5f6f8;
          }
        }

        &.head-close {
          color: #999;
          margin-left: 12px;

          &:hover {
            color: #333;
          }
        }
      }
    }

    .edit-title-wrap {
      flex: 1;
      margin: 0 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 380px;
      position: relative;

      .title-input {
        background: #fff;
        box-sizing: content-box;
        min-width: 40px;
        border: 1px solid #ecedf0;
        border-radius: 6px;
        padding: 4px 8px;
        font-size: 15px;
        font-weight: 500;
        color: #333;
        outline: none;
        transition: border 0.2s;
        text-align: center;
        font-family: inherit;
        letter-spacing: normal;
        display: block;
        margin: 0 auto;
      }

      .title-input-span {
        position: absolute;
        left: -9999px;
        top: 0;
        visibility: hidden;
        white-space: pre;
        font-size: 15px;
        font-weight: 500;
        font-family: inherit;
        padding: 4px 8px;
        box-sizing: content-box;
        letter-spacing: normal;
      }
    }
  }

  .content {
    width: 100%;
    height: auto;
    overflow: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .footer {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 12px;
    color: #cccccc;
    line-height: 42px;
    /* 毛玻璃效果 */
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #cccccc;
    line-height: 16px;
    padding: 8px 0;
  }

  .copyright {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 42px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;

    .text {
      font-size: 11px;
      color: #cccccc;
      line-height: 16px;
    }
  }
}

.ai-modal-bg-fade-enter-active,
.ai-modal-bg-fade-leave-active {
  transition: opacity 0.3s ease;
}

.ai-modal-bg-fade-enter,
.ai-modal-bg-fade-leave-to {
  opacity: 0;
}

.ai-modal-bg-fade-enter-active .ai-assistant-modal,
.ai-modal-bg-fade-leave-active .ai-assistant-modal {
  transition: all 0.3s ease;
}

.ai-modal-bg-fade-enter .ai-assistant-modal,
.ai-modal-bg-fade-leave-to .ai-assistant-modal {
  transform: translateX(100%);
}

.ai-modal-fade-enter-active,
.ai-modal-fade-leave-active {
  transition: all 0.3s ease;
}

.ai-modal-fade-enter,
.ai-modal-fade-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.session-detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  font-size: 15px;
  color: #999;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 4px solid #e5e5e5;
    border-top: 4px solid #115bd4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
:deep(.ivu-input-wrapper, .ivu-select) {
  max-width: unset;
}
</style>
