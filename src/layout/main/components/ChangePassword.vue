<template>
  <Modal
    :value="visible"
    :mask-closable="false"
    :width="430"
    title="修改密码"
    @on-cancel="cancel"
    @on-visible-change="visibleChange"
  >
    <div slot="header" class="ivu-modal-header-inner custom-header">修改密码</div>
    <div style="padding: 0 50px">
      <KWidget :labelWidth="70" label="姓名:">
        <div style="line-height: 30px; font-size: 14px">{{ userInfo.name }}</div>
      </KWidget>
      <KWidget :labelWidth="70" label="新密码:">
        <Input v-model="formData.new_password" type="password" placeholder="请输入新密码" />
      </KWidget>
      <KWidget :labelWidth="70" label="确认密码:">
        <Input v-model="formData.confirm_password" type="password" placeholder="请重新输入新密码" />
      </KWidget>
    </div>

    <template slot="footer">
      <div class="flex flex-item-center">
        <Button type="default" @click="cancel">取消</Button>
        <Button type="primary" @click="changePassWord">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
import S from '@/libs/util';
import config from '@/config';
import { logout } from '@/libs/runtime'; // Runtime information
let init_form_data = {
  new_password: '',
  confirm_password: '',
};
export default {
  name: 'ChangePassword',

  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    userInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      formData: { ...init_form_data },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    visibleChange(val) {
      if (!val) {
        this.formData = { ...init_form_data };
      }
    },
    changePassWord() {
      if (this.formData.new_password.trim() == '' || this.formData.confirm_password.trim() == '') {
        this.$Message.error('请填写完信息');
        this.psdModalLoading = false;
        return;
      }
      if (this.formData.new_password != this.formData.confirm_password) {
        this.$Message.error('两次密码需填写一致');
        this.psdModalLoading = false;
        return;
      }
      let query = {
        new_password: S.encrypt(
          JSON.stringify({
            password: this.formData.new_password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        confirm_password: S.encrypt(
          JSON.stringify({
            password: this.formData.confirm_password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        version: config.cryptoVersion,
      };
      this.$api
        .resetPassword(query)
        .then(
          res => {
            this.cancel();
            this.$Message.success({
              content: `密码修改成功，3秒后将重新登录`,
              duration: 3,
            });
            setTimeout(() => {
              logout();
              this.$router.push({ path: '/login', query: { from: encodeURIComponent(this.$route.fullPath) } });
            }, 3000);
          },
          err => {
            this.$Message.error(err.errmsg);
          }
        )
        .catch(e => {});
    },
    cancel() {
      this.$emit('update:visible', false);
      this.formData = { ...init_form_data };
    },
  },
};
</script>
<style scoped lang="less">
.custom-header {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}
</style>
