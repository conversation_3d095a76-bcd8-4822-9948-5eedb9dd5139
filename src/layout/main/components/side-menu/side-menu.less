.app-first-sidebar {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  height: 100%;
  width: 92px;
  background-color: #444;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 4;
  box-shadow: 2px 0 6px #4c4b4b82;
  .app-logo {
    position: fixed;
    top: 0;
    bottom: 0;
    height: 56px;
    width: 92px;
    word-break: break-word;
    background: #444;
    z-index: 1002;
  }
  .logo-wrap {
    height: 56px;
    line-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    .rsx-logo {
      //margin: 12px auto 0;
      //width: 32px;
      //height: 32px;
      //-webkit-border-radius: 50%;
      //border-radius: 2px;
      //background-size: cover;
      //background-position: 50% 50%;
      //background-color: #fff;
      //border: 1px solid #fff;
      //width: 132px;
      //height: 41px;
      width: 80px;
      height: 20px;
    }
    .rst-logo {
      width: 80px;
      height: 24px;
    }
  }
  .app-first-sidebar-nav {
    margin-top: 56px;
    padding-right: 20px;
    box-sizing: content-box;
    width: 100%;
    flex: 1 1 100%;
    overflow-x: hidden;
    overflow-y: scroll;
    li {
      width: 92px;
      font-size: 14px;
      height: 40px;
      line-height: 40px;
      cursor: pointer;
      &:hover, &.hover {
        background: #666;
      }
      &.active {
        background: #fff;
        a {
          color: #333;
        }
        .svg-icon{
          fill: #333!important;
        }
      }
      i.fa {
        padding-right: 3px;
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
      a {
        color: #c8c9cc;
        display: block;
        padding-left: 20px;
        text-decoration: none;
        .svg-icon{
          fill: #c8c9cc!important;
        }
      }
    }
  }
  .app-user-info {
    display: flex;
    flex: 0 0 0;
    flex-direction: row;
    color: #e5e5e5;
    padding: 10px 0;
    word-break: break-word;
    background: #535353;
    z-index: 100;
    .name {
      width: 90px;
      display: block;
      line-height: 26px;
      text-align: center;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

.app-second-sidebar {
  transition: all .2s;
  width: 118px;
  height: 100%;
  margin-left: 92px;
  padding-bottom: 40px;
  background-color: #fff;
  border-right: 1px solid #ebedf0;
  z-index: 1;
  position: absolute;
  top: 0;
  transform: translateX(0);
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  &.preview {
    z-index: 2;
    //-webkit-animation-name: fadeIn; /*动画名称*/
    //-webkit-animation-duration: .10s; /*动画持续时间*/
  }
  .second-sidebar-title {
    padding-left: 24px;
    font-size: 14px;
    height: 56px;
    line-height: 56px;
    box-sizing: border-box;
    border-bottom: 1px solid #f2f2f2;
  }
  .second-sidebar-nav {
    padding-top: 12px;
    padding-bottom: 12px;
    //padding-left: 12px;
    //padding-right: 12px;
    width: 118px;
    margin: 0;
    overflow-x: hidden;
    overflow-y: scroll;
    box-sizing: content-box;
  }
  li {
    font-size: 14px;
    height: 36px;
    line-height: 36px;
    margin-bottom: 14px;
    overflow: hidden;
    cursor: pointer;
    border-radius: 2px;
    margin-left: 12px;
    width: 92px;
    &.active {
      background: #ececec;
      a {
        color: #000;
      }
    }
    a {
      color: #666;
      display: inline-block;
      padding: 0 10px;
      width: 100%;
      &:hover {
        color: #3283FA;
      }
    }
  }
}
body.without-second-sidebar .app-second-sidebar {
  //transform: translateX(-100%);
}

@-webkit-keyframes fadeIn {
  //0% {
  //  opacity: 0; /*初始状态 透明度为0*/
  //}
  //20% {
  //  opacity: .2;
  //}
  //50% {
  //  opacity: .5; /*中间状态 透明度为0.5*/
  //}
  //70% {
  //  opacity: .7;
  //}
  //100% {
  //  opacity: 1; /*结尾状态 透明度为1*/
  //}

  0% {
    color: #66666621;
  }
  20% {
    color: #66666642;
  }
  50% {
    color: #6666668a;
  }
  70% {
    color: #666666bf;
  }
  100% {
    color: #666;
  }
}
