<template>
  <div style="height: 100%">
    <div class="app-first-sidebar">
      <div class="app-logo">
        <k-link :to="{ path: '/' }" class="logo-wrap" style="color: #f2f2f2">
          <!--          <div class="logo-img" style="background-image:url('https://img-sn-i01s-cdn.rsjxx.com/image/20210429/lsm/icon_120px.png');"></div>-->
          <svg-icon :iconClass="iconLogo" :class="iconLogoClass"></svg-icon>
        </k-link>
      </div>
      <!-- 一级菜单 -->
      <ul class="app-first-sidebar-nav" @mouseleave="onPreviewSecondMenus(false)">
        <li
          v-for="item in firstMenus"
          :key="item.pid"
          :data-pid="item.id"
          :class="{
            active: isActivePath(item),
            hover: isOpenPreview == true && item.id == preview.activeFirstMenuId && item.path.includes('his'),
          }"
        >
          <k-link v-if="isHisMenu(item)" isHISLink @click.native="toNewClinic(item)"
            ><i class="fa" :class="item.meta.icon"></i> {{ item.name }}
          </k-link>
          <k-link
            :to="{ path: item.path, query: item.query }"
            @mouseover.native="onPreviewSecondMenus(item.path, item.query)"
            target="_self"
            v-else
            ><i class="fa" :class="item.meta.icon"></i> {{ item.name }}
          </k-link>
        </li>
      </ul>
    </div>
    <!-- 二级菜单 -->
    <div class="app-second-sidebar" v-if="secondMenus.length > 0" v-show="!isOpenPreview">
      <div class="second-sidebar-title">{{ activeFirstTitle }}管理</div>
      <ul class="second-sidebar-nav hidden-scroll">
        <li
          v-for="(item, key) in secondMenus"
          :key="key"
          :class="[item.id == activeSecondMenuId ? 'active' : '', item.type === 'GROUP' ? 'is-group-title' : '']"
        >
          <span class="group-text" v-if="item.type === 'GROUP'">{{ item.name }}</span>
          <k-link :to="{ path: item.path, query: item.query }" target="_self">
            <!-- <Badge :dot="true" :offset="[10, -5]" v-if="getMenusItem(item)"> -->
            <span v-if="getMenusItem(item)">{{ item.name }}</span>
            <span class="statusUpdate" v-if="getMenusItem(item)"></span>
            <!-- </Badge> -->
            <span v-else>
              {{ item.name }}
            </span>
          </k-link>
        </li>
      </ul>
    </div>

    <!-- 动态二级菜单 -->
    <div class="app-second-sidebar preview" v-show="isOpenPreview" @mouseleave="isOpenPreview = false">
      <div class="second-sidebar-title">{{ preview.activeFirstTitle }}管理</div>
      <ul class="second-sidebar-nav hidden-scroll">
        <li
          v-for="(item, key) in preview.secondMenus"
          :key="key"
          :class="[item.type === 'GROUP' ? 'is-group-title' : '']"
        >
          <span class="group-text" v-if="item.type === 'GROUP'">{{ item.name }}</span>
          <k-link :to="{ path: item.path, query: item.query }" target="_self">{{ item.name }}</k-link>
        </li>
      </ul>
    </div>

    <Modal v-model="logoutModal" :width="300" @on-ok="onLogout"> 确定退出登录？</Modal>
  </div>
</template>

<script>
import './side-menu.less';
import { mapState } from 'vuex';
/* eslint-disable */
import S from '@/libs/util';
import * as runtime from '@/libs/runtime';

console.log('%c=>(side-menu_hide_his.vue:82) runtime', 'color: #ECA233;font-size: 16px;', runtime);
import global_config from '@/config';
import { isEnableHYMenu, isRstClinic, isRstOpcClinic } from '@/libs/runtime';

/* eslint-disable */

export default {
  name: 'SideMenu',
  data() {
    return {
      activeMenuPath: this.$route.path,
      activeFirstMenuId: 0,
      activeFirstTitle: '',
      activeSecondMenuId: 0,
      secondMenus: [],

      isOpenPreview: false,
      preview: {
        activeFirstMenuId: 0,
        activeFirstTitle: '',
        activeSecondMenuId: 0,
        secondMenus: [],
      },
      timerid: 0,

      logoutModal: false,
      userInfo: {},
    };
  },
  computed: {
    iconLogo() {
      if (isRstOpcClinic()) return 'rst-szh-logo';
      if (isRstClinic()) return 'rsj-szh-logo';
      return 'rsj-logo';
    },
    iconLogoClass() {
      if (isRstOpcClinic()) return 'rst-logo';
      if (isRstClinic()) return 'rst-logo';
      return 'rsx-logo';
    },
    ...mapState('menus', {
      firstMenus: state => state.firstMenus,
      secondMenusAll: state => state.secondMenusAll,
    }),
    isActivePath(item) {
      return item => {
        const firstPath = item.path !== '/' ? item.path.split('/') : item.redirect.split('/');
        const curPath = this.activeMenuPath.split('/')[1];
        if (
          item.path === this.activeMenuPath ||
          item.redirect == this.activeMenuPath ||
          (firstPath[1] && curPath === firstPath[1])
        ) {
          return true;
        }
        return false;
      };
    },
    getMenusItem() {
      return function (items) {
        // 这里判断是否要展示徽标
        if (items.meta.isBadge) {
          return true;
        }
        return false;
      };
    },
    isHisMenu() {
      return item => {
        return (
          (item.path.startsWith('/his') || item.redirect === '/his/newHis/newHis') && item.query.isHisTopMenu !== '1'
        );
      };
    },
    isRstClinic: () => isRstClinic(),
  },
  created() {
    const d = this.renderSecondMenus(this.activeMenuPath);
    this.userInfo = runtime.getUser();
  },
  mounted() {},
  watch: {
    $route(to, from) {
      this.activeMenuPath = to.path;
      this.$emit('getRouteChange', to, from);
      this.renderSecondMenus(this.activeMenuPath);
      this.isOpenPreview = false;
      // 入驻成功审核成功和开通云直通业务审核成功后，未添加药师，或未有已启用的药师，每日首次点击【互医模块】提醒一次去添加药师。
      if (isEnableHYMenu() && to.path.includes('internet-hospital')) {
        console.log('=>(side-menu_hide_his.vue:167) 互医模块');
        // 先判断cookie里存不存在该用户已经点击看过弹框
        let flag = S.Cookies.get(`_sj_hospital_${runtime.getUid()}`);
        if (!flag) {
          this.getClinicHospitalStatus();
        }
      }
    },
  },
  methods: {
    toNewClinic(item) {
      console.log('=>(side-menu_hide_his.vue:159) item', item);
      if (item.query.to_his === '1') {
        const a = document.createElement('a');
        if (item.redirect === '/his/newHis/newHis' && item.query.isHisTopMenu) {
          a.target = '_self';
        } else {
          a.target = '_blank';
        }
        a.href = `${global_config.HISDomain}/login`;
        document.body.appendChild(a);
        a.click();
        // this.$api.getHisToken().then(
        //   res => {
        //     // let IS_PROD = process.env.VUE_APP_NODE_ENV === 'production'?true:false
        //     let env = '';
        //
        //     // if (process.env.VUE_APP_NODE_ENV === "production") {
        //     //   a.href = `https://his.rsjxx.com/login?token=${res.token}`;
        //     //   document.body.appendChild(a);
        //     //   a.click();
        //     // } else if (process.env.VUE_APP_NODE_ENV === "test") {
        //     //   a.href = `http://his2test.rsjxx.com/login?token=${res.token}`;
        //     //   document.body.appendChild(a);
        //     //   a.click();
        //     // } else if (process.env.VUE_APP_NODE_ENV === "test-74") {
        //     //   a.href = `http://74his.rsjxx.com/login?token=${res.token}`;
        //     //   document.body.appendChild(a);
        //     //   a.click();
        //     // }
        //     document.body.removeChild(a);
        //
        //     // if(IS_PROD){
        //     //   a.href=`https://his.rsjxx.com?token=${res.token}`;
        //     //   document.body.appendChild(a);
        //     //   a.click()
        //     //   // window.open(`https://his.rsjxx.com?token=${res.token}`,'_target')
        //     // }else{
        //     //   a.href=`http://his2test.rsjxx.com?token=${res.token}`;
        //     //   document.body.appendChild(a);
        //     //   a.click()
        //     //   // window.open(`http://his2test.rsjxx.com?token=${res.token}`,'_target')
        //     // }
        //   },
        //   rej => {
        //     {};
        //   }
        // );
      }
    },
    renderSecondMenus: function (path) {
      const d = this.handlerSecondMenus(path);
      this.activeFirstMenuId = d.activeFirstMenuId;
      this.activeFirstTitle = d.activeFirstTitle;
      this.activeSecondMenuId = d.activeSecondMenuId;
      this.secondMenus = d.secondMenus;
      if (this.secondMenus.length > 0) {
        this.$store.commit('menus/SET_SHOW_SECOND_MENU', true);
      } else {
        this.$store.commit('menus/SET_SHOW_SECOND_MENU', false);
      }
    },

    onPreviewSecondMenus: function (path, query) {
      if (this.timerid != null) {
        clearTimeout(this.timerid);
        this.timerid = null;
      }
      if (!path) {
        return;
      }

      this.timerid = setTimeout(() => {
        const d = this.handlerSecondMenus(path);
        this.preview.activeFirstMenuId = d.activeFirstMenuId;
        //在此处return 是为了hover【HIS】的时候能丢掉hover样式，阻止二级菜单渲染
        if (path.startsWith('/his') && query.to_his === '1') {
          return false;
        }
        this.preview.activeFirstTitle = d.activeFirstTitle;
        this.preview.activeSecondMenuId = d.activeSecondMenuId;
        this.preview.secondMenus = d.secondMenus;

        if (this.preview.secondMenus.length <= 0 || this.activeFirstMenuId == this.preview.activeFirstMenuId) {
          this.isOpenPreview = false;
        } else {
          this.isOpenPreview = true;
        }
        this.timerid = null;
      }, 100);
    },

    handlerSecondMenus: function (path) {
      let activeFirstMenuId = 0;
      let activeSecondMenuId = 0;
      let secondMenus = [];
      let activeFirstTitle = '';

      this.firstMenus.forEach(item => {
        if (item.path == path) {
          activeFirstMenuId = item.pid;
          if (path == '/') {
            path = item.redirect;
          }
        }
      });

      let secondRoutes = [];
      for (let pid in this.secondMenusAll) {
        let items = this.secondMenusAll[pid];
        items.forEach(item => {
          if (item.path == path) {
            if (item.type == 'SUB_MENU') {
              activeFirstMenuId = item.p_id;
              activeSecondMenuId = item.id;
            } else {
              activeSecondMenuId = item.p_id;
              items.forEach(t => {
                if (t.id == item.p_id) {
                  activeFirstMenuId = t.p_id;
                }
              });
            }
            secondRoutes = items;
          }
        });
      }

      secondRoutes.forEach(item => {
        if (item.type == 'SUB_MENU' || item.type === 'GROUP') {
          secondMenus.push(item);
        }
      });

      // 一级标题
      this.firstMenus.forEach(item => {
        if (item.id == activeFirstMenuId) {
          activeFirstTitle = item.name;
        }
      });

      return {
        activeFirstMenuId,
        activeFirstTitle,
        activeSecondMenuId,
        secondMenus,
      };
    },

    onLogout: function () {
      runtime.logout();
      this.$router.push({
        path: '/login',
        query: { from: encodeURIComponent(this.$route.fullPath) },
      });
    },

    getClinicHospitalStatus() {
      let params = {};
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getClinicHospitalStatus(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          // clinic_status 诊所入驻状态 0= 尚未入驻，1 = 待审， 2 = 完成， 3 = 驳回  4 = 修改中
          // yzt_status 云直通状态 init ：尚未认证 、 processing 处理中、succeed 成功、failed ：驳回
          // not_pharmacist_status  1=未认证、2=已认证
          // flag = 入驻成功审核成功和开通云直通业务审核成功 && 未有已启用的药师
          let flag =
            (res.clinic_status === '2' || res.clinic_status === '4') &&
            res.yzt_status === 'succeeded' &&
            res.not_pharmacist_status === '1';
          if (flag) {
            this.$Modal.confirm({
              title: '提醒',
              content: '当前诊所暂无发药审核药师，当产生处方订单后会导致无法进行审核和发药，是否添加药师？',
              cancelText: '稍后添加',
              okText: '添加药师',
              onOk: () => {
                S.Cookies.set(`_sj_hospital_${runtime.getUid()}`, true, { expires: 1 }); // 本地
                this.$router.push('/internet-hospital/pharmacist/list');
              },
              onCancel: () => {
                S.Cookies.set(`_sj_hospital_${runtime.getUid()}`, true, { expires: 1 }); // 本地
              },
            });
          }
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
  },
};
</script>

<style lang="less">
.ivu-poptip-inner {
  border-radius: 1px !important;
}

.ivu-poptip-popper .ivu-poptip-body {
  padding: 0;
}

.app-team-info dl {
  margin: 0;

  div,
  a {
    display: block;
    padding: 10px 20px;
    color: #666;
    cursor: pointer;

    &:hover {
      background: #3283fa;
      color: #fff;
    }
  }
}

.statusUpdate {
  display: inline-block;
  width: 7px;
  height: 7px;
  background: red;
  border-radius: 50%;
  position: relative;
  top: -10px;
}

.second-sidebar-nav {
  .is-group-title {
    width: 117px;
    height: 36px;
    background: rgba(0, 0, 0, 0.03);
    box-shadow: 0px 1px 1px 0px #f1f1f1;
    cursor: unset;
    margin-left: 0;

    .group-text {
      width: 100%;
      display: inline-block;
      font-size: 12px;
      //font-weight: 300;
      color: #999999;
      line-height: 17px;
      text-indent: 20px;
    }
  }
}
</style>
