<template>
  <div style="height: 100%">
    <div class="app-first-sidebar">
      <div class="app-logo">
        <k-link :to="{ path: '/' }" class="logo-wrap">
          <div
            class="logo-img"
            style="background-image: url('http://img-sn-i01s-cdn.rsjxx.com/image/20210429/lsm/icon_120px.png')"
          ></div>
        </k-link>
      </div>
      <!-- 一级菜单 -->
      <ul class="app-first-sidebar-nav" @mouseleave="onPreviewSecondMenus(false)">
        <li
          v-for="item in firstMenus"
          :key="item.pid"
          :data-pid="item.id"
          :class="{
            active: item.path == activeMenuPath && item.path !== '/his/outpatient/list',
            hover: isOpenPreview == true && item.id == preview.activeFirstMenuId && item.path != '/his/outpatient/list',
          }"
        >
          <k-link
            :to="{ path: item.path, query: item.query }"
            @mouseover.native="onPreviewSecondMenus(item.path)"
            target="_self"
            v-if="item.path !== '/his/outpatient/list'"
          >
            <i class="fa" :class="item.meta.icon" v-if="item.meta.icon.startsWith('fa')"></i>
            <svg-icon v-else :name="item.meta.icon"></svg-icon>
            <span>{{ item.name }} {{ item.meta }} </span>
          </k-link>
          <k-link
            v-if="item.path === '/his/outpatient/list'"
            @mouseover.native="onPreviewSecondMenus(item.path)"
            @click.native="toNewClinic"
            ><i class="fa" :class="item.meta.icon"></i> {{ item.name }}
          </k-link>
        </li>
      </ul>
      <!--      <Poptip trigger="hover" placement="top-start" transfer>-->
      <!--        <div class="app-user-info">-->
      <!--          <span class="name">{{ userInfo.name }}</span>-->
      <!--        </div>-->
      <!--        <div class="app-team-info" slot="content">-->
      <!--          <dd>-->
      <!--            <dl><k-link to="/platform/member/update"> <i class="fa fa-cog"></i> 帐户资料</k-link></dl>-->
      <!--            <dl><k-link to="/platform/member/updatepassword"> <i class="fa fa-key"></i> 修改密码</k-link></dl>-->
      <!--            <dl><div @click="logoutModal=true"> <i class="fa fa-sign-out"></i> 退出登录</div></dl>-->
      <!--          </dd>-->
      <!--        </div>-->
      <!--      </Poptip>-->
    </div>
    <!-- 二级菜单 -->
    <div class="app-second-sidebar" v-if="secondMenus.length > 0" v-show="!isOpenPreview">
      <div class="second-sidebar-title">{{ activeFirstTitle }}管理</div>
      <ul class="second-sidebar-nav">
        <li v-for="(item, key) in secondMenus" :key="key" :class="item.id == activeSecondMenuId ? 'active' : ''">
          <k-link :to="{ path: item.path, query: item.query }" target="_self">{{ item.name }}</k-link>
        </li>
      </ul>
    </div>

    <!-- 动态二级菜单 -->
    <div class="app-second-sidebar preview" v-show="isOpenPreview" @mouseleave="isOpenPreview = false">
      <div class="second-sidebar-title">{{ preview.activeFirstTitle }}管理</div>
      <ul class="second-sidebar-nav">
        <li v-for="(item, key) in preview.secondMenus" :key="key">
          <k-link :to="{ path: item.path, query: item.query }" target="_self">{{ item.name }}</k-link>
        </li>
      </ul>
    </div>

    <Modal v-model="logoutModal" :width="300" @on-ok="onLogout"> 确定退出登录？</Modal>
  </div>
</template>

<script>
import './side-menu.less';
import { mapState } from 'vuex';
/* eslint-disable */
import S from '@/libs/util';
import * as runtime from '@/libs/runtime';
/* eslint-disable */

export default {
  name: 'SideMenu',
  data() {
    return {
      activeMenuPath: this.$route.path,

      activeFirstMenuId: 0,
      activeFirstTitle: '',
      activeSecondMenuId: 0,
      secondMenus: [],

      isOpenPreview: false,
      preview: {
        activeFirstMenuId: 0,
        activeFirstTitle: '',
        activeSecondMenuId: 0,
        secondMenus: [],
      },
      timerid: 0,

      logoutModal: false,
      userInfo: {},
    };
  },
  computed: {
    ...mapState('menus', {
      firstMenus: state => state.firstMenus,
      secondMenusAll: state => state.secondMenusAll,
    }),
  },
  created() {
    const d = this.renderSecondMenus(this.activeMenuPath);

    this.userInfo = runtime.getUser();
  },
  watch: {
    $route(to) {
      this.activeMenuPath = to.path;
      this.renderSecondMenus(this.activeMenuPath);
      this.isOpenPreview = false;
    },
  },
  methods: {
    toNewClinic() {
      console.log(321);
      this.$api.getHisToken().then(
        res => {
          console.log(res);
          window.open(`localhost:8081/v1/user/list?token=${res.token}`, '_target');
        },
        rej => {
          console.log(rej);
        }
      );
    },
    renderSecondMenus: function (path) {
      console.log('-> path', path);
      const d = this.handlerSecondMenus(path);
      this.activeFirstMenuId = d.activeFirstMenuId;
      this.activeFirstTitle = d.activeFirstTitle;
      this.activeSecondMenuId = d.activeSecondMenuId;
      this.secondMenus = d.secondMenus;

      if (this.secondMenus.length > 0) {
        this.$store.commit('menus/SET_SHOW_SECOND_MENU', true);
      } else {
        this.$store.commit('menus/SET_SHOW_SECOND_MENU', false);
      }
    },

    onPreviewSecondMenus: function (path) {
      if (this.timerid != null) {
        clearTimeout(this.timerid);
        this.timerid = null;
      }
      if (!path) {
        return;
      }

      this.timerid = setTimeout(() => {
        const d = this.handlerSecondMenus(path);
        this.preview.activeFirstMenuId = d.activeFirstMenuId;
        //在此处return 是为了hover【HIS】的时候能丢掉hover样式，阻止二级菜单渲染
        if (path === '/his/outpatient/list') {
          return false;
        }
        this.preview.activeFirstTitle = d.activeFirstTitle;
        this.preview.activeSecondMenuId = d.activeSecondMenuId;
        this.preview.secondMenus = d.secondMenus;

        if (this.preview.secondMenus.length <= 0 || this.activeFirstMenuId == this.preview.activeFirstMenuId) {
          this.isOpenPreview = false;
        } else {
          this.isOpenPreview = true;
        }
        this.timerid = null;
      }, 100);
    },

    handlerSecondMenus: function (path) {
      let activeFirstMenuId = 0;
      let activeSecondMenuId = 0;
      let secondMenus = [];
      let activeFirstTitle = '';

      this.firstMenus.forEach(item => {
        if (item.path == path) {
          activeFirstMenuId = item.pid;
          if (path == '/') {
            path = item.redirect;
          }
        }
      });

      let secondRoutes = [];
      for (let pid in this.secondMenusAll) {
        let items = this.secondMenusAll[pid];
        items.forEach(item => {
          if (item.path == path) {
            if (item.type == 'SUB_MENU') {
              activeFirstMenuId = item.p_id;
              activeSecondMenuId = item.id;
            } else {
              activeSecondMenuId = item.p_id;
              items.forEach(t => {
                if (t.id == item.p_id) {
                  activeFirstMenuId = t.p_id;
                }
              });
            }
            secondRoutes = items;
          }
        });
      }

      secondRoutes.forEach(item => {
        if (item.type == 'SUB_MENU') {
          secondMenus.push(item);
        }
      });

      // 一级标题
      this.firstMenus.forEach(item => {
        if (item.id == activeFirstMenuId) {
          activeFirstTitle = item.name;
        }
      });

      return {
        activeFirstMenuId,
        activeFirstTitle,
        activeSecondMenuId,
        secondMenus,
      };
    },

    onLogout: function () {
      runtime.logout();
      this.$router.push({ path: '/login', query: { from: encodeURIComponent(this.$route.fullPath) } });
    },
  },
};
</script>

<style lang="less">
.ivu-poptip-inner {
  border-radius: 1px !important;
}

.ivu-poptip-popper .ivu-poptip-body {
  padding: 0;
}

.app-team-info dl {
  margin: 0;

  div,
  a {
    display: block;
    padding: 10px 20px;
    color: #666;
    cursor: pointer;

    &:hover {
      background: #3283fa;
      color: #fff;
    }
  }
}
</style>
