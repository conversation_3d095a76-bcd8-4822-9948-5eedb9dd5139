<template>
  <div>
    <Modal
      ref="customModal"
      :value="value"
      width="620px"
      :title="title"
      :footer-hide="true"
      :mask-closable="false"
      :lock-scroll="true"
      @on-visible-change="changeVisible"
    >
      <div class="content">
        <div
          :class="{ 'item-active': item.current === '1' }"
          class="clinic-item"
          v-for="(item, index) in clinicList"
          :key="index"
          @click="switchClinic(item)"
        >
          <div>{{ item.name }}</div>
          <div v-if="item.current === '1'" class="login-tag">当前登入</div>
        </div>
      </div>
    </Modal>
    <AccessNote :confirmVisible="visibleAccess" @cancel="cancelFunc" @ok="confirmFunc" />
    <ChangeProv
      :confirmVisible="provVisible"
      :old-prov-name="oldProvName"
      :new-prov-name="newProvName"
      @cancel="cancelProv"
      @ok="confirmProv"
    />
  </div>
</template>

<script>
import S from '@/libs/util'; // Some commonly used tools
import * as runtime from '@/libs/runtime'; // Runtime information
import { getUser, logout } from '@/libs/runtime';
import AccessNote from '@/components/access-note';
import ChangeProv from '@/components/change-prov';
export default {
  name: 'switchModal',
  mixins: [],

  components: {
    AccessNote,
    ChangeProv,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '点击列表以切换诊所',
    },
  },

  data() {
    return {
      clinicList: [],
      loginUserInfo: {},
      visibleAccess: false,
      provVisible: false,
      oldProvName: '',
      newProvName: '',
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    confirmFunc() {
      this.$Modal.confirm({
        title: '确定开始使用系统？',
        onOk: () => {
          const { clinic_id, mer_uid } = this.loginUserInfo;
          this.$api.getConfirmUseSystem({ clinic_id, mer_uid }).then(res => {
            this.getSwitchclinic(this.loginUserInfo);
          });
        },
        onCancel: () => {},
      });
    },
    cancelFunc() {
      this.visibleAccess = false;
    },
    switchClinic(item) {
      if (item.current === '1') {
        return;
      }
      this.getSwitchclinic(item);
    },
    getSwitchclinic(item) {
      let params = {
        clinic_id: item.clinic_id,
      };
      this.$api.getSwitchclinic(params).then(userInfo => {
        this.closeModal();
        this.loginUserInfo = userInfo;

        if (userInfo?.is_ues_system_box === '1') {
          this.visibleAccess = true;
        } else if (userInfo?.is_cli_tip === '1') {
          this.oldProvName = this.loginUserInfo.change_company_tip?.old?.name || '';
          this.newProvName = this.loginUserInfo.change_company_tip?.now?.name || '';
          this.provVisible = true;
        } else {
          logout();
          this.$Message.success('切换成功');
          this.writeUserInfo(userInfo);
        }
      });
    },
    writeUserInfo(userInfo) {
      let from = this.$route.query.from || '/';
      const hisInfo = userInfo.his_info;
      let clinicLoginInfo = {
        uid: userInfo['mer_uid'],
        name: userInfo['user_name'],
        clinicid: userInfo['clinic_id'],
        clinic_name: userInfo['clinic_name'],
        auth_expired_time: userInfo['auth_expired_time'],
        auth_id: userInfo['auth_id'],
        auth_seq: userInfo['auth_seq'],
        mobile: userInfo['mobile'],
        role_name: userInfo['role_name'],
        expires: userInfo['expires'] ? Number(userInfo['expires']) : 7,
        time_stamp: this.$moment()
          .add(userInfo['expires'] ? Number(userInfo['expires']) : 7, 'days')
          .valueOf(),
        is_direct: userInfo['is_direct'],
        hy_menu_status: userInfo['hy_menu_status'],
        use_prod_package: userInfo['use_prod_package'],
        is_jishan_channel: userInfo['is_jishan_channel'],
        is_rst: userInfo['is_rst'],
        is_opc: userInfo['is_opc'],
        is_rst_opc: userInfo['is_rst_opc'],
        transform_type_flag: userInfo['transform_type_flag'],
        insure_status: userInfo['insure_status'],
        pay_platform_ap_status: userInfo['pay_platform_ap_status'],
        loginTime: new Date().getTime(),
        isLogin: true, // 是否已登录，针对踢下线功能上线时，未登录过的用户
        clinic_trace_code_status: userInfo['clinic_trace_code_status'], //1:开通诊所入库溯源码，2:未开通诊所入库溯源码
      };
      let expiresTime = userInfo['expire_secs']
        ? new Date(new Date().getTime() + userInfo.expire_secs * 1000)
        : this.$moment()
            .add(userInfo['expires'] ? Number(userInfo['expires']) : 7, 'days')
            .valueOf();
      // 鉴权
      // if (userInfo['expire_secs']) {
      //   expiresTime = new Date(new Date().getTime() + userInfo.expire_secs * 1000);
      // }else {
      //   expiresTime =
      // }
      clinicLoginInfo.expires = new Date(expiresTime);
      clinicLoginInfo.time_stamp = Math.floor(this.$moment(expiresTime).valueOf() / 1000);
      // 将date格式的日期转化为时间戳存储
      runtime.writeLoginCookie({ ...clinicLoginInfo });
      let hisLoginInfo = {};
      if (!S.isEmptyObject(hisInfo)) {
        hisLoginInfo = {
          uid: hisInfo['mer_uid'],
          name: hisInfo['user_name'],
          clinicid: hisInfo['clinic_id'],
          clinic_name: hisInfo['clinic_name'],
          auth_expired_time: hisInfo['auth_expired_time'],
          auth_id: hisInfo['auth_id'],
          auth_seq: hisInfo['auth_seq'],
          // expires: userInfo['expires'] ? Number(userInfo['expires']) : 27,
          // time_stamp: moment()
          //   .add(userInfo['expires'] ? Number(userInfo['expires']) : 27, 'days')
          //   .valueOf(),
          // his_early_access: hisInfo['his_early_access'],
          // his_early_access_v3: hisInfo['his_early_access_v3'],
          his_version: hisInfo['his_version'],
          is_direct: hisInfo['is_direct'],
          role_source: hisInfo['role_source'],
          // role_name: hisInfo['role_name']
          roles: hisInfo['roles'],
          is_rst: userInfo['is_rst'],
          is_opc: userInfo['is_opc'],
          is_rst_opc: userInfo['is_rst_opc'],
          transform_type_flag: userInfo['transform_type_flag'],
          insure_status: hisInfo['insure_status'],
          loginTime: new Date().getTime(),
          isLogin: true, // 是否已登录，针对踢下线功能上线时，未登录过的用户
          clinic_trace_code_status: userInfo['clinic_trace_code_status'], //1:开通诊所入库溯源码，2:未开通诊所入库溯源码
        };
        hisLoginInfo.time_stamp = Math.floor(this.$moment(expiresTime).valueOf() / 1000);
        hisLoginInfo.expires = new Date(expiresTime);
        runtime.writeHisLoginCookie({
          ...hisLoginInfo,
        });
      }
      try {
        let url = S.uri().path(decodeURIComponent(from)).build();
        console.log('-> %c url  === %o', 'font-size: 15px;color: green;', url);
        if (url.indexOf('/his/newHis/newHis') > -1) {
          url = '/';
        }
        location.href = url;
      } catch (e) {
        console.log('-> %c e  === %o', 'font-size: 15px;color: green;', e);
        location.href = '/';
      }
    },
    changeVisible(visible) {
      if (visible) {
        this.getSwitchcliniclist();
      } else {
        this.closeModal();
      }
    },

    clearData() {},

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },
    getSwitchcliniclist() {
      this.$api.getSwitchcliniclist().then(res => {
        this.clinicList = res;
      });
    },
    cancelProv() {
      this.provVisible = false;
    },
    confirmProv() {
      const { clinic_id, mer_uid } = this.loginUserInfo;
      this.$api.confirmChangeCompany({ clinic_id, mer_uid }).then(res => {
        this.cancelProv();
        this.getSwitchclinic(this.loginUserInfo);
      });
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 450px;
  min-height: 450px;
  overflow-y: auto;
}
.clinic-item {
  background: #f5f6f8;
  border-radius: 8px;
  padding: 20px;
  font-weight: 400;
  font-size: 14px;
  color: #303133;
  border: 1px solid #f5f6f8;
  line-height: 20px;
  margin-bottom: 18px;
  cursor: pointer;
  text-align: center;
  position: relative;
  &:hover {
    color: #3088ff;
    background: #f3f8ff;
    border: 1px solid #f5f6f8;
  }
}
.item-active {
  color: #3088ff;
  background: #f3f8ff;
  border: 1px solid #3088ff !important;
  cursor: not-allowed;
}
::v-deep .ivu-modal-header {
  border-bottom: none;
  text-align: center;
  padding-top: 24px;
  font-weight: 500;
  font-size: 16px;
  color: #000000;
  line-height: 18px;
}
.login-tag {
  width: 64px;
  height: 23px;
  background: #3088ff;
  border-radius: 0px 8px 0px 8px;
  font-weight: 400;
  font-size: 12px;
  color: #ffffff;
  line-height: 17px;
  position: absolute;
  top: 0px;
  right: -1px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
