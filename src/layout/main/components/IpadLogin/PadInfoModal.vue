<template>
  <Modal
    v-model="visible"
    title="榕管家登录"
    width="740"
    :closable="true"
    :mask-closable="false"
    class-name="vertical-center-modal pad-login-modal"
    @on-visible-change="changeVisible"
    :footer-hide="true"
  >
    <Tabs v-model="activeTab" :animated="false" @on-click="changeTab" class="pad-tabs">
      <TabPane label="扫码登录" name="qrcode">
        <QrCodeLogin ref="qrCodeRef" />
      </TabPane>
      <TabPane :label="`当前登录用户(${userCount})`" name="users">
        <LoginUsersModal ref="loginUserTable" @onlineUserCount="setUserAccount" />
      </TabPane>
    </Tabs>
  </Modal>
</template>

<script>
import QrCodeLogin from './QrCodeLogin.vue';
import LoginUsersModal from './LoginUsersModal.vue';

export default {
  name: 'PadInfoModal',
  components: {
    QrCodeLogin,
    LoginUsersModal,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeTab: 'qrcode',
      userCount: 0,
    };
  },
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      },
    },
  },
  watch: {},
  methods: {
    changeTab(name) {
      if (name === 'users') {
        this.$refs.loginUserTable.getOnlineUserList();
      }

      if (name === 'qrcode') {
        this.$refs.qrCodeRef.getQrCodeUrl();
      }
    },
    setUserAccount(count) {
      console.log('%c [ count ]-53-「PadInfoModal.vue」', 'font-size:13px; background:#8931b9; color:#cd75fd;', count);
      this.userCount = count;
      this.$emit('onlineUserCount', count);
    },
    changeVisible(v) {
      if (!v) {
        this.activeTab = 'qrcode';
      } else {
        this.$refs.qrCodeRef.getQrCodeUrl();
      }
    },
  },
};
</script>

<style lang="less" scoped>
/* 可根据需要自定义样式 */
:deep(.pad-tabs) {
  .ivu-tabs-nav-container {
    background: #f5f6f8;
    padding-left: 40px;
    color: #999999;

    .ivu-tabs-tab {
      padding: 11px 16px;
    }
  }
  .ivu-tabs-tabpane {
    padding: 0 20px;
  }
}
:deep(.pad-login-modal) {
  .ivu-modal-body {
    padding: 16px 0;
  }
}
</style>
