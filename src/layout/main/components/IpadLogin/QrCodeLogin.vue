<template>
  <div class="qr-code-login">
    <div class="qr-code-container" ref="qrCodeContainer"></div>
    <div class="refresh-mask" v-if="isExpired">
      <div class="refresh-content">
        <div>二维码已失效</div>
        <a @click="refreshQrCode">点击刷新</a>
      </div>
    </div>
    <div class="qr-code-tip">
      <template v-if="!isExpired"> 扫码登录移动端 </template>
    </div>
  </div>
</template>

<script>
import createQrCode from '@/mixins/creatQrCode';

export default {
  name: 'QrCodeLogin',
  mixins: [createQrCode],
  props: {},
  data() {
    return {
      qrCodeUrl: '',
      timer: null,
      countdownTimer: null,
      isExpired: false,
      remainingTime: 60, // 60秒倒计时
      exp: 60, // 二维码失效时间
    };
  },
  computed: {},
  methods: {
    handleVisibleChange(visible) {
      if (!visible) {
        this.clearTimers();
      } else {
        this.getQrCodeUrl();
      }
    },
    clearTimers() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    },
    startCountdown() {
      this.clearTimers();
      this.isExpired = false;
      this.remainingTime = this.exp;

      this.countdownTimer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--;
        } else {
          this.isExpired = true;
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
      }, 1000);
    },
    refreshQrCode() {
      this.getQrCodeUrl();
    },
    getQrCodeUrl() {
      this.$api.getPadQrCode().then(res => {
        this.qrCodeUrl = res.name + '-' + res.token;
        this.exp = res.exp;
        this.generateQrCode();
      });
    },
    generateQrCode() {
      try {
        const loginUrl = this.qrCodeUrl;
        this._creatQrCode(loginUrl, 'qrCodeContainer', {
          QR_width: 180,
          QR_height: 180,
        });
        this.startCountdown(); // 生成二维码后开始倒计时
      } catch (err) {
        console.error('生成二维码失败:', err);
        this.$Message.error('生成二维码失败，请重试');
      }
    },
  },
  beforeDestroy() {
    this.clearTimers();
  },
};
</script>

<style lang="less" scoped>
.qr-code-login {
  padding: 32px;
  text-align: center;
  position: relative;
  .qr-code-container {
    width: 200px;
    height: 200px;
    margin: 0 auto 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .refresh-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;

    .refresh-content {
      text-align: center;
      color: #333;
      margin-bottom: 12px;
      font-size: 14px;
    }
  }

  .qr-code-tip {
    color: #333333;
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
