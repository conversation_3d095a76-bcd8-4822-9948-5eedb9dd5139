<template>
  <!-- <Modal
    v-model="visible"
    title="当前移动端登录用户"
    :width="860"
    footer-hide
    class-name="pad-user-modal"
    @on-visible-change="handleVisibleChange"
  > -->
  <Table :columns="columns" :data="onlineUserList" :loading="loading" height="296" :max-height="400">
    <template slot-scope="{ row }" slot="login_time">
      {{ row.time | data_format }}
    </template>
    <template slot-scope="{ row }" slot="action">
      <a @click="handleKickOut(row)">踢下线</a>
    </template>
  </Table>
  <!-- </Modal> -->
</template>

<script>
export default {
  name: 'LoginUsersModal',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      columns: [
        {
          title: '登录用户',
          key: 'name',
        },
        {
          title: '手机号',
          key: 'mobile',
        },
        {
          title: '登录IP',
          key: 'ip',
        },
        {
          title: '登录设备',
          key: 'browser',
          tooltip: true,
        },
        {
          title: '登录时间',
          key: 'login_time',
          slot: 'login_time',
          width: 160,
        },
        {
          title: '操作',
          slot: 'action',
          width: 80,
        },
      ],
      onlineUserList: [],
    };
  },
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      },
    },
  },
  mounted() {
    this.$bus.$on('IPAD_LOGIN_SUCCESS', this.loginSuccessNotice);
    this.getUserList();
  },
  beforeDestroy() {
    // 清理事件监听
    this.$bus.$off('IPAD_LOGIN_SUCCESS', this.loginSuccessNotice);
  },
  watch: {},
  methods: {
    loginSuccessNotice() {
      this.getUserList();
    },
    handleVisibleChange(v) {
      v && this.getUserList();
    },
    getUserList() {
      // TODO: 调用接口获取用户列表
      this.$api.getPadLoginUsers().then(res => {
        this.onlineUserList = res.list;
        this.$emit('onlineUserCount', res.list.length);
      });
    },
    handleKickOut(row) {
      this.$Modal.confirm({
        title: '提示',
        content: `确定要将用户 ${row.name} 踢下线吗？`,
        onOk: () => {
          // TODO: 调用踢下线接口
          this.$api.kickOutPadUser({ member_id: row.member_id, device_id: row.device_id }).then(res => {
            this.$Message.success('操作成功');
            this.getUserList();
          });
        },
      });
    },
  },
};
</script>
<style lang="less">
.pad-user-modal {
  .ivu-modal-body {
    height: 450px;
  }
}
</style>
