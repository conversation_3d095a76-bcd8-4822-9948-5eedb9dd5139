<template>
  <div class="header-pad app-header_trigger">
    <Badge :count="userCount">
      <div class="header-quick-item" @click="showPadLogin">
        <svg-icon name="rgj-scan" size="18"></svg-icon>
        <span>榕管家</span>
      </div>
    </Badge>
    <pad-info-modal v-model="visible" @onlineUserCount="setUserAccount"></pad-info-modal>
  </div>
</template>

<script>
import PadInfoModal from './PadInfoModal.vue';
export default {
  name: 'IpadLogin',
  components: {
    PadInfoModal,
  },
  data() {
    return {
      visible: false,
      activeTab: 'qrcode',
      userCount: 0,
    };
  },
  computed: {},
  methods: {
    showPadLogin() {
      this.visible = true;
    },
    setUserAccount(count) {
      this.userCount = count;
    },
    showPadQrCode() {
      this.activeTab = 'qrcode';
    },
    showLoginUsersModal() {
      this.activeTab = 'users';
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.header-pad {
  display: inline-flex;
  align-items: center;
}
</style>
