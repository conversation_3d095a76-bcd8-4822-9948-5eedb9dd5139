<?xml version="1.0" encoding="UTF-8"?>
<svg width="68.278px" height="13.132px" viewBox="0 0 68.278 13.132" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <path d="M3.85,1.61 L0,11.606 L1.624,11.606 L2.562,9.03 L6.86,9.03 L7.798,11.606 L9.436,11.606 L5.586,1.61 L3.85,1.61 Z M3.024,7.77 L4.69,3.248 L4.746,3.248 L6.398,7.77 L3.024,7.77 Z M10.444,1.61 L10.444,11.606 L11.956,11.606 L11.956,1.61 L10.444,1.61 Z M15.596,2.212 L16.814,2.212 C16.786,2.576 16.744,2.926 16.688,3.276 L14.672,3.276 C14.994,2.982 15.302,2.632 15.596,2.212 Z M16.324,4.494 L16.296,4.55 C15.848,5.474 14.91,6.244 13.454,6.888 L14.35,7.966 C14.77,7.77 15.148,7.546 15.512,7.322 L15.512,13.076 L16.842,13.076 L16.842,12.53 L23.45,12.53 L23.45,13.076 L24.794,13.076 L24.794,7.126 L19.446,7.126 L20.272,6.286 C19.544,5.754 18.676,5.222 17.64,4.69 C17.654,4.634 17.668,4.564 17.696,4.494 L20.482,4.494 L20.482,3.276 L17.976,3.276 C18.032,2.94 18.06,2.59 18.088,2.212 L20.09,2.212 L20.09,1.036 L16.254,1.036 C16.366,0.784 16.478,0.532 16.576,0.252 L15.386,0 C14.91,1.064 14.336,1.876 13.692,2.45 L14.35,3.276 L13.72,3.276 L13.72,4.494 L16.324,4.494 Z M16.842,11.354 L16.842,10.43 L23.45,10.43 L23.45,11.354 L16.842,11.354 Z M16.842,9.282 L16.842,8.302 L23.45,8.302 L23.45,9.282 L16.842,9.282 Z M15.792,7.126 C16.394,6.678 16.87,6.188 17.206,5.656 C18.018,6.16 18.676,6.65 19.208,7.126 L15.792,7.126 Z M20.916,1.05 L20.916,6.342 L25.816,6.342 L25.816,1.05 L20.916,1.05 Z M24.514,5.18 L22.218,5.18 L22.218,2.226 L24.514,2.226 L24.514,5.18 Z M29.652,8.33 L32.536,8.33 L32.536,9.352 L29.652,9.352 L29.652,8.33 Z M32.536,7.238 L29.652,7.238 L29.652,6.16 L32.536,6.16 L32.536,7.238 Z M29.652,10.458 L32.536,10.458 L32.536,11.242 C32.536,11.606 32.368,11.788 32.046,11.788 L31.052,11.732 L31.374,12.978 L32.466,12.978 C33.362,12.978 33.824,12.488 33.824,11.522 L33.824,4.998 L28.364,4.998 L28.364,13.076 L29.652,13.076 L29.652,10.458 Z M38.598,6.328 C39.102,6.328 39.48,6.216 39.732,6.006 C39.984,5.768 40.18,5.068 40.292,3.906 L39.046,3.5 C39.018,4.284 38.934,4.76 38.822,4.928 C38.724,5.068 38.57,5.138 38.346,5.138 L36.68,5.138 C36.344,5.138 36.176,4.9 36.176,4.452 L36.176,3.052 C37.674,2.828 38.99,2.576 40.138,2.31 L39.704,1.134 C38.794,1.386 37.618,1.61 36.176,1.834 L36.176,0.182 L34.86,0.182 L34.86,4.732 C34.86,5.796 35.336,6.328 36.316,6.328 L38.598,6.328 Z M36.176,9.562 C37.576,9.422 38.906,9.198 40.138,8.862 L39.704,7.672 C38.668,7.98 37.492,8.218 36.176,8.358 L36.176,6.818 L34.86,6.818 L34.86,11.564 C34.86,12.404 35.336,12.838 36.288,12.838 L38.612,12.838 C39.13,12.838 39.508,12.726 39.746,12.516 C39.998,12.292 40.18,11.592 40.292,10.416 L39.088,10.024 C39.046,10.808 38.962,11.284 38.85,11.452 C38.752,11.578 38.584,11.648 38.36,11.648 L36.666,11.648 C36.33,11.648 36.176,11.522 36.176,11.27 L36.176,9.562 Z M32.816,1.106 L31.794,1.75 C32.074,2.156 32.34,2.548 32.592,2.94 C31.458,3.08 30.394,3.192 29.414,3.248 C30.142,2.548 30.8,1.638 31.388,0.49 L30.114,0.112 C29.666,1.064 29.008,2.016 28.154,2.968 C27.986,3.108 27.79,3.22 27.552,3.304 L27.916,4.41 C29.96,4.298 31.71,4.13 33.166,3.906 C33.334,4.2 33.488,4.494 33.628,4.774 L34.72,3.99 C34.272,3.178 33.642,2.212 32.816,1.106 Z M47.306,0.896 L42.434,0.896 L42.434,10.514 C42.112,10.57 41.79,10.64 41.454,10.71 L41.818,11.984 C44.226,11.466 46.34,10.906 48.188,10.304 L48.188,9.044 C47.908,9.128 47.614,9.226 47.306,9.324 L47.306,0.896 Z M43.736,10.234 L43.736,8.008 L46.018,8.008 L46.018,9.688 C45.29,9.87 44.52,10.052 43.736,10.234 Z M43.736,6.818 L43.736,4.998 L46.018,4.998 L46.018,6.818 L43.736,6.818 Z M43.736,3.794 L43.736,2.114 L46.018,2.114 L46.018,3.794 L43.736,3.794 Z M49.602,0.196 L49.602,2.884 L48.006,2.884 L48.006,4.172 L49.602,4.172 L49.602,4.312 C49.574,7.966 48.706,10.612 46.998,12.25 L47.992,13.132 C49.896,11.27 50.876,8.33 50.918,4.312 L50.918,4.172 L52.668,4.172 C52.64,7.896 52.598,10.08 52.514,10.738 C52.402,11.41 52.038,11.76 51.394,11.76 C51.03,11.76 50.61,11.732 50.12,11.69 L50.442,12.866 C50.988,12.894 51.38,12.922 51.604,12.922 C52.892,12.922 53.606,12.376 53.76,11.312 C53.9,10.248 53.97,7.434 53.97,2.884 L50.918,2.884 L50.918,0.196 L49.602,0.196 Z M66.598,0.224 C63.882,0.84 60.522,1.162 56.532,1.162 L56.952,2.366 C58.604,2.366 60.13,2.31 61.558,2.212 L61.558,4.13 L56.826,4.13 L56.826,5.418 L61.558,5.418 L61.558,7.406 L55.664,7.406 L55.664,8.68 L61.558,8.68 L61.558,11.228 C61.558,11.55 61.362,11.718 60.97,11.718 C60.326,11.718 59.654,11.69 58.954,11.648 L59.262,12.936 L61.432,12.936 C62.398,12.936 62.888,12.474 62.888,11.55 L62.888,8.68 L68.278,8.68 L68.278,7.406 L62.888,7.406 L62.888,5.418 L67.228,5.418 L67.228,4.13 L62.888,4.13 L62.888,2.1 C64.54,1.932 65.996,1.68 67.256,1.344 L66.598,0.224 Z" id="path-1"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="顶部导航栏样式优化" transform="translate(-894, -21.394)">
            <g id="编组-13" transform="translate(210, 0)">
                <g id="编组-12" transform="translate(650, 12)">
                    <g id="AI智能助手文字" transform="translate(34, 8)">
                        <rect id="矩形" fill-opacity="0" fill="#FFFFFF" x="0" y="0" width="69" height="16"></rect>
                        <g id="形状结合-2" transform="translate(0, 1.394)" fill-rule="nonzero">
                            <g id="形状结合">
                                <use fill="#333333" xlink:href="#path-1"></use>
                                <use fill="#FFFFFF" xlink:href="#path-1"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>