<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="100%" y1="100%" x2="1.36834988e-12%" y2="-2.77555756e-13%" id="linearGradient-1">
            <stop stop-color="#155BD4" offset="0%"></stop>
            <stop stop-color="#7166FF" offset="99.9317089%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill-rule="evenodd">
        <g id="引用来源展开" transform="translate(-1013, -760)">
            <g id="编组-9" transform="translate(226, 56)">
                <g id="编组-10" transform="translate(0, 594)">
                    <g id="编组-6" transform="translate(31, 40)">
                        <g id="暂停icon" transform="translate(756, 70)">
                            <circle id="椭圆形" cx="16" cy="16" r="16"></circle>
                            <rect id="矩形" fill="#FFFFFF" x="10.5" y="10.5" width="11" height="11" rx="1"></rect>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
