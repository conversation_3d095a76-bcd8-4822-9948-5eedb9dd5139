<?xml version="1.0" encoding="UTF-8"?>
<svg width="78.208px" height="15.104px" viewBox="0 0 78.208 15.104" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>AI智能助手文字大</title>
    <defs>
        <linearGradient x1="100%" y1="51.864883%" x2="1.36834988e-12%" y2="48.135117%" id="linearGradient-1">
            <stop stop-color="#155BD4" offset="0%"></stop>
            <stop stop-color="#7166FF" offset="99.9317089%"></stop>
        </linearGradient>
        <path d="M4.368,1.888 L0,13.312 L2,13.312 L3.04,10.448 L7.824,10.448 L8.864,13.312 L10.864,13.312 L6.496,1.888 L4.368,1.888 Z M3.6,8.912 L5.408,3.904 L5.472,3.904 L7.264,8.912 L3.6,8.912 Z M11.984,1.888 L11.984,13.312 L13.856,13.312 L13.856,1.888 L11.984,1.888 Z M18.016,2.624 L19.312,2.624 C19.28,3.008 19.232,3.36 19.168,3.712 L17.088,3.712 C17.408,3.392 17.728,3.04 18.016,2.624 Z M18.688,5.232 C18.192,6.224 17.12,7.088 15.488,7.808 L16.608,9.168 C17.024,8.96 17.424,8.736 17.808,8.512 L17.808,15.024 L19.488,15.024 L19.488,14.432 L26.88,14.432 L26.88,15.024 L28.576,15.024 L28.576,8.128 L22.432,8.128 L23.376,7.184 C22.56,6.576 21.568,5.984 20.384,5.376 L20.432,5.232 L23.584,5.232 L23.584,3.712 L20.768,3.712 C20.816,3.36 20.848,3.008 20.88,2.624 L23.136,2.624 L23.136,1.168 L18.832,1.168 C18.96,0.912 19.072,0.624 19.184,0.336 L17.68,0 C17.12,1.184 16.48,2.096 15.744,2.736 L16.528,3.712 L15.824,3.712 L15.824,5.232 L18.688,5.232 Z M19.488,12.96 L19.488,12.032 L26.88,12.032 L26.88,12.96 L19.488,12.96 Z M19.488,10.592 L19.488,9.6 L26.88,9.6 L26.88,10.592 L19.488,10.592 Z M18.368,8.128 C18.976,7.664 19.472,7.152 19.84,6.608 C20.656,7.12 21.344,7.616 21.92,8.128 L18.368,8.128 Z M24.016,1.216 L24.016,7.344 L29.744,7.344 L29.744,1.216 L24.016,1.216 Z M28.112,5.888 L25.648,5.888 L25.648,2.688 L28.112,2.688 L28.112,5.888 Z M34.128,9.632 L37.264,9.632 L37.264,10.672 L34.128,10.672 L34.128,9.632 Z M37.264,8.256 L34.128,8.256 L34.128,7.152 L37.264,7.152 L37.264,8.256 Z M34.128,12.048 L37.264,12.048 L37.264,12.784 C37.264,13.2 37.072,13.424 36.72,13.424 L35.6,13.344 L36.016,14.896 L37.248,14.896 C38.336,14.896 38.88,14.304 38.88,13.136 L38.88,5.712 L32.512,5.712 L32.512,15.008 L34.128,15.008 L34.128,12.048 Z M44.288,7.312 C44.88,7.312 45.328,7.184 45.616,6.944 C45.904,6.688 46.112,5.872 46.24,4.528 L44.688,4.016 C44.656,4.88 44.576,5.392 44.464,5.568 C44.352,5.728 44.192,5.808 43.968,5.808 L42.144,5.808 C41.76,5.808 41.584,5.536 41.584,5.024 L41.584,3.568 C43.264,3.312 44.752,3.04 46.064,2.752 L45.52,1.28 C44.496,1.552 43.184,1.808 41.584,2.064 L41.584,0.24 L39.936,0.24 L39.936,5.36 C39.936,6.656 40.512,7.312 41.68,7.312 L44.288,7.312 Z M41.584,11.024 C43.184,10.864 44.672,10.608 46.064,10.24 L45.52,8.752 C44.368,9.104 43.056,9.36 41.584,9.52 L41.584,7.84 L39.936,7.84 L39.936,13.216 C39.936,14.224 40.512,14.736 41.664,14.736 L44.288,14.736 C44.88,14.736 45.312,14.608 45.6,14.368 C45.888,14.112 46.112,13.312 46.24,11.952 L44.72,11.472 C44.672,12.336 44.592,12.848 44.464,13.024 C44.368,13.184 44.192,13.264 43.968,13.264 L42.128,13.264 C41.76,13.264 41.584,13.12 41.584,12.848 L41.584,11.024 Z M37.744,1.28 L36.464,2.096 C36.752,2.528 37.04,2.944 37.296,3.36 C36.112,3.488 34.992,3.6 33.936,3.664 C34.752,2.88 35.472,1.872 36.112,0.624 L34.512,0.144 C34.032,1.136 33.328,2.208 32.368,3.328 C32.16,3.52 31.904,3.648 31.616,3.744 L32.064,5.136 C34.352,5.008 36.336,4.8 38,4.544 C38.176,4.88 38.352,5.216 38.512,5.536 L39.888,4.56 C39.376,3.632 38.672,2.544 37.744,1.28 Z M54.272,1.024 L48.608,1.024 L48.608,12 L47.504,12.224 L47.968,13.824 C50.704,13.216 53.136,12.576 55.248,11.872 L55.248,10.288 L54.272,10.592 L54.272,1.024 Z M50.256,11.648 L50.256,9.264 L52.656,9.264 L52.656,11.056 C51.888,11.248 51.088,11.456 50.256,11.648 Z M50.256,7.776 L50.256,5.84 L52.656,5.84 L52.656,7.776 L50.256,7.776 Z M50.256,4.336 L50.256,2.544 L52.656,2.544 L52.656,4.336 L50.256,4.336 Z M56.768,0.24 L56.768,3.28 L55.008,3.28 L55.008,4.88 L56.768,4.88 L56.768,5.008 C56.736,9.136 55.76,12.128 53.856,13.984 L55.12,15.104 C57.28,12.976 58.384,9.616 58.432,5.008 L58.432,4.88 L60.256,4.88 C60.224,9.12 60.176,11.6 60.08,12.32 C59.968,13.024 59.568,13.376 58.896,13.376 C58.48,13.376 58,13.344 57.44,13.312 L57.84,14.768 C58.464,14.816 58.912,14.848 59.168,14.848 C60.64,14.848 61.456,14.256 61.648,13.072 C61.808,11.824 61.888,8.56 61.888,3.28 L58.432,3.28 L58.432,0.24 L56.768,0.24 Z M76.24,0.272 C73.104,0.976 69.28,1.328 64.752,1.328 L65.28,2.832 C67.12,2.832 68.832,2.768 70.432,2.672 L70.432,4.688 L65.088,4.688 L65.088,6.32 L70.432,6.32 L70.432,8.432 L63.76,8.432 L63.76,10.048 L70.432,10.048 L70.432,12.816 C70.432,13.152 70.208,13.328 69.792,13.328 C69.056,13.328 68.304,13.296 67.504,13.232 L67.888,14.848 L70.368,14.848 C71.52,14.848 72.112,14.304 72.112,13.216 L72.112,10.048 L78.208,10.048 L78.208,8.432 L72.112,8.432 L72.112,6.32 L77.008,6.32 L77.008,4.688 L72.112,4.688 L72.112,2.528 C73.968,2.336 75.616,2.048 77.056,1.68 L76.24,0.272 Z" id="path-2"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="AI智能助手-未展开" transform="translate(-284, -50.48)">
            <g id="编组-9" transform="translate(240, 30)">
                <g id="AI智能助手文字大" transform="translate(44, 18)">
                    <rect id="矩形" fill-opacity="0" fill="#FFFFFF" x="0" y="0" width="80" height="20"></rect>
                    <g id="形状结合-3" transform="translate(0, 2.48)" fill-rule="nonzero">
                        <g id="形状结合">
                            <use fill="#333333" xlink:href="#path-2"></use>
                            <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>