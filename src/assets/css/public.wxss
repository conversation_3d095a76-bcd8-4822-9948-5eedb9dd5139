.mt-0 {
  margin-top: 0px;
}
.mr-0 {
  margin-right: 0px;
}
.mb-0 {
  margin-bottom: 0px;
}
.ml-0 {
  margin-left: 0px;
}
.m-0 {
  margin: 0px;
}
.pt-0 {
  padding-top: 0px;
}
.pr-0 {
  padding-right: 0px;
}
.pb-0 {
  padding-bottom: 0px;
}
.pl-0 {
  padding-left: 0px;
}
.p-0 {
  padding: 0px;
}
.mt-1 {
  margin-top: 1px;
}
.mr-1 {
  margin-right: 1px;
}
.mb-1 {
  margin-bottom: 1px;
}
.ml-1 {
  margin-left: 1px;
}
.m-1 {
  margin: 1px;
}
.pt-1 {
  padding-top: 1px;
}
.pr-1 {
  padding-right: 1px;
}
.pb-1 {
  padding-bottom: 1px;
}
.pl-1 {
  padding-left: 1px;
}
.p-1 {
  padding: 1px;
}
.mt-2 {
  margin-top: 2px;
}
.mr-2 {
  margin-right: 2px;
}
.mb-2 {
  margin-bottom: 2px;
}
.ml-2 {
  margin-left: 2px;
}
.m-2 {
  margin: 2px;
}
.pt-2 {
  padding-top: 2px;
}
.pr-2 {
  padding-right: 2px;
}
.pb-2 {
  padding-bottom: 2px;
}
.pl-2 {
  padding-left: 2px;
}
.p-2 {
  padding: 2px;
}
.mt-3 {
  margin-top: 3px;
}
.mr-3 {
  margin-right: 3px;
}
.mb-3 {
  margin-bottom: 3px;
}
.ml-3 {
  margin-left: 3px;
}
.m-3 {
  margin: 3px;
}
.pt-3 {
  padding-top: 3px;
}
.pr-3 {
  padding-right: 3px;
}
.pb-3 {
  padding-bottom: 3px;
}
.pl-3 {
  padding-left: 3px;
}
.p-3 {
  padding: 3px;
}
.mt-4 {
  margin-top: 4px;
}
.mr-4 {
  margin-right: 4px;
}
.mb-4 {
  margin-bottom: 4px;
}
.ml-4 {
  margin-left: 4px;
}
.m-4 {
  margin: 4px;
}
.pt-4 {
  padding-top: 4px;
}
.pr-4 {
  padding-right: 4px;
}
.pb-4 {
  padding-bottom: 4px;
}
.pl-4 {
  padding-left: 4px;
}
.p-4 {
  padding: 4px;
}
.mt-5 {
  margin-top: 5px;
}
.mr-5 {
  margin-right: 5px;
}
.mb-5 {
  margin-bottom: 5px;
}
.ml-5 {
  margin-left: 5px;
}
.m-5 {
  margin: 5px;
}
.pt-5 {
  padding-top: 5px;
}
.pr-5 {
  padding-right: 5px;
}
.pb-5 {
  padding-bottom: 5px;
}
.pl-5 {
  padding-left: 5px;
}
.p-5 {
  padding: 5px;
}
.mt-6 {
  margin-top: 6px;
}
.mr-6 {
  margin-right: 6px;
}
.mb-6 {
  margin-bottom: 6px;
}
.ml-6 {
  margin-left: 6px;
}
.m-6 {
  margin: 6px;
}
.pt-6 {
  padding-top: 6px;
}
.pr-6 {
  padding-right: 6px;
}
.pb-6 {
  padding-bottom: 6px;
}
.pl-6 {
  padding-left: 6px;
}
.p-6 {
  padding: 6px;
}
.mt-7 {
  margin-top: 7px;
}
.mr-7 {
  margin-right: 7px;
}
.mb-7 {
  margin-bottom: 7px;
}
.ml-7 {
  margin-left: 7px;
}
.m-7 {
  margin: 7px;
}
.pt-7 {
  padding-top: 7px;
}
.pr-7 {
  padding-right: 7px;
}
.pb-7 {
  padding-bottom: 7px;
}
.pl-7 {
  padding-left: 7px;
}
.p-7 {
  padding: 7px;
}
.mt-8 {
  margin-top: 8px;
}
.mr-8 {
  margin-right: 8px;
}
.mb-8 {
  margin-bottom: 8px;
}
.ml-8 {
  margin-left: 8px;
}
.m-8 {
  margin: 8px;
}
.pt-8 {
  padding-top: 8px;
}
.pr-8 {
  padding-right: 8px;
}
.pb-8 {
  padding-bottom: 8px;
}
.pl-8 {
  padding-left: 8px;
}
.p-8 {
  padding: 8px;
}
.mt-9 {
  margin-top: 9px;
}
.mr-9 {
  margin-right: 9px;
}
.mb-9 {
  margin-bottom: 9px;
}
.ml-9 {
  margin-left: 9px;
}
.m-9 {
  margin: 9px;
}
.pt-9 {
  padding-top: 9px;
}
.pr-9 {
  padding-right: 9px;
}
.pb-9 {
  padding-bottom: 9px;
}
.pl-9 {
  padding-left: 9px;
}
.p-9 {
  padding: 9px;
}
.mt-10 {
  margin-top: 10px;
}
.mr-10 {
  margin-right: 10px;
}
.mb-10 {
  margin-bottom: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.m-10 {
  margin: 10px;
}
.pt-10 {
  padding-top: 10px;
}
.pr-10 {
  padding-right: 10px;
}
.pb-10 {
  padding-bottom: 10px;
}
.pl-10 {
  padding-left: 10px;
}
.p-10 {
  padding: 10px;
}
.mt-11 {
  margin-top: 11px;
}
.mr-11 {
  margin-right: 11px;
}
.mb-11 {
  margin-bottom: 11px;
}
.ml-11 {
  margin-left: 11px;
}
.m-11 {
  margin: 11px;
}
.pt-11 {
  padding-top: 11px;
}
.pr-11 {
  padding-right: 11px;
}
.pb-11 {
  padding-bottom: 11px;
}
.pl-11 {
  padding-left: 11px;
}
.p-11 {
  padding: 11px;
}
.mt-12 {
  margin-top: 12px;
}
.mr-12 {
  margin-right: 12px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-12 {
  margin-left: 12px;
}
.m-12 {
  margin: 12px;
}
.pt-12 {
  padding-top: 12px;
}
.pr-12 {
  padding-right: 12px;
}
.pb-12 {
  padding-bottom: 12px;
}
.pl-12 {
  padding-left: 12px;
}
.p-12 {
  padding: 12px;
}
.mt-13 {
  margin-top: 13px;
}
.mr-13 {
  margin-right: 13px;
}
.mb-13 {
  margin-bottom: 13px;
}
.ml-13 {
  margin-left: 13px;
}
.m-13 {
  margin: 13px;
}
.pt-13 {
  padding-top: 13px;
}
.pr-13 {
  padding-right: 13px;
}
.pb-13 {
  padding-bottom: 13px;
}
.pl-13 {
  padding-left: 13px;
}
.p-13 {
  padding: 13px;
}
.mt-14 {
  margin-top: 14px;
}
.mr-14 {
  margin-right: 14px;
}
.mb-14 {
  margin-bottom: 14px;
}
.ml-14 {
  margin-left: 14px;
}
.m-14 {
  margin: 14px;
}
.pt-14 {
  padding-top: 14px;
}
.pr-14 {
  padding-right: 14px;
}
.pb-14 {
  padding-bottom: 14px;
}
.pl-14 {
  padding-left: 14px;
}
.p-14 {
  padding: 14px;
}
.mt-15 {
  margin-top: 15px;
}
.mr-15 {
  margin-right: 15px;
}
.mb-15 {
  margin-bottom: 15px;
}
.ml-15 {
  margin-left: 15px;
}
.m-15 {
  margin: 15px;
}
.pt-15 {
  padding-top: 15px;
}
.pr-15 {
  padding-right: 15px;
}
.pb-15 {
  padding-bottom: 15px;
}
.pl-15 {
  padding-left: 15px;
}
.p-15 {
  padding: 15px;
}
.mt-16 {
  margin-top: 16px;
}
.mr-16 {
  margin-right: 16px;
}
.mb-16 {
  margin-bottom: 16px;
}
.ml-16 {
  margin-left: 16px;
}
.m-16 {
  margin: 16px;
}
.pt-16 {
  padding-top: 16px;
}
.pr-16 {
  padding-right: 16px;
}
.pb-16 {
  padding-bottom: 16px;
}
.pl-16 {
  padding-left: 16px;
}
.p-16 {
  padding: 16px;
}
.mt-17 {
  margin-top: 17px;
}
.mr-17 {
  margin-right: 17px;
}
.mb-17 {
  margin-bottom: 17px;
}
.ml-17 {
  margin-left: 17px;
}
.m-17 {
  margin: 17px;
}
.pt-17 {
  padding-top: 17px;
}
.pr-17 {
  padding-right: 17px;
}
.pb-17 {
  padding-bottom: 17px;
}
.pl-17 {
  padding-left: 17px;
}
.p-17 {
  padding: 17px;
}
.mt-18 {
  margin-top: 18px;
}
.mr-18 {
  margin-right: 18px;
}
.mb-18 {
  margin-bottom: 18px;
}
.ml-18 {
  margin-left: 18px;
}
.m-18 {
  margin: 18px;
}
.pt-18 {
  padding-top: 18px;
}
.pr-18 {
  padding-right: 18px;
}
.pb-18 {
  padding-bottom: 18px;
}
.pl-18 {
  padding-left: 18px;
}
.p-18 {
  padding: 18px;
}
.mt-19 {
  margin-top: 19px;
}
.mr-19 {
  margin-right: 19px;
}
.mb-19 {
  margin-bottom: 19px;
}
.ml-19 {
  margin-left: 19px;
}
.m-19 {
  margin: 19px;
}
.pt-19 {
  padding-top: 19px;
}
.pr-19 {
  padding-right: 19px;
}
.pb-19 {
  padding-bottom: 19px;
}
.pl-19 {
  padding-left: 19px;
}
.p-19 {
  padding: 19px;
}
.mt-20 {
  margin-top: 20px;
}
.mr-20 {
  margin-right: 20px;
}
.mb-20 {
  margin-bottom: 20px;
}
.ml-20 {
  margin-left: 20px;
}
.m-20 {
  margin: 20px;
}
.pt-20 {
  padding-top: 20px;
}
.pr-20 {
  padding-right: 20px;
}
.pb-20 {
  padding-bottom: 20px;
}
.pl-20 {
  padding-left: 20px;
}
.p-20 {
  padding: 20px;
}
.mt-21 {
  margin-top: 21px;
}
.mr-21 {
  margin-right: 21px;
}
.mb-21 {
  margin-bottom: 21px;
}
.ml-21 {
  margin-left: 21px;
}
.m-21 {
  margin: 21px;
}
.pt-21 {
  padding-top: 21px;
}
.pr-21 {
  padding-right: 21px;
}
.pb-21 {
  padding-bottom: 21px;
}
.pl-21 {
  padding-left: 21px;
}
.p-21 {
  padding: 21px;
}
.mt-22 {
  margin-top: 22px;
}
.mr-22 {
  margin-right: 22px;
}
.mb-22 {
  margin-bottom: 22px;
}
.ml-22 {
  margin-left: 22px;
}
.m-22 {
  margin: 22px;
}
.pt-22 {
  padding-top: 22px;
}
.pr-22 {
  padding-right: 22px;
}
.pb-22 {
  padding-bottom: 22px;
}
.pl-22 {
  padding-left: 22px;
}
.p-22 {
  padding: 22px;
}
.mt-23 {
  margin-top: 23px;
}
.mr-23 {
  margin-right: 23px;
}
.mb-23 {
  margin-bottom: 23px;
}
.ml-23 {
  margin-left: 23px;
}
.m-23 {
  margin: 23px;
}
.pt-23 {
  padding-top: 23px;
}
.pr-23 {
  padding-right: 23px;
}
.pb-23 {
  padding-bottom: 23px;
}
.pl-23 {
  padding-left: 23px;
}
.p-23 {
  padding: 23px;
}
.mt-24 {
  margin-top: 24px;
}
.mr-24 {
  margin-right: 24px;
}
.mb-24 {
  margin-bottom: 24px;
}
.ml-24 {
  margin-left: 24px;
}
.m-24 {
  margin: 24px;
}
.pt-24 {
  padding-top: 24px;
}
.pr-24 {
  padding-right: 24px;
}
.pb-24 {
  padding-bottom: 24px;
}
.pl-24 {
  padding-left: 24px;
}
.p-24 {
  padding: 24px;
}
.mt-25 {
  margin-top: 25px;
}
.mr-25 {
  margin-right: 25px;
}
.mb-25 {
  margin-bottom: 25px;
}
.ml-25 {
  margin-left: 25px;
}
.m-25 {
  margin: 25px;
}
.pt-25 {
  padding-top: 25px;
}
.pr-25 {
  padding-right: 25px;
}
.pb-25 {
  padding-bottom: 25px;
}
.pl-25 {
  padding-left: 25px;
}
.p-25 {
  padding: 25px;
}
.mt-26 {
  margin-top: 26px;
}
.mr-26 {
  margin-right: 26px;
}
.mb-26 {
  margin-bottom: 26px;
}
.ml-26 {
  margin-left: 26px;
}
.m-26 {
  margin: 26px;
}
.pt-26 {
  padding-top: 26px;
}
.pr-26 {
  padding-right: 26px;
}
.pb-26 {
  padding-bottom: 26px;
}
.pl-26 {
  padding-left: 26px;
}
.p-26 {
  padding: 26px;
}
.mt-27 {
  margin-top: 27px;
}
.mr-27 {
  margin-right: 27px;
}
.mb-27 {
  margin-bottom: 27px;
}
.ml-27 {
  margin-left: 27px;
}
.m-27 {
  margin: 27px;
}
.pt-27 {
  padding-top: 27px;
}
.pr-27 {
  padding-right: 27px;
}
.pb-27 {
  padding-bottom: 27px;
}
.pl-27 {
  padding-left: 27px;
}
.p-27 {
  padding: 27px;
}
.mt-28 {
  margin-top: 28px;
}
.mr-28 {
  margin-right: 28px;
}
.mb-28 {
  margin-bottom: 28px;
}
.ml-28 {
  margin-left: 28px;
}
.m-28 {
  margin: 28px;
}
.pt-28 {
  padding-top: 28px;
}
.pr-28 {
  padding-right: 28px;
}
.pb-28 {
  padding-bottom: 28px;
}
.pl-28 {
  padding-left: 28px;
}
.p-28 {
  padding: 28px;
}
.mt-29 {
  margin-top: 29px;
}
.mr-29 {
  margin-right: 29px;
}
.mb-29 {
  margin-bottom: 29px;
}
.ml-29 {
  margin-left: 29px;
}
.m-29 {
  margin: 29px;
}
.pt-29 {
  padding-top: 29px;
}
.pr-29 {
  padding-right: 29px;
}
.pb-29 {
  padding-bottom: 29px;
}
.pl-29 {
  padding-left: 29px;
}
.p-29 {
  padding: 29px;
}
.mt-30 {
  margin-top: 30px;
}
.mr-30 {
  margin-right: 30px;
}
.mb-30 {
  margin-bottom: 30px;
}
.ml-30 {
  margin-left: 30px;
}
.m-30 {
  margin: 30px;
}
.pt-30 {
  padding-top: 30px;
}
.pr-30 {
  padding-right: 30px;
}
.pb-30 {
  padding-bottom: 30px;
}
.pl-30 {
  padding-left: 30px;
}
.p-30 {
  padding: 30px;
}
.mt-31 {
  margin-top: 31px;
}
.mr-31 {
  margin-right: 31px;
}
.mb-31 {
  margin-bottom: 31px;
}
.ml-31 {
  margin-left: 31px;
}
.m-31 {
  margin: 31px;
}
.pt-31 {
  padding-top: 31px;
}
.pr-31 {
  padding-right: 31px;
}
.pb-31 {
  padding-bottom: 31px;
}
.pl-31 {
  padding-left: 31px;
}
.p-31 {
  padding: 31px;
}
.mt-32 {
  margin-top: 32px;
}
.mr-32 {
  margin-right: 32px;
}
.mb-32 {
  margin-bottom: 32px;
}
.ml-32 {
  margin-left: 32px;
}
.m-32 {
  margin: 32px;
}
.pt-32 {
  padding-top: 32px;
}
.pr-32 {
  padding-right: 32px;
}
.pb-32 {
  padding-bottom: 32px;
}
.pl-32 {
  padding-left: 32px;
}
.p-32 {
  padding: 32px;
}
.mt-33 {
  margin-top: 33px;
}
.mr-33 {
  margin-right: 33px;
}
.mb-33 {
  margin-bottom: 33px;
}
.ml-33 {
  margin-left: 33px;
}
.m-33 {
  margin: 33px;
}
.pt-33 {
  padding-top: 33px;
}
.pr-33 {
  padding-right: 33px;
}
.pb-33 {
  padding-bottom: 33px;
}
.pl-33 {
  padding-left: 33px;
}
.p-33 {
  padding: 33px;
}
.mt-34 {
  margin-top: 34px;
}
.mr-34 {
  margin-right: 34px;
}
.mb-34 {
  margin-bottom: 34px;
}
.ml-34 {
  margin-left: 34px;
}
.m-34 {
  margin: 34px;
}
.pt-34 {
  padding-top: 34px;
}
.pr-34 {
  padding-right: 34px;
}
.pb-34 {
  padding-bottom: 34px;
}
.pl-34 {
  padding-left: 34px;
}
.p-34 {
  padding: 34px;
}
.mt-35 {
  margin-top: 35px;
}
.mr-35 {
  margin-right: 35px;
}
.mb-35 {
  margin-bottom: 35px;
}
.ml-35 {
  margin-left: 35px;
}
.m-35 {
  margin: 35px;
}
.pt-35 {
  padding-top: 35px;
}
.pr-35 {
  padding-right: 35px;
}
.pb-35 {
  padding-bottom: 35px;
}
.pl-35 {
  padding-left: 35px;
}
.p-35 {
  padding: 35px;
}
.mt-36 {
  margin-top: 36px;
}
.mr-36 {
  margin-right: 36px;
}
.mb-36 {
  margin-bottom: 36px;
}
.ml-36 {
  margin-left: 36px;
}
.m-36 {
  margin: 36px;
}
.pt-36 {
  padding-top: 36px;
}
.pr-36 {
  padding-right: 36px;
}
.pb-36 {
  padding-bottom: 36px;
}
.pl-36 {
  padding-left: 36px;
}
.p-36 {
  padding: 36px;
}
.mt-37 {
  margin-top: 37px;
}
.mr-37 {
  margin-right: 37px;
}
.mb-37 {
  margin-bottom: 37px;
}
.ml-37 {
  margin-left: 37px;
}
.m-37 {
  margin: 37px;
}
.pt-37 {
  padding-top: 37px;
}
.pr-37 {
  padding-right: 37px;
}
.pb-37 {
  padding-bottom: 37px;
}
.pl-37 {
  padding-left: 37px;
}
.p-37 {
  padding: 37px;
}
.mt-38 {
  margin-top: 38px;
}
.mr-38 {
  margin-right: 38px;
}
.mb-38 {
  margin-bottom: 38px;
}
.ml-38 {
  margin-left: 38px;
}
.m-38 {
  margin: 38px;
}
.pt-38 {
  padding-top: 38px;
}
.pr-38 {
  padding-right: 38px;
}
.pb-38 {
  padding-bottom: 38px;
}
.pl-38 {
  padding-left: 38px;
}
.p-38 {
  padding: 38px;
}
.mt-39 {
  margin-top: 39px;
}
.mr-39 {
  margin-right: 39px;
}
.mb-39 {
  margin-bottom: 39px;
}
.ml-39 {
  margin-left: 39px;
}
.m-39 {
  margin: 39px;
}
.pt-39 {
  padding-top: 39px;
}
.pr-39 {
  padding-right: 39px;
}
.pb-39 {
  padding-bottom: 39px;
}
.pl-39 {
  padding-left: 39px;
}
.p-39 {
  padding: 39px;
}
.mt-40 {
  margin-top: 40px;
}
.mr-40 {
  margin-right: 40px;
}
.mb-40 {
  margin-bottom: 40px;
}
.ml-40 {
  margin-left: 40px;
}
.m-40 {
  margin: 40px;
}
.pt-40 {
  padding-top: 40px;
}
.pr-40 {
  padding-right: 40px;
}
.pb-40 {
  padding-bottom: 40px;
}
.pl-40 {
  padding-left: 40px;
}
.p-40 {
  padding: 40px;
}
.mt-41 {
  margin-top: 41px;
}
.mr-41 {
  margin-right: 41px;
}
.mb-41 {
  margin-bottom: 41px;
}
.ml-41 {
  margin-left: 41px;
}
.m-41 {
  margin: 41px;
}
.pt-41 {
  padding-top: 41px;
}
.pr-41 {
  padding-right: 41px;
}
.pb-41 {
  padding-bottom: 41px;
}
.pl-41 {
  padding-left: 41px;
}
.p-41 {
  padding: 41px;
}
.mt-42 {
  margin-top: 42px;
}
.mr-42 {
  margin-right: 42px;
}
.mb-42 {
  margin-bottom: 42px;
}
.ml-42 {
  margin-left: 42px;
}
.m-42 {
  margin: 42px;
}
.pt-42 {
  padding-top: 42px;
}
.pr-42 {
  padding-right: 42px;
}
.pb-42 {
  padding-bottom: 42px;
}
.pl-42 {
  padding-left: 42px;
}
.p-42 {
  padding: 42px;
}
.mt-43 {
  margin-top: 43px;
}
.mr-43 {
  margin-right: 43px;
}
.mb-43 {
  margin-bottom: 43px;
}
.ml-43 {
  margin-left: 43px;
}
.m-43 {
  margin: 43px;
}
.pt-43 {
  padding-top: 43px;
}
.pr-43 {
  padding-right: 43px;
}
.pb-43 {
  padding-bottom: 43px;
}
.pl-43 {
  padding-left: 43px;
}
.p-43 {
  padding: 43px;
}
.mt-44 {
  margin-top: 44px;
}
.mr-44 {
  margin-right: 44px;
}
.mb-44 {
  margin-bottom: 44px;
}
.ml-44 {
  margin-left: 44px;
}
.m-44 {
  margin: 44px;
}
.pt-44 {
  padding-top: 44px;
}
.pr-44 {
  padding-right: 44px;
}
.pb-44 {
  padding-bottom: 44px;
}
.pl-44 {
  padding-left: 44px;
}
.p-44 {
  padding: 44px;
}
.mt-45 {
  margin-top: 45px;
}
.mr-45 {
  margin-right: 45px;
}
.mb-45 {
  margin-bottom: 45px;
}
.ml-45 {
  margin-left: 45px;
}
.m-45 {
  margin: 45px;
}
.pt-45 {
  padding-top: 45px;
}
.pr-45 {
  padding-right: 45px;
}
.pb-45 {
  padding-bottom: 45px;
}
.pl-45 {
  padding-left: 45px;
}
.p-45 {
  padding: 45px;
}
.mt-46 {
  margin-top: 46px;
}
.mr-46 {
  margin-right: 46px;
}
.mb-46 {
  margin-bottom: 46px;
}
.ml-46 {
  margin-left: 46px;
}
.m-46 {
  margin: 46px;
}
.pt-46 {
  padding-top: 46px;
}
.pr-46 {
  padding-right: 46px;
}
.pb-46 {
  padding-bottom: 46px;
}
.pl-46 {
  padding-left: 46px;
}
.p-46 {
  padding: 46px;
}
.mt-47 {
  margin-top: 47px;
}
.mr-47 {
  margin-right: 47px;
}
.mb-47 {
  margin-bottom: 47px;
}
.ml-47 {
  margin-left: 47px;
}
.m-47 {
  margin: 47px;
}
.pt-47 {
  padding-top: 47px;
}
.pr-47 {
  padding-right: 47px;
}
.pb-47 {
  padding-bottom: 47px;
}
.pl-47 {
  padding-left: 47px;
}
.p-47 {
  padding: 47px;
}
.mt-48 {
  margin-top: 48px;
}
.mr-48 {
  margin-right: 48px;
}
.mb-48 {
  margin-bottom: 48px;
}
.ml-48 {
  margin-left: 48px;
}
.m-48 {
  margin: 48px;
}
.pt-48 {
  padding-top: 48px;
}
.pr-48 {
  padding-right: 48px;
}
.pb-48 {
  padding-bottom: 48px;
}
.pl-48 {
  padding-left: 48px;
}
.p-48 {
  padding: 48px;
}
.mt-49 {
  margin-top: 49px;
}
.mr-49 {
  margin-right: 49px;
}
.mb-49 {
  margin-bottom: 49px;
}
.ml-49 {
  margin-left: 49px;
}
.m-49 {
  margin: 49px;
}
.pt-49 {
  padding-top: 49px;
}
.pr-49 {
  padding-right: 49px;
}
.pb-49 {
  padding-bottom: 49px;
}
.pl-49 {
  padding-left: 49px;
}
.p-49 {
  padding: 49px;
}
.mt-50 {
  margin-top: 50px;
}
.mr-50 {
  margin-right: 50px;
}
.mb-50 {
  margin-bottom: 50px;
}
.ml-50 {
  margin-left: 50px;
}
.m-50 {
  margin: 50px;
}
.pt-50 {
  padding-top: 50px;
}
.pr-50 {
  padding-right: 50px;
}
.pb-50 {
  padding-bottom: 50px;
}
.pl-50 {
  padding-left: 50px;
}
.p-50 {
  padding: 50px;
}
.ivu-tooltip-popper {
  max-height: 400px;
}
.ivu-tooltip-inner {
  word-break: break-all;
  white-space: normal;
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.ivu-tooltip-inner::-webkit-scrollbar {
  display: none;
}
.viewer-fixed {
  z-index: 9999 !important;
}
.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
  border-color: #447cdd !important;
  box-shadow: 0 0 0 2px rgba(21, 91, 212, 0.2);
  outline: 0;
}
.el-cascader .el-input__inner[disabled] {
  border-color: #bcc3d7 !important;
  background-color: #f3f3f3 !important;
  color: #AAAAAA !important;
}
.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: #447cdd !important;
}
.ivu-table {
  color: #323232;
  font-size: 13px;
}
.cursor {
  cursor: pointer;
}
.show-menus-modal .ivu-modal-mask,
.show-menus-modal .ivu-modal-wrap {
  left: 210px;
}
.custom_white_space .ivu-tooltip-inner {
  white-space: pre-wrap;
}
.justified-text {
  text-align: justify;
  text-justify: distribute-all-lines;
  text-align-last: justify;
}
.justified-text-3 {
  display: inline-block;
  width: 39px;
}
.justified-text-4 {
  display: inline-block;
  width: 52px;
}
.justified-text-5 {
  display: inline-block;
  width: 65px;
}
.ivu-modal-content {
  border-radius: 4px;
}
.disableTableRow {
  background-color: #f6f6f6!important;
  color: #ccc!important;
}
.custom-option-table {
  min-width: 500px;
  padding: 0px 0px 5px;
}
.custom-option-table li:first-child {
  position: sticky;
  top: 0px;
  padding: 0px;
}
.custom-option-table .option-header {
  display: flex;
  background: #F5F6F8;
  padding: 7px 16px;
}
.custom-option-table .option-header .header-cell {
  font-weight: 500;
  font-size: 13px;
  color: #909399;
  line-height: 18px;
}
.custom-option-table .option-empty {
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.customer-option-table {
  min-width: 300px !important;
  top: unset !important;
  bottom: 40px !important;
  left: 0px !important;
  max-height: 264px;
  height: fit-content;
}
.customer-option-table .popper__arrow {
  display: none !important;
}
.customer-option-table .danger-color {
  color: #E63E3E !important;
}
.customer-option-table .customer-item-header {
  background: #F5F6F8;
  height: 34px !important;
  position: sticky;
  top: 0px;
  z-index: 10;
}
.customer-option-table .customer-item-header .customer-item {
  font-size: 12px;
  color: #333 !important;
  line-height: 18px;
  white-space: normal !important;
  font-weight: bold !important;
}
.customer-option-table .customer-item-box--active {
  background: #ebf7ff;
}
.customer-option-table .customer-item-box {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
}
.customer-option-table .customer-item-box .customer-item {
  padding: 8px 10px;
  height: 100%;
  display: inline-block;
  color: #909399;
  line-height: 18px;
  min-height: 22px;
  white-space: pre-line;
  font-weight: 400;
  font-size: 12px;
}
.customer-option-table .customer-item-box .customer-item:last-child {
  margin-right: 20px;
}
.customer-option-table .customer-item-box .customer-item--disabled {
  color: #ccc !important;
}
.customer-option-table .el-select-dropdown__list {
  padding: 0px;
}
.customer-option-table .el-select-dropdown__list li:first-child {
  position: sticky;
  top: 0px;
  z-index: 10;
  cursor: default;
  height: 46px;
}
.customer-option-table .el-select-dropdown__list li:last-child:hover {
  background: none;
  cursor: default;
}
.customer-option-table .el-select-dropdown__list .el-select-dropdown__item {
  padding: 0px !important;
  height: auto !important;
}
.customer-option-table .el-select-dropdown__list .el-select-dropdown__item .custom-item-empty {
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.custom-radiu-poptip .ivu-poptip-inner {
  border-radius: 4px !important;
}
::-webkit-scrollbar-thumb {
  background: #e6eaed;
  background-clip: content-box;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 6px;
  cursor: pointer;
}
::-webkit-scrollbar-thumb:hover {
  background: #dee2e6;
  background-clip: content-box;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
}
::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0);
  opacity: 0;
}
::-webkit-scrollbar {
  background: transparent;
}
.ivu-input::-webkit-input-placeholder {
  color: #bbbbbb;
}
