//.vertical-center-modal{
//    display: flex;
//    align-items: center;
//    justify-content: center;
//  .ivu-modal{
//    top: -28px;
//  }
//}
// 生成 margin 和 padding 的通用方法
.generate-spaces(@i) when (@i =< 50) {
  // margin
  .mt-@{i} { margin-top: unit(@i, px); }
  .mr-@{i} { margin-right: unit(@i, px); }
  .mb-@{i} { margin-bottom: unit(@i, px); }
  .ml-@{i} { margin-left: unit(@i, px); }
  .m-@{i}  { margin: unit(@i, px); }

  // padding
  .pt-@{i} { padding-top: unit(@i, px); }
  .pr-@{i} { padding-right: unit(@i, px); }
  .pb-@{i} { padding-bottom: unit(@i, px); }
  .pl-@{i} { padding-left: unit(@i, px); }
  .p-@{i}  { padding: unit(@i, px); }

  // 递归调用
  .generate-spaces((@i + 1));
}

// 从0开始生成
.generate-spaces(0);

.ivu-tooltip-popper {
  max-height: 400px;

}

.ivu-tooltip-inner {
  // white-space: pre-wrap;
}

.ivu-tooltip-inner {
  //数字字母换行

  word-break: break-all;
  // white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: none; //兼容火狐
  -ms-overflow-style: none; //兼容IE10
  &::-webkit-scrollbar {
    display: none;
  }
}

.viewer-fixed {
  z-index: 9999 !important;
}

.el-cascader .el-input .el-input__inner:focus, .el-cascader .el-input.is-focus .el-input__inner {
  border-color: #447cdd !important;
  box-shadow: 0 0 0 2px rgba(21, 91, 212, 0.2);
  outline: 0;
}

.el-cascader .el-input__inner[disabled] {
  border-color: #bcc3d7 !important;
  background-color: #f3f3f3 !important;
  color: #AAAAAA !important;

}

.el-cascader-node.in-active-path, .el-cascader-node.is-active, .el-cascader-node.is-selectable.in-checked-path {
  color: #447cdd !important;
}


.ivu-table-small {
  //font-size: 14px;
}

.ivu-table {
  color: #323232;
  font-size: 13px;
}

.cursor {
  cursor: pointer;
}


//iview 相关全局样式
.show-menus-modal {
  .ivu-modal-mask, .ivu-modal-wrap {
    left: 210px;
  }
}

// renderHeader 提示语句，支持换行样式
.custom_white_space {
  .ivu-tooltip-inner {
    white-space: pre-wrap;
  }
}

.justified-text {
  text-align: justify;
  text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
  text-align-last: justify;
}

.justified-text-3 {
  display: inline-block;
  width: 39px;
}

.justified-text-4 {
  display: inline-block;
  width: 52px;
}

.justified-text-5 {
  display: inline-block;
  width: 65px;
}

.ivu-modal-content {
  border-radius: 4px;
}

//.modal-select {
//  z-index: 3333 !important;
//}
// 表格禁用行样式
.disableTableRow{
  background-color: #f6f6f6!important;
  color: #ccc!important;
}


// 在options内置table
.custom-option-table {
  min-width: 500px;
  padding: 0px 0px 5px;

  li:first-child {
    position: sticky;
    top: 0px;
    padding: 0px;
  }

  .option-header {
    display: flex;
    background: #F5F6F8;
    padding: 7px 16px;

    .header-cell {
      font-weight: 500;
      font-size: 13px;
      color: #909399;
      line-height: 18px;
    }
  }

  .option-empty {
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// element 在options内置table 当前用于远程搜索，将弹出数据一直置于input上方，relative使用
.customer-option-table {
  min-width: 300px !important;
  top: unset !important;
  bottom: 40px !important;
  left: 0px !important;
  max-height: 264px;
  height: fit-content;

  .popper__arrow {
    display: none !important;
  }

  .danger-color {
    color: #E63E3E !important;
  }

  .customer-item-header {
    background: #F5F6F8;
    height: 34px !important;
    position: sticky;
    top: 0px;
    z-index: 10;

    .customer-item {
      font-size: 12px;
      color: #333 !important;
      line-height: 18px;
      white-space: normal !important;
      font-weight: bold !important;
    }
  }

  .customer-item-box--active {
    background: #ebf7ff;
  }

  .customer-item-box {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;

    .customer-item {
      padding: 8px 10px;
      height: 100%;
      display: inline-block;
      color: #909399;

      line-height: 18px;
      min-height: 22px;
      white-space: pre-line;
      font-weight: 400;
      font-size: 12px;

      &:last-child {
        margin-right: 20px;
      }
    }

    .customer-item--disabled {
      color: #ccc !important;
    }
  }

  .el-select-dropdown__list {
    padding: 0px;

    li:first-child {
      position: sticky;
      top: 0px;
      z-index: 10;
      cursor: default;
      height: 46px;
    }

    li:last-child:hover {
      background: none;
      cursor: default;
    }

    .el-select-dropdown__item {
      padding: 0px !important;
      height: auto !important;

      .custom-item-empty {
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

}

// poptip圆角
.custom-radius-poptip {
  .ivu-poptip-inner {
    border-radius: 4px !important;
  }
}


::-webkit-scrollbar-thumb {
  background: #e6eaed;
  background-clip: content-box;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 6px;
  cursor: pointer;
}
::-webkit-scrollbar-thumb:hover {
  background: #dee2e6;
  background-clip: content-box;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
}
::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0);
  opacity: 0;
}

::-webkit-scrollbar {
  background: transparent;
}

@search-default-height: 52px; // 32 + 20
@list-theme-color:#155BD4; // #3088FF;
@list-base-size: 13px;
@input-default-border-color: #DCDFE6;
// 按钮统一变量
@btn-height: 32px;
@btn-primary-bg-color:  #155BD4; // #3088FF;
@btn-primary-border-color: #3088FF; // 边框色
@btn-primary-color: #FFF;
@btn-default-bg-color: #FFF;
@btn-default-color: #606266;
@btn-default-border-color: #DCDFE6; // 边框色
@btn-radius: 4px;
@btn-size: 13px;

.standardTable-wrap {

  // 禁用的表格行置灰样式
  .ivu-table .del-cell td{
    color: #C0C4CC;
    a, p, div, span {
      color: #C0C4CC;
    }
    .global-status-dot {
      background: #C0C4CC !important;
    }
  }
}
input:disabled::placeholder {
  color: transparent;
}

.ivu-input::-webkit-input-placeholder{
  color: #bbbbbb;
}
