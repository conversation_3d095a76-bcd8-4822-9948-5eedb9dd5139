import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import * as Sentry from '@sentry/vue';
import './plugins/iview';
import 'font-awesome/css/font-awesome.min.css';
import './index.less';
import './flex.less';
import './plugins/element.js';
import './plugins/svg-icon.js';
import '@/assets/font/font.css';
import util from '@/libs/util'; // 工具
import '@/assets/iconfont/iconfont.css';
import VuePageStack from '@/components/vue-page-stack'; // 页面导航管理器
import * as lodash from 'lodash-es';
import moment from 'moment';
import VueStorage from 'vue-ls';
// 按需引入 echarts 核心模块
import * as echarts from 'echarts/core';
// 引入常用的图表类型
import { BarChart, LineChart, PieChart, ScatterChart } from 'echarts/charts';
// 引入常用的组件
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components';
// 引入 Canvas 渲染器
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  CanvasRenderer
]);
import * as filters from '@/libs/filters';
import { Bus } from '@/libs/bus';
import API from './api/index';
import io from '@/libs/io';
import prints from '@/libs/print';
import Viewer from 'v-viewer';
import 'viewerjs/dist/viewer.css';
import draggable from 'vuedraggable';
import eChartFn from '@/components/k-configure-echarts/index.js';
import ChartPanel from '@/components/k-configure-echarts/index.vue';
import './directives';
import MarkStatus from './components/MarkStatus';
import StatusText from './components/StatusText/index.vue';
import BackButton from '@/components/BackButton/BackButton';
import RemoteSelectSearch from './components/RemoteSelectSearch/index.vue';
import Dvd from '@/components/dvd';
import KPage from '@/components/k-page';
import KWidget from '@/components/k-widget';
import KLink from '@/components/k-link';
import VueAwesomeCountdown from 'vue-awesome-countdown';
import MaterialPicture from './components/MaterialCenter/MaterialPicture.vue';
import MaterialVideo from './components/MaterialCenter/MaterialVideo.vue';
import FullTabs from '@/plugins/jump-tabs.js';
import TableStatus from './components/TableStatus';
import OperationFolding from './components/operationFolding/index.vue';
import Scaner from './plugins/scaner';
import ProductShow from './components/commonProduct/productShow.vue';
import cdiv from './components/cdiv';
import SocketManager from './plugins/socketio';
// 先引入到入口文件
import ResizeObserver from 'resize-observer-polyfill';
import commandDialog from './components/commandDialog';
import StandardTable from "@/components/StandardTable/index.vue";

Vue.prototype.$dialog = commandDialog;
// 增加判断如果不支持当前依赖再设置即可
if (window.ResizeObserver === undefined) {
  window.ResizeObserver = ResizeObserver;
}
Vue.use(SocketManager);
Vue.use(Scaner);
Vue.component('MaterialPicture', MaterialPicture);
Vue.component('StandardTable', StandardTable);
Vue.component('MaterialVideo', MaterialVideo);
Vue.use(FullTabs);
Vue.use(Viewer);
// Vue.use(draggable);
Vue.use(prints);
Vue.component(ChartPanel.name, ChartPanel);
console.log('=>(main.js:51) abc');
Vue.component('MarkStatus', MarkStatus);
Vue.component('TableStatus', TableStatus);
Vue.component('StatusText', StatusText);
Vue.component('draggable', draggable);
Vue.component('OperationFolding', OperationFolding);
Vue.component('ProductShow', ProductShow);
Vue.component('cdiv', cdiv);
for (const key in filters) {
  Vue.filter(key, filters[key]);
}
Vue.prototype.$eChartFn = eChartFn;
Vue.prototype.$lodash = lodash;
Vue.prototype.$moment = moment;
Vue.prototype.$http = io;
//事件bus
const bus = new Vue();
Vue.prototype.$bus = bus;
Vue.prototype.$api = new API(io);

// Vue.use(vRegion)
// Vue.use(Fragment.Plugin)
Vue.use(VuePageStack, { router });

Vue.component('Dvd', Dvd);
Vue.component('KPage', KPage);
Vue.component('KWidget', KWidget);
Vue.component('KLink', KLink);
Vue.component('BackButton', BackButton);
Vue.component('RemoteSelectSearch', RemoteSelectSearch);
Vue.use(VueAwesomeCountdown, 'vac');
Vue.prototype.$echarts = echarts;
// import logger from './libs/logger';

Vue.use(VueStorage, {
  namespace: 'bms__',
  name: 'ls',
  storage: 'local',
});
// if (config.codeVersion == undefined) util.log('环境变量VUE_APP_CODE_VERSION未定义', 'VUE_APP_CODE_VERSION');
/**
 * 生产环境关掉提示
 */
Vue.config.productionTip = false;

if (!util.isProdEnv) util.log(process.env.VUE_APP_NODE_ENV, 'NODE_ENV');
// if (!util.isProdEnv) require('@/mock');
if (process.env.VUE_APP_NODE_ENV === 'production') {
  Sentry.init({
    Vue,
    dsn: 'https://<EMAIL>/4',
    integrations: [Sentry.browserTracingIntegration({ router }), Sentry.replayIntegration()],
    release: 'v1.0.2',
    environment: process.env.VUE_APP_NODE_ENV,
    // Tracing
    tracesSampleRate: 0.1, //  Capture 100% of the transactions
    // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
    tracePropagationTargets: ['https://clinic.rsjxx.com'],
    // Session Replay
    replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
    replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
    beforeSend(event, hint) {
      if (/Failed to execute 'removeChild' on 'Node'/i.test(hint.originalException)) {
        return null;
      }
      if (/Object captured as promise rejection with keys/i.test(hint.originalException)) {
        return null;
      }
      if (/Failed to fetch/i.test(hint.originalException)) {
        return null;
      }
      if (/Loading chunk/i.test(hint.originalException)) {
        return null;
      }
      if (/Can't find variable: WeixinJSBridge/i.test(hint.originalException)) {
        return null;
      }
      if (/Non-Error promise rejection captured with value/i.test(hint.originalException)) {
        return null;
      }
      if (/iView warn/i.test(hint.originalException)) {
        return null;
      }
      if (/window.android/i.test(hint.originalException)) {
        return null;
      }
      return event;
    },
  });
}

// 忽略某些警告， 该配置只对开发环境有效
Vue.config.warnHandler = function (err, vm, info) {
  if (err.indexOf('Invalid prop: type check failed for prop "reference". Expected Object, got HTMLDivElement') > -1)
    return;
  console.error('[Vue warn]: ' + err + info);
};
/******* 过滤器 Begin *********/
/******* 过滤器 End *********/

const setClientHeight = () => {
  store.dispatch('app/setClientHeight', document.documentElement.clientHeight);
};
new Vue({
  router,
  store,
  // logger,
  render: h => h(App),
  mounted() {
    window.addEventListener('resize', () => setClientHeight());
    store.dispatch('app/setClientHeight', document.documentElement.clientHeight).then();
    Vue.nextTick(SocketManager.init);
  },
  beforeDestroy() {
    window.removeEventListener('resize', setClientHeight);
  },
}).$mount('#app');
