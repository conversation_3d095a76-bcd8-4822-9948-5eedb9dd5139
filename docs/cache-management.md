# 缓存管理最佳实践

## 🎯 概述

本文档介绍如何有效管理项目缓存，避免因缓存问题导致的构建错误和开发困扰。

## 🛠️ 缓存管理工具

### 快速命令

```bash
# 基础缓存清理（推荐日常使用）
npm run cache:clear

# 深度缓存清理（解决顽固问题）
npm run cache:deep

# 完全重置（包括 node_modules）
npm run cache:reset

# 清理缓存后构建
npm run build:clean

# 清理缓存后开发
npm run dev:clean
```

### 手动清理

```bash
# 使用缓存管理脚本
node scripts/cache-manager.js [basic|deep|full]

# 手动删除缓存目录
rm -rf node_modules/.cache dist .eslintcache
```

## 🚨 何时需要清理缓存

### 必须清理的情况

1. **依赖更新后出现奇怪错误**
   - 升级 webpack、babel、vue-cli 等构建工具
   - 更新 lodash、moment 等大型库
   - 添加或删除 webpack 插件

2. **构建结果不符合预期**
   - 代码修改后构建结果没有更新
   - 删除的文件仍然出现在构建中
   - 新添加的文件没有被处理

3. **开发环境异常**
   - 热更新失效
   - 样式不生效
   - 组件更新不及时

### 建议清理的情况

1. **切换分支后**
   - 特别是涉及构建配置的分支
   - 依赖版本差异较大的分支

2. **长时间开发后**
   - 缓存文件过大影响性能
   - 定期清理保持环境整洁

3. **部署前**
   - 确保构建结果的一致性
   - 避免缓存导致的部署问题

## 🔧 缓存配置优化

### Webpack 5 缓存配置

```javascript
// vue.config.js
config.cache = {
  type: 'filesystem',
  cacheDirectory: path.join(__dirname, './node_modules/.cache/webpack'),
  // 缓存版本，修改此值可强制清除缓存
  version: '1.0.0',
  buildDependencies: {
    config: [__filename],
    tsconfig: [path.resolve(__dirname, 'tsconfig.json')],
    package: [path.resolve(__dirname, 'package.json')],
  },
  // 缓存名称，区分不同环境
  name: `${process.env.NODE_ENV}-${process.env.VUE_APP_NODE_ENV}`,
};
```

### 缓存失效策略

1. **文件变化自动失效**
   - `package.json` 变化时清除缓存
   - 构建配置文件变化时清除缓存

2. **版本控制失效**
   - 修改 `cache.version` 强制清除所有缓存
   - 适用于重大更新或问题排查

3. **环境隔离**
   - 不同环境使用不同的缓存目录
   - 避免环境间缓存冲突

## 📋 故障排查流程

### 1. 基础排查
```bash
# 清理基础缓存
npm run cache:clear
npm run serve:test
```

### 2. 深度排查
```bash
# 深度清理
npm run cache:deep
npm run build:test
```

### 3. 完全重置
```bash
# 完全重置（最后手段）
npm run cache:reset
```

### 4. 验证修复
```bash
# 验证开发环境
npm run serve:test

# 验证构建
npm run build:test

# 验证静态服务
serve -s dist
```

## 🎯 预防措施

### 1. 定期清理
- 每周清理一次基础缓存
- 重大更新前清理深度缓存
- 发布前完全重置验证

### 2. 监控缓存大小
```bash
# 检查缓存目录大小
du -sh node_modules/.cache
du -sh dist
```

### 3. 团队协作
- 在 README 中说明缓存清理方法
- 遇到问题时优先尝试清理缓存
- 分享缓存相关的解决方案

### 4. CI/CD 配置
```yaml
# GitHub Actions 示例
- name: Clear cache on dependency changes
  if: steps.cache-deps.outputs.cache-hit != 'true'
  run: npm run cache:clear
```

## 🚀 性能优化建议

### 1. 缓存目录优化
- 使用 SSD 存储缓存文件
- 定期清理过期缓存
- 监控磁盘空间使用

### 2. 缓存策略优化
- 合理设置缓存失效条件
- 区分开发和生产环境缓存
- 使用版本号管理缓存

### 3. 构建优化
- 启用持久化缓存
- 优化 babel 和 typescript 缓存
- 使用多进程构建

## 📚 相关资源

- [Webpack 5 缓存指南](https://webpack.js.org/configuration/cache/)
- [Vue CLI 缓存配置](https://cli.vuejs.org/guide/webpack.html#caching)
- [项目构建性能优化](./build-optimization.md)

---

**记住**：遇到奇怪的构建问题时，清理缓存往往是最简单有效的解决方案！
